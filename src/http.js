import axios from "axios";
import VueCookies from "vue-cookies";
import router from "./router";
import { Notification } from "element-ui";
import Vue from "vue";
// import { uuid } from "vue-uuid";

// axios 配置
// axios.defaults.timeout = 50000;
// axios.defaults.baseURL = process.env.VUE_APP_BASE_API;
// axios.defaults.withCredentials = true;
const http = axios.create({
  timeout: 50000,
  baseURL: process.env.VUE_APP_BASE_API // 地址1
  // baseURL: "https://tg-api.estar-go.com"
});

// http request 拦截器
http.interceptors.request.use(
  (config) => {
    let params = {};
    console.log(config);
    if (["post", "put"].includes(config.method.toLocaleLowerCase())) {
      params = config.data;
    } else if (config.method.toLocaleLowerCase() === "get") {
      params = config.params;
    }
    if (params?.from === "mini") {
      config.headers.visitor = params.visitor;
      config.headers.token = params.token;
      if (params.operationId) {
        config.headers["Operation-Id"] = params.operationId;
      }
    } else {
      const token = VueCookies.get("user_token");
      if (token) {
        config.headers.token = token;
      }
    }
    config.headers.client = "web";
    const userInfo = localStorage.getItem("user_info");
    if (userInfo) {
      const { employee_id, name } = JSON.parse(userInfo);
      VueCookies.set("operatorId", employee_id);
      VueCookies.set("operatorName", name);
    }
    // 只针对结算收费接口修改请求超时时长
    if (config.url === "/api/order-service/admin/order/create") {
      config.timeout = 120000;
    }
    if (config.params && config.params.exportData === 1) {
      // 暂时为get方法
      config.responseType = "blob";
      config.timeout = 10 * 60 * 1000;
    }
    return config;
  },
  (err) => {
    return Promise.reject(err);
  }
);

// http response 拦截器
http.interceptors.response.use(
  (response) => {
    if (response.data == null) response.data = [];
    return response;
  },
  (error) => {
    console.log(error);
    if (error.toString().indexOf("Network Error") > -1) {
      Vue.prototype.$message.error("服务端网络异常，请重试或者刷新页面！");
      return;
    }
    if (error.toString().toLowerCase().indexOf("timeout") > -1) {
      Vue.prototype.$message.error("请求超时，请重试或者刷新页面！");
      return;
    }
    if (error.response) {
      const { status } = error.response;
      // console.log(error.response);
      if (Notification) {
        Notification.closeAll();
      }
      // 只有在当前路由不是登录页面才跳转
      if (status === 401) {
        localStorage.removeItem("user_token");
        Vue.prototype.$message.info("登录信息已失效，请重新登录！");
        router.push({ path: "/login" });
        return;
      } else if (status === 400) {
        if (error.response?.data?.err) {
          Vue.prototype.$message.error(
            error.response.data.err || error.response.data.message
          );
        }
        return Promise.reject(error.response.data);
      } else if (status === 403) {
        Vue.prototype.$message.error("权限不足，请联系管理员！");
        return false;
      } else if (status === 404) {
        Vue.prototype.$message.error("请求的接口不存在！");
        return false;
      } else if (status === 510) {
        Vue.prototype.$message.error("token错误，请退出重新登录！");
        const err = {
          err: "token错误，请退出重新登录！"
        };
        return Promise.reject(err);
        // return;
      }
      return Promise.reject(error.response.data);
    } else {
      Vue.prototype.$message.error("未知错误，请重试或者刷新页面！");
      return Promise.reject(error);
    }
  }
);

export default http;
