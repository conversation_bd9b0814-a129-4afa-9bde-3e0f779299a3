/* eslint-disable */
<template>
  <div v-cloak id="app">
    <!-- <el-collapse-transition> -->
    <router-view />
    <!-- </el-collapse-transition> -->
    <soundPlayer v-if="showAudioPlayer"></soundPlayer>
  </div>
</template>
<script>
import soundPlayer from "@/components/weihu/soundPlayer.vue";

export default {
  components: {
    soundPlayer
  },
  computed: {
    showAudioPlayer() {
      return this.$store.getters.showAudioPlayer;
    }
  },
  mounted() {}
};
</script>
<style lang="scss">
@import url("./views/questionManagement/management/styles/icon.scss");
@import url("./views/questionManagement/materials/questions/common/css/icon.scss");
@import url("./views/questionManagement/management/styles/reset.scss");
</style>
<style lang="less">
[v-cloak] {
  display: none !important;
}
.el-popover.my-popover {
  padding: 0;
  border: 1px solid #2d80ed;
}
body {
  margin: 0;
  padding: 0;
  overflow-y: hidden;
  -moz-osx-font-smoothing: grayscale;
}
.el-main::-webkit-scrollbar {
  width: 0;
  height: 0;
  display: none;
}
#chargeOptionHover {
  z-index: 9999;
  border: 1px solid #2d80ed;
  padding: 10px;
  border-radius: 3px;
  background-color: #fff;
  width: 300px;
  margin-left: -110px;
}

/*三角气泡*/
// #chargeOptionHover:after {
//   content: "";
//   position: absolute;
//   top: -10px;
//   left: 50%;
//   border: 10px solid white;
//   border-bottom-color: #67c23a;
//   border-top: 0;
//   margin: 0 0 -10px -10px;
// }

.balloon-top:before {
  border-style: solid;
  border-width: 0 6px 6px 6px;
  border-color: transparent transparent #fff transparent;
  content: "";
  position: absolute;
  top: -5px;
  left: 50%;
  margin-left: -5px;
  display: block;
  width: 0px;
  height: 0px;
  z-index: 0;
}
.balloon-top:after {
  border-style: solid;
  border-width: 0 7px 7px 7px;
  border-color: transparent transparent #2d80ed transparent;
  content: "";
  position: absolute;
  top: -7px;
  left: 50%;
  margin-left: -6px;
  display: block;
  width: 0px;
  height: 0px;
  z-index: -1;
}
#nprogress .bar {
  background: #2d80ed !important;
}
#app {
  min-height: 762px;
  min-width: 1366px;
}

html {
  /*隐藏滚动条，当IE下溢出，仍然可以滚动*/
  -ms-overflow-style: none;
  /*火狐下隐藏滚动条*/
  overflow: -moz-scrollbars-none;
  -webkit-font-smoothing: auto !important;
}

/*日期*/
.tg-date-picker.el-picker-panel {
  border: 1px solid @base-color!important;
  border-radius: 4px;
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  .el-picker-panel__content {
    width: 286px;
  }
  .el-picker-panel__sidebar {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
  }
  .popper__arrow {
    display: none;
  }
  .el-date-table td.in-range div {
    background-color: #ebf4ff;
  }
  .el-date-table td.end-date span,
  .el-date-table td.start-date span {
    border-radius: 4px;
    background-color: @base-color;
  }
  .el-date-table td span {
    width: 30px;
    height: 30px;
    font-family: @text-famliy_normal;
    font-size: 14px;
    line-height: 30px;
    border-radius: 4px;
  }
  .el-date-range-picker__content {
    padding: 20px;
  }
  .el-date-table tr {
    margin-top: 6px;
  }
  .el-date-table tr:first-child {
    margin-top: 0;
  }
  .el-date-table tr .available:first-child {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
  }
  .el-date-table tr .available:last-child {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
  }
  .el-date-table td {
    width: 30px;
    height: 30px;
    padding: 3px 0;
    // margin: 6px 0;
  }
  .el-date-table td div {
    height: 30px;
    padding: 0;
  }
  .el-date-table td:first-of-type {
    padding-left: 0;
  }
  .el-date-table td:last-of-type {
    padding-right: 0;
  }
  .el-date-table tr td:first-child div {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
  }
  .el-date-table tr td:last-child div {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
  }
  .el-date-range-picker__content .el-date-range-picker__header div {
    font-size: 14px;
    font-family: @text-famliy_normal;
  }
}
.tg-date--range.el-date-range-picker {
  width: 576px;
}
.tg-date--range.el-date-range-picker.has-sidebar {
  width: 684px;
}
.el-menu--vertical {
  .el-menu-item {
    height: 40px;
    line-height: 40px;
  }
}
/**时间 */
.tg-time-picker.el-time-panel,
.el-time-range-picker.el-picker-panel {
  margin-top: -1px !important;
  border: 1px solid @base-color;
  .popper__arrow {
    display: none;
  }
  .el-time-panel__content::after,
  .el-time-panel__content::before {
    border-color: @base-color;
  }
}
.el-time-range-picker {
  .el-time-panel__content {
    border: 1px solid @base-color;
  }
}

@media (min-width: 1000px) {
  //删除提示框
  .el-message-box {
    padding-bottom: 0 !important;
    width: 460px !important;
    height: 300px;
    .el-message-box__header {
      padding: 0;
      height: 55px;
      line-height: 55px;
      padding-left: 24px;
      border-bottom: 1px solid #e9f0f7;
      position: relative;
      &::after {
        content: "";
        position: absolute;
        background-color: @base-color;
        top: 17px;
        left: 16px;
        height: 20px;
        width: 2px;
        z-index: 1;
        border-radius: 2px;
      }
      .el-message-box__title {
        font-family: @text-famliy_semibold;
        font-size: 16px;
        font-weight: bold;
        line-height: 55px;
      }
    }
    .el-icon-warning {
      background: url("~@/assets/图片/tips_info.png");
      background-size: 100% 100%;
      width: 106px;
      height: 80px;
    }
    .el-icon-warning:before {
      content: "1";
      visibility: hidden;
    }
    .el-message-box__message {
      padding-top: 113px;
      p {
        font-size: @text-size_medium;
        font-family: @text-color_second;
        color: @text-famliy_medium;
        text-align: center;
        position: relative;
        z-index: 3;
      }
    }
    .el-message-box__content {
      height: 190px;
      padding: 0;
    }
    .el-message-box__status {
      top: 42px;
      transform: translateX(-50%);
      left: 50%;
    }
    .el-message-box__status + .el-message-box__message {
      padding-left: 0;
      padding-right: 0;
    }
    .el-message-box__btns {
      border-top: 1px solid #e9f0f7;
      padding: 11px 16px 11px 0;
      .el-button--default {
        background-color: transparent;
        border: 1px solid @base-color;
        color: @base-color;
        padding: 0 17px;
        height: 32px;
        font-family: @text-famliy_light;
        font-weight: normal;
        font-size: @text-size_normal;
        &:hover {
          background-color: @base-color;
          color: #fff;
        }
      }
      .el-button--primary {
        background-color: @base-color;
        color: #fff;
        &:hover {
          background: #4c92f0;
          border-color: #4c92f0;
        }
      }
    }
    // .el-icon-close:before {
    //   content: "1";
    //   visibility: hidden;
    // }
    // .el-icon-close {
    //   background: url("~@/assets/图片/icon_close.png");
    //   background-size: 100% 100%;
    //   width: 12px;
    //   height: 12px;
    // }
    .el-message-box__headerbtn {
      top: 0px;
      right: 26px;
    }
  }
}
@media (max-width: 768px) {
  .el-message-box {
    width: 320px !important;
  }
  #app {
    width: 100vw;
    height: 100vh;
    min-height: auto;
    min-width: auto;
  }
  .el-notification {
    width: 80% !important;
  }
  .message-info {
    min-width: 80% !important;
  }
  .message-error {
    min-width: 80% !important;
  }
  .message-success {
    min-width: 80% !important;
  }
}
.living_video {
  height: 400px;
  .el-message-box__headerbtn {
    top: 3px;
  }
  .el-message-box__content {
    height: 288px;
    padding: 0;
    .el-message-box__message {
      padding-left: 20px;
      padding-right: 20px;
      p {
        text-align: left;
      }
    }
  }
}
.performances_message {
  height: 160px;
  .el-message-box__headerbtn {
    top: 0px;
    right: 15px;
  }
  .el-message-box__content {
    height: 50px;
    .el-message-box__message {
      padding-top: 14px;
    }
  }
}
.el-icon-close:before {
  content: "1";
  visibility: hidden;
}
.el-icon-close {
  background: url("~@/assets/图片/icon_close.png");
  background-size: 100% 100%;
  width: 12px;
  height: 12px;
}
.el-icon-message-success {
  background: url("~@/assets/图片/icon-message-success.png");
  background-size: 100% 100%;
}
.el-icon-message-success:before {
  content: "替";
  font-size: 24px;
  visibility: hidden;
}

.el-icon-message-success {
  font-size: 24px;
}
.el-icon-message-success:before {
  content: "\e611";
}
.message-success {
  min-width: 540px !important;
  height: 64px;
  border-left: 2px solid #0fce8c !important;
  background: #f3ffed !important;
  .el-notification__title {
    color: #2d80ed;
  }
}
.el-icon-message-warning {
  background: url("~@/assets/图片/icon-message-warning.png");
  background-size: 100% 100%;
}
.el-icon-message-warning:before {
  content: "替";
  font-size: 24px;
  visibility: hidden;
}

.el-icon-message-warning {
  font-size: 24px;
}
.el-icon-message-warning:before {
  content: "\e611";
}
.message-warning {
  min-width: 540px !important;
  height: 64px;
  border-left: 2px solid #ffaa2f !important;
  background: #fff5e8 !important;
  .el-notification__title {
    color: #ffaa2f;
  }
}
.el-icon-message-error {
  background: url("~@/assets/图片/icon-message-error.png");
  background-size: 100% 100%;
}
.el-icon-message-error:before {
  content: "替";
  font-size: 24px;
  visibility: hidden;
}

.el-icon-message-error {
  font-size: 24px;
}
.el-icon-message-error:before {
  content: "\e611";
}
.message-error {
  min-width: 540px !important;
  height: 64px;
  // border-radius: 6px !important;
  border-left: 2px solid #fe4f4f !important;
  background: #ffeced !important;
  .el-notification__title {
    color: #fe4f4f;
  }
}
.el-icon-message-info {
  background: url("~@/assets/图片/icon-message-info.png");
  background-size: 100% 100%;
}
.el-icon-message-info:before {
  content: "替";
  font-size: 24px;
  visibility: hidden;
}

.el-icon-message-info {
  font-size: 24px;
}
.el-icon-message-info:before {
  content: "\e611";
}
.el-icon-add {
  background: url("~@/assets/图片/icon_add_white.png") center no-repeat;
  /* background-size: cover;*/
}
.el-icon-add :before {
  content: "替";
  font-size: 14px;
  visibility: hidden;
}

.el-icon-add {
  font-size: 14px;
}
.el-icon-add:before {
  content: "\e611";
}
.message-info {
  min-width: 540px !important;
  height: 64px;
  // border-radius: 6px !important;
  border-left: 2px solid #188eff !important;
  background: #e4f2ff !important;
  .el-notification__title {
    color: #188eff;
  }
}
/**菜单 */
.el-menu--vertical {
  .el-menu--popup {
    padding: 0;
    min-width: 120px;
    .el-menu-item {
      padding-left: 12px !important;
      border-left: 2px solid transparent;
      font-size: @text-size_small;
    }
    .el-menu-item.is-active {
      background: #ebf4ff;
      border-color: @base-color;
    }
  }
}
ul.el-dropdown-menu {
  padding: 0;
  margin: 0;
}
.el-tooltip__popper {
  max-width: 80%;
}
.tg-switch--dropdown {
  margin-right: 4px;
  .el-switch__core {
    height: 10px;
    &::after {
      width: 6px;
      height: 6px;
    }
  }
}
.el-dropdown-menu {
  min-width: 120px;
  .popper__arrow {
    display: none !important;
  }
  .el-dropdown-menu__item {
    padding: 0 10px;
    font-size: 13px;
    font-family: @text-famliy_medium;
  }
}
.tg-switch--dropdown.is-checked .el-switch__core::after {
  margin-left: -7px !important;
}
.dropdown-icon {
  width: 14px;
  height: 14px;
  margin-right: 8px;
}
.dropdown-icon__wrap {
  display: inline-table;
  vertical-align: middle;
}
/****** 清除浮动 ********/
.clearfix {
  *zoom: 1;
}
.clearfix:after {
  visibility: hidden;
  display: block;
  font-size: 0;
  content: " ";
  clear: both;
  height: 0;
}

.tg-tooltip {
  border: 1px solid @base-color!important;
  background: #fff !important;
  border-radius: 4px;
  padding: 6px 10px 8px 10px;
  line-height: 20px;
  font-weight: bold;
  width: 200px;
  text-align: center;
}
.tg-tooltip.el-tooltip__popper[x-placement^="top"] .popper__arrow {
  border-top-color: @base-color!important;
}

.tg-tooltip.el-tooltip__popper[x-placement^="top"] .popper__arrow::after {
  border-top-color: #fff !important;
}
.tg-tooltip.fill {
  background: #ebf4ff !important;
  color: @base-color!important;
}
.tg-tooltip.fill.el-tooltip__popper[x-placement^="top"] .popper__arrow::after {
  border-top-color: #ebf4ff !important;
}
.el-notification {
  min-width: 540px !important;
  min-height: 64px !important;
  padding-top: 10px !important;
  padding-bottom: 10px !important;
  padding-left: 18px !important;
  top: 56px !important;
  right: 0 !important;
  align-items: center !important;
  border-radius: 0 !important;
  .el-notification__group {
    margin-left: 16px;
  }
  .el-notification__title {
    font-size: 16px;
    font-weight: bold;
  }
  .el-notification__content {
    font-size: 14px;
    color: #1f2d3d;
  }
}
.copy_name {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 10px;
}
.copy_img {
  width: 16px;
  height: 16px;
  background: url("~@/assets/图片/复制.png") no-repeat;
  background-size: cover;
  cursor: pointer;
}
/**表格的合计展示样式 */
.table-summaries {
  color: #157df0;
  display: flex;
  align-items: center;
  .summaries {
    font-size: 16px;
    color: #157df0;
    margin-left: 8px;
    &.el-icon-view {
      cursor: pointer;
    }
    &.el-icon-refresh-right {
      cursor: pointer;
    }
  }
}
.open_eye {
  width: 16px;
  height: 14px;
  background: url("~@/assets/图片/睁眼.png") no-repeat;
  background-size: contain;
  cursor: pointer;
}
.close_eye {
  width: 16px;
  height: 14px;
  background: url("~@/assets/图片/闭眼.png") no-repeat;
  background-size: contain;
  cursor: pointer;
}
.el-popover {
  // width: 300px;
  // height: 300px;
  padding: 5px;

  .emojis {
    width: 100%;
    height: 100%;
    display: flex;
    flex-wrap: wrap;
    overflow-y: scroll;
    padding: 10px;
    box-sizing: border-box;
    .emoji {
      width: 40px;
      height: 40px;
      text-align: center;
      display: flex;
      padding: 8px;
      img {
        width: 40px;
        height: 40px;
      }
    }
  }
}
.vdr.active:before {
  outline: none !important;
}
.el-dialog__header button {
  font: initial !important;
}
.testPopover {
  width: 300px;
  height: 300px;
}
.tg__match-icon {
  display: flex;
  height: 20px;
  font-size: 12px;
  color: #fff;
  border: 1px solid #03c4f3;
  border-radius: 50%;
  padding: 3px;
  margin-right: 4px;
  background-color: #03c4ff;
  align-items: center;
}
</style>
