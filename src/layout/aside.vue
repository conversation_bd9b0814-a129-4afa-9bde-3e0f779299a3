<template>
  <div id="Aside" class="aside">
    <el-menu
      :default-active="activePath"
      :router="false"
      ref="menu"
      @select="select"
      class="menu-list"
      :unique-opened="true"
      :collapse="is_collapse"
    >
      <div class="header__inner--left">
        <img src="../assets/图片/header_logo.png" />
        <div class="header__inner--box" v-if="!is_collapse">
          <span>天工智能管理系统</span>
        </div>
      </div>
      <template v-for="(item, index) in list">
        <el-submenu
          :index="index.toString()"
          :key="index"
          v-if="item.children.length > 0 && item.type != 'only'"
        >
          <template slot="title">
            <div
              class="menu-subtitle"
              :class="{
                'menu-subtitle--active': menu_index == index
              }"
            >
              <i class="menu-subtitle--line" v-if="menu_index == index"></i>
              <img :src="menu_index == index ? item.src_ac : item.src" alt="" />
              <span>{{ item.meta.title }}</span>
              <span v-if="item.badge" class="red_cricle"></span>
            </div>
          </template>
          <template v-for="(item1, index1) in item.children">
            <el-menu-item
              :index="item1.path"
              v-if="item1.show"
              :key="`${index}-${index1}`"
              :class="{
                'menu-secondtitle--active': item1.path == activePath
              }"
            >
              <!-- <i
                class="circle"
                :class="{ 'circle--active': item1.path == activePath }"
              ></i>
              <i class="line" v-if="index1 < item.children.length - 1"></i> -->
              {{ item1.meta.title }}
              <span v-if="item1.badge" class="red_cricle m-left-5"></span>
              <span v-if="item1.count > 0" class="red_txt m-left-5">{{
                item1.count
              }}</span>
            </el-menu-item>
          </template>
        </el-submenu>
        <template v-else>
          <template v-if="item.children.length > 0">
            <el-menu-item
              :index="item.children[0].path"
              :key="index"
              v-if="item.children[0].show"
              :class="{
                'menu-subtitle--active': item.children[0].path == activePath
              }"
            >
              <i
                class="menu-subtitle--line"
                v-if="item.children[0].path == activePath"
              ></i>
              <img
                v-if="item.children[0].src"
                :src="
                  item.children[0].path == activePath
                    ? item.children[0].src_ac
                    : item.children[0].src
                "
                alt=""
              />
              <span slot="title">{{ item.children[0].meta.title }}</span>
            </el-menu-item>
          </template>
        </template>
      </template>
    </el-menu>
    <img
      src="@/assets/图片/icon_menu_collapse.png"
      alt=""
      class="collapse"
      @click="is_collapse = !is_collapse"
      :class="{ 'collapse--active': is_collapse }"
    />
    <span class="version_num">v_{{ APP_VERSION }}</span>
  </div>
</template>
<script>
// import staffApi from "@/api/staff";
import newWindowPaths from "@/public/newWindowPaths";
import userFeedback from "@/api/userFeedback";

export default {
  name: "TgAside",
  data() {
    return {
      activePath: "/",
      menu_index: "-1",
      is_collapse: false,
      APP_VERSION: ""
    };
  },
  methods: {
    getPendingTotal() {
      userFeedback
        .getComplaintPending({
          department_id: this.schoolId
        })
        .then((res) => {
          if (res.data.code === 0) {
            const targetCount = res.data.data.count ?? 0;
            this.list.forEach((el) => {
              if (el.path === "/questionManagement") {
                el.children.forEach((item) => {
                  if (item.path === "/userFeedback") {
                    this.$set(item, "count", targetCount);
                  }
                });
              }
            });
          }
        });
    },
    select(index, indexPath) {
      if (newWindowPaths.includes(index)) {
        window.open(index);
      } else {
        this.menu_index = indexPath[0];
        this.activePath = index;
        this.$router.push(index);
      }
    },
    routerChange() {
      // * 该方法会将路由包含当前item1.path的值进行匹配，后续需要对路由起名进行注意，减少单词包含的情况产生
      this.list.forEach((item, index) => {
        if (item.children) {
          item.children.forEach((item1) => {
            if (
              item1.path.indexOf(
                this.$router?.currentRoute.path.split("/")[1]
              ) > -1
            ) {
              this.menu_index = index.toString();
              this.activePath = item1.parent || this.$router?.currentRoute.path;
            }
          });
        }
      });
    }
  },
  created() {
    this.APP_VERSION = process.env.APP_VERSION;
  },
  mounted() {
    this.routerChange();
  },
  computed: {
    list() {
      return this.$store.getters.doneGetMenu;
    },
    schoolId() {
      return this.$store.getters.doneGetSchoolId;
    },
    approveStatistics() {
      return this.$store.getters.doneGetApproveStatistics;
    },
    replyFeedback() {
      return this.$store.getters.doneGetReplyFeedback;
    }
  },
  watch: {
    replyFeedback: {
      handler(val) {
        if (val === "success") {
          this.getPendingTotal();
        }
      },
      deep: true
    },
    is_collapse(newVal) {
      this.$store.commit("setIsCollapse", newVal);
    },
    approveStatistics(val) {
      let num = 0;
      for (const k in val) {
        num += val[k];
      }
      this.list.forEach((el) => {
        if (el.path === "/process") {
          el.badge = num > 0;
          el.children[0].badge = num > 0;
        }
      });
      this.$forceUpdate();
    },
    schoolId: {
      handler(val) {
        //   console.log("object :>> ", val);
        if (val.length) {
          this.$store.dispatch("getApproveStatistics", {
            department_id: val
          });
          this.getPendingTotal();
        }
      },
      immediate: true,
      deep: true
    },
    $route() {
      this.$nextTick(() => {
        this.routerChange();
      });
    }
  }
};
</script>

<style lang="less" scoped>
.aside {
  padding-bottom: 80px;
  height: calc(100% - 100px);
  position: relative;
  span {
    font-family: @text-famliy_medium;
  }
  .menu-list {
    width: 210px;
    height: 100%;
    overflow-y: scroll;
    overflow-x: hidden;
  }
  .el-submenu .el-menu-item {
    min-width: 0px !important;
  }
  .circle {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #e7eaed;
    position: absolute;
    top: 15px;
    left: 36px;
  }
  .circle--active {
    background-color: @base-color;
  }
  ::v-deep .el-submenu .el-menu-item {
    margin: 0;
  }
  ::v-deep .el-submenu .el-menu-item {
    padding-left: 64px !important;
    padding-right: 0;
    height: 40px;
    line-height: 40px;
    color: @text-color_third;
    width: 194px;
    margin-left: 8px;
  }
  ::v-deep .el-submenu .el-menu {
    padding: 8px 0;
    border-right: none;
    width: 210px;
    background: #f9f9f9;
    // box-shadow: -1px 0 0 0 #d8dde6 inset;
    margin-left: -8px;
  }
  ::v-deep .el-menu-item {
    height: 40px;
    line-height: 40px;
    padding-left: 28px !important;
    margin: 0 8px;
  }

  ::v-deep .el-submenu {
    margin: 0 8px;
  }
  .line {
    height: 20px;
    width: 1px;
    background-color: #e7eaed;
    position: absolute;
    top: 26px;
    left: 38px;
  }
  ::v-deep .el-submenu__title {
    height: 40px;
    line-height: 40px;
    padding-left: 0 !important;
    padding-right: 0;
  }
  .menu-subtitle {
    padding-left: 28px;
    padding-right: 12px;
    span {
      color: @text-color_third;
    }
  }
  .menu-subtitle--active {
    background-color: transparent;
    span {
      color: @base-color;
    }
  }
  .menu-subtitle--line {
    height: 24px;
    width: 4px;
    border-radius: 2px;
    background-color: @base-color;
    position: absolute;
    left: 0;
    top: 8px;
  }
  .menu-list img {
    width: 20px;
    height: 20px;
    margin-right: 16px;
  }
  ::v-deep .el-submenu.is-active .el-submenu__icon-arrow {
    color: @base-color;
  }
  ::v-deep .el-submenu__icon-arrow {
    font-weight: bold;
    margin-top: -3px;
  }
  ::v-deep .el-submenu__icon-arrow.el-icon-arrow-down {
    background: url("../assets/图片/icon_menu_down.png");
    background-size: cover;
    width: 10px;
    height: 6px;
  }
  ::v-deep .el-submenu__icon-arrow.el-icon-arrow-down:before {
    content: "1";
    visibility: hidden;
  }
  ::v-deep .is-active .el-submenu__icon-arrow.el-icon-arrow-down {
    background: url("../assets/图片/icon_menu_down_ac.png");
    background-size: cover;
    width: 10px;
    height: 6px;
  }
  ::v-deep .is-active .el-submenu__icon-arrow.el-icon-arrow-down:before {
    content: "1";
    visibility: hidden;
  }
  ::v-deep .el-submenu__icon-arrow.el-icon-arrow-top {
    background: url("../assets/图片/icon_menu_top.png");
    background-size: cover;
    width: 10px;
    height: 6px;
  }
  ::v-deep .el-submenu__icon-arrow.el-icon-arrow-top:before {
    content: "1";
    visibility: hidden;
  }
  ::v-deep .is-active .el-submenu__icon-arrow.el-icon-arrow-top {
    background: url("../assets/图片/icon_menu_top_ac.png");
    background-size: cover;
    width: 10px;
    height: 6px;
  }
  ::v-deep .is-active .el-submenu__icon-arrow.el-icon-arrow-top:before {
    content: "1";
    visibility: hidden;
  }
  .el-submenu ul > li:last-child .line {
    display: none;
  }
  .collapse {
    position: absolute;
    left: 50%;
    transform: translate(-50%);
    -ms-transform: translate(-50%);
    -moz-transform: translate(-50%);
    -webkit-transform: translate(-50%);
    -o-transform: translate(-50%);
    transition: 0.5s;
    bottom: 32px;
    height: 20px;
    width: 20px;
    cursor: pointer;
  }
  .collapse--active {
    transition: 0.5s;
    transform: rotate(180deg) translate(50%);
    -ms-transform: rotate(180deg) translate(50%); /* IE 9 */
    -moz-transform: rotate(180deg) translate(50%); /* Firefox */
    -webkit-transform: rotate(180deg) translate(50%); /* Safari 和 Chrome */
    -o-transform: rotate(180deg) translate(50%);
  }
  .el-menu--collapse {
    width: 60px;
    // .menu-subtitle--line {
    //   display: none;
    // }
    .el-submenu {
      margin-bottom: 16px;
    }
    .menu-subtitle {
      padding: 0;
      border-radius: 4px;
    }
    img {
      margin-left: 16px;
      margin-right: 0;
    }
  }
  .header__inner--left {
    height: 56px;
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    text-align: left;
    cursor: pointer;
    background: linear-gradient(90deg, #f7fffd -3.1%, #fefffb 102.38%);
    margin-bottom: 20px;
    img {
      height: 40px;
      width: 40px;
      position: relative;
      margin-right: 8px;
    }
    span {
      font-size: 14px;
      color: #333333;
      font-weight: bold;
    }
    .header__inner--box {
      display: flex;
      flex-direction: column;
    }
  }
}
.red_cricle {
  display: inline-block;
  width: 8px;
  height: 8px;
  margin-left: 8px;
  border-radius: 50%;
  background-color: #ff0517;
}
.red_txt {
  margin-left: 8px;
  color: #ff0517;
}
.m-left-5 {
  margin-left: 5px !important;
}
.menu-secondtitle--active {
  background: @base-color;
  color: @text-color_white!important;
  box-shadow: 0 4px 8px 0 @base-shadow-color;
  border-radius: 4px;
}
.version_num {
  font-size: 13px;
  padding-top: 2px;
  box-sizing: border-box;
  position: absolute;
  bottom: 0px;
  text-align: center;
  width: 100%;
  color: #999;
}
</style>
