<template>
  <el-drawer
    :visible.sync="shareDrawer.visible"
    custom-class="share-drawer"
    :append-to-body="true"
    :show-close="false"
    direction="btt"
    :before-close="handleShareDrawerClose"
    :size="220"
  >
    <img
      src="https://tg-prod.oss-cn-beijing.aliyuncs.com/845a95f8-e69c-41e8-85b2-e36f13ef6237.png"
      class="guidance-img"
      v-if="isShowGuidanceImg"
      :style="{
        bottom: shareDrawer.guideBottom / 2 + 'px',
        left: shareDrawer.guideLeft / 2 + 'px'
      }"
      alt=""
    />
    <div class="share-drawer-title">
      分享至
      <img
        @click="handleShareDrawerClose"
        class="close"
        src="https://tg-prod.oss-cn-beijing.aliyuncs.com/59386f9f-a41c-463e-bb86-6f3c5ddd35b7.png"
        alt=""
      />
    </div>
    <div class="share-bar">
      <div class="share-bar-item" @click="handleShareWechat">
        <button open-type="share" class="share-bar-item-btn">
          <div class="share-bar-item-img">
            <img
              src="https://tg-prod.oss-cn-beijing.aliyuncs.com/c419e231-8d78-4dee-9fd0-fcab597802ac.png"
              alt=""
            />
          </div>
          <div class="share-bar-item-text">
            <div>微信好友</div>
          </div>
        </button>
      </div>
      <div class="share-bar-item" @click="handleGenerateShareImage">
        <div class="share-bar-item-img">
          <img
            src="https://tg-prod.oss-cn-beijing.aliyuncs.com/a1f14fe7-0dc8-46b8-a951-938656bbc6c3.png"
            alt=""
          />
        </div>
        <div class="share-bar-item-text">
          <div>生成分享图</div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script>
export default {
  props: {
    shareDrawer: {
      type: Object,
      default: () => {
        return {
          visible: false,
          teacher_name: "",
          feedback_id: "",
          student_id: "",
          pageTitle: "",
          guideBottom: 0,
          guideLeft: 0
        };
      }
    }
  },
  data() {
    return {
      isShowGuidanceImg: false
    };
  },
  // created() {
  //   window.addEventListener("message", (event) => {
  //     console.log("收到来自小程序的消息:", event.data);
  //   });
  // },
  methods: {
    handleShareDrawerClose() {
      this.$emit("close");
    },
    // 分享到微信好友
    handleShareWechat() {
      const { teacher_name, feedback_id, student_id, pageTitle } =
        this.shareDrawer;
      this.isShowGuidanceImg = true;
      wx.miniProgram.postMessage({
        data: {
          type: "shareToFriend",
          payload: {
            title: teacher_name + "发布的" + pageTitle,
            path: `/pages/teacher/subpages/richtext/index?openEdit=1&openPreview=1&isShare=1&feedback_id=${feedback_id}&student_id=${student_id}&pageTitle=${pageTitle}`,
            imageUrl:
              "https://tg-prod.oss-cn-beijing.aliyuncs.com/f860ad82-95fd-49c4-a8de-c629f069411b.png"
          }
        }
      });
      // this.shareDrawer.visible = false;
      // this.$emit("update:shareDrawer", {
      //   ...this.shareDrawer,
      //   visible: false
      // });
      this.$emit("handleShareWechat");
      // 获取class为.v-modal的元素
      const modal = document.querySelector(".v-modal");
      if (modal) {
        modal.style.opacity = "0.6";
      }
    },
    // 生成分享图片
    handleGenerateShareImage() {
      const { feedback_id, student_id, pageTitle } = this.shareDrawer;
      this.isShowGuidanceImg = false;
      const host = process.env.VUE_APP_TG_H5_HOST;
      // const host = "http://*************:9000";
      const webviewUrl = `${host}/tg-minigram/studyReport?feedback_id=${feedback_id}&student_id=${student_id}&pageTitle=${pageTitle}`;
      console.log("webviewUrl :>> ", webviewUrl);
      wx.miniProgram.redirectTo({
        url: `/pages/student/subpages/shareWebview/index?webviewUrl=${encodeURIComponent(
          webviewUrl
        )}`
      });
      this.$emit("handleGenerateShareImage");
    }
  }
};
</script>

<style lang="scss">
.share-drawer {
  overflow: visible;
  border-radius: 11px 11px 0 0;
  .guidance-img {
    position: absolute;
    left: 40px;
    width: 288px;
    height: 378px;
  }
  .el-drawer__header {
    display: none;
  }
  .share-drawer-title {
    position: relative;
    width: 100%;
    padding-top: 18px;
    font-size: 16px;
    font-weight: 500;
    color: #333;
    display: flex;
    align-items: center;
    justify-content: center;
    .close {
      position: absolute;
      right: 16px;
      top: 21px;
      width: 15px;
      height: 15px;
    }
  }
  .share-bar {
    padding: 27px 29px 0px 26px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .share-bar-item {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .share-bar-item-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }
      .share-bar-item-img {
        width: 50px;
        height: 50px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .share-bar-item-text {
        margin-top: 5px;
        font-size: 15px;
        font-weight: 400;
        color: #333;
      }
    }
  }
}
</style>
