<template>
  <el-drawer
    :visible.sync="visible"
    direction="btt"
    :show-close="false"
    :with-header="false"
    size="80%"
    :before-close="handleClose"
  >
    <div class="resource-hub-tab">
      <el-tabs v-model="tabIndex" @tab-click="handleTabClick">
        <el-tab-pane
          v-for="(item, index) in tabData"
          :key="index"
          :label="item.name"
          :name="item.value"
        >
          <div class="serach-input">
            <el-input
              placeholder="请输入内容"
              prefix-icon="el-icon-search"
              v-model="item.searchValue"
            >
            </el-input>
            <span @click="search" class="search-txt">搜索</span>
          </div>
          <div class="infinite-list-wrapper" style="overflow: auto">
            <div
              class="list"
              v-infinite-scroll="load"
              :infinite-scroll-disabled="item.loading || item.noMore"
            >
              <div
                :key="index2"
                v-for="(item2, index2) in item.data"
                class="list-item"
              >
                <div class="name">{{ item2.name }}</div>
                <div v-if="tabIndex === '0'" class="box img">
                  <el-image
                    style="width: 38.8vw; height: 24vw"
                    :src="item2.content"
                    fit="cover"
                    :z-index="2024"
                    :preview-src-list="[item2.content]"
                  >
                  </el-image>
                </div>
                <div
                  @click="previewVideoHandle(item2)"
                  v-else-if="tabIndex === '1'"
                  class="box video"
                >
                  <!-- <video
                    :src="item2.content"
                    :poster="item2.poster"
                    controls
                    controlsList="nodownload"
                    style="width: 38.8vw; height: 24vw"
                  ></video> -->
                  <div class="play-btn"></div>
                  <div
                    :style="'background-image: url(' + item2.poster + ')'"
                    class="poster-img"
                  />
                </div>
                <div
                  v-else-if="tabIndex === '2'"
                  @click="prewViewWordsHandle(item2.content)"
                  style="width: 38.8vw; height: 24vw"
                  class="box text"
                >
                  <div class="words">{{ item2.content }}</div>
                </div>
                <div class="btn-box">
                  <el-checkbox
                    @change="(val) => checkedChoose(val, index2)"
                    v-model="item2.checked"
                  ></el-checkbox>
                </div>
              </div>
            </div>
            <div v-if="item.loading" class="loading">
              <i class="el-icon-loading"></i><span>加载中...</span>
            </div>
            <div v-if="item.noMore" class="no-more">没有更多了哦~</div>
          </div>
        </el-tab-pane>
      </el-tabs>
      <div class="resource-hub-footer">
        <el-button type="primary" @click="confirm">确定</el-button>
      </div>
    </div>
    <el-drawer
      :visible.sync="prewViewWordsVisible"
      size="50%"
      direction="btt"
      :with-header="false"
      :append-to-body="true"
    >
      <div class="prew-view-words" v-html="prewViewWords"></div>
    </el-drawer>

    <el-drawer
      :visible.sync="previewVideoVisible"
      size="50%"
      direction="btt"
      :with-header="false"
      :append-to-body="true"
    >
      <div class="video-box">
        <video
          id="drawer-video"
          class="video-js vjs-default-skin vjs-big-play-centered"
          controls
          autoplay
          playsinline
          webkit-playsinline
          x-webkit-airplay="deny"
          controlslist="nodownload  noremoteplayback"
          disablepictureinpicture
        ></video>
      </div>
    </el-drawer>
  </el-drawer>
</template>

<script>
import appletResource from "@/api/appletResource";
export default {
  data() {
    return {
      tabIndex: "0",
      tabData: [
        {
          name: "图片库",
          value: "0",
          data: [],
          page: 1,
          searchValue: "",
          loading: false,
          noMore: false
        },
        {
          name: "视频库",
          value: "1",
          page: 1,
          data: [],
          searchValue: "",
          loading: false,
          noMore: false
        },
        {
          name: "话术库",
          value: "2",
          page: 1,
          data: [],
          searchValue: "",
          loading: false,
          noMore: false
        }
      ],
      prewViewWordsVisible: false,
      previewVideoVisible: false,
      prewViewWords: "",
      videoSrc: "",
      poster: "",
      drawerPlayer: null
    };
  },
  props: {
    department_id: {
      type: String,
      default: "",
      required: true
    },
    token: {
      type: String,
      default: "",
      required: true
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    previewVideoVisible(val) {
      if (!val) {
        if (this.drawerPlayer) {
          console.log("this.myPlayer :>>pause ");
          this.drawerPlayer.pause();
        }
      }
    }
  },
  computed: {},
  mounted() {},
  methods: {
    handleClose() {
      const currData = this.tabData[+this.tabIndex].data;
      currData.map((item) => {
        item.checked = false;
      });
      this.$emit("close");
    },
    confirm() {
      const currData = this.tabData[+this.tabIndex].data;
      if (currData.length) {
        if (this.tabIndex === "0") {
          // 图片库多选
          const selectedItems = currData.filter((item) => item.checked);
          if (selectedItems.length > 0) {
            const contents = selectedItems.map((item) => item.content);
            this.$emit("confirm", contents, +this.tabIndex + 1);
            this.$emit("close");
          } else {
            this.$message.info("请至少选择一张图片！");
            return;
          }
        } else if (this.tabIndex === "1") {
          // 视频库多选
          const selectedItems = currData.filter((item) => item.checked);
          if (selectedItems.length > 0) {
            const contents = selectedItems.map((item) => ({
              url: item.content,
              poster: item.poster || ""
            }));
            this.$emit("confirm", contents, +this.tabIndex + 1);
            this.$emit("close");
          } else {
            this.$message.info("请至少选择一个视频！");
            return;
          }
        } else {
          // 话术库单选
          const item = currData.find((item) => item.checked);
          if (item) {
            const content = item.content;
            this.$emit("confirm", content, +this.tabIndex + 1);
            this.$emit("close");
          } else {
            this.$message.info("请选择一项内容！");
            return;
          }
        }

        // 清空选中状态
        currData.map((item) => {
          item.checked = false;
        });
      }
    },
    handleTabClick() {
      console.log("this.tabIndex :>> ", this.tabIndex);
      const data = this.tabData[+this.tabIndex].data;
      if (data.length <= 0) {
        this.load();
      } else {
        // 切换tab时清空所有选中状态
        data.map((item) => {
          item.checked = false;
        });
      }
    },
    checkedChoose(val, index) {
      console.log("val :>> ", val, index);
      const currData = this.tabData[+this.tabIndex].data;

      // 如果是取消选中，直接处理
      if (!val) {
        currData[index].checked = false;
        this.$forceUpdate();
        return;
      }

      // 如果是选中，需要校验数量限制
      if (this.tabIndex === "0") {
        // 图片库：最多5张
        // const selectedCount = currData.filter((item) => item.checked).length;
        // if (selectedCount >= 5) {
        //   this.$message.info("每次最多只能选择5张图片！");
        //   return;
        // }
        currData[index].checked = val;
      } else if (this.tabIndex === "1") {
        // 视频库：最多3个
        // const selectedCount = currData.filter((item) => item.checked).length;
        // if (selectedCount >= 3) {
        //   this.$message.info("每次最多只能选择3个视频！");
        //   return;
        // }
        currData[index].checked = val;
      } else {
        // 话术库保持单选
        currData.map((item) => {
          item.checked = false;
        });
        currData[index].checked = val;
      }

      this.$forceUpdate();
    },
    prewViewWordsHandle(content) {
      this.prewViewWords = content;
      this.prewViewWordsVisible = true;
    },
    previewVideoHandle(item) {
      this.videoSrc = item.content;
      this.poster = item.poster;
      this.previewVideoVisible = true;
      this.$nextTick(() => {
        // const _this = this;
        this.drawerPlayer = videojs(
          "drawer-video",
          {
            controls: true,
            poster: this.poster
          },
          () => {
            this.drawerPlayer.src({
              src: this.videoSrc
            });
            this.drawerPlayer.play();
          }
        );
      });
      // videojs("drawer-video", {}, function () {
      //   window.drawerPlayer = this;
      //   drawerPlayer.src(this.videoSrc);
      //   drawerPlayer.load(this.videoSrc);
      //   drawerPlayer.play();
      // });
    },
    getList() {
      const index = +this.tabIndex;
      const { page, searchValue, data } = this.tabData[index];
      this.tabData[index].loading = true;
      appletResource
        .getFileList({
          page,
          category_id: index + 1,
          department_id: this.department_id,
          name: searchValue,
          page_size: 10,
          token: this.token,
          requestType: "MINIGRAMER"
        })
        .then((res) => {
          this.tabData[index].loading = false;
          if (res.data.code === 0) {
            const results = res.data?.data?.results || [];
            if (results.length) {
              this.tabData[index].data = data.concat(results);
              this.tabData[index].page = page + 1;
            } else {
              this.tabData[index].noMore = true;
            }
          } else {
            this.$message.error(res.data.message);
          }
        })
        .catch((err) => {
          this.tabData[index].loading = false;
          console.error(err);
          this.$message.error("获取数据失败!");
        });
    },
    search() {
      const index = +this.tabIndex;
      this.tabData[index].page = 1;
      this.tabData[index].data = [];
      this.tabData[index].noMore = false;
      this.getList();
    },
    load() {
      this.getList();
    }
  },
  beforeDestroy() {
    if (this.drawerPlayer) {
      this.drawerPlayer.dispose();
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .el-drawer {
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  .el-drawer__body {
    // overflow: hidden;
  }
}
/deep/ .resource-hub-tab {
  height: 100%;

  background-color: #fafafa;
  .el-tabs__header {
    padding: 0px 20px;
    background-color: #fff;
    .el-tabs__item {
      height: 50px;
      line-height: 50px;
      font-weight: bold;
    }
  }
  .serach-input {
    // height: 50px;
    display: flex;
    align-items: center;
    padding: 0 20px;
    margin-bottom: 10px;
    .el-input {
      width: 100%;

      .el-input__inner {
        height: 40px;
        line-height: 40px;
        border-radius: 20px;
        padding-right: 60px;
      }
      .el-input__prefix {
        top: 4px;
      }
    }
    .search-txt {
      width: 50px;
      height: 30px;
      line-height: 30px;
      position: absolute;
      top: 10px;
      right: 10px;
      color: #0280e4;
      font-weight: bold;
    }
  }
  .infinite-list-wrapper {
    height: calc(100vh - 20vh - 50px - 15px - 60px);

    padding: 0 20px;
    background-color: #fafafa;
    padding-bottom: calc(50px + env(safe-area-inset-bottom));
    // padding-top: 20px;
    .list-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 2.6667vw 5.6vw;
      background-color: #fff;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      border-radius: 4px;
      background-color: #fff;
      overflow: hidden;
      color: #303133;
      transition: 0.3s;
      margin-bottom: 16px;
      border-radius: 16px;
      .name {
        color: #475669;
        font-size: 12px;
        font-style: normal;
        font-weight: 500;
        width: 22.6667vw;
        word-break:break-all
        // display: -webkit-box;
        // -webkit-box-orient: vertical;
        // -webkit-line-clamp: 2;
        // overflow: hidden;
        // text-overflow: ellipsis;
      }
      .box {
        margin: 0 2.6667vw;
        &.img {
          background: #fafafa;
          .el-image {
            overflow: hidden;
            border-radius: 2.6667vw;
          }
        }
        &.video {
          background: #b0b0b0;
          position: relative;
          border-radius: 2.6667vw;
          overflow: hidden;
          .play-btn {
            position: absolute;
            top: 50%;
            left: 50%;
            z-index: 3;
            transform: translate(-50%, -50%);
            background: url(~@/assets/图片/playerBtn.png) center no-repeat;
            width: 10vw;
            height: 10vw;
            background-size: cover;
          }
          .poster-img {
            width: 38.8vw;
            height: 24vw;
            background-size: cover;
            opacity: 0.8;
          }
        }
        &.text {
          display: flex;
          align-items: center;
          .words {
            width: 100%;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }
    .loading,
    .no-more {
      text-align: center;
      color: #666;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
    }
    .loading {
      span {
        margin-left: 6px;
      }
    }
  }
  .resource-hub-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 10;
    background-color: #fff;
    padding-bottom: env(safe-area-inset-bottom);
    .el-button {
      width: 100%;
      background: linear-gradient(270deg, #3667f0 0%, #568ff5 100%);
      border-radius: 0;
      font-weight: bold;
      padding: 15px 20px;
      font-size: 17px;
    }
  }
}
.prew-view-words {
  padding: 20px;
}
.video-box {
  padding: 20px;
  .video-js {
    /* 设置播放器的宽度和高度 */
    width: 100%;
    height: 260px;
    background: #000;
    border-radius: 1.3333vw;
    overflow: hidden;
  }
  video {
    // width: 100%;
    // height: 260px;
    background: #000;
    border-radius: 1.3333vw;
  }
}
</style>
