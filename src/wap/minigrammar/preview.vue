<template>
  <el-drawer
    :visible="true"
    direction="rtl"
    :show-close="false"
    :with-header="false"
    size="100%"
    :before-close="handleClose"
  >
    <div class="study-report-detail-page" :class="pageTheme[pageTitle].class">
      <div class="content">
        <div class="content__wrap">
          <div class="content__wrap-teacher-auto">
            <div class="avatar">
              <img
                :src="
                  genders_list[studyReportInfo.choose_head] || genders_list[1]
                "
              />
            </div>
            <div class="student-name">
              {{ studyReportInfo.student_name }}
              <template v-if="students.length > 1">
                等{{ students.length }}名学员
              </template>
            </div>
            <div class="student-info">
              <img
                :src="pageTheme[pageTitle].joinImg"
                class="fixed-img left"
                alt=""
              />
              <img
                :src="pageTheme[pageTitle].joinImg"
                class="fixed-img right"
                alt=""
              />
              <div>
                <div class="classroom-name">
                  {{ studyReportInfo.classroom_name }}
                </div>
                <div class="report-date">
                  <template v-if="query.isFirstEdit !== '1'">
                    <div v-if="!isClassNotice">
                      上课时间：{{ studyReportInfo.start_time }}
                    </div>
                    <template v-if="studyReportInfo.created_at">
                      <span class="teacher-name">{{
                        studyReportInfo.teacher_name
                      }}</span>
                      <span
                        class="report-date-time"
                        v-if="
                          !studyReportInfo.created_at.includes('0001-01-01')
                        "
                      >
                        发布于{{
                          moment(
                            new Date(studyReportInfo.created_at).getTime()
                          ).format("YYYY-MM-DD HH:mm:ss")
                        }}
                      </span>
                      <span class="report-date-time" v-else>
                        发布于{{
                          moment(new Date().getTime()).format(
                            "YYYY-MM-DD HH:mm:ss"
                          )
                        }}
                      </span>
                    </template>
                  </template>
                  <template v-else>--</template>
                </div>
              </div>
              <div class="title">{{ pageTitle }}</div>
            </div>
            <div class="teacher-remark">
              <div class="content-txt-wrap">
                <template>
                  <div class="content-txt" v-html="format_content"></div>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="mark-block" v-if="isCourseSummary">
        <div class="mark-title">
          <div class="first-mark-tilte" v-if="studyReportInfo.score_num === 0">
            请给本节课程做个评分吧！
          </div>
          <div class="second-mark-tilte" v-else>
            <div class="mark-status">
              <img
                src="https://tg-prod.oss-cn-beijing.aliyuncs.com/6ff30fa4-ff8a-4f7f-9b10-1fcfedd12dc8.webp"
                alt=""
              />
              评价完成
            </div>
            <div class="mark-tips">感谢您的反馈</div>
          </div>
        </div>
        <div class="mark-content">
          <div class="mark-item-wrap" v-if="studyReportInfo.score_num === 0">
            <div class="mark-item" v-for="item in markList" :key="item.value">
              <div class="mark-item-url">
                <img
                  :src="
                    studyReportInfo.score && studyReportInfo.score >= item.value
                      ? item.scoreStyle[studyReportInfo.score_num]?.activeUrl
                      : item.scoreStyle[studyReportInfo.score_num]?.url
                  "
                  alt=""
                />
              </div>
              <div
                :class="[
                  'mark-item-name',
                  studyReportInfo.score && studyReportInfo.score === item.value
                    ? 'active'
                    : ''
                ]"
              >
                {{ item.name }}
              </div>
            </div>
          </div>
          <div v-else class="second-mark-wrap">
            <div class="label">我的评价</div>
            <div class="second-mark-item-wrap">
              <div
                class="second-mark-item"
                v-for="item in markList"
                :key="item.value"
              >
                <div class="second-mark-item-url">
                  <img
                    :src="
                      studyReportInfo.score >= item.value
                        ? item.scoreStyle[studyReportInfo.score_num > 0 ? 1 : 1]
                            ?.activeUrl
                        : item.scoreStyle[studyReportInfo.score_num > 0 ? 1 : 1]
                            ?.url
                    "
                    alt=""
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="feedback-content">
          <el-input
            :disabled="true"
            v-model="scoreParams.advice"
            class="feedback-content-input"
            type="textarea"
            maxlength="100"
            show-word-limit
            placeholder="您的反馈会让我们做的更好"
          ></el-input>
        </div>
      </div>
      <div class="bar" v-if="!$route.query.isShare">
        <template v-if="$route.query.isShowBar == 1">
          <div class="bar-item revoke" @click="handleRevoke">撤回</div>
          <div class="bar-item share" @click="handleShareDrawer">分享</div>
        </template>
        <div
          class="bar-item share"
          v-if="$route.query.isShowBack == 1"
          @click="handleBack"
        >
          返回编辑
        </div>
      </div>
    </div>
    <share-drawer
      ref="shareDrawer"
      :shareDrawer="shareDrawerInfo"
      @close="handleShareDrawerClose"
    />
    <el-dialog
      append-to-body
      :visible.sync="secondaryConfirmation"
      width="291px"
      center
    >
      <div class="score-tips">确定要撤销该总结吗？</div>
      <div slot="footer" class="confirm-buttons">
        <div class="confirm-button cancel-button" @click="handleCancel">
          取消
        </div>
        <div
          class="confirm-button confirm-button-primary"
          @click="handleConfirm"
        >
          确定
        </div>
      </div>
    </el-dialog>
  </el-drawer>
</template>

<script>
import { markList, pageTheme, genders_list } from "@/wap/config/studyReport";
import { feedbackCancel } from "@/api/preview";
import shareDrawer from "@/wap/components/drawer";
export default {
  components: {
    shareDrawer
  },
  props: {
    students: {
      type: Array,
      default: () => {
        return [];
      }
    },
    pageTitle: {
      type: String,
      default: ""
    },
    studyReportInfo: {
      type: Object,
      default: () => {
        return {};
      }
    },
    student_gender: {
      type: String,
      default: ""
    },
    student_name: {
      type: String,
      default: ""
    },
    html: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      markList,
      pageTheme,
      genders_list,
      scoreParams: {
        advice: "",
        feedback_id: "",
        student_id: "",
        score: ""
      },
      shareDrawerInfo: {
        visible: false,
        teacher_name: "",
        feedback_id: "",
        student_id: "",
        pageTitle: "",
        guideBottom: 0,
        guideLeft: 0
      },
      previewTime: new Date().getTime(),
      drawerPlayer: null,
      secondaryConfirmation: false,
      openShareDrawer: false
    };
  },
  computed: {
    query() {
      return this.$route.query;
    },
    format_content() {
      return this.formatContent(this.studyReportInfo.content);
    },
    isCourseSummary() {
      console.log("this.pageTitle :>> ", this.pageTitle);
      return this.pageTheme[this.pageTitle].class === "course-summary";
    },
    isClassNotice() {
      return (
        this.pageTheme[this.$route.query.pageTitle].class === "class-notice"
      );
    }
  },
  mounted() {},
  created() {
    const { feedback_id, student_id, pageTitle, guideBottom, guideLeft } =
      this.query;
    this.shareDrawerInfo = {
      visible: false,
      teacher_name: this.studyReportInfo.teacher_name,
      feedback_id,
      student_id,
      pageTitle,
      guideBottom,
      guideLeft
    };
  },
  methods: {
    // 格式化studyReportInfo.content中的img标签，在img标签上添加preview="preview" preview-text=""
    formatContent(content) {
      console.log("content :>> ", content);
      if (content) {
        return content.replace(/<img/g, '<img preview="0" preview-text=""');
      }
      return content;
    },
    handleRevoke() {
      this.secondaryConfirmation = true;
    },
    handleBack() {
      this.$emit("close");
    },
    handleShareDrawer() {
      this.shareDrawerInfo.visible = true;
      this.$refs.shareDrawer.isShowGuidanceImg = false;
      console.log("this.shareDrawerInfo :>> ", this.shareDrawerInfo);
    },
    handleShareDrawerClose() {
      this.shareDrawerInfo.visible = false;
    },
    handleCancel() {
      this.secondaryConfirmation = false;
    },
    handleConfirm() {
      feedbackCancel({
        is_cancel: 1,
        from: "mini",
        token: this.$route.query.token,
        student_feedback_id: [this.$route.query.student_feedback_id]
      }).then((res) => {
        const { code, message } = res;
        if (code === 0) {
          this.$message.success("撤销成功");
          setTimeout(() => {
            wx.miniProgram.navigateBack();
          }, 2000);
        } else {
          this.$message.error(message);
        }
        console.log("res :>> ", res);
      });
    },
    handleClose() {
      this.$emit("close");
    },
    handSend() {
      this.$emit("send");
    }
  }
};
</script>

<style lang="scss">
.content-txt {
  img {
    max-width: 100%;
  }
  video {
    max-width: 100%;
    max-height: 200px;
    object-fit: contain;
    background-color: #000;
    border-radius: 8px;
  }

  // 确保段落间距合理
  p {
    margin: 8px 0;
    line-height: 1.5;
  }

  // 媒体容器样式
  div {
    margin: 12px 0;
  }
}
body {
  overflow-y: auto;
}

.parent-class {
  background: url(https://tg-prod.oss-cn-beijing.aliyuncs.com/f9639fc0-d239-40e0-9bec-61474f4ca593.png)
    no-repeat;
  .content__wrap {
    box-shadow: 0px 4px 20px 0px rgba(211, 158, 0, 0.15);
  }
  .student-info {
    background: linear-gradient(91deg, #fff8e59b 8.16%, #ffe49e9a 80.04%);
    box-shadow: 0px -2.16px 5.76px 0px #fbd17d inset;
  }
  .teacher-remark {
    background: linear-gradient(93deg, #ffeed4a3 37.2%, #ffc36a9d 167.16%);
    box-shadow: 0px -2.16px 5.76px 0px #fbc07d92 inset;
  }
  .title {
    background: linear-gradient(127deg, #ffb32f 15.34%, #ff8411 86.73%);
    box-shadow: 0px -0.864px 4.32px 0px #f90 inset,
      0px 1.728px 6.912px 0px rgba(229, 179, 0, 0.27);
    &::after {
      border-color: transparent transparent #cf6500 transparent;
    }
    &::before {
      border-color: transparent transparent #cf6500 transparent;
    }
  }
}
.class-notice {
  background: url(https://tg-prod.oss-cn-beijing.aliyuncs.com/410f5f7a-1f0b-4be1-bbff-a95f25decff4.png)
    no-repeat;
  .content__wrap {
    box-shadow: 0px 4px 20px 0px rgba(255, 213, 111, 0.32);
  }
  .student-info {
    background: linear-gradient(91deg, #fff8e5 8.16%, #ffe49e 80.04%);
    box-shadow: 0px -2.16px 5.76px 0px #fbd17d inset;
  }
  .teacher-remark {
    background: linear-gradient(96deg, #fff8e5 37.35%, #ffe9b2 96.35%);
    box-shadow: 0px -2.16px 5.76px 0px #fbd17d inset;
  }
  .title {
    background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
    box-shadow: 0px -0.864px 4.32px 0px #f90 inset,
      0px 1.728px 6.912px 0px rgba(229, 179, 0, 0.27);
    &::after {
      border-color: transparent transparent #d99f00 transparent;
    }
    &::before {
      border-color: transparent transparent #d99f00 transparent;
    }
  }
}
.course-summary {
  background: url(https://tg-prod.oss-cn-beijing.aliyuncs.com/601e5b52-2363-47f8-bb92-4ba4c2ae1be7.png)
    no-repeat;
  .content__wrap {
    box-shadow: 0px 4px 20px 0px rgba(211, 158, 0, 0.15);
  }
  .student-info {
    background: linear-gradient(92deg, #efe6ff 32.3%, #bbbcff 131.41%);
    box-shadow: 0px -2.16px 5.76px 0px #a599ff inset;
  }
  .teacher-remark {
    background: linear-gradient(96deg, #e7e1ff 34.6%, #d3bfff 157.55%);
    box-shadow: 0px -2.16px 5.76px 0px #a599ff inset,
      0px -2.16px 5.76px 0px #fbd77d inset;
  }
  .title {
    background: linear-gradient(6deg, #c26ff2 30.97%, #d68fff 92.66%);
    box-shadow: 0px -0.864px 4.32px 0px #b01dff inset,
      0px 1.728px 6.912px 0px rgba(153, 25, 237, 0.27);
    &::after {
      border-color: transparent transparent #ad3cee transparent;
    }
    &::before {
      border-color: transparent transparent #ad3cee transparent;
    }
  }
}
</style>
<style lang="scss" scoped>
.study-report-detail-page {
  width: 100vw;
  min-height: 100vh;
  height: auto;
  padding: 34px 19px 50px 19px;
  background-size: 100% 100%;
  ::v-deep .u-navbar {
    // .u-navbar--fixed {
    //   top: 60rpx;
    // }
    .u-navbar__content__title {
      text-align: center;
      font-size: 16px;
      color: #fff;
      font-weight: 600;
    }
  }
  ::v-deep .el-textarea__inner {
    border-radius: 7px;
    border: 1px solid #e9e9e9;
    background: #f5f6f7;
  }
  ::v-deep .el-drawer__title {
    border-radius: 11px 11px 0 0;
    .el-drawer__header {
      border-bottom: 0;
    }
  }
  .content {
    width: 100%;
    .content__wrap {
      position: relative;
      width: 100%;
      // min-height: 80vh;
      padding: 18px 12px 18px 12px;
      border-radius: 15px;
      background: #fff;
      .avatar {
        width: 72px;
        height: 72px;
        position: absolute;
        top: -19px;
        left: 11px;
        border-radius: 50%;
        // border: 1px solid #fff;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .content__wrap-teacher-auto {
        height: 100%;
        border-radius: 14px;
        display: flex;
        flex-direction: column;
        .student-name {
          color: #333;
          font-size: 15px;
          font-weight: 500;
          margin-left: 83px;
          margin-bottom: 27px;
        }
        .student-info {
          padding: 11px 25px;
          border-radius: 14px;
          margin-bottom: 12px;
          position: relative;
          .fixed-img {
            position: absolute;
            bottom: -17px;
            width: 7px;
            height: 25px;
            z-index: 2;
            &.left {
              left: 14px;
            }
            &.right {
              right: 14px;
            }
          }
          .classroom-name {
            color: #333;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 5px;
          }
          .report-date {
            color: #666;
            font-size: 14px;
            font-weight: 400;
            .teacher-name {
              margin-right: 5px;
            }
          }
          .title {
            color: #fff;
            border-radius: 0px 0px 21.6px 21.6px;
            font-size: 16px;
            font-weight: 600;
            position: absolute;
            bottom: -34px;
            left: 50%;
            z-index: 2;
            transform: translateX(-50%);
            border-radius: 0px 0px 11px 11px;
            width: 108px;
            height: 26px;
            text-align: center;
            line-height: 26px;
            &::after {
              content: "";
              display: block;
              width: 0;
              height: 0;
              border-width: 4px;
              position: absolute;
              top: 1px;
              left: -4px;
              border-style: solid;
              transform: rotate(135deg);
            }
            &::before {
              content: "";
              display: block;
              width: 0;
              height: 0;
              border-width: 3px;
              position: absolute;
              top: 1px;
              right: -3px;
              border-style: solid;
              transform: rotate(-135deg);
            }
          }
        }
        .teacher-remark {
          height: 100%;
          flex-grow: 1;
          border-radius: 12px;
          position: relative;
          padding: 30px 16px 16px 16px;
          overflow: auto;
          .page-corner {
            width: 35px;
            height: 33px;
            position: absolute;
            right: -2px;
            bottom: 10px;
          }
          .content-txt-wrap {
            height: 100%;
            overflow: auto;
            .content-txt {
              width: 100%;
              height: 100%;
              overflow: auto;
            }
          }
        }
      }
    }
  }

  .mark-block {
    margin-top: 15px;
    padding: 16px 18px;
    border-radius: 15px;
    background: #fff;
    box-shadow: 0px 4px 20px 0px rgba(60, 0, 211, 0.07);
    .mark-title {
      text-align: center;
      margin-bottom: 15px;
      .first-mark-tilte {
        font-weight: 500;
        color: #333;
        font-size: 15px;
      }
      .second-mark-tilte {
        .mark-status {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 15px;
          font-weight: 500;
          color: #333;
          img {
            margin-right: 5px;
            width: 15px;
            height: 15px;
          }
        }
        .mark-tips {
          margin-top: 4px;
          font-size: 12px;
          color: #666;
          font-weight: 400;
        }
      }
    }
    .mark-item-wrap {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      .mark-item {
        width: calc(20% - 19px);
        margin-right: 19px;
        text-align: center;
        white-space: nowrap;
        &:last-child {
          margin-right: 0;
        }
        .mark-item-name {
          color: #999;
          font-weight: 400;
        }
        .active {
          color: #fb0;
        }
        img {
          width: 30px;
          height: 30px;
        }
      }
    }
    .second-mark-wrap {
      display: flex;
      align-items: center;
      .label {
        font-weight: 500;
        font-size: 14px;
        margin-right: 10px;
      }
      .second-mark-item-wrap {
        display: flex;
        align-items: center;
        .second-mark-item {
          .second-mark-item-url {
            img {
              width: 20px;
              height: 20px;
              margin-right: 8px;
            }
          }
        }
      }
    }
    .feedback-content {
      margin-top: 16px;
      .error-message {
        font-size: 12px;
        font-weight: 400;
        color: #fe4f37;
        margin-top: 6px;
      }
      .feedback-content-input {
        width: 100%;
        ::v-deep .el-textarea__inner {
          height: 80px;
        }
      }
    }
    .submit-btn {
      width: 80px;
      height: 32px;
      margin: 0 auto;
      margin-top: 15px;
      font-size: 12px;
      color: #fff;
      border-radius: 16px;
      background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
      box-shadow: 0px -4px 8px 0px #eaac00 inset;
      border-radius: 25px;
      text-align: center;
      line-height: 32px;
      font-weight: 500;
      flex-shrink: 0;
    }
  }
  .bar {
    margin-top: 15px;
    display: flex;
    justify-content: center;
    .bar-item {
      width: 134px;
      height: 50px;
      text-align: center;
      line-height: 50px;
      font-size: 17px;
      font-weight: 500;
      border-radius: 50px;
      margin-right: 19px;
      &.share {
        color: #fff;
        background: linear-gradient(270deg, #3667f0 0%, #568ff5 100%);
      }
      &.revoke {
        color: #8492a6;
        background: #d3dce6;
      }
    }
  }
}
::v-deep .el-dialog {
  border-radius: 18px;
}
::v-deep .el-dialog__header {
  display: none;
}
::v-deep .el-dialog__body {
  padding: 30px 0px 35px 0 !important;
}
::v-deep .el-dialog__footer {
  padding: 0px 17px 30px 17px;
  padding-top: 0;
}
.score-tips {
  font-size: 18px;
  font-weight: 500;
  text-align: center;
  color: #333;
}
.confirm-buttons {
  display: flex;
  width: 100%;
  justify-content: space-between;
}
.confirm-button {
  width: 119px;
  height: 44px;
  border-radius: 22px;
  font-size: 17px;
  font-weight: bold;
  text-align: center;
  line-height: 44px;

  &:active {
    opacity: 0.8;
  }
}

.cancel-button {
  border: 1px solid #ffc525;
  color: #ffc525;
}

.confirm-button-primary {
  color: #fff;
  background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
  box-shadow: 0px -5px 12px 0px #fc0 inset, 0px 9px 20px 0px #fff7e1 inset;
  filter: drop-shadow(0px 4px 4px rgba(255, 192, 18, 0.11));
}
</style>
