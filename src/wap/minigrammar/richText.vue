<template>
  <div>
    <div v-show="isShow" class="editor_container">
      <!-- <div class="editor-toolbox">
        <img
          @click="open_resource_hub"
          class="loc"
          src="https://tg-prod.oss-cn-beijing.aliyuncs.com/c8291bd9-8e0c-411b-a0eb-68cb3fc85309.png"
          alt=""
        />
      </div> -->
      <div class="editor-content">
        <!-- 文字区域 -->
        <div class="text-area">
          <textarea
            class="text-input"
            placeholder="写点什么..."
            v-model="textContent"
            rows="10"
            maxlength="500"
          ></textarea>
          <div class="char-count">{{ (textContent || "").length }}/500</div>
        </div>
        <!-- 上传图片、视频、选择本地资料库区域 -->
        <div class="upload-area">
          <!-- 已上传文件预览区域 -->
          <div class="uploaded-files" v-if="uploadedFiles.length > 0">
            <div
              class="file-preview"
              v-for="(file, index) in uploadedFiles"
              :key="index"
            >
              <!-- 图片预览 -->
              <div v-if="file.type === 'image'" class="preview-item">
                <img
                  preview="preview"
                  preview-text=""
                  :src="file.url"
                  :alt="file.name"
                  class="preview-image"
                />
                <!-- 上传中遮罩 -->
                <div v-if="file.uploading" class="uploading-overlay">
                  <div class="uploading-spinner"></div>
                </div>
                <div class="close-btn" @click="removeFile(index)">
                  <img
                    src="https://tg-prod.oss-cn-beijing.aliyuncs.com/7d2b660a-78e4-45ee-9bd7-1450f284e706.png"
                    alt="删除"
                    class="close-icon"
                  />
                </div>
              </div>

              <!-- 视频预览 -->
              <div v-else-if="file.type === 'video'" class="preview-item">
                <div
                  class="video-container"
                  @click="!file.uploading && playVideo(file)"
                >
                  <video
                    :poster="
                      file.poster ? file.poster : file.url + imageProcess
                    "
                    :src="file.url"
                    class="preview-video"
                  ></video>
                  <div v-if="!file.uploading" class="video-overlay">
                    <img
                      src="https://tg-prod.oss-cn-beijing.aliyuncs.com/52c7d9d2-ac99-4743-8fb7-f21f833c000a.png"
                      alt="播放"
                      class="play-button"
                    />
                  </div>
                  <!-- 视频上传中遮罩 -->
                  <div v-if="file.uploading" class="uploading-overlay">
                    <div class="uploading-spinner"></div>
                  </div>
                </div>
                <div class="close-btn" @click="removeFile(index)">
                  <img
                    src="https://tg-prod.oss-cn-beijing.aliyuncs.com/7d2b660a-78e4-45ee-9bd7-1450f284e706.png"
                    alt="删除"
                    class="close-icon"
                  />
                </div>
              </div>
            </div>
          </div>

          <div class="upload-buttons">
            <!-- 添加图片按钮 -->
            <div class="upload-btn" @click="selectImage">
              <img
                src="https://tg-prod.oss-cn-beijing.aliyuncs.com/9e84f9b9-df5b-48dd-9cd0-f7fad64e374f.png"
                alt="添加图片"
                class="btn-icon"
              />
              <span class="btn-text">上传图片</span>
            </div>

            <!-- 上传视频按钮 -->
            <div class="upload-btn" @click="selectVideo">
              <img
                src="https://tg-prod.oss-cn-beijing.aliyuncs.com/c198cbba-a178-46e2-be30-3aef8626e65f.png"
                alt="上传视频"
                class="btn-icon"
              />
              <span class="btn-text">上传视频</span>
            </div>

            <!-- 本地资料库按钮 -->
            <div class="upload-btn" @click="open_resource_hub">
              <img
                src="https://tg-prod.oss-cn-beijing.aliyuncs.com/c8291bd9-8e0c-411b-a0eb-68cb3fc85309.png"
                alt="本地资料库"
                class="btn-icon"
              />
              <span class="btn-text">本地资料库</span>
            </div>
          </div>
        </div>
      </div>
      <div class="ctrl-box">
        <span @click="previewClick" class="preview">预览</span>
        <span @click="beforSend" class="send">发送</span>
      </div>
    </div>
    <preview
      v-if="preview_visible"
      :html="html"
      :student_name="student_name"
      :students="students"
      :student_gender="student_gender"
      :studyReportInfo="studyReportInfo"
      :pageTitle="query.pageTitle"
      @close="closePreview"
      @send="beforSend"
    ></preview>
    <resourceHub
      @confirm="resourceHubChoose"
      @close="resource_hub_visible = false"
      :department_id="department_id"
      :token="query.token"
      :visible="resource_hub_visible"
    ></resourceHub>

    <!-- 视频播放弹出层 -->
    <div
      v-if="videoPreviewVisible"
      class="video-preview-modal"
      @click="closeVideoPreview"
    >
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <span class="modal-title">视频预览</span>
          <div class="modal-close" @click="closeVideoPreview">
            <img
              src="https://tg-prod.oss-cn-beijing.aliyuncs.com/7d2b660a-78e4-45ee-9bd7-1450f284e706.png"
              alt="关闭"
              class="close-icon"
            />
          </div>
        </div>
        <div class="modal-body">
          <video
            v-if="currentVideoUrl"
            :src="currentVideoUrl"
            class="modal-video"
            controls
            :poster="currentVideoPoster"
            autoplay
            playsinline
            webkit-playsinline
            x-webkit-airplay="deny"
            controlslist="nodownload  noremoteplayback"
            disablepictureinpicture
          ></video>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import resourceHub from "./resourceHub";
import preview from "./preview";
// import { Base64 } from "js-base64";
import { getFeedbackDetail } from "@/api/preview";
import appletResource from "@/api/appletResource"; // 资料库
export default {
  name: "richText",
  components: { preview, resourceHub },
  data() {
    return {
      isShow: false,
      preview_visible: false,
      uplaodLoding: false,
      resource_hub_visible: false,
      editor: null,
      html: "",
      textContent: "", // 新增：文本内容
      uploadedFiles: [], // 新增：已上传文件列表
      videoPreviewVisible: false, // 视频预览弹窗显示状态
      currentVideoUrl: "", // 当前预览的视频URL
      currentVideoPoster: "", // 当前预览的视频封面
      students: [],
      studyReportInfo: {},
      student_name: "",
      student_gender: "",
      department_id: "",
      send_types: {
        course_summary: "课程总结",
        class_notice: "班级通知",
        parent_class: "家长课堂"
      },
      imageProcess: "?x-oss-process=video/snapshot,t_2000,m_fast,w_320,ar_auto"
    };
  },
  computed: {
    query() {
      return this.$route.query;
    }
  },
  watch: {
    preview_visible(val) {
      if (val) {
        document.title = "预览";
      } else {
        document.title = "编辑内容";
      }
    }
  },
  created() {
    const { openPreview } = this.query;
    if (!openPreview) {
      this.isShow = true;
    }
  },
  mounted() {
    console.log("this.query :>> ", this.query);
    const {
      source,
      department_id,
      students,
      student_id,
      student_name,
      student_gender
    } = this.query;
    this.department_id = department_id;
    if (source === "student") {
      if (students) {
        // 批量发送
        this.students = JSON.parse(decodeURI(students));
      } else {
        this.student_name = student_name;
        this.student_gender = student_gender;
        this.student_id = student_id;
        this.students = [
          {
            student_id,
            student_name,
            student_gender
          }
        ];
      }
    }
    if (this.query.pageType === "class_notice") {
      this.getClassNotice();
    } else {
      if (this.query.openEdit) {
        this.getEditorContent();
        // this.preview_visible = true;
      } else {
        this.getSchedulingInfo();
      }
    }

    console.log(" this.students :>> ", this.students);
    this.Oss.getAliyun({
      token: this.query.token,
      requestType: "MINIGRAMER"
    });
  },
  methods: {
    async getClassNotice() {
      const { code, message, data } = await getFeedbackDetail({
        feedback_id: this.query.feedback_id,
        student_id: this.query.student_id || this.students[0].student_id,
        classroom_id: this.query.classroom_id,
        token: this.query.isShare ? undefined : this.query.token,
        requestType: this.query.isShare ? undefined : "MINIGRAMER"
      });
      if (code === 0) {
        this.html = data.content;
        this.studyReportInfo = data;

        // 解析HTML内容进行回显
        this.parseContentForEdit(data.content);

        if (this.query.openPreview) {
          this.previewClick();
        }
      } else {
        this.$message.error(message);
      }
    },
    async getEditorContent() {
      const res = await appletResource.getMiniProgramFeedbackDetail({
        feedback_id: this.query.feedback_id,
        student_id: this.query.student_id,
        token: this.query.isShare ? undefined : this.query.token,
        requestType: this.query.isShare ? undefined : "MINIGRAMER"
      });
      const { code, data, message } = res.data;
      if (code === 0) {
        this.html = data.content;
        this.studyReportInfo = data;

        // 解析HTML内容进行回显
        this.parseContentForEdit(data.content);

        if (this.query.openPreview) {
          this.previewClick();
        }
      } else {
        this.$message.error(message);
      }
    },
    async getSchedulingInfo() {
      const res = await appletResource.schedulingInfo({
        scheduling_id: this.query.scheduling_id,
        student_id: this.query.student_id || this.students[0].student_id,
        token: this.query.token,
        requestType: "MINIGRAMER"
      });
      this.studyReportInfo = {
        ...res.data,
        ...res.data.scheduling_info,
        advice: "",
        score: 0,
        score_num: 0
      };
      const { start_time, end_time, week_day_chn } = this.studyReportInfo;
      const timeRange =
        moment(start_time).format("HH:mm") +
        " - " +
        moment(end_time).format("HH:mm");
      this.studyReportInfo.start_time =
        moment(start_time).format("YYYY-MM-DD") +
        " " +
        timeRange +
        week_day_chn;
      console.log("this.studyReportInfo :>> ", this.studyReportInfo);
    },
    previewClick() {
      document.title = "预览";

      // 生成HTML内容
      const htmlContent = this.generatePreviewHTML();
      if (!this.query.openEdit) {
        if (!htmlContent || htmlContent.trim() === "") {
          this.$message.info("请输入内容或上传图片/视频");
          return;
        }
      }

      this.studyReportInfo.content = htmlContent;
      this.html = htmlContent;
      console.log("预览HTML内容 :>> ", htmlContent);
      this.preview_visible = true;
    },
    beforSend() {
      // 生成HTML内容
      const htmlContent = this.generatePreviewHTML();

      if (!htmlContent || htmlContent.trim() === "") {
        this.$message.info("请输入内容或上传图片/视频");
        return;
      }

      this.editorDialogConfirm(htmlContent);
    },
    // 发送学员报告
    editorDialogConfirm(content) {
      console.log("content :>> ", content);
      const { send_types, students } = this;
      const { pageType, classroom_id, classroom_name, source, scheduling_id } =
        this.query;
      // 询问框提示
      this.$confirm(`确定发送${send_types[pageType]}吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        const params = {
          classroom: {
            classroom_id,
            classroom_name
          },
          content,
          scheduling_id,
          type: pageType,
          students: source === "student" ? students : [],
          token: this.query.token,
          requestType: "MINIGRAMER"
        };
        appletResource
          .miniprogramFeedbackSend(params)
          .then((res) => {
            if (res.data.code === 0) {
              this.$message.success("发送成功！");
              setTimeout(() => {
                wx.miniProgram.navigateBack();
              }, 2000);
            } else {
              this.$message.error(res.data.message);
            }
          })
          .catch((err) => {
            console.error(err);
            this.$message.error("发送失败！");
          });
      });
    },
    closePreview() {
      this.preview_visible = false;
    },

    back() {
      this.$emit("close");
    },
    confirm() {
      const data = this.editor.getHtml();
      this.$emit("confirm", data);
      this.back();
    },
    // 修改 resourceHubChoose 方法来处理话术库选择：

    resourceHubChoose(content, type, poster = "") {
      console.log("content :>> ", content, type);

      if (type === 1 && Array.isArray(content)) {
        // 处理多选图片
        content.forEach((imageUrl, index) => {
          const fileItem = {
            name: `资料库图片_${Date.now()}_${index}`,
            url: imageUrl,
            type: "image",
            uploading: false
          };
          this.uploadedFiles.push(fileItem);
        });

        this.$nextTick(() => {
          this.scrollToLatestFile();
        });
      } else if (type === 2 && Array.isArray(content)) {
        // 处理多选视频
        content.forEach((videoItem, index) => {
          console.log("videoItem :>> ", videoItem);
          const fileItem = {
            name: `资料库视频_${Date.now()}_${index}`,
            url: videoItem.url,
            type: "video",
            poster: videoItem.poster,
            uploading: false
          };
          this.uploadedFiles.push(fileItem);
        });

        this.$nextTick(() => {
          this.scrollToLatestFile();
        });
      } else if (type === 2) {
        // 处理单个视频（兼容旧逻辑）
        const fileItem = {
          name: `资料库视频_${Date.now()}`,
          url: content,
          type: "video",
          poster,
          uploading: false
        };
        this.uploadedFiles.push(fileItem);

        this.$nextTick(() => {
          this.scrollToLatestFile();
        });
      } else if (type === 3) {
        // 处理话术库内容 - 自动填入文字区域
        this.insertTextToTextarea(content);
      }
      this.$previewRefresh();
      this.resource_hub_visible = false;
    },
    open_resource_hub() {
      this.resource_hub_visible = true;
    },
    // 选择图片
    selectImage() {
      console.log("选择图片");
      // 创建文件输入元素
      const input = document.createElement("input");
      input.type = "file";
      input.accept = "image/*";
      input.multiple = true; // 允许多选
      input.onchange = (e) => {
        const files = Array.from(e.target.files);
        if (files.length > 0) {
          // 限制每次最多选择5张图片
          if (files.length > 5) {
            this.$message.info("每次最多只能选择5张图片！");
            return;
          }
          // 批量处理图片上传
          files.forEach((file, index) => {
            this.handleFileUpload(file, "image", index === files.length - 1);
          });
        }
      };
      input.click();
    },
    // 选择视频
    selectVideo() {
      console.log("选择视频");
      // 创建文件输入元素
      const input = document.createElement("input");
      input.type = "file";
      input.accept = "video/*";
      input.multiple = false; // 视频不允许多选
      input.onchange = (e) => {
        const file = e.target.files[0];
        if (file) {
          // 视频每次只能选择一个
          this.handleFileUpload(file, "video");
        }
      };
      input.click();
    },
    // 处理文件上传
    handleFileUpload(file, type, isLast = true) {
      // 文件大小验证
      const isLt50M = file.size / 1024 / 1024 < 50;
      const isLt300M = file.size / 1024 / 1024 < 300;
      if (!isLt50M && file.type.indexOf("image") !== -1) {
        this.$message.warning("上传图片大小不能超过50MB!");
        return;
      } else if (!isLt300M && file.type.indexOf("video") !== -1) {
        this.$message.warning("上传视频大小不能超过300MB!");
        return;
      }

      // 显示上传loading
      this.uplaodLoding = true;

      // 先创建本地预览URL和临时文件项
      const localUrl = URL.createObjectURL(file);
      const tempFileItem = {
        name: file.name,
        url: localUrl,
        type,
        file,
        uploading: true // 标记为上传中
      };

      // 添加到上传文件列表（先显示本地预览）
      this.uploadedFiles.push(tempFileItem);
      const fileIndex = this.uploadedFiles.length - 1;

      console.log("开始上传文件:", file.name);

      // 生成唯一文件名
      const orginName = file.name.substring(0, file.name.lastIndexOf("."));
      const suffix = file.name.match(/[^.]+$/)[0];
      const uniqueName = `${orginName}_${this.$uuid.v1()}.${suffix}`;
      const copyFile = new File([file], uniqueName);

      // 上传到OSS
      this.Oss.uploadFile(copyFile)
        .then((res) => {
          console.log("OSS上传结果:", res);
          if (res.code === 0) {
            // 上传成功，更新为OSS真实地址
            this.uploadedFiles[fileIndex].url = res.url;
            this.uploadedFiles[fileIndex].uploading = false;
            // 释放本地URL
            URL.revokeObjectURL(localUrl);
            console.log("文件上传成功:", res.url);
          } else {
            // 上传失败，移除该项
            this.uploadedFiles.splice(fileIndex, 1);
            URL.revokeObjectURL(localUrl);
            this.$message.error("上传失败！");
          }
          this.uplaodLoding = false;

          // 只在最后一个文件处理完成时滚动
          if (isLast) {
            this.$nextTick(() => {
              this.scrollToLatestFile();
            });
          }
          this.$previewRefresh();
        })
        .catch((error) => {
          console.error("上传错误:", error);
          // 上传失败，移除该项
          this.uploadedFiles.splice(fileIndex, 1);
          URL.revokeObjectURL(localUrl);
          this.$message.error("上传失败！");
          this.uplaodLoding = false;
        });
    },
    // 删除文件
    removeFile(index) {
      const file = this.uploadedFiles[index];
      // 如果是本地URL，需要释放
      if (file.url && file.url.startsWith("blob:")) {
        URL.revokeObjectURL(file.url);
      }
      // 从列表中移除
      this.uploadedFiles.splice(index, 1);
      console.log("文件已删除");
    },
    // 滚动到最新文件位置
    scrollToLatestFile() {
      const uploadedFilesContainer = document.querySelector(".uploaded-files");
      if (uploadedFilesContainer && this.uploadedFiles.length > 0) {
        // 使用 setTimeout 确保DOM完全渲染后再滚动
        setTimeout(() => {
          const scrollLeft =
            uploadedFilesContainer.scrollWidth -
            uploadedFilesContainer.clientWidth;
          uploadedFilesContainer.scrollTo({
            left: Math.max(0, scrollLeft),
            behavior: "smooth"
          });
        }, 100);
      }
    },

    // doPlay() {
    //   WeixinJSBridge.invoke("getNetworkType", {}, function (e) {
    //     const video = document.querySelector(".modal-video");
    //     video.play();
    //   });
    // },
    // 播放视频
    playVideo(file) {
      this.currentVideoUrl = file.url;
      this.currentVideoPoster = file.poster
        ? file.poster
        : file.url + this.imageProcess;
      this.videoPreviewVisible = true;
      // if (window.WeixinJSBridge) {
      //   doPlay();
      // } else {
      //   document.addEventListener(
      //     "WeixinJSBridgeReady",
      //     function () {
      //       doPlay();
      //     },
      //     false
      //   );
      // }

      console.log("播放视频:", file.name);
    },
    // 关闭视频预览
    closeVideoPreview() {
      this.videoPreviewVisible = false;
      this.currentVideoUrl = "";
      this.currentVideoPoster = "";
    },

    // 改进的 insertTextToTextarea 方法：

    insertTextToTextarea(content) {
      // 去除HTML标签
      const tempDiv = document.createElement("div");
      tempDiv.innerHTML = content;
      const plainText = tempDiv.textContent || tempDiv.innerText || "";

      const textarea = document.querySelector(".text-input");
      if (!textarea) return;

      const currentText = this.textContent || "";
      const cursorPosition = textarea.selectionStart || currentText.length;

      // 分割当前文本
      const beforeCursor = currentText.substring(0, cursorPosition);
      const afterCursor = currentText.substring(cursorPosition);

      // 检查长度限制
      const maxLength = 500;
      const newText = beforeCursor + plainText + afterCursor;

      let insertedTextLength = plainText.length;

      if (newText.length > maxLength) {
        const availableLength =
          maxLength - beforeCursor.length - afterCursor.length;
        if (availableLength <= 0) {
          this.$message.info("文本内容已达到最大长度限制！");
          return;
        }

        const truncatedText = plainText.substring(0, availableLength);
        this.textContent = beforeCursor + truncatedText + afterCursor;
        insertedTextLength = availableLength;
        this.$message.info(`话术内容过长，已自动截断。`);
      } else {
        this.textContent = newText;
        // this.$message.success(`话术已插入！`);
      }

      // 设置新的光标位置
      this.$nextTick(() => {
        const newCursorPosition = beforeCursor.length + insertedTextLength;
        textarea.focus();
        textarea.setSelectionRange(newCursorPosition, newCursorPosition);
      });
    },

    // 更精细的HTML生成方法：

    generatePreviewHTML() {
      let htmlContent = "";

      // 1. 添加文字内容
      if (this.textContent && this.textContent.trim()) {
        // 处理换行和空格，保持格式
        const formattedText = this.textContent
          .split("\n")
          .map((line) => {
            const trimmedLine = line.trim();
            return trimmedLine
              ? `<p style="margin: 8px 0; line-height: 1.5; color: #333; font-size: 16px;">${trimmedLine}</p>`
              : '<p style="margin: 4px 0;">&nbsp;</p>';
          })
          .join("");
        htmlContent += formattedText;
      }

      // 2. 添加媒体内容
      if (this.uploadedFiles.length > 0) {
        // 如果有文字内容，在媒体内容前添加一些间距
        if (htmlContent) {
          htmlContent += '<div style="margin: 15px 0;"></div>';
        }

        this.uploadedFiles.forEach((file, index) => {
          if (file.type === "image") {
            htmlContent += `
              <div style="margin: 12px 0; text-align: center;"><img src="${file.url}" alt="${file.name}" style="max-width: 100%;height: auto;border-radius: 8px;box-shadow: 0 2px 8px rgba(0,0,0,0.1);display: block;margin: 0 auto;" /></div>`;
          } else if (file.type === "video") {
            htmlContent += `
              <div style="margin: 12px 0; text-align: center;"><video src="${
                file.url
              }" poster="${
              file.poster ? file.poster : file.url + this.imageProcess
            }" controls  playsinline webkit-playsinline x-webkit-airplay="deny" controlslist="nodownload noremoteplayback" disablepictureinpicturestyle=" width: 100%;max-height: 200px;object-fit: contain;background-color: #000;border-radius: 8px;box-shadow: 0 2px 8px rgba(0,0,0,0.1);"></video></div>`;
          }
        });
      }

      // 如果没有任何内容
      if (!htmlContent.trim()) {
        return "";
      }

      // 包装在一个容器中，确保样式一致性
      return `<div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">${htmlContent}</div>`;
    },

    // 解析HTML内容进行回显
    parseContentForEdit(htmlContent) {
      if (!htmlContent || htmlContent.trim() === "") {
        return;
      }

      console.log("开始解析HTML内容进行回显:", htmlContent);

      // 创建临时DOM容器来解析HTML
      const tempDiv = document.createElement("div");
      tempDiv.innerHTML = htmlContent;

      const textParts = [];
      const mediaFiles = [];

      // 按DOM顺序遍历所有节点，确保媒体文件顺序正确
      this.parseContentInOrder(tempDiv, textParts, mediaFiles);

      // 如果没有找到任何内容，尝试备用解析方法
      if (textParts.length === 0 && mediaFiles.length === 0) {
        this.parseUnstructuredContent(tempDiv, textParts, mediaFiles);
      }

      // 设置文本内容
      const extractedText = textParts.join("\n").trim();
      if (extractedText) {
        this.textContent = extractedText;
      }

      // 设置媒体文件（按解析顺序）
      if (mediaFiles.length > 0) {
        this.uploadedFiles = mediaFiles;
        console.log("回显的媒体文件（按顺序）:", mediaFiles);
      }

      console.log(
        "回显解析完成 - 文本:",
        this.textContent,
        "媒体文件数量:",
        mediaFiles.length
      );
    },

    // 按DOM顺序解析内容，确保媒体文件顺序正确
    parseContentInOrder(container, textParts, mediaFiles) {
      // 使用TreeWalker按文档顺序遍历所有节点
      const walker = document.createTreeWalker(
        container,
        NodeFilter.SHOW_ELEMENT | NodeFilter.SHOW_TEXT,
        {
          acceptNode: function (node) {
            // 接受文本节点、图片、视频和段落元素
            if (node.nodeType === Node.TEXT_NODE) {
              return NodeFilter.FILTER_ACCEPT;
            }
            if (node.nodeType === Node.ELEMENT_NODE) {
              const tagName = node.tagName.toLowerCase();
              if (
                tagName === "img" ||
                tagName === "video" ||
                tagName === "p" ||
                tagName === "source"
              ) {
                return NodeFilter.FILTER_ACCEPT;
              }
            }
            return NodeFilter.FILTER_SKIP;
          }
        },
        false
      );

      let node;
      let mediaIndex = 0;

      while ((node = walker.nextNode())) {
        if (node.nodeType === Node.TEXT_NODE) {
          // 处理文本节点
          const text = node.textContent.trim();
          if (text && text !== "&nbsp;" && text !== "") {
            // 检查父元素是否是段落
            const parent = node.parentElement;
            if (parent && parent.tagName === "P") {
              // 只在段落内的文本才添加
              if (!textParts.includes(text)) {
                textParts.push(text);
              }
            }
          }
        } else if (node.nodeType === Node.ELEMENT_NODE) {
          const tagName = node.tagName.toLowerCase();

          if (tagName === "p") {
            // 处理段落元素
            const text = node.textContent.trim();
            if (text && text !== "&nbsp;" && text !== "") {
              textParts.push(text);
            }
          } else if (tagName === "img") {
            // 处理图片，按顺序添加
            if (node.src) {
              mediaFiles.push({
                name: `回显图片_${Date.now()}_${mediaIndex++}`,
                url: node.src,
                type: "image",
                uploading: false
              });
            }
          } else if (tagName === "video") {
            // 处理视频，按顺序添加
            let videoUrl = node.src;

            // 如果video标签没有直接的src，检查source子元素
            if (!videoUrl) {
              const sourceElements = node.querySelectorAll("source");
              if (sourceElements.length > 0) {
                // 取第一个source的src作为视频URL
                videoUrl = sourceElements[0].src;
              }
            }

            if (videoUrl) {
              mediaFiles.push({
                name: `回显视频_${Date.now()}_${mediaIndex++}`,
                url: videoUrl,
                type: "video",
                poster: node.poster || videoUrl + this.imageProcess,
                uploading: false
              });
            }
          }
        }
      }
    },

    // 解析元素内容的辅助方法
    parseElementContent(element, textParts, mediaFiles) {
      if (element.tagName === "P") {
        // 处理段落文本
        const textNode = element.textContent || element.innerText || "";
        if (textNode.trim() && textNode.trim() !== "&nbsp;") {
          textParts.push(textNode.trim());
        }
      } else if (element.tagName === "DIV") {
        // 递归处理div容器
        const children = Array.from(element.children);
        children.forEach((child) => {
          this.parseElementContent(child, textParts, mediaFiles);
        });

        // 处理div中的图片和视频
        const imgs = element.querySelectorAll("img");
        const videos = element.querySelectorAll("video");

        imgs.forEach((img, index) => {
          if (img.src) {
            mediaFiles.push({
              name: `回显图片_${Date.now()}_${index}`,
              url: img.src,
              type: "image",
              uploading: false
            });
          }
        });

        videos.forEach((video, index) => {
          if (video.src) {
            mediaFiles.push({
              name: `回显视频_${Date.now()}_${index}`,
              url: video.src,
              type: "video",
              poster: video.poster || video.src + this.imageProcess,
              uploading: false
            });
          }
        });
      }
    },

    // 解析非结构化内容的辅助方法
    parseUnstructuredContent(container, textParts, mediaFiles) {
      // 提取所有文本节点
      const textNodes = [];
      const walker = document.createTreeWalker(
        container,
        NodeFilter.SHOW_TEXT,
        null,
        false
      );

      let node;
      while ((node = walker.nextNode())) {
        const text = node.textContent.trim();
        if (text && text !== "&nbsp;") {
          textNodes.push(text);
        }
      }

      // 将文本节点添加到textParts数组
      textParts.push(...textNodes);

      // 提取所有图片
      const imgs = container.querySelectorAll("img");
      imgs.forEach((img, index) => {
        if (img.src) {
          mediaFiles.push({
            name: `回显图片_${Date.now()}_${index}`,
            url: img.src,
            type: "image",
            uploading: false
          });
        }
      });

      // 提取所有视频
      const videos = container.querySelectorAll("video");
      videos.forEach((video, index) => {
        let videoUrl = video.src;

        // 如果video标签没有直接的src，检查source子元素
        if (!videoUrl) {
          const sourceElements = video.querySelectorAll("source");
          if (sourceElements.length > 0) {
            // 取第一个source的src作为视频URL
            videoUrl = sourceElements[0].src;
          }
        }

        if (videoUrl) {
          mediaFiles.push({
            name: `回显视频_${Date.now()}_${index}`,
            url: videoUrl,
            type: "video",
            poster: video.poster || videoUrl + this.imageProcess,
            uploading: false
          });
        }
      });
    }
  },
  beforeDestroy() {
    // 清理上传文件的本地URL对象
    this.uploadedFiles.forEach((file) => {
      if (file.url && file.url.startsWith("blob:")) {
        URL.revokeObjectURL(file.url);
      }
    });
  }
};
</script>
<style>
#app {
  width: 100vw;
  height: 100vh;
  min-height: auto;
  min-width: auto;
  /* ios 底部安全区 */
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  box-sizing: border-box;
}
.el-notification {
  width: 80% !important;
}
.message-info {
  min-width: 80% !important;
}
.message-error {
  min-width: 80% !important;
}
.message-success {
  min-width: 80% !important;
}
</style>
<style lang="less" scoped>
.editor_container {
  height: 100vh;
  overflow: hidden;
  display: grid;
  grid-template-rows: 1fr 100px;
}
.editor-content {
  border-bottom: 1px solid #ebeef5;
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;

  .text-area {
    flex: 1;
    padding: 15px;
    position: relative;

    .text-input {
      width: 100%;
      height: 100%;
      border: none;
      outline: none;
      resize: none;
      font-size: 16px;
      line-height: 1.5;
      color: #333;
      padding-bottom: 10px;
      background-color: transparent;

      &::placeholder {
        color: #c0c4cc;
        font-size: 16px;
      }
    }
    .char-count {
      position: absolute;
      bottom: 5px;
      right: 10px;
      font-size: 12px;
      color: #999;
      background: rgba(255, 255, 255, 0.8);
      padding: 2px 6px;
      border-radius: 4px;
    }
  }

  .upload-area {
    border-top: 1px solid #f0f0f0;
    width: 100%;
    box-sizing: border-box;
    overflow: hidden; // 防止子元素溢出

    .uploaded-files {
      display: flex;
      flex-wrap: nowrap;
      gap: 20px;
      overflow-x: auto;
      overflow-y: hidden;
      padding: 10px;
      padding-top: 15px;
      border-bottom: 1px solid #f0f0f0;
      max-width: 100vw; // 明确设置宽度
      box-sizing: border-box; // 确保padding不会增加总宽度
      // 隐藏滚动条但保持滚动功能
      &::-webkit-scrollbar {
        display: none;
      }
      -ms-overflow-style: none;
      scrollbar-width: none;

      .file-preview {
        position: relative;
        flex-shrink: 0; // 确保每个预览项不被压缩
        max-width: none; // 移除最大宽度限制

        .preview-item {
          position: relative;
          border-radius: 12px;
          display: flex;
          align-items: center;
          // overflow: hidden;

          .preview-image {
            width: 60px; // 从80px减少到60px
            height: 60px; // 保持正方形
            object-fit: cover;
            border-radius: 8px;
            background-color: #f5f5f5;
          }

          .video-container {
            position: relative;
            cursor: pointer;
            flex-shrink: 0; // 确保不被压缩

            .preview-video {
              width: 100px;
              height: 60px;
              object-fit: cover;
              border-radius: 8px;
              background-color: #000;
              display: block;
            }

            .video-overlay {
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: rgba(0, 0, 0, 0.3);
              border-radius: 8px;
              display: flex;
              align-items: center;
              justify-content: center;
              transition: all 0.3s ease;

              &:hover {
                background: rgba(0, 0, 0, 0.5);
              }

              .play-button {
                width: 30px; // 从40px减少到30px
                height: 30px;
                object-fit: contain;
                opacity: 0.9;
              }
            }
          }

          .uploading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;

            .uploading-spinner {
              width: 20px;
              height: 20px;
              border: 2px solid #f3f3f3;
              border-top: 2px solid #3667f0;
              border-radius: 50%;
              animation: spin 1s linear infinite;
            }
          }

          .close-btn {
            position: absolute;
            top: -8px;
            right: -8px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 20;

            &:active {
              opacity: 0.7;
              transform: scale(0.9);
            }

            .close-icon {
              width: 18px;
              height: 18px;
              object-fit: contain;
            }
          }
        }
      }
    }

    .upload-buttons {
      display: flex;
      justify-content: space-around;
      align-items: center;
      padding: 5px 15px;
      .upload-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 8px 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        flex: 1; // 平均分配空间

        &:active {
          opacity: 0.6;
          transform: scale(0.95);
        }

        .btn-icon {
          width: 24px;
          height: 24px;
          margin-bottom: 4px;
          object-fit: contain;
        }

        .btn-text {
          font-size: 12px;
          color: #9c9c9c;
          text-align: center;
          white-space: nowrap;
        }
      }
    }
  }

  .w-e-text-container p {
    margin: 5px 0;
  }
  .w-e-text-placeholder {
    top: 7px;
  }
  img {
    max-width: 100%;
  }
  video {
    width: 100%;
    max-height: 200px;
    object-fit: contain;
    background-color: #000;
  }
}
.ctrl-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 25px;
  width: 100%;
  span {
    width: 134px;
    height: 50px;
    display: block;
    border-radius: 50px;
    text-align: center;
    line-height: 50px;
    font-size: 17px;
    font-weight: 500;
    &.preview {
      color: #8492a6;
      background: rgba(211, 220, 230, 0.4);
    }
    &.send {
      color: #fff;
      background: linear-gradient(270deg, #3667f0 0%, #568ff5 100%);
    }
    &:active {
      opacity: 0.5;
    }
  }
}

// 视频预览弹出层样式
.video-preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;

  .modal-content {
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
    max-width: 90vw;
    max-height: 90vh;
    position: relative;

    .modal-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px 20px;
      border-bottom: 1px solid #f0f0f0;

      .modal-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }

      .modal-close {
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        .close-icon {
          width: 20px;
          height: 20px;
          object-fit: contain;
        }
      }
    }

    .modal-body {
      padding: 20px;

      .modal-video {
        width: 100%;
        max-width: 80vw;
        max-height: 60vh;
        border-radius: 8px;
        background-color: #000;
      }
    }
  }
}

// 旋转动画
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
