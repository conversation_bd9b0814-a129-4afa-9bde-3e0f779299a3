<template>
  <div>
    <!-- <div class="row-item">
        <span class="left">申请编号</span>
        <span class="right">{{ approve_info.serial_number }}</span>
      </div>
      <div class="row-item">
        <span class="left">审批状态</span>
        <span class="right">{{ approve_info.status | getStatusCn }}</span>
      </div>
      <div class="row-item">
        <span class="left">业务类型</span>
        <span class="right">{{ approve_info.template_name }}</span>
      </div> -->
    <div class="row-item">
      <span class="left">收据号</span>
      <span class="right">{{ basic_info.receipt_infos | getReceiptNo }}</span>
    </div>
    <div class="row-item">
      <span class="left">申请编号</span>
      <span class="right">{{ approve_info.serial_number }}</span>
    </div>
    <div class="row-item">
      <span class="left">审批状态</span>
      <span class="right">{{ approve_info.status | getStatusCn }}</span>
    </div>
    <div class="row-item">
      <span class="left">业务类型</span>
      <span class="right">{{ approve_info.template_name }}</span>
    </div>
    <div v-if="basic_info.head_type === 1" class="row-item">
      <span class="left">企业名称</span>
      <span class="right">{{ basic_info.company_name }}</span>
    </div>
    <div v-if="basic_info.head_type === 1" class="row-item">
      <span class="left">纳税人识别号</span>
      <span class="right">{{ basic_info.tax_number }}</span>
    </div>
    <div v-if="basic_info.head_type === 2" class="row-item">
      <span class="left">姓名</span>
      <span class="right">{{ basic_info.full_name }}</span>
    </div>
    <div class="row-item">
      <span class="left">电话号码</span>
      <span class="right">{{ basic_info.phone_number }}</span>
    </div>
    <div class="row-item">
      <span class="left">地址</span>
      <span class="right">{{ basic_info.address }}</span>
    </div>
    <div class="row-item">
      <span class="left">银行账号</span>
      <span class="right">{{ basic_info.bank_account }}</span>
    </div>
    <div class="row-item">
      <span class="left">开户银行</span>
      <span class="right">{{ basic_info.bank_name }}</span>
    </div>
    <div class="row-item">
      <span class="left">邮箱</span>
      <span class="right">{{ basic_info.email }}</span>
    </div>
  </div>
</template>

<script>
import { base_info_components_name, workflow_status } from "@/public/dict";
export default {
  name: base_info_components_name.receipt_invoice,
  props: {
    approve_info: Object,
    basic_info: Object
  },
  data() {
    return {
      refundChannels: {
        back: "原路退回",
        cash: "虚拟退款",
        transfer: "银行转账"
      }
    };
  },
  filters: {
    getStatusCn(val) {
      const item = workflow_status.find((item) => val === item.id);
      return typeof item === "undefined" ? "" : item.name;
    },
    getReceiptNo(val) {
      const receipt_infos = val.map((item) => item.receipt_no);
      return receipt_infos.join("、");
    }
  }
};
</script>

<style lang="scss" scoped></style>
