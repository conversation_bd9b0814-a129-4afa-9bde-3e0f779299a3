<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div
    class="study-report-detail-page"
    :class="pageTheme[query.pageTitle].class"
  >
    <div class="content">
      <div class="content__wrap">
        <div class="content__wrap-teacher-auto">
          <div class="student-info-wrap" style="position: relative">
            <div>
              <div class="avatar">
                <img
                  :src="genders[studentInfo.choose_head] || genders[1]"
                  alt=""
                />
              </div>
              <div class="student-name">{{ studyReportInfo.student_name }}</div>
            </div>
            <div
              class="share-btn"
              v-if="!$route.query.isShare"
              @click="handleShareBtn"
            >
              分享
            </div>
          </div>
          <div class="student-info">
            <img
              :src="pageTheme[query.pageTitle].joinImg"
              class="fixed-img left"
              alt=""
            />
            <img
              :src="pageTheme[query.pageTitle].joinImg"
              class="fixed-img right"
              alt=""
            />
            <div>
              <div class="classroom-name">
                {{ studyReportInfo.classroom_alias_name }}
              </div>
              <div class="report-date">
                <!-- -->
                <div v-if="!isClassNotice">
                  上课时间：{{ studyReportInfo.start_time }}
                </div>
                <span class="teacher-name">{{
                  studyReportInfo.teacher_name
                }}</span>
                <span class="report-date-time">
                  发布于{{
                    moment(
                      new Date(studyReportInfo.created_date).getTime()
                    ).format("YYYY-MM-DD HH:mm:ss")
                  }}
                </span>
              </div>
            </div>
            <div class="title">
              <img :src="imageUrl" alt="" />
            </div>
          </div>
          <div class="teacher-remark">
            <div class="content-txt-wrap">
              <!-- v-if="isRender" -->
              <template>
                <!-- v-html="format_content" -->
                <div class="content-txt" id="content-txt"></div>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="mark-block" v-if="isCourseSummary">
      <div class="mark-title">
        <div class="common-mark-tilte" v-if="studyReportInfo.score_num === 0">
          请给本节课程做个评分吧！
        </div>
        <div class="second-mark-tilte" v-else>
          <template v-if="isShowEditTips">
            <div class="common-mark-tilte">修改课程评分</div>
          </template>
          <template v-else>
            <div class="mark-status">
              <img
                src="https://tg-prod.oss-cn-beijing.aliyuncs.com/6ff30fa4-ff8a-4f7f-9b10-1fcfedd12dc8.webp"
                alt=""
              />
              评价完成
            </div>
            <div class="mark-tips">感谢您的反馈</div>
          </template>
        </div>
      </div>
      <div class="mark-content">
        <div class="mark-item-wrap" v-if="studyReportInfo.score_num === 0">
          <div
            class="mark-item"
            @click="handleMark(item)"
            v-for="item in markList"
            :key="item.value"
          >
            <div class="mark-item-url">
              <img
                :src="
                  scoreParams.score && scoreParams.score >= item.value
                    ? item.scoreStyle[studyReportInfo.score_num]?.activeUrl
                    : item.scoreStyle[studyReportInfo.score_num]?.url
                "
                alt=""
              />
            </div>
            <div
              :class="[
                'mark-item-name',
                scoreParams.score && scoreParams.score === item.value
                  ? 'active'
                  : ''
              ]"
            >
              {{ item.name }}
            </div>
          </div>
        </div>
        <div v-else class="second-mark-wrap">
          <div class="label">我的评价</div>
          <div class="second-mark-item-wrap">
            <div
              class="second-mark-item"
              @click="handleMark(item)"
              v-for="item in markList"
              :key="item.value"
            >
              <div class="second-mark-item-url">
                <img
                  :src="
                    scoreParams.score && scoreParams.score >= item.value
                      ? item.scoreStyle[studyReportInfo.score_num > 0 ? 1 : 1]
                          ?.activeUrl
                      : item.scoreStyle[studyReportInfo.score_num > 0 ? 1 : 1]
                          ?.url
                  "
                  alt=""
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="error-message" v-show="isShowErrorMessage">
        {{ errorMessage }}
      </div>
      <!-- v-if="scoreParams.score === 1" -->
      <div class="feedback-content">
        <!-- 
           -->
        <el-input
          :disabled="studyReportInfo.score_num >= 2 || unlocking"
          v-model="scoreParams.advice"
          @change="handleTextChange"
          class="feedback-content-input"
          maxlength="100"
          show-word-limit
          type="textarea"
          placeholder="您的反馈会让我们做的更好"
        ></el-input>
      </div>
      <!--   -->
      <!--  && scoreParams.score === 1 -->
      <!--  -->
      <div
        v-if="studyReportInfo.score_num <= 1"
        class="submit-btn"
        @click="submitScore"
      >
        {{ unlocking ? "修改评价" : "提交" }}
      </div>
    </div>
    <el-dialog
      :visible.sync="secondaryConfirmation"
      width="291px"
      center
      lock-scroll
    >
      <div class="score-tips">仅可修改一次评价哦</div>
      <div slot="footer" class="confirm-buttons">
        <div class="confirm-button cancel-button" @click="handleCancel">
          取消
        </div>
        <div
          class="confirm-button confirm-button-primary"
          @click="handleConfirm"
        >
          修改
        </div>
      </div>
    </el-dialog>
    <share-drawer
      ref="shareDrawer"
      :shareDrawer="shareDrawerInfo"
      @close="handleShareDrawerClose"
      @shareWechat="handleShareWechat"
      @generateShareImage="handleGenerateShareImage"
    />
  </div>
</template>

<script>
import moment from "moment";
import shareDrawer from "@/wap/components/drawer";
import {
  studentFeedbackInfo,
  studentFeedbackScore,
  getStudentInfo
} from "@/api/preview";
import { markList, pageTheme, genders } from "@/wap/config/studyReport";
// import imageViewer from "./components/image-viewer.vue";
export default {
  components: { shareDrawer },
  data() {
    return {
      errorMessage: "请输入您的意见或建议",
      isShowErrorMessage: false,
      isShowEditTips: false,
      unlocking: false,
      secondaryConfirmation: false,
      moment,
      isShowImg: false,
      zIndex: 2024,
      imageIndex: 0,
      previewSrcList: [],
      editorIns: null,
      isRender: false,
      studyReportInfo: {},
      scoreParams: {
        advice: "",
        feedback_id: "",
        student_id: "",
        score: ""
      },
      markList,
      pageTheme,
      genders,
      shareDrawerInfo: {
        visible: false,
        teacher_name: "",
        feedback_id: "",
        student_id: "",
        pageTitle: "",
        guideBottom: 0,
        guideLeft: 0
      },
      studentInfo: {}
    };
  },
  computed: {
    query() {
      return this.$route.query;
    },
    format_content() {
      return this.formatContent(this.studyReportInfo.content);
    },
    isCourseSummary() {
      return this.pageTheme[this.query.pageTitle].class === "course-summary";
    },
    isClassNotice() {
      return this.pageTheme[this.query.pageTitle].class === "class-notice";
    },
    imageUrl() {
      return this.pageTheme[this.query.pageTitle].class === "course-summary"
        ? "https://tg-prod.oss-cn-beijing.aliyuncs.com/a5d15e4b-a89f-4310-8438-647899f6f1e3.webp"
        : this.pageTheme[this.query.pageTitle].class === "class-notice"
        ? "https://tg-prod.oss-cn-beijing.aliyuncs.com/78dec7e3-cbf5-4264-9ff5-a1f7a50bf1df.webp"
        : "https://tg-prod.oss-cn-beijing.aliyuncs.com/beb913cf-06e5-4226-8e5a-2530580278dd.webp";
    }
  },
  created() {
    this.getStudyReportInfo({ ...this.query, from: "mini" });
    this.getStudent();
  },
  methods: {
    async getStudent() {
      const { code, data } = await getStudentInfo({
        ...this.query,
        from: "mini"
      });
      if (code === 0) {
        this.studentInfo = data;
      }
    },
    async getStudyReportInfo(params) {
      const { code, data } = await studentFeedbackInfo(params);
      if (code === 0) {
        const {
          feedback_id,
          student_id,
          token,
          visitor,
          pageTitle,
          guideBottom,
          guideLeft
        } = this.query;
        this.isRender = true;
        this.studyReportInfo = data;
        document.getElementById("content-txt").innerHTML = this.format_content;
        this.scoreParams.feedback_id = feedback_id;
        this.scoreParams.student_id = student_id;
        this.scoreParams.token = token;
        this.scoreParams.visitor = visitor;
        this.scoreParams.from = "mini";
        this.scoreParams.score = data.score;
        this.scoreParams.advice = data.advice;
        if (data.score_num >= 1) {
          this.unlocking = true;
        }
        this.shareDrawerInfo = {
          visible: false,
          teacher_name: data.teacher_name,
          feedback_id,
          student_id,
          pageTitle,
          guideBottom,
          guideLeft
        };
        console.log(this.shareDrawerInfo, "this.shareDrawerInfo");
        this.$previewRefresh();
      }
    },
    handleClick({ target }) {
      if (target.tagName.toLowerCase() === "img") {
        const srcValue = target.src;
        this.isShowImg = true;
        this.previewSrcList = [srcValue];
      }
    },
    closeViewer() {
      this.isShowImg = false;
    },
    handleMark(item) {
      if (this.unlocking) {
        return;
      }
      this.isShowErrorMessage = false;
      if (this.studyReportInfo.score_num <= 1) {
        this.scoreParams.score = item.value;
        // if (item.value >= 2) {
        //   this.submitScore("call");
        // }
      }
    },
    // 格式化studyReportInfo.content中的img标签，在img标签上添加preview="preview" preview-text=""
    formatContent(content) {
      console.log("content :>> ", content);
      if (content) {
        return content.replace(/<img/g, '<img preview="0" preview-text=""');
      }
      return content;
    },
    handleCancel() {
      this.scoreParams.score = this.studyReportInfo.score;
      this.secondaryConfirmation = false;
    },
    handleConfirm() {
      this.isShowEditTips = true;
      this.unlocking = false;
      this.secondaryConfirmation = false;
      // this.submitScore("call");
    },
    handleTextChange() {
      this.isShowErrorMessage = !this.scoreParams.advice;
    },
    async submitScore(type) {
      console.log(this.scoreParams);
      if (!this.scoreParams.score) {
        this.errorMessage = "请选择评分";
        this.isShowErrorMessage = true;
        return false;
      }
      if (this.unlocking) {
        this.secondaryConfirmation = true;
      } else {
        if (this.scoreParams.advice === "" && this.scoreParams.score === 1) {
          this.isShowErrorMessage = true;
          this.errorMessage = "请输入您的意见或建议";
          return false;
        }
        this.isShowEditTips = false;
        const res = await studentFeedbackScore(this.scoreParams);
        if (res.code === 0) {
          this.getStudyReportInfo({ ...this.query, from: "mini" });
        } else {
          this.$message.error(res.message);
        }
        // if (type === "call" || this.studyReportInfo.score_num === 0) {
        //   const res = await studentFeedbackScore(this.scoreParams);
        //   if (res.code === 0) {
        //     this.getStudyReportInfo({ ...this.query, from: "mini" });
        //   } else {
        //     this.$message.error(res.message);
        //   }
        // }
      }
    },
    handleShareBtn() {
      this.shareDrawerInfo.visible = true;
      this.$refs.shareDrawer.isShowGuidanceImg = false;
    },
    handleShareDrawerClose() {
      this.shareDrawerInfo.visible = false;
    },
    // 分享到微信好友
    handleShareWechat() {
      this.shareDrawerInfo.visible = false;
      console.log("handleShareWechat");
    },
    // 生成分享图片
    handleGenerateShareImage() {
      this.shareDrawerInfo.visible = false;
      console.log("handleGenerateShareImage");
    }
  }
};
</script>

<style lang="scss">
.content-txt {
  img {
    max-width: 100%;
    border-radius: 12px;
  }
  video {
    max-width: 100%;
  }
}
body {
  overflow-y: auto;
}

.parent-class {
  background: url(https://tg-prod.oss-cn-beijing.aliyuncs.com/f9639fc0-d239-40e0-9bec-61474f4ca593.png)
    no-repeat;
  .content__wrap {
    box-shadow: 0px 4px 20px 0px rgba(211, 158, 0, 0.15);
  }
  .student-info {
    background: rgba(255, 236, 209, 0.8);
    box-shadow: 0px -2.16px 5.76px 0px #fbc17d inset;
  }
  .teacher-remark {
    background: rgba(255, 236, 209, 0.8);
    box-shadow: 0px -2.16px 5.76px 0px #fbc17d inset;
  }
  .title {
    // background: linear-gradient(127deg, #ffb32f 15.34%, #ff8411 86.73%);
    // box-shadow: 0px -0.864px 4.32px 0px #f90 inset,
    //   0px 1.728px 6.912px 0px rgba(229, 179, 0, 0.27);
    // &::after {
    //   border-color: transparent transparent #cf6500 transparent;
    // }
    // &::before {
    //   border-color: transparent transparent #cf6500 transparent;
    // }
  }
}
.class-notice {
  background: url(https://tg-prod.oss-cn-beijing.aliyuncs.com/410f5f7a-1f0b-4be1-bbff-a95f25decff4.png)
    no-repeat;
  .content__wrap {
    box-shadow: 0px 4px 20px 0px rgba(255, 213, 111, 0.32);
  }
  .student-info {
    background: rgba(255, 241, 205, 0.8);
    box-shadow: 0px -2.16px 5.76px 0px #fbd17d inset;
  }
  .teacher-remark {
    background: rgba(255, 241, 205, 0.8);
    box-shadow: 0px -2.16px 5.76px 0px #fbd17d inset;
  }
  .title {
    // background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
    // box-shadow: 0px -0.864px 4.32px 0px #f90 inset,
    //   0px 1.728px 6.912px 0px rgba(229, 179, 0, 0.27);
    // &::after {
    //   border-color: transparent transparent #d99f00 transparent;
    // }
    // &::before {
    //   border-color: transparent transparent #d99f00 transparent;
    // }
  }
}
.course-summary {
  background: url(https://tg-prod.oss-cn-beijing.aliyuncs.com/601e5b52-2363-47f8-bb92-4ba4c2ae1be7.png)
    no-repeat;
  .content__wrap {
    box-shadow: 0px 4px 20px 0px rgba(60, 0, 211, 0.07);
  }
  .student-info {
    background: rgba(234, 222, 255, 0.8);
    box-shadow: 0px -2.16px 5.76px 0px #a599ff inset;
  }
  .teacher-remark {
    background: rgba(234, 222, 255, 0.8);
    box-shadow: 0px -2.16px 5.76px 0px #a599ff inset;
  }
  .title {
    // background: linear-gradient(6deg, #c26ff2 30.97%, #d68fff 92.66%);
    // box-shadow: 0px -0.864px 4.32px 0px #b01dff inset,
    //   0px 1.728px 6.912px 0px rgba(153, 25, 237, 0.27);
    // &::after {
    //   border-color: transparent transparent #ad3cee transparent;
    // }
    // &::before {
    //   border-color: transparent transparent #ad3cee transparent;
    // }
  }
}
</style>
<style lang="scss" scoped>
.study-report-detail-page {
  width: 100vw;
  min-height: 100vh;
  height: auto;
  padding: 34px 19px 50px 19px;
  background-size: 100% 100%;
  ::v-deep .u-navbar {
    // .u-navbar--fixed {
    //   top: 60rpx;
    // }
    .u-navbar__content__title {
      text-align: center;
      font-size: 16px;
      color: #fff;
      font-weight: 600;
    }
  }
  ::v-deep .el-textarea__inner {
    border-radius: 7px;
    border: 1px solid #e9e9e9;
    background: #f5f6f7;
  }
  .content {
    width: 100%;
    .content__wrap {
      position: relative;
      width: 100%;
      // min-height: 80vh;
      padding: 18px 12px 18px 12px;
      border-radius: 15px;
      background: #fff;
      .content__wrap-teacher-auto {
        height: 100%;
        border-radius: 14px;
        display: flex;
        flex-direction: column;
        .student-info-wrap {
          position: relative;
          // height: 53px; // 72px - 19px
          margin-bottom: 20px;
          display: flex;
          // align-items: center;
          justify-content: space-between;
          .avatar {
            width: 72px;
            height: 72px;
            position: absolute;
            top: -35px;
            left: -4px;
            border-radius: 50%;
            // border: 1px solid #fff;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .student-name {
            color: #333;
            font-size: 15px;
            font-weight: 500;
            margin-left: 70px;
          }
          .share-btn {
            width: 68px;
            height: 32px;
            text-align: center;
            line-height: 32px;
            border-radius: 16px;
            font-size: 13px;
            font-weight: 600;
            color: #fff;
            background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
            box-shadow: 0px -4px 8px 0px #eaac00 inset;
          }
        }
        .student-info {
          padding: 11px 14px;
          border-radius: 14px;
          margin-bottom: 12px;
          position: relative;
          .fixed-img {
            position: absolute;
            bottom: -17px;
            width: 7px;
            height: 25px;
            z-index: 2;
            &.left {
              left: 14px;
            }
            &.right {
              right: 14px;
            }
          }
          .classroom-name {
            color: #333;
            font-size: 15px;
            font-weight: 500;
            margin-bottom: 5px;
          }
          .report-date {
            color: #666;
            font-size: 14px;
            font-weight: 400;
            .teacher-name {
              margin-right: 5px;
            }
          }
          .title {
            color: #fff;
            border-radius: 0px 0px 21.6px 21.6px;
            font-size: 16px;
            font-weight: 600;
            position: absolute;
            bottom: -34px;
            left: 50%;
            z-index: 2;
            transform: translateX(-50%);
            border-radius: 0px 0px 11px 11px;
            width: 108px;
            height: 26px;
            text-align: center;
            line-height: 26px;
            width: 102.824px;
            height: 25.84px;
            img {
              width: 100%;
              height: 100%;
            }
            // &::after {
            //   content: "";
            //   display: block;
            //   width: 0;
            //   height: 0;
            //   border-width: 4px;
            //   position: absolute;
            //   top: 1px;
            //   left: -4px;
            //   border-style: solid;
            //   transform: rotate(135deg);
            // }
            // &::before {
            //   content: "";
            //   display: block;
            //   width: 0;
            //   height: 0;
            //   border-width: 3px;
            //   position: absolute;
            //   top: 1px;
            //   right: -3px;
            //   border-style: solid;
            //   transform: rotate(-135deg);
            // }
          }
        }
        .teacher-remark {
          height: 100%;
          flex-grow: 1;
          border-radius: 12px;
          position: relative;
          padding: 30px 14px 16px 14px;
          overflow: auto;
          .page-corner {
            width: 35px;
            height: 33px;
            position: absolute;
            right: -2px;
            bottom: 10px;
          }
          .content-txt-wrap {
            height: 100%;
            overflow: auto;
            .content-txt {
              width: 100%;
              height: 100%;
              overflow: auto;
            }
          }
        }
      }
    }
  }

  .mark-block {
    margin-top: 15px;
    padding: 16px 18px;
    border-radius: 15px;
    background: #fff;
    box-shadow: 0px 4px 20px 0px rgba(60, 0, 211, 0.07);
    .mark-title {
      text-align: center;
      margin-bottom: 15px;
      .common-mark-tilte {
        font-weight: 500;
        color: #333;
        font-size: 15px;
      }
      .second-mark-tilte {
        .mark-status {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 15px;
          font-weight: 500;
          color: #333;
          img {
            margin-right: 5px;
            width: 15px;
            height: 15px;
          }
        }
        .mark-tips {
          margin-top: 4px;
          font-size: 12px;
          color: #666;
          font-weight: 400;
        }
      }
    }
    .mark-item-wrap {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      .mark-item {
        width: calc(20% - 19px);
        margin-right: 19px;
        text-align: center;
        white-space: nowrap;
        &:last-child {
          margin-right: 0;
        }
        .mark-item-name {
          color: #999;
          font-weight: 400;
        }
        .active {
          color: #fb0;
        }
        img {
          width: 30px;
          height: 30px;
        }
      }
    }
    .second-mark-wrap {
      display: flex;
      align-items: center;
      .label {
        font-weight: 500;
        font-size: 14px;
        margin-right: 10px;
      }
      .second-mark-item-wrap {
        display: flex;
        align-items: center;
        .second-mark-item {
          .second-mark-item-url {
            img {
              width: 20px;
              height: 20px;
              margin-right: 8px;
            }
          }
        }
      }
    }
    .error-message {
      font-size: 12px;
      font-weight: 400;
      color: #fe4f37;
      margin-top: 6px;
    }
    .feedback-content {
      margin-top: 6px;
      .feedback-content-input {
        width: 100%;
        ::v-deep .el-textarea__inner {
          height: 80px;
        }
      }
    }
    .submit-btn {
      width: 80px;
      height: 32px;
      margin: 0 auto;
      margin-top: 15px;
      font-size: 12px;
      color: #fff;
      border-radius: 16px;
      background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
      box-shadow: 0px -4px 8px 0px #eaac00 inset;
      border-radius: 25px;
      text-align: center;
      line-height: 32px;
      font-weight: 500;
      flex-shrink: 0;
    }
  }
}
::v-deep .el-dialog {
  border-radius: 18px;
}
::v-deep .el-dialog__header {
  display: none;
}
::v-deep .el-dialog__body {
  padding: 30px 0px 35px 0 !important;
}
::v-deep .el-dialog__footer {
  padding: 0px 17px 30px 17px;
  padding-top: 0;
}
.score-tips {
  font-size: 18px;
  font-weight: 500;
  text-align: center;
  color: #333;
}
.confirm-buttons {
  display: flex;
  width: 100%;
  justify-content: space-between;
}
.confirm-button {
  width: 119px;
  height: 44px;
  border-radius: 22px;
  font-size: 17px;
  font-weight: bold;
  text-align: center;
  line-height: 44px;

  &:active {
    opacity: 0.8;
  }
}

.cancel-button {
  border: 1px solid #ffc525;
  color: #ffc525;
}

.confirm-button-primary {
  color: #fff;
  background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
  box-shadow: 0px -5px 12px 0px #fc0 inset, 0px 9px 20px 0px #fff7e1 inset;
  filter: drop-shadow(0px 4px 4px rgba(255, 192, 18, 0.11));
}
::v-deep .el-input__count {
  background-color: transparent;
}
::v-deep .is-disabled {
  .el-textarea__inner {
    color: #999;
  }
}
</style>
