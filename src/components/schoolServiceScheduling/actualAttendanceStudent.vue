<!--点名上课-->
<template>
  <div>
    <el-dialog
      class="actual-attendance-student"
      custom-class="actual-attendance-student"
      :visible="true"
      title="实到人数"
      width="80%"
      :before-close="back"
    >
      <div class="content tg-box--margin">
        <div class="tg-table__box">
          <div class="tg-box--border"></div>
          <el-table
            ref="list_table"
            :data="list"
            tooltip-effect="dark"
            class="student-table tg-table top-table"
            :summary-method="getSummaries"
            show-summary
            :sum-text="''"
            v-loading="loading"
            max-height="610px"
            @selection-change="handleSelectionChange"
          >
            <el-table-column
              type="selection"
              width="50"
              :reserve-selection="true"
            >
            </el-table-column>
            <el-table-column label="学员姓名" width="130" prop="student_name">
              <template slot-scope="scope">
                <span class="nameBox">
                  {{ scope.row.student_name }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="ylb_status" label="元萝卜用户" width="120">
              <template slot-scope="scope">
                <ylb-tag :ylb_status="scope.row.ylb_status"></ylb-tag>
              </template>
            </el-table-column>
            <el-table-column label="状态" width="80" prop="student_type_chn">
            </el-table-column>
            <el-table-column
              label="上课来源"
              width="80"
              prop="handler_type_chn"
            >
            </el-table-column>
            <el-table-column label="联系电话" width="120" prop="student_mobile">
            </el-table-column>
            <el-table-column
              label="出勤"
              width="100"
              prop="is_attendance"
              sortable
            >
              <template slot-scope="scope">
                <el-checkbox
                  disabled
                  v-model="scope.row.is_attendance"
                  :true-label="'YES'"
                  :false-label="'NO'"
                  >{{ scope.column.label }}</el-checkbox
                >
              </template>
            </el-table-column>
            <el-table-column
              label="计费"
              :width="info.dynamic_lesson ? 280 : 150"
              prop="is_billable"
            >
              <template slot-scope="scope">
                <el-checkbox
                  disabled
                  v-model="scope.row.is_billable"
                  :true-label="'YES'"
                  :false-label="'NO'"
                  >计费
                </el-checkbox>
                <el-input-number
                  v-model="scope.row.number_deduct"
                  v-if="info.dynamic_lesson"
                  size="small"
                  :min="0"
                  :precision="2"
                  class="sortIpt"
                ></el-input-number>
                <span v-if="scope.row.is_in_arrears" class="arrears"
                  >(剩余课时不足)</span
                >
              </template>
            </el-table-column>
            <el-table-column label="课时" width="160" prop="classHour">
              <template slot-scope="scope">
                <span
                  v-for="(item, index) in scope.row.lesson_list"
                  :key="index"
                  class="classVessel"
                >
                  <span
                    :style="{ color: index > 0 ? '#FF0317' : '#475669' }"
                    class="textOverflow"
                  >
                    {{ item.course_name }}
                  </span>
                  剩{{ item.total_lesson }}课时
                </span>
              </template>
            </el-table-column>
            <el-table-column label="试听" width="80" prop="is_try">
              <template slot-scope="scope">
                <el-checkbox
                  disabled
                  v-model="scope.row.is_try"
                  :true-label="'YES'"
                  :false-label="'NO'"
                  >{{ scope.column.label }}</el-checkbox
                >
              </template>
            </el-table-column>
            <el-table-column label="缺勤原因" width="100" prop="">
              <template slot-scope="scope">
                <span class="el-dropdown-link">
                  {{ scope.row.leave_reason | getReasonId }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="缺勤统计" width="80" prop="number_present">
              <template slot-scope="scope">
                {{ scope.row.number_present }}次
              </template>
            </el-table-column>
            <el-table-column label="备注" prop="memo">
              <template slot-scope="scope">
                <div class="table_edit">
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="scope.row[scope.column.property]"
                    placement="top-end"
                  >
                    <span class="tips">{{
                      scope.row[scope.column.property]
                    }}</span>
                  </el-tooltip>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <!-- <div class="modal" v-if="!can_opearte_student"></div> -->
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="tg-button--plain" type="plain" @click="back"
          >关闭</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import schoolServiceSchedulingApi from "@/api/schoolServiceScheduling";
// import specifyByNameApi from "@/api/specifyByName.js";
import { leave_reason } from "@/public/dict";
export default {
  props: {
    id: {
      type: String
    }
  },
  data() {
    return {
      list: [],
      loading: false,
      reason_list: leave_reason,
      info: {
        dynamic_lesson: ""
      },
      checked_student: [],
      send_type: "",
      send_types: {
        course_summary: "课程总结",
        class_notice: "班级通知",
        parent_class: "家长课堂"
      },
      isBatchSend: false,
      editorDialogVisible: false,
      editorDialogDepartmentId: "",
      rowData: {}
    };
  },
  watch: {},
  mounted() {
    this.getStudents({ scheduling_id: this.id });
  },

  filters: {
    getReasonId(val) {
      const index = leave_reason.findIndex((item) => item.id === val);
      if (index === -1) return val;
      return leave_reason[index].name;
    }
  },
  methods: {
    handleSelectionChange(row) {
      this.checked_student = row;
    },
    refresh() {
      this.loading = false;
      this.getStudents({ scheduling_id: this.id });
    },

    back() {
      this.$emit("close");
    },
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = `共：${this.list.length}人`;
          return;
        }
        const values = data.map((item) => item[column.property]);
        const arr = ["is_attendance", "is_billable", "is_try"];
        if (arr.indexOf(column.property) > -1) {
          let sum = 0;
          values.forEach((item) => {
            if (item === "YES") {
              sum += 1;
            }
          });
          sums[index] = sum;
          sums[index] += " 人";
        } else {
          sums[index] = "";
        }
      });

      return sums;
    },

    closeDialog() {
      this.$emit("close");
    },

    // 获取课程信息
    getStudents(data) {
      this.loading = true;
      schoolServiceSchedulingApi.getClassStudentList(data).then((res) => {
        this.loading = false;
        this.info = res.data;
        const format_class_time = `${this.moment(this.info.startTime).format(
          "YYYY-MM-DD HH:mm"
        )}-${this.moment(this.info.endTime).format("HH:mm")} (${
          this.info.week_day_chn
        })`;
        this.$set(this.info, "format_class_time", format_class_time);
        this.delete_list = res.data.trash == null ? [] : res.data.trash;
        this.list = res.data.available == null ? [] : res.data.available;
        const checked_attendance = [];
        const checked_try = [];
        const checked_billable = [];
        this.list.forEach((item) => {
          // 判断是否计费 如果是动态课消
          if (!item.on_billing) {
            this.$set(item, "number_deduct", res.data.dynamic_discount);
          }
          // 模拟一个是否位动态课消&&课消默认值
          // this.$set(item, "dynamic_discount", res.data.dynamic_discount);
          item.is_try = item.is_try === "" ? "NO" : item.is_try;
          if (res.data.mandatory_lesson) {
            if (item.is_in_arrears) {
              item.is_billable = "NO";
            } else {
              item.is_billable = "YES";
            }
          } else {
            item.is_billable =
              item.is_billable === "" ? "NO" : item.is_billable;
          }
          item.is_attendance =
            item.is_attendance === "" ? "NO" : item.is_attendance;
          if (item.is_attendance === "YES") {
            checked_attendance.push(item.id);
          }
          if (item.is_try === "YES") {
            checked_try.push(item.id);
          }
          if (item.is_billable === "YES") {
            checked_billable.push(item.id);
          }
        });
        this.stop_list = res.data.stop == null ? [] : res.data.stop;
        // 是否强制课消
        if (res.data.mandatory_lesson) {
          this.head_billable_check = true;
        } else {
          this.head_billable_check =
            checked_billable.length === this.list.length;
        }
        this.billable_indeterminate =
          checked_billable.length > 0 &&
          checked_billable.length < this.list.length;
        this.head_try_check = checked_try.length === this.list.length;
        this.try_indeterminate =
          checked_try.length > 0 && checked_try.length < this.list.length;
        this.head_attendance_check =
          checked_attendance.length === this.list.length;
        this.attendance_indeterminate =
          checked_attendance.length > 0 &&
          checked_attendance.length < this.list.length;

        // if (res.data.scheduling_status === "is_started") {
        //   this.can_opearte_student = true;
        // }
      });
    }
  }
};
</script>
<style lang="less" scoped>
/deep/ button.disabled {
  background-color: #ccc;
  border: 1px solid #ccc;
  color: #fff;
}
.actual-attendance-student {
  .arrears {
    color: red;
    font-size: 13px;
    margin-left: 5px;
  }
  /deep/ .el-table__footer-wrapper td {
    border-bottom: none;
  }
  ::v-deep .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
    background-color: #2d80ed;
    border-color: #2d80ed;
  }
  ::v-deep
    .el-checkbox__input.is-disabled.is-checked
    .el-checkbox__inner::after {
    border-color: #fff;
  }
  input:disabled {
    background-color: initial;
  }
  ::v-deep .student-table {
    &:before,
    &:after {
      background: #f5f8fc;
      content: "";
      height: 48px;
      width: 16px;
      position: absolute;
      left: 0;
      top: 0;
    }
    &:before {
      right: 0;
      bottom: unset;
      left: unset;
    }
    th.el-table-column--selection .cell,
    td.el-table-column--selection .cell {
      padding: 0 8px;
    }
    th {
      background: #f5f8fc;
      .el-checkbox__label {
        font-weight: bold;
      }
      .el-checkbox__inner {
        background-color: #f5f8fc;
      }
      .el-checkbox__input.is-indeterminate .el-checkbox__inner,
      .el-checkbox__input.is-checked .el-checkbox__inner {
        background-color: @base-color;
      }
    }
    .el-table__body tr.current-row > td {
      background-color: #ebf4ff;
    }
    .el-checkbox__label {
      font-size: @text-size_small;
      font-family: @text-famliy_medium;
      color: @text-color_second;
    }
    .classVessel {
      display: inline-flex;
      align-items: center;
      .textOverflow {
        display: inline-block;
        max-width: 85px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
  }
  ::v-deep .el-table__footer td {
    border-bottom: none;
  }
  ::v-deep .top-table {
    .cell {
      padding: 0;
    }
    em {
      font-style: normal;
    }
    .is_zero {
      color: #ff0317;
    }
    span.el-dropdown-link {
      font-size: @text-size_small;
    }
  }
  .tg-text--blue {
    font-weight: normal;
  }
  ::v-deep .el-table__header {
    // width: auto !important;
  }
  ::v-deep & > .el-dialog__body {
    padding: 0 16px;
    height: 649px;
    overflow-y: scroll;
  }

  ::v-deep .el-table__footer {
    .el-table__cell {
      background: transparent;
    }
  }

  .tg-table__box {
    margin: 16px 0 0 0;
  }
  .no-shadow {
    box-shadow: none;
  }
  .tips {
    width: 90px;
    display: inline-block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .nameBox {
    display: flex;
    align-items: center;
  }
  .remove {
    width: 16px;
    margin-left: 4px;
    cursor: pointer;
  }
  .del {
    width: 14px;
    margin-left: 4px;
    cursor: pointer;
  }

  .edit-msg {
    ::v-deep & > .el-dialog__body {
      padding: 0 16px;
      height: 388px;
    }
    ::v-deep .el-textarea__inner {
      height: 280px;
    }
  }
  .modal {
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.7);
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1000;
    border-radius: 4px;
  }
  ::v-deep .tg-table {
    border-radius: 4px;
  }
  .sortIpt {
    margin-left: 6px;
    ::v-deep .el-input {
      width: 130px;
    }
  }
}
</style>
