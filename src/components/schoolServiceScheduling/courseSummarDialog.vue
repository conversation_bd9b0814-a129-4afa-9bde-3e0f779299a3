<template>
  <el-dialog
    class="actual-attendance-student"
    custom-class="actual-attendance-student"
    :visible="true"
    :title="`${send_types[type]}发送状态`"
    width="80%"
    :modal-append-to-body="false"
    :before-close="back"
  >
    <div class="content tg-box--margin">
      <div class="search_right_box d-if tg-box--margin tg-box--width">
        <template v-if="type === 'parent_class'">
          <el-button
            v-has="{ m: 'class', o: 'parent_class' }"
            type="plain"
            class="tg-button--plain"
            :disabled="checked_student.length > 0 ? false : true"
            :class="{ disabled: checked_student.length > 0 ? false : true }"
            @click="batchSendContent('parent_class')"
            >批量发送家长课堂</el-button
          >
        </template>
        <template v-if="type === 'class_notice'">
          <el-button
            type="plain"
            v-has="{ m: 'class', o: 'class_notice' }"
            :disabled="checked_student.length > 0 ? false : true"
            :class="{ disabled: checked_student.length > 0 ? false : true }"
            class="tg-button--plain"
            @click="batchSendContent('class_notice')"
            >批量发送班级通知</el-button
          >
        </template>
        <template v-if="type === 'course_summary'">
          <el-button
            v-has="{ m: 'class', o: 'course_summary' }"
            type="plain"
            :disabled="checked_student.length > 0 ? false : true"
            :class="{ disabled: checked_student.length > 0 ? false : true }"
            class="tg-button--plain"
            @click="batchSendContent('course_summary')"
            >批量发送课程总结</el-button
          >
        </template>
        <el-button
          type="plain"
          :disabled="checked_student.length > 0 ? false : true"
          :class="{ disabled: checked_student.length > 0 ? false : true }"
          class="tg-button--plain"
          @click="batchRevoke()"
          >批量撤销</el-button
        >
      </div>
      <div class="tg-table__box">
        <div class="tg-box--border"></div>
        <el-table
          ref="list_table"
          :data="list"
          tooltip-effect="dark"
          class="student-table tg-table top-table"
          :row-key="getRowKeys"
          v-loading="loading"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            type="selection"
            width="50"
            :reserve-selection="true"
          >
          </el-table-column>

          <template v-for="(item, index) in table_title">
            <el-table-column
              v-if="item.isShow"
              :key="index"
              :prop="item.props"
              :label="item.label"
              show-overflow-tooltip
              :min-width="item.width"
              :sortable="item.sort"
            >
              <template slot-scope="scope">
                <span v-if="item.type === 'date'">
                  {{ scope.row[scope.column.property] | getTime }}
                </span>
                <span v-else-if="item.props === 'read_status'">
                  <el-tag v-if="scope.row.read_status === 1" type="success"
                    >已读</el-tag
                  >
                  <el-tag v-if="scope.row.read_status === 2" type="info"
                    >未读</el-tag
                  >
                </span>
                <span v-else-if="item.props === 'is_billable'">
                  <el-tag v-if="scope.row.is_billable === 'YES'" type="success"
                    >是</el-tag
                  >
                  <el-tag v-if="scope.row.is_billable === 'NO'" type="info"
                    >否</el-tag
                  >
                </span>
                <span v-else-if="item.props === 'is_attendance'">
                  <el-tag
                    v-if="scope.row.is_attendance === 'YES'"
                    type="success"
                    >是</el-tag
                  >
                  <el-tag v-if="scope.row.is_attendance === 'NO'" type="info"
                    >否</el-tag
                  >
                </span>
                <span v-else-if="item.props === 'is_cancel'">
                  <el-tag v-if="scope.row.is_cancel === 1" type="success"
                    >已撤销</el-tag
                  >
                  <el-tag v-if="scope.row.is_cancel === 2" type="info"
                    >未撤销</el-tag
                  >
                </span>
                <div class="el-rate" v-else-if="item.type === 'rate'">
                  <span class="el-rate__item">
                    <i
                      class="el-rate__icon"
                      :class="
                        Number(scope.row[scope.column.property]) >= 1
                          ? 'el-icon-star-on'
                          : 'el-icon-star-off'
                      "
                    ></i>
                  </span>
                  <span class="el-rate__item">
                    <i
                      class="el-rate__icon"
                      :class="
                        Number(scope.row[scope.column.property]) >= 2
                          ? 'el-icon-star-on'
                          : 'el-icon-star-off'
                      "
                    ></i>
                  </span>
                  <span class="el-rate__item">
                    <i
                      class="el-rate__icon"
                      :class="
                        Number(scope.row[scope.column.property]) >= 3
                          ? 'el-icon-star-on'
                          : 'el-icon-star-off'
                      "
                    ></i>
                  </span>
                  <span class="el-rate__item">
                    <i
                      class="el-rate__icon"
                      :class="
                        Number(scope.row[scope.column.property]) >= 4
                          ? 'el-icon-star-on'
                          : 'el-icon-star-off'
                      "
                    ></i>
                  </span>
                  <span class="el-rate__item">
                    <i
                      class="el-rate__icon"
                      :class="
                        Number(scope.row[scope.column.property]) >= 5
                          ? 'el-icon-star-on'
                          : 'el-icon-star-off'
                      "
                    ></i>
                  </span>
                </div>
                <span v-else>{{ scope.row[scope.column.property] }}</span>
              </template>
            </el-table-column>
          </template>
          <el-table-column label="操作" width="150" fixed="right">
            <template slot-scope="scope">
              <el-button
                v-if="
                  $_has({ m: 'traineeReport', o: 'info' }) &&
                  scope.row.feedback_id
                "
                @click="toDetail(scope.row)"
                type="text"
                class="tg-text--blue tg-span__divide-line"
                >查看</el-button
              >
              <el-button
                v-if="
                  $_has({ m: 'traineeReport', o: 'revoke' }) &&
                  scope.row.is_cancel === 2
                "
                type="text"
                @click="revoke(scope.row)"
                class="tg-text--blue tg-span__divide-line"
                >撤销</el-button
              >
              <el-button
                v-if="scope.row.is_cancel === 1"
                type="text"
                @click="edit(scope.row)"
                class="tg-text--blue tg-span__divide-line"
                >修改</el-button
              >
            </template>
          </el-table-column>
          <template slot="empty">
            <div style="margin-top: 4%">
              <div style="width: 100%; min-height: 300px" v-if="loading">
                <loading></loading>
              </div>
              <div class="empty-container" v-else>暂无数据～</div>
            </div>
          </template>
        </el-table>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button class="tg-button--plain" type="plain" @click="back"
        >关闭</el-button
      >
    </span>
    <editorDialog
      v-if="editorDialogVisible"
      :department_id="editorDialogDepartmentId"
      :editContent="editContent"
      @close="closeEditorDialog"
      @confirm="editorDialogConfirm"
    ></editorDialog>
  </el-dialog>
</template>

<script>
import { getFeedbackDetail } from "@/api/preview";
import traineeReportApi from "@/api/traineeReport.js";
import schoolServiceSchedulingExcelApi from "@/api/schoolServiceScheduling";
import loading from "@/views/loading";
import editorDialog from "@/views/classManagement/editorDialog.vue";
import appletResource from "@/api/appletResource"; // 资料库
export default {
  components: { loading, editorDialog },
  props: {
    id: {
      type: String,
      default: ""
    },
    rowData: {
      type: Object,
      default: () => {}
    },
    type: {
      type: String,
      default: "parent_class" // parent_class class_notice course_summary
    }
  },
  data() {
    return {
      checked_student: [],
      isBatchSend: false,
      editorDialogVisible: false,
      editorDialogDepartmentId: "",
      editContent: "",
      editorDialogClassroomId: "",
      loading: false,
      list: [],
      table_title: [
        {
          isShow: true,
          props: "student_number",
          label: "学号",
          width: 100
        },
        {
          isShow: true,
          props: "student_name",
          label: "学员姓名",
          width: 100
        },
        {
          isShow: true,
          props: "student_type",
          label: "状态",
          width: 100
        },
        {
          isShow: true,
          props: "handler_type_ch",
          label: "上课来源",
          width: 100
        },
        {
          isShow: true,
          props: "student_mobile",
          label: "联系电话",
          width: 100
        },
        {
          isShow: true,
          props: "is_attendance",
          label: "出勤",
          width: 100
        },
        {
          isShow: true,
          props: "is_billable",
          label: "计费",
          width: 100
        },
        {
          isShow: true,
          props: "teacher_name",
          label: "发送人",
          width: 100
        },
        {
          isShow: true,
          props: "send_time",
          label: "发送时间",
          width: 100,
          type: "date"
        },
        {
          isShow: true,
          props: "read_status",
          label: "是否已读",
          width: 100
        },
        {
          isShow: true,
          props: "is_cancel",
          label: "消息状态",
          width: 100
        },
        {
          isShow: this.type === "course_summary",
          props: "score",
          type: "rate",
          label: "评分",
          width: 130
        },
        {
          isShow: this.type === "course_summary",
          props: "advice",
          label: "意见或建议",
          width: 130
        },
        {
          isShow: true,
          props: "cancel_date",
          label: "撤销时间",
          width: 100
        },
        {
          isShow: true,
          props: "revocation_name",
          label: "撤销人",
          width: 100
        }
      ],
      send_type: "",
      send_types: {
        course_summary: "课程总结",
        class_notice: "班级通知",
        parent_class: "家长课堂"
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    back() {
      this.$emit("close");
    },
    getRowKeys(row) {
      // this.$refs.formInline.clearSelection();完成后需要手动清空
      return row.student_id;
    },
    async getList() {
      const res = await schoolServiceSchedulingExcelApi.schedulingStudents({
        feedback_type: this.type,
        scheduling_id: this.id
      });
      const { code, data, message } = res.data;
      if (code === 0) {
        this.list = data;
      } else {
        this.$message.error(message);
      }
    },
    selectable(row) {
      if (row.is_cancel === 2) {
        return false;
      } else {
        return (
          (this.type === "course_summary" && row.is_attendance === "YES") ||
          this.type === "parent_class"
        );
      }
    },
    batchRevoke() {
      console.log(this.checked_student);

      if (this.checked_student.some((item) => !item.teacher_name)) {
        this.$message.warning("您勾选的学员含有未发送的学员，无法撤销！");
        return;
      }
      if (this.checked_student.some((item) => item.is_cancel === 1)) {
        this.$message.warning("您勾选的学员含有已撤销的学员，无法撤销！");
        return;
      }
      // 撤销前弹出确认询问
      this.$confirm("确认批量撤销学员报告？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(async () => {
        const res = await traineeReportApi.revoke({
          is_cancel: 1,
          student_feedback_id: this.checked_student.map(
            (item) => item.student_feedback_id
          ),
          feedback_id: undefined
        });
        const { code, message } = res.data;
        if (code === 0) {
          this.$message.success("撤销成功!");
          this.getList();
          this.$refs.list_table.clearSelection();
        } else {
          this.$message.error(message);
        }
      });
    },
    batchSendContent(type) {
      if (this.checked_student.length === 0) {
        this.$message.warning("请选择学员！");
        return;
      }
      if (
        this.checked_student.some(
          (item) => item.teacher_name && item.is_cancel !== 1
        )
      ) {
        this.$message.warning("您勾选含有已发送的学员，无法发送！");
        return;
      }
      if (this.type === "course_summary") {
        if (this.checked_student.some((item) => item.is_attendance !== "YES")) {
          this.$message.warning("您勾选的学员含有未出勤的学员，无法发送！");
          return;
        }
      }
      console.log(this.checked_student);
      this.isBatchSend = true;
      this.send_type = type;
      const oneStu = this.checked_student[0];
      this.editorDialogDepartmentId = oneStu.department_id;
      this.editorDialogVisible = true;
    },
    sendContent(row, type) {
      if (row.student_numb <= 0) {
        this.$message.warning("班级学员数量为0，无法发送！");
        return;
      }
      console.log(row);
      this.send_type = type;
      this.editorDialogDepartmentId = row.department_id;
      this.editorDialogClassroomId = row.id;
      this.rowData = row;
      this.editorDialogVisible = true;
    },
    closeEditorDialog() {
      this.editorDialogVisible = false;
      this.editContent = "";
      this.$refs.list_table.clearSelection();
    },
    // 发送学员报告
    editorDialogConfirm(content) {
      console.log("content :>> ", content);
      const { send_type, send_types, id } = this;
      console.log(send_type, send_types, "send_type");
      // 询问框提示
      this.$confirm(`确定发送${send_types[send_type]}吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        const data = {
          classroom_id: "",
          classroom_name: "",
          student_id: "",
          student_gender: "",
          student_name: ""
        };
        let students = [];
        // 批量发送
        students = this.checked_student.map((item) => {
          return {
            student_gender: item.student_gender,
            student_id: item.student_id,
            student_name: item.student_name
          };
        });
        data.classroom_id = this.checked_student[0].classroom_id;
        data.classroom_name = this.checked_student[0].classroom_name;
        const { classroom_id, classroom_name } = this.rowData;
        console.log(this.rowData);
        const params = {
          classroom: {
            classroom_id,
            classroom_name
          },
          scheduling_id: id,
          content,
          type: send_type,
          students
        };
        console.log(params, "params");
        appletResource
          .feedbackSend(params)
          .then((res) => {
            if (res.data.code === 0) {
              this.$message.success("发送成功！");
              this.getList();
              this.closeEditorDialog();
              this.$refs.list_table.clearSelection();
            } else {
              this.$message.error(res.data.message);
            }
          })
          .catch((err) => {
            console.error(err);
            this.$message.error("发送失败！");
          });
      });
    },
    handleSelectionChange(val) {
      this.checked_student = val;
    },
    toDetail(row) {
      // this.back();
      const { feedback_id, student_id } = row;
      // 路由到学员报告详情
      this.$router.push({
        name: "traineeReportManagementDetail",
        query: {
          feedback_id,
          student_id
        }
      });
    },
    // 撤销
    revoke(row) {
      // 撤销前弹出确认询问
      this.$confirm("确认撤销该条学员报告？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(async () => {
        const res = await traineeReportApi.revoke({
          is_cancel: 1,
          student_feedback_id: [row.student_feedback_id],
          feedback_id: undefined
        });
        const { code, message } = res.data;
        if (code === 0) {
          this.$message.success("撤销成功!");
          this.getList();
          this.$refs.list_table.clearSelection();
        } else {
          this.$message.error(message);
        }
      });
    },
    edit(row) {
      getFeedbackDetail({
        feedback_id: row.feedback_id,
        scheduling_id: row.scheduling_id,
        student_id: row.student_id
      }).then((res) => {
        const { code, data, message } = res;
        console.log(this.type);
        if (code === 0) {
          this.editorDialogVisible = true;
          this.editContent = data.content;
          this.checked_student = [data];
          this.send_type = this.type;
          this.editorDialogDepartmentId = data.department_id;
          this.editorDialogClassroomId = data.classroom_id;
          this.rowData = data;
        } else {
          this.$message.error(message);
        }
      });
    }
  }
};
</script>

<style></style>
