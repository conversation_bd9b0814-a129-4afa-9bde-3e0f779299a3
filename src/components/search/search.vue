<!--搜索-->
<template>
  <div
    class="tg-search"
    @keyup.enter="search()"
    :class="[
      searchTitle.length <= showNum
        ? 'tg-search--normal'
        : showNum >= 4
        ? 'tg-expand'
        : !isSpeard
        ? 'tg-search--large'
        : 'tg-expand',
      notUnfold ? 'p-top-16' : ''
    ]"
  >
    <el-form
      ref="form"
      :model="form"
      :inline="inline ? true : false"
      @submit.native.prevent
    >
      <div
        v-for="(item, index) in searchTitle"
        :key="index"
        class="tg-search__box"
        :class="notUnfold ? 'm-bottom-16' : ''"
      >
        <el-form-item :label="item.label" v-if="item.show">
          <el-input
            v-model.trim="form[item.props]"
            :maxlength="item.maxlength || 200"
            :placeholder="
              item.placeholder ? item.placeholder : `请输入${item.label}`
            "
            :style="item.style"
            :type="item.label === '沟通次数' ? 'number' : 'text'"
            v-if="item.type === 'input'"
          >
            <template slot="append" v-if="item.append">{{
              item.append
            }}</template>
          </el-input>
          <el-input
            v-model.trim="form[item.props[0]]"
            :maxlength="item.maxlength || 200"
            :placeholder="
              item.placeholder ? item.placeholder : `请输入${item.label}`
            "
            :type="
              item.label === '电子钱包余额' ||
              item.label === '余额' ||
              item.label === '课程剩余课时'
                ? 'number'
                : 'text'
            "
            class="input-with-select"
            v-if="item.type === 'input-with-select'"
          >
            <el-select
              slot="prepend"
              v-model="form[item.props[1]]"
              placeholder="请选择"
            >
              <el-option
                :label="item1.name"
                :value="item1.id"
                v-for="(item1, index1) in item.selectOptions"
                :key="index1"
              ></el-option>
            </el-select>
          </el-input>
          <div
            class="input-with-select input-number-with-select"
            v-else-if="item.type === 'input-number-with-select'"
          >
            <el-select v-model="form[item.props[1]]" placeholder="请选择">
              <el-option
                :label="item1.name"
                :value="item1.id"
                v-for="(item1, index1) in item.selectOptions"
                :key="index1"
              ></el-option>
            </el-select>
            <el-input-number
              v-model.trim="form[item.props[0]]"
              :min="item.min || 0"
              :max="item.max || 20000"
              :placeholder="
                item.placeholder ? item.placeholder : `请输入${item.label}`
              "
              :controls="false"
              :precision="item.precision"
            >
            </el-input-number>
          </div>
          <el-select
            v-model="form[item.props]"
            :placeholder="
              item.placeholder ? item.placeholder : `请选择${item.label}`
            "
            :filterable="item.filterable"
            v-else-if="item.type === 'select'"
            class="tg-select"
            :popper-append-to-body="false"
            @change="item?.selectChange && item?.selectChange(item.props)"
          >
            <el-option
              :label="item1.name"
              :value="item1.id"
              v-for="(item1, index1) in item.selectOptions"
              :key="index1"
            ></el-option>
          </el-select>
          <el-select
            v-model="form[item.props]"
            filterable
            :placeholder="
              item.placeholder ? item.placeholder : `请选择${item.label}`
            "
            v-else-if="item.type === 'select_filterable'"
            :loading="item.loading"
            remote
            :remote-method="item.remoteMethod"
            class="tg-select"
            :popper-append-to-body="false"
            clearable
            @change="
              item?.selectChange &&
                item?.selectChange(form[item.props], item.props)
            "
          >
            <el-option
              :label="item1.name"
              :value="item1.id"
              v-for="(item1, index1) in item.selectOptions"
              :key="index1"
            ></el-option>
          </el-select>
          <el-select
            v-model="form[item.props]"
            :placeholder="
              item.placeholder ? item.placeholder : `请选择${item.label}`
            "
            v-else-if="item.type === 'cascader_select'"
            class="tg-select"
            :popper-append-to-body="false"
            @change="cascaderSelect"
          >
            <el-option
              :label="item1.name"
              :value="item1.id"
              v-for="(item1, index1) in item.selectOptions"
              :key="index1"
            ></el-option>
          </el-select>
          <course-staff
            :check_id.sync="form[item.props]"
            :check_name.sync="form[`${item.props}_name`]"
            v-else-if="item.type === 'course_staff'"
            :is_leave="item.is_leave"
            :has_modal="
              typeof item.has_modal == 'undefined' ? true : item.has_modal
            "
            :staff_placeholder="
              item.placeholder ? item.placeholder : `请选择${item.label}`
            "
          ></course-staff>
          <week-time-frame
            :check_week.sync="form[item.assignProps]"
            :check_time.sync="form[item.props]"
            v-else-if="item.type === 'week_time_frame'"
            :placeholder="
              item.placeholder ? item.placeholder : `请选择${item.label}`
            "
            :has_modal="
              typeof item.has_modal == 'undefined' ? true : item.has_modal
            "
          ></week-time-frame>
          <market-staff
            :check_ids.sync="form[item.props]"
            :check_names.sync="form[`${item.props}_name`]"
            :is_leave="item.is_leave"
            v-else-if="item.type === 'mark_staff'"
            :has_modal="
              typeof item.has_modal == 'undefined' ? true : item.has_modal
            "
            :staff_placeholder="
              item.placeholder ? item.placeholder : `请选择${item.label}`
            "
          ></market-staff>
          <jobsSelect
            :check_id.sync="form[item.props]"
            v-else-if="item.type === 'jobs'"
          ></jobsSelect>
          <div v-else-if="item.type === 'checkboxGroup'" class="checkbox-div">
            <el-checkbox-group v-model="form[item.props]">
              <el-checkbox
                v-for="(item, index) in item.option"
                :key="index"
                :label="item.value"
                >{{ item.name }}</el-checkbox
              >
            </el-checkbox-group>
            <div class="checkbox-line" v-if="item.line"></div>
          </div>
          <div v-else-if="item.type === 'radio'" class="radio-div">
            <el-radio
              v-for="(opt, index) in item.selectOptions"
              :key="index"
              v-model="form[item.props]"
              :label="opt.value"
              >{{ opt.name }}</el-radio
            >
          </div>
          <!-- <el-cascader
              v-else-if="item.type === 'cascader'"
              :options="(item1, index1) in item.selectOptions"
              :props="{ checkStrictly: true }"
              clearable>
          </el-cascader>-->
          <div
            class="tg-search-cascader-select"
            v-else-if="item.type === 'cascader'"
          >
            <el-select
              v-model="form[item.props[0]]"
              :placeholder="
                item.placeholder
                  ? item.placeholder
                  : `请选择${item.subLabel[0]}`
              "
              class="tg-select"
              :popper-append-to-body="false"
              @change="change(form[item.props[0]], item.props[1])"
            >
              <el-option
                :label="item1.name"
                :value="item1.id"
                v-for="(item1, index1) in item.selectOptionsOne"
                :key="index1"
              ></el-option>
            </el-select>
            <el-select
              v-model="form[item.props[1]]"
              :placeholder="
                item.placeholder
                  ? item.placeholder
                  : `请选择${item.subLabel[1]}`
              "
              class="tg-select tg-select--margin"
              :popper-append-to-body="false"
            >
              <el-option
                :label="item1.name"
                :value="item1.id"
                v-for="(item1, index1) in item.selectOptionsTwo"
                :key="index1"
              ></el-option>
            </el-select>
          </div>
          <div
            v-else-if="item.type === 'num_range'"
            class="tg-num-range__input"
          >
            <el-input-number
              v-model="form[item.props][0]"
              :controls="false"
              :min="0"
              :max="form[item.props][1]"
              :placeholder="item.placeholder ? item.placeholder[0] : '最小值'"
            ></el-input-number>
            <span>-</span>
            <el-input-number
              v-model="form[item.props][1]"
              :controls="false"
              :min="form[item.props][0]"
              :max="form[item.props][1]"
              :placeholder="item.placeholder ? item.placeholder[1] : '最大值'"
            ></el-input-number>
          </div>
          <div v-else-if="item.type === 'school'" class="school">
            <el-input
              :placeholder="
                item.placeholder ? item.placeholder : `请选择${item.label}`
              "
              readonly
              show-word-limit
              :validate-event="false"
              @click.native="school_tree_visible = true"
              v-model="form[`${item.props}_name`]"
              class="tg-select tg-select--dialog"
              @mouseenter.native="school_flag = true"
              @mouseleave.native="school_flag = false"
            >
              <!-- <i slot="suffix" class="el-input__icon el-icon-arrow-down"></i> -->
              <img
                slot="suffix"
                :src="
                  !school_flag
                    ? require('../../assets/图片/icon_more.png')
                    : require('../../assets/图片/icon_more_ac.png')
                "
                alt
                class="btn__img--dotted"
              />
            </el-input>
            <school-tree
              :flag.sync="school_tree_visible"
              v-if="school_tree_visible"
              :id.sync="form[item.props]"
              :name.sync="form[`${item.props}_name`]"
              :type="item.school_choose_type"
              :use_store_options="item.use_store_options"
            ></school-tree>
          </div>
          <div v-else-if="item.type === 'choose_channel'">
            <channel-input
              :check_ids.sync="form[item.props[2]]"
              :channel_id.sync="form[item.props[0]]"
              :sub_channel_id.sync="form[item.props[1]]"
              :check_names.sync="form[`${item.props[2]}_name`]"
            ></channel-input>
          </div>
          <div v-else-if="item.type === 'student'" class="school">
            <el-input
              style="width: 238px"
              :placeholder="item.placeholder || '请选择学员'"
              readonly
              show-word-limit
              :validate-event="false"
              @click.native="add_student_visible = true"
              v-model="form[`${item.props}_name`]"
              class="tg-select tg-select--dialog"
              @mouseenter.native="student_flag = true"
              @mouseleave.native="student_flag = false"
            >
              <!-- <i slot="suffix" class="el-input__icon el-icon-arrow-down"></i> -->
              <img
                slot="suffix"
                :src="
                  !school_flag
                    ? require('../../assets/图片/icon_more.png')
                    : require('../../assets/图片/icon_more_ac.png')
                "
                alt
                class="btn__img--dotted"
              />
            </el-input>
            <choose-student
              type="radio"
              v-if="add_student_visible"
              :check_id.sync="form[item.props]"
              :check_arr.sync="student_check_arr"
              :check_name.sync="form[`${item.props}_name`]"
              @close="add_student_visible = false"
              :department_id="ids"
            ></choose-student>
          </div>
          <div v-else-if="item.type === 'custom_student'" class="school">
            <el-input
              placeholder="请选择意向客户"
              readonly
              show-word-limit
              :validate-event="false"
              @click.native="add_custom_student_visible = true"
              v-model="form[`${item.props}_name`]"
              class="tg-select tg-select--dialog"
              @mouseenter.native="custom_student_flag = true"
              @mouseleave.native="custom_student_flag = false"
            >
              <!-- <i slot="suffix" class="el-input__icon el-icon-arrow-down"></i> -->
              <img
                slot="suffix"
                :src="
                  !custom_student_flag
                    ? require('../../assets/图片/icon_more.png')
                    : require('../../assets/图片/icon_more_ac.png')
                "
                alt
                class="btn__img--dotted"
              />
            </el-input>
            <choose-custom-student
              type="radio"
              v-if="add_custom_student_visible"
              :check_id.sync="form[item.props]"
              :check_arr.sync="custom_student_check_arr"
              :check_name.sync="form[`${item.props}_name`]"
              @close="add_custom_student_visible = false"
              :department_id="ids"
            ></choose-custom-student>
          </div>
          <el-date-picker
            v-else-if="item.type === 'singleDate'"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择日期"
            v-model="form[item.props]"
            popper-class="tg-date-picker"
            :picker-options="item.pickerOptions"
            :clearable="item.clearable"
          ></el-date-picker>
          <el-date-picker
            v-else-if="item.type === 'operationLog_date'"
            v-model="form[item.props]"
            type="daterange"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            prefix-icon
            popper-class="tg-date-picker tg-date--range"
            :picker-options="pickerOptions_month"
          ></el-date-picker>
          <date-picker
            v-model="form[item.props]"
            v-else-if="item.type === 'custom_date'"
          ></date-picker>
          <el-date-picker
            v-else-if="item.type === 'date' || item.type === 'date_no_clear'"
            v-model="form[item.props]"
            type="daterange"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :clearable="item.type === 'date_no_clear' ? false : true"
            prefix-icon
            popper-class="tg-date-picker tg-date--range"
            :pickerOptions="
              item.has_options
                ? picker_options
                : item.disabledDate
                ? disabledDate
                : ''
            "
          ></el-date-picker>
          <el-date-picker
            v-else-if="item.type === 'datetime'"
            value-format="yyyy-MM-dd HH:mm:ss"
            v-model="form[item.props]"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            popper-class="tg-date-picker tg-date--range"
            :pickerOptions="item.has_options ? picker_datetime_options : ''"
          ></el-date-picker>
          <el-time-picker
            v-else-if="item.type === 'timePicker'"
            is-range
            v-model="form[item.props]"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            placeholder="选择时间范围"
            format="HH:mm"
            value-format="HH:mm"
          >
          </el-time-picker>
          <div class="custom--select" v-else-if="item.type === 'choose_course'">
            <el-input
              v-model="form[`${item.props}_name`]"
              readonly
              :placeholder="
                item.placeholder ? item.placeholder : `请选择${item.label}`
              "
              @click.native="choose_course_visible = true"
              @mouseenter.native="course_flag = true"
              @mouseleave.native="course_flag = false"
              :class="{ 'border--active': course_flag }"
            >
              <img
                :src="
                  !course_flag
                    ? require('../../assets/图片/icon_more.png')
                    : require('../../assets/图片/icon_more_ac.png')
                "
                slot="suffix"
                alt
                class="more"
              />
            </el-input>
            <choose-course
              :check_id.sync="form[item.props]"
              :check_name.sync="form[`${item.props}_name`]"
              :check_arr.sync="check_course_arr"
              :choose_course_visible="choose_course_visible"
              v-if="choose_course_visible"
              @close="choose_course_visible = false"
              :type="item.radio"
              :course_type="item.label === '剩余试听课程' ? 2 : undefined"
            ></choose-course>
          </div>
          <div class="custom--select" v-else-if="item.type === 'choose_class'">
            <el-input
              v-model="form[`${item.props}_name`]"
              readonly
              :placeholder="
                item.placeholder ? item.placeholder : `请选择${item.label}`
              "
              @click.native="choose_class_visible = true"
              @mouseenter.native="class_flag = true"
              @mouseleave.native="class_flag = false"
              :class="{ 'border--active': class_flag }"
            >
              <img
                :src="
                  !class_flag
                    ? require('../../assets/图片/icon_more.png')
                    : require('../../assets/图片/icon_more_ac.png')
                "
                slot="suffix"
                alt
                class="more"
              />
            </el-input>
            <choose-class
              :check_id.sync="form[item.props]"
              :check_name.sync="form[`${item.props}_name`]"
              :check_arr.sync="check_class_arr"
              :choose_class_visible="choose_class_visible"
              :useStoreSchool="item.useStoreSchool"
              v-if="choose_class_visible"
              @close="choose_class_visible = false"
            ></choose-class>
          </div>
          <div class="custom--select" v-else-if="item.type === 'choose_goods'">
            <el-input
              v-model="form[`${item.props}_name`]"
              readonly
              :placeholder="
                item.placeholder ? item.placeholder : `请选择${item.label}`
              "
              @click.native="goods_visible = true"
              @mouseenter.native="goods_flag = true"
              @mouseleave.native="goods_flag = false"
              :class="{ 'border--active': goods_flag }"
            >
              <img
                :src="
                  !goods_flag
                    ? require('../../assets/图片/icon_more.png')
                    : require('../../assets/图片/icon_more_ac.png')
                "
                slot="suffix"
                alt
                class="more"
              />
            </el-input>
            <choose-warehouse-goods
              v-if="goods_visible"
              :check_arr.sync="item.arr"
              :check_name.sync="form[`${item.props}_name`]"
              :check_id.sync="form[item.props]"
              type="radio"
              @close="goods_visible = false"
              :is_lease="item.is_lease"
            ></choose-warehouse-goods>
          </div>
          <div
            class="custom--select"
            v-else-if="item.type === 'choose_package'"
          >
            <el-input
              v-model="form[`${item.props}_name`]"
              readonly
              :placeholder="
                item.placeholder ? item.placeholder : `请选择${item.label}`
              "
              @click.native="package_visible = true"
              @mouseenter.native="package_flag = true"
              @mouseleave.native="package_flag = false"
              :class="{ 'border--active': package_flag }"
            >
              <img
                :src="
                  !package_flag
                    ? require('../../assets/图片/icon_more.png')
                    : require('../../assets/图片/icon_more_ac.png')
                "
                slot="suffix"
                alt
                class="more"
              />
            </el-input>
            <chooseTeachAidPackage
              v-if="package_visible"
              :check_id.sync="form[item.props]"
              :check_name.sync="form[`${item.props}_name`]"
              @close="package_visible = false"
              :hide_header_checkbox="true"
              :check_can_buy="true"
              :status="true"
              :is_charge="true"
            ></chooseTeachAidPackage>
          </div>
          <div class="custom--select" v-else-if="item.type === 'choose_room'">
            <el-input
              v-model="form[`${item.props}_name`]"
              readonly
              :placeholder="
                item.placeholder ? item.placeholder : `请选择${item.label}`
              "
              @click.native="room_visible = true"
              @mouseenter.native="room_flag = true"
              @mouseleave.native="room_flag = false"
              :class="{ 'border--active': room_flag }"
            >
              <img
                :src="
                  !room_flag
                    ? require('../../assets/图片/icon_more.png')
                    : require('../../assets/图片/icon_more_ac.png')
                "
                slot="suffix"
                alt
                class="more"
              />
            </el-input>
            <chooseRoom
              v-if="room_visible"
              :check_id.sync="form[item.props]"
              :check_name.sync="form[`${item.props}_name`]"
              @close="room_visible = false"
              :hide_header_checkbox="true"
              :check_can_buy="true"
              :status="true"
              :is_charge="true"
            ></chooseRoom>
          </div>
          <div class="custom--select" v-else-if="item.type === 'choose_match'">
            <el-input
              v-model="form[`${item.props}_name`]"
              readonly
              :placeholder="
                item.placeholder ? item.placeholder : `请选择${item.label}`
              "
              @click.native="match_visible = true"
              @mouseenter.native="match_flag = true"
              @mouseleave.native="match_flag = false"
              :class="{ 'border--active': match_flag }"
            >
              <img
                :src="
                  !match_flag
                    ? require('../../assets/图片/icon_more.png')
                    : require('../../assets/图片/icon_more_ac.png')
                "
                slot="suffix"
                alt
                class="more"
              />
            </el-input>
            <choose-match
              v-if="match_visible"
              :check_arr.sync="item.arr"
              :check_name.sync="form[`${item.props}_name`]"
              :check_id.sync="form[item.props]"
              type="radio"
              @close="match_visible = false"
            ></choose-match>
          </div>
          <div class="custom--select" v-else-if="item.type === 'label_flag'">
            <div
              class="label_item"
              @click="label_flag_visible = true"
              @mouseenter="class_flag = true"
              @mouseleave="class_flag = false"
            >
              <div class="label_box">
                <div v-for="(i, idx) in label_arr" :key="idx" class="label_tag">
                  <div class="label" :style="`color:${i.color};`">
                    {{ i.name }}
                  </div>
                  <div class="label_bg" :style="`background:${i.color};`"></div>
                </div>
              </div>
              <img
                :src="
                  !class_flag
                    ? require('../../assets/图片/icon_more.png')
                    : require('../../assets/图片/icon_more_ac.png')
                "
                slot="suffix"
                alt
                class="more"
              />
            </div>
            <LabelDialog
              label_type="search"
              :label_ids.sync="form[item.props]"
              :label_arr.sync="label_arr"
              :label_flag_visible="label_flag_visible"
              v-if="label_flag_visible"
              @close="label_flag_visible = false"
            ></LabelDialog>
          </div>
          <el-select
            v-model="form[item.props]"
            :placeholder="
              item.placeholder ? item.placeholder : `请选择${item.label}`
            "
            multiple
            collapse-tags
            :filterable="item.filterable"
            v-else-if="item.type === 'mutipleSelect'"
            :popper-append-to-body="false"
            clearable
          >
            <el-option
              :label="item1.name"
              :value="item1.id"
              v-for="(item1, index1) in item.selectOptions"
              :key="index1"
            ></el-option>
          </el-select>
          <el-select
            v-model="form[item.props]"
            :placeholder="
              item.placeholder ? item.placeholder : `请选择${item.label}`
            "
            :clearable="item.clearable ? true : false"
            filterable
            multiple
            collapse-tags
            v-else-if="item.type === 'mutipleSelect_filterable'"
            :popper-append-to-body="false"
          >
            <el-option
              :label="item1.name"
              :value="item1.id"
              v-for="(item1, index1) in item.selectOptions"
              :key="index1"
            ></el-option>
          </el-select>
          <el-rate
            v-model="form[item.props]"
            v-else-if="item.type === 'rate'"
          ></el-rate>
          <el-checkbox
            v-model="form[item.props]"
            v-else-if="item.type === 'bool'"
            @change="checkboxChange($event, item.props)"
            >{{ item.content }}</el-checkbox
          >
          <el-cascader
            v-else-if="item.type === 'area'"
            v-model="form[item.props]"
            :placeholder="item.placeholder"
            ref="cascaderHandleRef"
            clearable
            :props="{ value: 'name', label: 'name', checkStrictly: true }"
            :options="area"
            filterable
          >
            <span
              slot-scope="{ node, data }"
              style="margin-left: -10px; padding-left: 10px; display: block"
              @click="clickNode($event, node)"
              >{{ data.name }}</span
            >
          </el-cascader>
          <div v-else-if="item.type === 'select_wal_select'">
            <el-select
              v-model="form[item.props[0]]"
              :placeholder="
                item.placeholder
                  ? item.placeholder
                  : `请选择${item.subLabel[0]}`
              "
              class="tg-select tg-select-wal"
              :popper-append-to-body="false"
              @change="change(form[item.props[0]], item.props[1])"
            >
              <el-option
                :label="item1.name"
                :value="item1.id"
                v-for="(item1, index1) in item.selectOptionsOne"
                :key="index1"
              ></el-option>
            </el-select>
            <el-select
              v-model="form[item.props[1]]"
              filterable
              :placeholder="
                item.placeholder
                  ? item.placeholder
                  : `请选择${item.subLabel[1]}`
              "
              class="tg-select"
              :popper-append-to-body="false"
              @change="item?.selectChange(form[item.props[1]], item.props[2])"
            >
              <el-option
                :label="item1.name"
                :value="item1.id"
                v-for="(item1, index1) in item.selectOptionsTwo"
                :key="index1"
              ></el-option>
            </el-select>
          </div>
          <div v-else-if="item.type === 'custom-slot'">
            <slot :name="item.slot"></slot>
          </div>
        </el-form-item>
      </div>
      <el-form-item class="tg-search__box btnGroup">
        <el-button
          type="primary"
          v-throttle="{ func: search, wait: 1000 }"
          class="tg-button--primary tg-button__icon educeBtn"
          :loading="searchLoadingState"
        >
          <img
            src="../../assets/图片/icon_search.png"
            alt
            class="tg-button__icon--large"
          />查询
        </el-button>
        <el-button
          v-if="isReset"
          type="primary"
          v-throttle="reset"
          class="tg-button--primary tg-button__icon"
        >
          <img
            src="../../assets/图片/icon_reset.png"
            alt
            class="tg-button__icon--large"
          />重置
        </el-button>
        <el-button
          v-if="isRefresh"
          type="primary"
          v-throttle="refresh"
          class="tg-button--primary tg-button__icon"
        >
          <img
            src="../../assets/图片/icon_refresh_white.png"
            alt
            class="tg-button__icon--large"
          />刷新
        </el-button>
        <el-button
          v-if="isExport"
          type="primary"
          @click="educe()"
          :loading="loadingState"
          class="tg-button--primary tg-button__icon educeBtn"
        >
          <img
            v-if="!loadingState"
            src="../../assets/图片/export.png"
            alt
            class="tg-button__icon--large"
          />导出
        </el-button>
        <!-- 前端导出插槽 -->
        <slot name="frontExport"></slot>
        <el-button
          v-if="isDownload"
          type="primary"
          @click="download()"
          class="tg-button--primary tg-button__icon"
        >
          <img
            src="../../assets/图片/batch_download.png"
            alt
            class="tg-button__icon--large"
          />批量下载
        </el-button>
        <slot name="extra"></slot>
      </el-form-item>
    </el-form>
    <div
      class="search-speard"
      v-if="!isSpeard && searchTitle.length > showNum"
      :class="{ 'is-expand': showNum >= 4 }"
    >
      <div
        @click="showAllSearch(true)"
        @mouseenter="hoverFlag = true"
        @mouseleave="hoverFlag = false"
        class="search-speard__btn"
      >
        <span>展开</span>
        <img
          :src="
            hoverFlag
              ? require('../../assets/图片/icon_down_ac.png')
              : require('../../assets/图片/icon_down.png')
          "
          alt
        />
      </div>
    </div>
    <div class="search-speard is-expand" v-if="isSpeard">
      <div
        @click="showAllSearch(false)"
        @mouseenter="hoverFlag = true"
        @mouseleave="hoverFlag = false"
        class="search-speard__btn"
      >
        <span>收起</span>
        <img
          :src="
            hoverFlag
              ? require('../../assets/图片/icon_top_ac.png')
              : require('../../assets/图片/icon_top.png')
          "
          alt
        />
      </div>
    </div>
  </div>
</template>
<script>
// import marketStaff from "@/components/staff/marketStaff";
// import courseStaff from "@/components/staff/courseStaff";
// import chooseCourse from "../studentInfo/chooseCourse.vue";
// import chooseClass from "../studentInfo/chooseClass.vue";
// import schoolTree from "@/components/schoolTree/schoolTree.vue";
// import jobsSelect from "@/components/staff/jobs.vue";
import chooseWarehouseGoods from "@/components/goods/chooseWarehouseGoods.vue";
import ChannelInput from "@/components/search/channelInput.vue";
import moment from "moment";
import { picker_options } from "@/public/datePickerOptions";
import { area } from "@/public/area";
export default {
  data() {
    return {
      // form: {},
      area,
      value: "",
      // schoolId: [],
      isSpeard: false,
      hoverFlag: false,
      choose_course_visible: false,
      choose_class_visible: false,
      label_flag_visible: false,
      class_flag: false,
      course_flag: false,
      school_tree_visible: false,
      school_flag: false,
      goods_visible: false,
      goods_flag: false,
      match_visible: false,
      match_flag: false,
      package_visible: false,
      package_flag: false,
      room_visible: false,
      room_flag: false,
      add_student_visible: false,
      student_flag: false,
      add_custom_student_visible: false,
      custom_student_flag: false,
      check_class_arr: [],
      check_course_arr: [],
      label_arr: [],
      goods_check_arr: [],
      match_check_arr: [],
      student_check_arr: [],
      custom_student_check_arr: [],
      disabledDate: {
        disabledDate: (date) => {
          return date.getTime() > Date.now();
        }
      },
      picker_options,
      picker_datetime_options: {
        shortcuts: [
          {
            text: "今天",
            onClick(picker) {
              const start = new Date(
                new Date(new Date().toLocaleDateString()).getTime()
              );
              const end = new Date(
                new Date(new Date().toLocaleDateString()).getTime() +
                  24 * 60 * 60 * 1000 -
                  1
              );
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "本周",
            onClick(picker) {
              const start = new Date(
                new Date(new Date().toLocaleDateString()).getTime()
              );
              const end = new Date(
                new Date(new Date().toLocaleDateString()).getTime() +
                  24 * 60 * 60 * 1000 -
                  1
              );
              const nows = start.getDay() || 7; // 注意周日算第一天，如果周日查询本周的话，天数是0，所有如     果是0，默认设置为7
              start.setTime(start.getTime() - 3600 * 1000 * 24 * (nows - 1));
              end.setTime(
                start.getTime() + 3600 * 1000 * 24 * 6 + 24 * 60 * 60 * 1000 - 1
              );
              // console.log(end);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "上周",
            onClick(picker) {
              const oDate = new Date();
              oDate.setTime(oDate.getTime() - 3600 * 1000 * 24 * 7);

              const day = oDate.getDay() - 1;

              const start = new Date();
              const end = new Date();
              start.setTime(
                new Date(
                  new Date(
                    new Date(
                      oDate.getTime() - 3600 * 1000 * 24 * day
                    ).toLocaleDateString()
                  ).getTime()
                )
              );
              end.setTime(
                new Date(
                  new Date(
                    new Date(
                      oDate.getTime() + 3600 * 1000 * 24 * (6 - day)
                    ).toLocaleDateString()
                  ).getTime() +
                    24 * 60 * 60 * 1000 -
                    1
                )
              );

              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "本月",
            onClick(picker) {
              const start = new Date(
                new Date(
                  new Date(
                    moment().add("month", 0).format("YYYY-MM") + "-01"
                  ).toLocaleDateString()
                ).getTime()
              );
              const end = new Date(
                new Date(
                  new Date(
                    moment(start)
                      .add("month", 1)
                      .add("days", -1)
                      .format("YYYY-MM-DD")
                  ).toLocaleDateString()
                ).getTime() +
                  24 * 60 * 60 * 1000 -
                  1
              );

              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "上月",
            onClick(picker) {
              const oDate = new Date();
              let year = oDate.getFullYear();
              const month = oDate.getMonth();
              let start, end;
              if (month === 0) {
                year--;
                start = new Date(
                  new Date(new Date(year, 11, 1).toLocaleDateString()).getTime()
                );
                end = new Date(
                  new Date(
                    new Date(year, 11, 31).toLocaleDateString()
                  ).getTime() +
                    24 * 60 * 60 * 1000 -
                    1
                );
              } else {
                start = new Date(
                  new Date(
                    new Date(year, month - 1, 1).toLocaleDateString()
                  ).getTime()
                );
                end = new Date(
                  new Date(
                    new Date(year, month, 0).toLocaleDateString()
                  ).getTime() +
                    24 * 60 * 60 * 1000 -
                    1
                );
              }

              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "最近三十天",
            onClick(picker) {
              const start = new Date(
                new Date(new Date().toLocaleDateString()).getTime()
              );
              const end = new Date(
                new Date(new Date().toLocaleDateString()).getTime() +
                  24 * 60 * 60 * 1000 -
                  1
              );
              start.setTime(end.getTime() - 3600 * 1000 * 24 * 29);
              picker.$emit("pick", [start, end]);
            }
          }
        ]
      },
      pickerOptions_month: {
        disabledDate(time) {
          // 最近6个月
          const now = new Date();
          const sixMonthsAgo = new Date(
            now.getFullYear(),
            now.getMonth() - 5,
            1
          );
          return (
            time.getTime() < sixMonthsAgo.getTime() ||
            time.getTime() > now.getTime()
          );
        },
        shortcuts: [
          {
            text: "今天",
            onClick(picker) {
              const start = new Date(
                new Date(new Date().toLocaleDateString()).getTime()
              );
              const end = new Date(
                new Date(new Date().toLocaleDateString()).getTime() +
                  24 * 60 * 60 * 1000 -
                  1
              );
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "本周",
            onClick(picker) {
              const start = new Date(
                new Date(new Date().toLocaleDateString()).getTime()
              );
              const end = new Date(
                new Date(new Date().toLocaleDateString()).getTime() +
                  24 * 60 * 60 * 1000 -
                  1
              );
              const nows = start.getDay() || 7; // 注意周日算第一天，如果周日查询本周的话，天数是0，所有如     果是0，默认设置为7
              start.setTime(start.getTime() - 3600 * 1000 * 24 * (nows - 1));
              end.setTime(
                start.getTime() + 3600 * 1000 * 24 * 6 + 24 * 60 * 60 * 1000 - 1
              );
              // console.log(end);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "上周",
            onClick(picker) {
              const oDate = new Date();
              oDate.setTime(oDate.getTime() - 3600 * 1000 * 24 * 7);

              const day = oDate.getDay() - 1;

              const start = new Date();
              const end = new Date();
              start.setTime(
                new Date(
                  new Date(
                    new Date(
                      oDate.getTime() - 3600 * 1000 * 24 * day
                    ).toLocaleDateString()
                  ).getTime()
                )
              );
              end.setTime(
                new Date(
                  new Date(
                    new Date(
                      oDate.getTime() + 3600 * 1000 * 24 * (6 - day)
                    ).toLocaleDateString()
                  ).getTime() +
                    24 * 60 * 60 * 1000 -
                    1
                )
              );

              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "本月",
            onClick(picker) {
              const start = new Date(
                new Date(
                  new Date(
                    moment().add("month", 0).format("YYYY-MM") + "-01"
                  ).toLocaleDateString()
                ).getTime()
              );
              const end = new Date(
                new Date(
                  new Date(
                    moment(start)
                      .add("month", 1)
                      .add("days", -1)
                      .format("YYYY-MM-DD")
                  ).toLocaleDateString()
                ).getTime() +
                  24 * 60 * 60 * 1000 -
                  1
              );

              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "上月",
            onClick(picker) {
              const oDate = new Date();
              let year = oDate.getFullYear();
              const month = oDate.getMonth();
              let start, end;
              if (month === 0) {
                year--;
                start = new Date(
                  new Date(new Date(year, 11, 1).toLocaleDateString()).getTime()
                );
                end = new Date(
                  new Date(
                    new Date(year, 11, 31).toLocaleDateString()
                  ).getTime() +
                    24 * 60 * 60 * 1000 -
                    1
                );
              } else {
                start = new Date(
                  new Date(
                    new Date(year, month - 1, 1).toLocaleDateString()
                  ).getTime()
                );
                end = new Date(
                  new Date(
                    new Date(year, month, 0).toLocaleDateString()
                  ).getTime() +
                    24 * 60 * 60 * 1000 -
                    1
                );
              }

              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "最近三十天",
            onClick(picker) {
              const start = new Date(
                new Date(new Date().toLocaleDateString()).getTime()
              );
              const end = new Date(
                new Date(new Date().toLocaleDateString()).getTime() +
                  24 * 60 * 60 * 1000 -
                  1
              );
              start.setTime(end.getTime() - 3600 * 1000 * 24 * 29);
              picker.$emit("pick", [start, end]);
            }
          }
        ]
      }
    };
  },
  props: {
    inline: {
      require: false,
      default: true,
      type: Boolean
    },
    labelWidth: {
      require: false,
      type: Number,
      default: 120
    },
    searchTitle: {
      require: true,
      type: Array
    },
    isExport: {
      type: Boolean,
      default: false
    },
    isDownload: {
      type: Boolean,
      default: false
    },
    isReset: {
      type: Boolean,
      default: true
    },
    isRefresh: {
      type: Boolean,
      default: false
    },
    showNum: {
      type: Number,
      default: 4
    },
    // 查询按钮的loading状态
    searchLoadingState: {
      type: Boolean,
      default: false
    },
    // 导出按钮的loading状态
    loadingState: {
      type: Boolean,
      default: false
    },
    form: {
      type: Object,
      require: false,
      default: () => ({})
    },
    notUnfold: {
      type: Boolean,
      default: false
    },
    // 是否有默认日期
    hasDefaultDate: {
      type: Boolean,
      default: false
    },
    // 是否默认展开
    defaultSpread: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ids() {
      return this.$store.getters.doneGetSchoolId;
    }
  },
  mounted() {
    this.initData();
    // this.schoolId = this.ids.split(",");
    this.isSpeard = this.hasDefaultDate;
    if (this.hasDefaultDate) {
      this.showAllSearch(this.hasDefaultDate);
    }
    this.$nextTick(() => {
      if (this.defaultSpread) {
        this.showAllSearch(true);
      }
    });
  },
  methods: {
    search() {
      this.$emit("search", this.form);
    },
    reset() {
      this.label_arr = [];
      this.initData();
      this.$emit("reset");
    },
    refresh() {
      this.$emit("refresh");
    },
    educe() {
      this.$emit("educe");
    },
    download() {
      this.$emit("download");
    },
    initData() {
      this.check_course_arr = [];
      this.check_class_arr = [];
      if (typeof this.form !== "undefined") {
        return;
      }
      this.searchTitle.forEach((item) => {
        switch (item.type) {
          case "mutipleSelect":
          case "datetime":
          case "date":
          case "market_staff":
            this.$set(this.form, item.props, []);
            break;
          case "cascader":
            item.props.forEach((item1) => {
              this.$set(this.form, item1, "");
            });
            break;
          case "rate":
            this.$set(this.form, item.props, 0);
            break;
          default:
            this.$set(this.form, item.props, "");
            break;
        }
      });
      // console.log(this.form);
    },
    clickNode($event, node) {
      $event.target.parentElement.parentElement.firstElementChild.click();
    },
    change(id, props) {
      this.$emit("change", id);
      this.$set(this.form, props, "");
    },
    cascaderSelect(id) {
      this.$emit("cascaderSelect", id);
    },
    checkboxChange(event, id) {
      this.$emit("checkboxChange", { event, id });
    },
    showAllSearch(flag) {
      this.hoverFlag = false;
      const arr = this.searchTitle;
      if (flag) {
        // 点击展开
        arr.forEach((item) => {
          if (!item.isCustom) {
            item.show = flag;
          }
        });
      } else {
        arr.forEach((item, index) => {
          if (!item.isCustom) {
            if (index < this.showNum) {
              item.show = true;
            } else {
              item.show = flag;
            }
          }
        });
        this.tableTitle = arr;
      }
      this.$emit("update:searchTitle", arr);
      this.isSpeard = flag;
    }
  },
  components: {
    chooseWarehouseGoods,
    ChannelInput
    // courseStaff,
    // chooseCourse,
    // chooseClass,
    // schoolTree,
    // jobsSelect
  }
};
</script>

<style lang="less" scoped>
.m-bottom-16 {
  margin-bottom: 16px;
}
.p-top-16 {
  padding-top: 16px;
}
.tg-search {
  // width: calc(100% - 28px);
  width: 100%;
  box-sizing: border-box;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 6px 0 rgba(204, 208, 217, 0.35);
  padding-left: 16px;
  flex-shrink: 0;
  // padding-right: 16px;
  // margin-left: 6px;
  // margin-right: 6px;
  margin-bottom: 6px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  .tg-num-range__input {
    width: 168px;
    border-radius: 4px;
    height: 30px;
    display: flex;
    border: 1px solid #d3dce6;
    ::v-deep .el-input-number {
      width: 79px;
      display: inline-block;
      .el-input {
        width: 100%;
        height: 30px;
        .el-input__inner {
          position: absolute;
        }
      }
      .el-input__inner {
        border: none;
        background: transparent;
      }
    }
  }
  .search-speard {
    display: inline-flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    margin-left: 20px;
    // cursor: pointer;
    &:hover {
      .search-speard__btn span {
        color: #157df0;
      }
    }
    span {
      font-family: @text-famliy_medium;
      font-size: 13px;
      color: @text-color_third;
      margin-right: 12px;
    }
    img {
      height: 4px;
      width: 8px;
    }
  }

  .search-speard__btn {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: 100px;
    cursor: pointer;
  }
  .is-expand {
    padding: 10px 0;
  }
  ::v-deep .el-form-item__label,
  ::v-deep .el-form-item__content {
    line-height: 32px;
  }
  .tg-button__icon--normal {
    width: 10px;
    height: 11px;
    margin-right: 9px;
  }
  .tg-button__icon--large {
    width: 14px;
    height: 14px;
    margin-right: 8px;
  }
  .tg-search__box {
    display: inline-block;
    margin: 5px auto;
  }
  .el-rate {
    margin-top: 8px;
    width: 105px;
  }
  .tg-select--margin {
    margin-left: 16px;
  }
  ::v-deep .tg-select .el-select-dropdown.el-popper {
    left: 0 !important;
  }
  .more {
    width: 16px;
    height: 4px;
  }
  .custom--select {
    & > ::v-deep .el-input .el-input__inner {
      cursor: pointer;
    }
    & > ::v-deep .el-input .el-input__suffix-inner {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      height: inherit;
      margin-right: 10px;
      cursor: pointer;
    }
  }
  .custom--select {
    cursor: pointer;
  }
  .border--active {
    position: relative;
    &::after {
      content: "";
      position: absolute;
      width: 168px;
      height: 32px;
      left: -2px;
      top: -2px;
      border: none;
      border-radius: 6px;
      z-index: 10;
    }
    ::v-deep .el-input__inner {
      border-color: @base-color;
    }
  }
  .label_item {
    width: 158px;
    height: 32px;
    cursor: pointer;
    position: relative;
    font-size: 14px;
    border: 1px solid #d3dce6;
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 15px;
    .label_box {
      overflow: hidden;
      height: 32px;
      .label_tag {
        display: inline-block;
        padding: 0 10px;
        position: relative;
        margin-right: 3px;
      }
      .label {
        height: 24px;
        line-height: 24px;
      }
      .label_bg {
        width: 100%;
        height: 24px;
        border-radius: 4px;
        opacity: 0.12;
        position: absolute;
        left: 0;
        top: 0;
      }
    }
  }
  .checkbox-div {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    padding-right: 16px;
    .checkbox-line {
      background: #cbcfda;
      width: 1px;
      height: 13px;
      position: absolute;
      top: 9px;
      right: 0;
    }
  }
}
::v-deep.tg-button__icon > span {
  display: flex;
}

.tg-expand {
  padding-top: 16px;
  .tg-search__box {
    // padding-top: 16px;
  }
}
.tg-search--normal {
  min-height: 80px;
}
.tg-search--large {
  min-height: 104px;
  padding-top: 16px;
  justify-content: flex-start;
  .search-speard {
    padding: 10px 0;
  }
}
.tg-select {
  cursor: pointer;
  .el-input__inner {
    cursor: pointer;
  }
  &:hover {
    position: relative;
    &::after {
      content: "";
      position: absolute;
      width: 168px;
      height: 32px;
      left: -2px;
      top: -2px;
      border: none;
      border-radius: 6px;
      z-index: 10;
    }
    ::v-deep .el-input__inner {
      border-color: @base-color;
    }
  }
}
.btnGroup {
  ::v-deep .el-form-item__content {
    display: inline-flex !important;
  }
  .educeBtn {
    display: inline-flex;
    align-items: center;
  }
}
.input-with-select {
  ::v-deep .el-input-group__prepend {
    .el-select .el-input {
      width: 100px;
      input {
        background: #fff;
        width: 100px;
        border: 1px solid #d3dce6;
        box-sizing: border-box;
        color: #1f2d3d;
      }
    }
  }
  ::v-deep input {
    width: 168px;
  }
}
.search-choose_channel {
  cursor: pointer;
}
.input-number-with-select {
  display: flex;
  .el-select {
    margin-right: 12px;
  }
  ::v-deep .el-input-number .el-input {
    display: flex;
  }
  ::v-deep .el-select .el-input {
    width: 120px;
    input {
      background: #fff;
      width: 120px;
      border: 1px solid #d3dce6;
      box-sizing: border-box;
      color: #1f2d3d;
    }
  }
}
::v-deep .tg-select-wal {
  width: 120px;
  .el-input {
    width: 100%;
  }
}
/deep/ .tg-search-cascader-select {
  .el-select:first-child {
    .el-input {
      width: 120px;
    }
  }
  .el-select:last-child {
    margin-left: -2px;
    .el-input {
      width: 140px;
    }
  }
}
</style>
