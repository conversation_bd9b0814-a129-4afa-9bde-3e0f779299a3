<template>
  <div class="moudle">
    <div class="section-title">课程基本信息</div>
    <div class="section-content tg-box--margin">
      <el-form-item
        style="width: 860px; margin-bottom: 16px; display: inline-block"
        label="课程名称"
        required
        prop="base_info.name"
      >
        <el-input
          v-model="formData.name"
          placeholder=""
          class="tg-input--inherit"
        ></el-input>
      </el-form-item>
      <el-checkbox
        v-show="type != 1"
        style="margin-left: 22px"
        label="自动生成"
        v-model="auto"
        @change="autoChange"
      ></el-checkbox>
      <div v-show="type != 1" class="desc" style="margin-bottom: 16px">
        勾选自动生成时，课程名称会根据选择的年份、课程种类、班型、课程属性自动生成本课程的课程名称。
      </div>
      <el-form-item
        label="天弈课程名称"
        label-width="160"
        style="margin-bottom: 10px"
        ><el-button
          type="primary"
          icon="el-icon-plus"
          @click="addCourse"
          class="tg-button--primary"
          >添加</el-button
        >
        <el-row
          style="display: flex; align-items: center"
          v-for="(item, index) in ty_link_course"
          :key="index"
        >
          <!-- <el-input
            v-model="item.name"
            placeholder="请选择"
            class="ty_course tg-input--inherit"
            @change="
              (val) => {
                tyCourseInput(val, index);
              }
            "
          ></el-input> -->
          <el-select
            class="ty_course"
            v-model="ty_link_course[index]"
            filterable
            value-key="id"
            @change="
              (val) => {
                tyCourseInput(val, index);
              }
            "
            placeholder="请选择天弈课程"
          >
            <el-option
              v-for="(item, optionIndex) in ty_course_list"
              :key="optionIndex"
              :label="item.name"
              :value="item"
              :disabled="
                ty_link_course.some(
                  (selected) => selected && selected.id === item.id
                )
              "
            >
            </el-option>
          </el-select>
          <div class="close_input" @click="delCourse(index)"></div>
        </el-row>
      </el-form-item>
      <div v-if="type == 1">
        <div style="margin: 10px 0">课程属性</div>
        <div class="box" style="justify-content: start; margin-bottom: 16px">
          <div class="b-item">
            <el-form-item label="年份">
              <el-select
                v-model="formData.attrs.year"
                @change="propChange"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in years"
                  :key="item"
                  :label="item"
                  :value="item"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </div>
          <div class="b-item" style="margin-left: 20px">
            <el-form-item
              label="课时包类型"
              label-width="100px"
              required
              prop="base_info.attrs.type"
            >
              <el-select
                v-model="formData.attrs.type"
                @change="propChange"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-show="type == 1"
                  v-for="item in course_package_class_type"
                  :key="item.config_name"
                  :label="item.config_name"
                  :value="item.config_name"
                >
                </el-option>
                <el-option
                  v-show="type != 1"
                  v-for="item in course_class_type"
                  :key="item.config_name"
                  :label="item.config_name"
                  :value="item.config_name"
                >
                </el-option>
              </el-select>
              <span @click="addPropsClick('course_class_type')" class="add-txt"
                >新增</span
              >
            </el-form-item>
          </div>
        </div>
      </div>
      <div v-show="type != 1">
        <div style="margin: 10px 0">课程属性</div>
        <div class="box">
          <div class="b-item">
            <el-form-item label="年份">
              <el-select
                v-model="formData.attrs.year"
                @change="propChange"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in years"
                  :key="item"
                  :label="item"
                  :value="item"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </div>
          <div class="b-item">
            <el-form-item label="课程种类">
              <el-select
                v-model="formData.attrs.rank"
                @change="propChange"
                placeholder="请选择"
                clearable
                multiple
                class="rank-select"
              >
                <el-option
                  v-for="item in course_level"
                  :key="item.config_name"
                  :label="item.config_name"
                  :value="item.config_name"
                >
                </el-option>
              </el-select>
              <span @click="addPropsClick('course_level')" class="add-txt"
                >新增</span
              >
            </el-form-item>
          </div>
          <div class="b-item">
            <el-form-item
              :label="type == 3 ? '班型' : '类型'"
              :required="type != 1"
              prop="base_info.attrs.type"
            >
              <el-select
                v-model="formData.attrs.type"
                @change="propChange"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in course_class_type"
                  :key="item.config_name"
                  :label="item.config_name"
                  :value="item.config_name"
                >
                </el-option>
              </el-select>
              <span @click="addPropsClick('course_class_type')" class="add-txt"
                >新增</span
              >
            </el-form-item>
          </div>
          <div class="b-item">
            <el-form-item
              label="课程属性"
              :required="type != 1"
              prop="base_info.attrs.prop"
            >
              <el-select
                class="js_course_props"
                v-model="formData.attrs.prop"
                @change="propChange"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in course_attribute"
                  :key="item.value"
                  :label="item.config_name"
                  :value="item.config_value"
                >
                </el-option>
              </el-select>
              <!-- <span @click="addPropsClick('course_props')" class="add-txt"
                >新增</span
              > -->
            </el-form-item>
          </div>
        </div>
      </div>
      <div v-if="type != 1" style="margin-top: 10px" class="course-types">
        <el-form-item label="课程类型" required prop="base_info.type">
          <el-radio-group v-model="formData.type">
            <el-radio
              v-for="item in course_genre"
              :key="item.config_value"
              :label="item.config_value"
              >{{ item.config_name }}</el-radio
            >
          </el-radio-group>
          <span style="margin-left: -50px" class="desc"
            >创建后课程类型不支持修改</span
          >
        </el-form-item>
      </div>
      <div v-if="type != 1" class="elimination-box">
        <el-form-item label="动态课消" label-width="67px">
          <el-switch v-model="formData.elimination"></el-switch>
        </el-form-item>
        <div v-show="formData.elimination" class="e-item tg-box--margin">
          <el-form-item
            prop="base_info.hours"
            label-width="109px"
            label="每排一次课，计"
          >
            <el-input
              style="width: 100px"
              placeholder="请输入"
              :min="0"
              :max="999"
              v-model="formData.hours"
            ></el-input>
            <span class="ext">课时</span>
            <span style="margin-left: -65px" class="desc"
              >动态课消是允许在上课点名时动态决定实际课消的课程</span
            >
          </el-form-item>
        </div>
      </div>
      <el-form-item
        v-if="showSell"
        prop="base_info.stu_sell_type"
        class="showSell"
        required
        label="售卖对象"
      >
        <el-select
          v-model="formData.stu_sell_type"
          clearable
          placeholder="请选择"
          class="stu-sell-type"
          multiple
        >
          <el-option
            v-for="(item, index) in student_state_list"
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-option>
          <!-- :disabled="['out_school', 'drop_school'].includes(item.value)" -->
          <el-option label="意向客户" value="customer"></el-option>
          <el-option label="游客" value="visitor"></el-option>
        </el-select>
      </el-form-item>
    </div>
    <add-props
      v-if="addPropsDialog"
      @confirm="addPropsConfirm"
      @close="close"
      :label="addPropsLable"
    ></add-props>
  </div>
</template>
<script>
import studentInforApi from "@/api/studentInfor";
import AddProps from "./addProps.vue";
import {
  getCourseConfigAll,
  packageClassTypeAdd,
  addCourseConfigByName,
  getTYCourseList
} from "@/api/courseManagement.js";
import { course_years } from "@/public/dict.js";
// import Vue from "vue";
export default {
  props: {
    formData: Object,
    type: String
  },
  data() {
    return {
      auto: false,
      addPropsLable: "",
      addPropsProp: "",
      addPropsDialog: false,
      student_state_list: [],
      course_genre: [],
      course_class_type: [],
      course_level: [],
      course_attribute: [],
      course_package_class_type: [],
      years: [],
      ty_link_course: [],
      ty_link_course_id: [], // 添加ID数组
      ty_course_list: []
    };
  },
  components: {
    AddProps
  },
  created() {
    this.getStudentStateList();
    this.getCourseConfig();
    this.years = course_years;
    // 获取天弈课程
    this.getTYCourse();
  },
  methods: {
    getTYCourse() {
      getTYCourseList().then((res) => {
        const arr = res.data ?? [];
        // 保存完整的课程对象数组，包含id和name
        this.ty_course_list = arr;
      });
    },
    addCourse() {
      this.ty_link_course.push(null); // 存储课程对象，初始为null
      this.ty_link_course_id.push(""); // 同步添加ID
      this.formData.ty_link_course = this.ty_link_course;
      this.formData.ty_link_course_id = this.ty_link_course_id;
    },
    delCourse(index) {
      this.ty_link_course.splice(index, 1);
      this.ty_link_course_id.splice(index, 1); // 同步删除ID
      this.formData.ty_link_course.splice(index, 1);
      this.formData.ty_link_course_id = this.ty_link_course_id;
    },
    tyCourseInput(courseObj, index) {
      console.log(courseObj, index, "courseObj");

      // 直接使用传入的课程对象
      if (courseObj && courseObj.id && courseObj.name) {
        // 更新课程名称
        this.formData.ty_link_course[index] = courseObj.name;

        // 更新对应的课程ID
        this.ty_link_course_id[index] = courseObj.id.toString();
        this.formData.ty_link_course_id[index] = courseObj.id.toString();
      } else {
        // 清空数据
        this.formData.ty_link_course[index] = "";
        this.ty_link_course_id[index] = "";
        this.formData.ty_link_course_id[index] = "";
      }

      console.log("ty_link_course_id:", this.ty_link_course_id);
      console.log("ty_link_course:", this.formData.ty_link_course);
    },
    getConfigListByType(data, config_type) {
      data.map((item) => {
        if (config_type === item.config_type) {
          this[config_type] = item.config_list;
        }
      });
    },
    getCourseConfig() {
      getCourseConfigAll().then((res) => {
        if (res.data) {
          const config_name_list = [
            "course_genre",
            "course_level",
            "course_class_type",
            "course_attribute",
            "course_package_class_type"
          ];
          config_name_list.map((item) => {
            this.getConfigListByType(res.data, item);
          });
        }
      });
    },
    autoChange(value) {
      if (value) {
        const { prop, rank, year, type } = this.formData.attrs;
        const arr = this.course_attribute.filter(
          (item) => item.config_value === prop
        );
        const prop_chn = arr.length ? arr[0].config_name : "";
        this.formData.name = `${year}${prop_chn}${rank.join("")}${type}`;
      } else {
        this.formData.name = "";
      }
    },
    propChange() {
      if (this.auto) {
        const { prop, rank, year, type } = this.formData.attrs;
        const arr = this.course_attribute.filter(
          (item) => item.config_value === prop
        );
        const prop_chn = arr[0].config_name;
        this.formData.name = `${year}${prop_chn}${rank.join("")}${type}`;
      }
    },
    addPropsConfirm(data) {
      if (+this.type === 1) {
        this.addPropsProp = "course_package_class_type";
      }
      const arr = this[this.addPropsProp].filter(
        (item) => item.config_name === data
      );
      if (arr.length > 0) {
        this.$message.info("名称已存在，不能重复添加");
        return;
      }
      if (+this.type === 1) {
        packageClassTypeAdd({
          config_name: data
        }).then((res) => {
          if (res.data) {
            this.course_package_class_type.push({
              config_name: data,
              config_value: data
            });
            this.addPropsDialog = false;
          }
        });
      } else {
        addCourseConfigByName(
          {
            config_name: data
          },
          this.addPropsProp
        ).then((res) => {
          if (res.data) {
            this[this.addPropsProp].push({
              config_name: data,
              config_value: data
            });
            this.addPropsDialog = false;
          }
        });
      }
    },
    addPropsClick(prop) {
      this.addPropsProp = prop;
      this.addPropsDialog = true;
      switch (prop) {
        case "course_level":
          this.addPropsLable = "课程种类";
          break;
        case "course_class_type":
          this.addPropsLable =
            +this.type === 3
              ? "班型"
              : +this.type === 1
              ? "课时包类型"
              : "类型";
          break;
        // case "course_props":
        //   this.addPropsLable = "属性";
        //   break;
        default:
          break;
      }
    },
    getStudentStateList(data) {
      this.student_state_list = [];
      studentInforApi.getStudentState(data).then((res) => {
        for (const key in res.data) {
          console.log(key);
          if (!["out_school", "drop_school"].includes(key)) {
            this.student_state_list.push({ label: res.data[key], value: key });
          }
        }
      });
    },
    close() {
      this.addPropsDialog = false;
    }
  },
  computed: {
    showSell() {
      if (+this.type === 1) {
        return true;
      } else {
        const { attrs } = this.formData;
        console.log(attrs, "att");
        if (attrs.prop === "" || +attrs.prop === 1) {
          return false;
        } else return true;
      }
    }
  }
};
</script>
<style lang="less" scoped>
.section-content {
  /deep/ .ty_course {
    margin: 10px 0 0 94px;
    width: 736px;
    .el-input {
      width: 100%;
    }
  }
  .cliose_button {
    display: inline-block;
    height: 32px;
    padding-top: 12px;
  }
  .close_input {
    width: 20px;
    height: 20px;
    display: inline-block;
    background: url("~@/assets/图片/icon-message-error.png") no-repeat;
    background-size: 100%;
    margin: 10px 0 0 10px;
    cursor: pointer;
  }

  .box {
    display: flex;
    border: 1px solid @base-color;
    border-radius: 4px;
    padding: 9px 16px 16px 16px;
    justify-content: space-between;

    /deep/ .b-item {
      .el-form-item__label {
        padding-bottom: 3px;
        display: block;
        float: none;
        text-align: left;
      }

      .el-form-item__content {
        margin-left: 0 !important;

        .el-input {
          width: 187px;
        }

        .el-input.is-focus::after {
          border: none;
        }

        .add-txt {
          color: @base-color;
          margin-left: 16px;
          cursor: pointer;
        }
      }
    }
  }

  .showSell {
    /deep/ .el-input.is-focus::after {
      // top: 2px;
    }
  }

  .elimination-box {
    margin-top: 10px;

    .ext {
      color: #b3b7c6;
      margin-left: 16px;
    }
  }

  /deep/ .stu-sell-type {
    .el-input {
      width: 780px;
    }
  }

  /deep/ .rank-select {
    .el-input {
      .el-input__inner {
        height: 32px !important;
      }
    }
  }

  .tg-input--inherit {
    width: 780px;
  }

  ::v-deep .el-form-item__content,
  ::v-deep .el-form-item__label {
    line-height: 32px;
  }
}
</style>
