<template>
  <!-- <el-dialog
    :visible="true"
    width="1516px"
    title="导入"
    :show-close="true"
    class="batch-import"
    :modal-append-to-body="true"
    :close-on-press-escape="false"
    append-to-body
    top="7vh"
    :before-close="handleClose"
  > -->
  <div class="batch-import">
    <div
      class="content"
      v-has="{ m: 'compensationStructure', o: 'importData' }"
    >
      <el-upload
        drag
        :multiple="false"
        accept=".xls,.xlsx"
        action="#"
        :show-file-list="false"
        :on-change="toJson"
        :auto-upload="false"
        class="tg-upload"
      >
        <img src="../../assets/图片/icon_import.png" alt="" />
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      </el-upload>
      <!-- 下载模版 -->
      <el-button type="plain" class="tg-button--plain" @click="downloadTemplate"
        >下载模版</el-button
      >
    </div>
    <div class="batch--top tg-table--custom">
      <div class="tg-table__box">
        <div class="tg-box--border"></div>
        <el-table width="100%" height="183" :data="importData" class="tg-table">
          <el-table-column
            v-for="(item, index) in importTitle"
            :key="index"
            :label="item.label"
            :prop="item.prop"
            :width="item.width"
          >
          </el-table-column>
        </el-table>
      </div>
      <div class="tg-upload__button">
        <div class="tg-upload__tips">
          <img src="../../assets/图片/icon_info.png" alt="" />
          <span
            >只能上传<em>excel文件</em>，注意数据标题为首行，上方为导入数据预览</span
          >
        </div>
        <!-- 添加月份选择器 -->
        <div class="month-selector">
          <el-date-picker
            v-model="selectedMonth"
            type="month"
            placeholder="选择月份"
            format="yyyy-MM"
            value-format="yyyy-MM"
            style="width: 150px; margin-right: 16px"
            v-has="{ m: 'compensationStructure', o: 'importData' }"
          >
          </el-date-picker>
          <!-- 手动导出按钮 -->
          <!-- <el-button
            type="primary"
            class="tg-button--primary"
            v-has="{ m: 'compensationStructure', o: 'exportData' }"
            @click="exportData"
            :disabled="!selectedMonth || isImporting || !isUploadCompleted"
            >手动导出</el-button
          > -->
          <el-button
            type="primary"
            v-has="{ m: 'compensationStructure', o: 'importData' }"
            :disabled="importData.length == 0 || !selectedMonth"
            @click="dataUpload"
            class="tg-button--primary"
            >上传合思</el-button
          >
        </div>
      </div>
      <div class="tg-upload__loading" v-if="isImporting">
        <el-progress type="circle" :percentage="percentage"></el-progress>
      </div>
    </div>
    <div class="error-table">
      <div class="errTitle">
        <span class="error-table__title">错误数据</span>
        <span>
          <el-button
            type="primary"
            :disabled="errorData.length > 0 ? false : true"
            @click="downloadExcel"
            class="tg-button--primary"
            >导出</el-button
          >
          <!-- <el-button
            type="primary"
            :disabled="errorData.length > 0 ? false : true"
            @click="reUpload"
            class="tg-button--primary"
            v-has="{ m: 'compensationStructure', o: 'importData' }"
            >重新上传</el-button
          > -->
        </span>
      </div>
      <div class="tg-table__box" style="position: relative">
        <div class="tg-box--border"></div>
        <el-table
          width="100%"
          height="200"
          :data="errorData"
          class="tg-table"
          ref="tgTable"
        >
          <el-table-column
            v-for="(item, index) in errorTitle"
            :key="index"
            :label="item.label"
            :prop="item.prop"
            :width="item.width"
          >
          </el-table-column>
        </el-table>
        <div class="tg-uploaderr__loading" v-if="isReUploading">
          <el-progress
            type="circle"
            :percentage="reUploadPercentage"
          ></el-progress>
        </div>
      </div>
    </div>
  </div>
  <!-- <span slot="footer" class="dialog-footer">
      <el-button
        @click="handleClose"
        class="tg-button--plain"
        :disabled="isImporting ? true : false"
        >关闭</el-button
      >
    </span> -->
  <!-- </el-dialog> -->
</template>
<script>
import { readExcel } from "@/public/importExcel";
import { BigNumber } from "bignumber.js";
import compensationApi from "@/api/compensation";
import { exportTableToExcel } from "@/public/downloadExcel";
import timeFormat from "@/public/timeFormat";
// import { downLoadFile } from "@/public/downLoadFile";
export default {
  props: {
    type: {
      type: String
    }
  },
  // 需要选择渠道
  data() {
    return {
      fullscreenLoading: false,
      isImporting: false,
      importTitle: [
        {
          prop: "cost_bearing_department",
          label: "费用承担部门(部门类型)",
          type: "String"
        },
        {
          prop: "program_name",
          label: "项目(自定义档案类型)",
          type: "String"
        },
        {
          prop: "business_type",
          label: "业务类型(自定义档案类型)",
          width: 120,
          type: "String"
        },
        {
          prop: "amortization_ratio",
          label: "分摊比例(%)(数值类型)",
          width: 120
        },
        {
          prop: "amortization_price",
          label: "分摊金额(数值类型)",
          width: 120,
          type: "String"
        },
        {
          prop: "split_name",
          label: "拆表逻辑",
          width: 120
        },
        {
          prop: "type_name",
          label: "类型",
          width: 120,
          type: "String"
        },
        {
          prop: "body_name",
          label: "主体",
          type: "String"
        }
      ],
      tableTitle: [],
      errorTitle: [],
      importData: [],
      errorData: [],
      percentage: 0,
      excelTitle: [],
      erroReUpload: [],
      isReUploading: false,
      reUploadPercentage: 0,
      selectedMonth: null,
      isUploadCompleted: false
    };
  },
  mounted() {
    this.errorTitle = [
      ...this.importTitle,
      { prop: "msg", label: "错误原因", width: 120 }
    ];
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
    async toJson(file) {
      this.fullscreenLoading = this.$loading({
        lock: true,
        text: "Loading",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)"
      });
      const extension = file.name.split(".").pop().toLowerCase();
      if (extension !== "xlsx") {
        this.$message.error("只能上传xlsx文件");
        this.$nextTick(() => {
          // 以服务的方式调用的 Loading 需要异步关闭
          this.fullscreenLoading.close();
        });
        return; // 阻止上传
      }
      try {
        const fileData = await readExcel(file);
        const importData = fileData[0].sheet;
        this.excelTitle = [];
        for (const key in importData[0]) {
          this.excelTitle.push({ label: key, prop: key });
        }
        // 原始数据
        const initObj = {};
        this.importTitle.forEach((item) => {
          initObj[item.prop] = "";
        });
        const newData = importData.map((item) => {
          const obj = JSON.parse(JSON.stringify(initObj));
          for (const i in item) {
            const titleObj = this.importTitle.find((it) => it.label === i);
            if (titleObj) {
              obj[titleObj.prop] =
                item[i] === "null"
                  ? ""
                  : titleObj.type === "String"
                  ? item[i].toString()
                  : item[i];
            }
          }
          return obj;
        });
        this.$nextTick(() => {
          // 以服务的方式调用的 Loading 需要异步关闭
          this.fullscreenLoading.close();
        });
        this.importData = newData;
      } catch (err) {
        this.$nextTick(() => {
          // 以服务的方式调用的 Loading 需要异步关闭
          this.fullscreenLoading.close();
        });
      }
    },
    async dataUpload() {
      if (!this.selectedMonth) {
        this.$message.warning("请选择月份");
        return;
      }
      const lockRes = await compensationApi.lockData();
      if (lockRes.data.code !== 0) {
        this.$message.error(lockRes.data.message);
        return;
      }
      this.errorData = [];
      this.isImporting = true;
      this.isUploadCompleted = false; // 重置上传完成状态
      this.percentage = 0;
      console.log(this.importData);
      for (let index = 0; index < this.importData.length; index++) {
        const value = this.importData[index];
        const save_obj = Object.assign({}, value);
        save_obj.is_first = index === 0; // 只有第一条数据（索引为0）的is_first为true
        save_obj.amortization_price = Number(save_obj.amortization_price || 0);
        save_obj.amortization_ratio = Number(save_obj.amortization_ratio || 0);
        // save_obj.category_names = save_obj.category_names
        //   ? save_obj.category_names.split(",")
        //   : [];
        // save_obj.education_names = save_obj.education_names
        //   ? save_obj.education_names.split(",")
        //   : [];
        // save_obj.responsible_names = save_obj.responsible_names
        //   ? save_obj.responsible_names.split(",")
        //   : [];
        // save_obj.student_birth_day = save_obj.student_birth_day
        //   ? this.getDate(save_obj.student_birth_day)
        //   : "";
        // save_obj.sign_up_time = save_obj.sign_up_time
        //   ? this.getDate(save_obj.sign_up_time)
        //   : "";
        // save_obj.suspension_time = save_obj.suspension_time
        //   ? this.getDate(save_obj.suspension_time)
        //   : "";
        // save_obj.stop_time = save_obj.stop_time
        //   ? this.getDate(save_obj.stop_time)
        //   : "";
        // save_obj.school_manager_names = save_obj.school_manager_names
        //   ? save_obj.school_manager_names.split(",")
        //   : [];
        await compensationApi
          .importData(save_obj)
          .then((res) => {
            const length = this.importData.length;
            const b = new BigNumber(this.percentage);
            const a = new BigNumber(Math.round((1 / length) * 10000) / 100.0);
            const c = Number(b.plus(a));
            this.percentage = c;
            if (res.err) {
              this.errorData.push({ ...value, msg: res.err });
            }
            if (+res?.data?.code === 1) {
              this.errorData.push({ ...value, msg: res.data.message });
            }
          })
          .catch(() => {
            const length = this.importData.length;
            const b = new BigNumber(this.percentage);
            const a = new BigNumber(Math.round((1 / length) * 10000) / 100.0);
            const c = Number(b.plus(a));
            this.percentage = c;
            this.errorData.push({ ...value, msg: "未知错误" });
          });
      }
      setTimeout(() => {
        this.isImporting = false;
        this.isUploadCompleted = true; // 标记上传已完成
      }, 2000);
      if (this.errorData.length === 0) {
        this.$message.success("上传结束，请留意企微消息通知");
        this.exportData();
      } else {
        this.$message.warning("上传结束，请检查错误数据");
        this.erroReUpload = this.errorData;
        // this.exportData();
      }
    },
    getDate(time) {
      return timeFormat.GetDate(time);
    },
    downloadExcel() {
      const tableRef = this.$refs.tgTable;
      const filename = "人力拆表错误数据";
      const tableHeader = this.errorTitle.map((item) => item.label);
      exportTableToExcel(tableRef, filename, tableHeader);
    },
    // 导出
    async exportData() {
      compensationApi.pushHes({ month: this.selectedMonth.split("-")[1] });
      // const that = this;
      // setTimeout(async () => {
      //   this.$message.info("正在导出数据,请稍等...");

      //   await compensationApi
      //     .exportData({ month: this.selectedMonth.split("-")[1] })
      //     .then(function (res) {
      //       // 导出流
      //       console.log(res, "导出");
      //       const blob = new Blob([res.data]); // response.data为后端传的流文件
      //       const downloadFilename =
      //         "北京聂圣体育发展有限责任公司_" +
      //         that.moment(new Date()).format("YYYY-MM-DD HH:mm:ss") +
      //         ".zip"; // 设置导出的文件名  用moment时间插件对文件命名防止每次都是一样的文件名
      //       if (window.navigator && window.navigator.msSaveOrOpenBlob) {
      //         // 兼容ie浏览器
      //         window.navigator.msSaveOrOpenBlob(blob, downloadFilename);
      //       } else {
      //         // 谷歌,火狐等浏览器
      //         const url = window.URL.createObjectURL(blob);
      //         const downloadElement = document.createElement("a");
      //         downloadElement.style.display = "none";
      //         downloadElement.href = url;
      //         downloadElement.download = downloadFilename;
      //         document.body.appendChild(downloadElement);
      //         downloadElement.click();
      //         document.body.removeChild(downloadElement);
      //         window.URL.revokeObjectURL(url);
      //       }
      //       that.$message.success("导出成功");
      //     })
      //     .catch(function (err) {
      //       console.log(err);
      //     })
      //     .finally(function () {});
      //   // downLoadFile(res.data, "人力拆表数据");
      // }, 500);
    },
    async reUpload() {
      this.errorData = [];
      this.isReUploading = true;
      this.reUploadPercentage = 0;
      for (let index = 0; index < this.erroReUpload.length; index++) {
        const value = this.erroReUpload[index];
        const save_obj = Object.assign({}, value);
        save_obj.is_first = false; // 只有第一条数据（索引为0）的is_first为true
        save_obj.amortization_price = Number(save_obj.amortization_price || 0);
        save_obj.amortization_ratio = Number(save_obj.amortization_ratio || 0);
        // save_obj.category_names = save_obj.category_names
        //   ? save_obj.category_names.split(",")
        //   : [];
        // save_obj.education_names = save_obj.education_names
        //   ? save_obj.education_names.split(",")
        //   : [];
        // save_obj.responsible_names = save_obj.responsible_names
        //   ? save_obj.responsible_names.split(",")
        //   : [];
        // save_obj.student_birth_day = save_obj.student_birth_day
        //   ? this.getDate(save_obj.student_birth_day)
        //   : "";
        // save_obj.sign_up_time = save_obj.sign_up_time
        //   ? this.getDate(save_obj.sign_up_time)
        //   : "";
        // save_obj.suspension_time = save_obj.suspension_time
        //   ? this.getDate(save_obj.suspension_time)
        //   : "";
        // save_obj.stop_time = save_obj.stop_time
        //   ? this.getDate(save_obj.stop_time)
        //   : "";
        // save_obj.school_manager_names = save_obj.school_manager_names
        //   ? save_obj.school_manager_names.split(",")
        //   : [];
        await compensationApi
          .importData(save_obj)
          .then((res) => {
            const length = this.erroReUpload.length;
            const b = new BigNumber(this.reUploadPercentage);
            const a = new BigNumber(Math.round((1 / length) * 10000) / 100.0);
            const c = Number(b.plus(a));
            this.reUploadPercentage = c;
            if (res.err) {
              this.errorData.push({ ...value, msg: res.err });
            }
            if (+res?.data?.code === 1) {
              this.errorData.push({ ...value, msg: res.data.message });
            }
          })
          .catch(() => {
            const length = this.erroReUpload.length;
            const b = new BigNumber(this.reUploadPercentage);
            const a = new BigNumber(Math.round((1 / length) * 10000) / 100.0);
            const c = Number(b.plus(a));
            this.reUploadPercentage = c;
            this.errorData.push({ ...value, msg: "未知错误" });
          });
      }
      setTimeout(() => {
        this.isReUploading = false;
        this.isUploadCompleted = true; // 标记重新上传已完成
      }, 2000);
      if (this.errorData.length === 0) {
        this.$message.success("上传结束，请留意企微消息通知");
        // this.exportData();
      } else {
        this.$message.warning("上传结束，请检查错误数据");
        this.erroReUpload = this.errorData;
        // this.exportData();
      }
    },
    downloadTemplate() {
      const downloadElement = document.createElement("a");
      downloadElement.href =
        "./compensationImport.xlsx?v=" + window.__APP_VERSION__;
      downloadElement.download = `人力拆表模版.xlsx`; // 下载后文件名
      document.body.appendChild(downloadElement);
      downloadElement.click(); // 点击下载
      document.body.removeChild(downloadElement);
    }
  }
};
</script>
<style lang="less" scoped>
::v-deep .el-table__header {
  line-height: 0 !important;
}
.batch-import {
  .batch--top {
    // display: flex;
    // flex-direction: row;
    padding-bottom: 16px;
    position: relative;
    .tg-upload__loading {
      position: absolute;
      top: 0;
      left: 0;
      background: rgba(0, 0, 0, 0.5);
      margin: auto;
      right: 0;
      bottom: 0;
      width: 100%;
      height: inherit;
      z-index: 10;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      .el-progress {
        z-index: 11;
        ::v-deep .el-progress__text {
          color: white;
        }
      }
    }
  }
  .tg-upload__button {
    // text-align: right;
    margin-top: 16px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }
  .tg-table--custom {
    // width: calc(100% - 400px);
    width: 100%;
    ::v-deep .el-table {
      padding: 0;
      tr td .cell {
        padding-left: 26px;
      }
    }
    ::v-deep .el-table__header tr {
      background-color: #f5f8fc;
      & > th {
        background-color: transparent;
      }
    }
    ::v-deep .el-table__header {
      background-color: #f5f8fc;
      padding: 0 16px;
    }
  }
  .error-table {
    width: 100%;
    .error-table__title {
      font-size: 14px;
      font-weight: bold;
      color: @text-color_second;
      font-family: @text-famliy_medium;
      padding-left: 12px;
      position: relative;
      &::after {
        content: "";
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background-color: @base-color;
        position: absolute;
        top: 7px;
        left: 0;
      }
    }
  }
  .content {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    height: 32px;
    align-items: center;
    gap: 16px;
    .month-selector {
      flex-shrink: 0;
    }
    .el-select + .el-select {
      margin-left: 16px;
    }
  }
  ::v-deep .el-upload-dragger {
    border: 1px dashed #d0dce7;
    border-radius: 4px;
    height: 32px;
    background-color: #f7f8fa;
    width: 220px;
    img {
      width: 20px;
      height: 16px;
      margin-right: 12px;
      vertical-align: middle;
      margin-top: -3px;
    }
    .el-upload__text {
      display: inline-block;
      font-size: 10px;
      font-family: @text-famliy_medium;
      line-height: 30px;
    }
  }
  .tg-upload__tips {
    img {
      width: 12px;
      height: 12px;
      margin-right: 8px;
      vertical-align: middle;
      margin-top: -1px;
    }
    span {
      font-size: 12px;
      font-family: @text-famliy_medium;
      color: #b3b7c6;
    }
    em {
      color: #ff0317;
      font-style: normal;
    }
  }
  .tg-table__box {
    margin-left: 0;
    margin-right: 0;
    box-shadow: none;
    .tg-box--border {
      width: calc(100% - 1px);
    }
  }
  ::v-deep .el-dialog__body {
    padding: 16px;
  }
  ::v-deep .el-dialog__footer {
    padding: 11px 16px 7px 16px;
  }
  ::v-deep .el-dialog__body {
    height: 531px;
  }
  .tg-text--disabled {
    color: #8492a6;
    em {
      color: #455569;
    }
  }
  .tg-upload--disabled {
    ::v-deep .el-upload-dragger {
      cursor: not-allowed;
    }
  }
  .tg-row {
    display: inline-block;
  }
  .tg-row + .tg-row {
    margin-left: 40px;
  }
  .errTitle {
    display: inline-flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
}
.tg-upload {
  line-height: 0;
}
.tg-uploaderr__loading {
  position: absolute;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.5);
  margin: auto;
  right: 0;
  bottom: 0;
  width: 100%;
  height: inherit;
  z-index: 10;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  .el-progress {
    z-index: 11;
    ::v-deep .el-progress__text {
      color: white;
    }
  }
}
</style>
