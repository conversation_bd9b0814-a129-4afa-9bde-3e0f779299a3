<!-- 字段为“物品名称、物品金额、物品实交金额、购买时间” -->
<template>
  <div class="attend-class">
    <div class="tg-table__box class-table-margin">
      <div class="tg-box--border"></div>
      <el-table
        ref="table"
        :data="tableData"
        tooltip-effect="dark"
        style="width: 100%"
        class="tg-table"
        v-loading="is_loading"
      >
        <el-table-column prop="good_name" label="物品名称" />
        <el-table-column prop="standard_price" label="物品金额" />
        <el-table-column prop="price" label="物品实交金额" />
        <el-table-column prop="order_charge_date" label="购买时间" />
      </el-table>
    </div>
  </div>
</template>

<script>
import studentInforApi from "@/api/studentInfor";
export default {
  data() {
    return {
      tableData: [],
      is_loading: false
    };
  },
  props: {
    id: {
      type: String,
      default: ""
    },
    departmentId: {
      type: String,
      default: ""
    }
  },
  mounted() {
    this.getTableData();
  },
  methods: {
    getTableData() {
      this.tableData = [];
      studentInforApi
        .getArticleList({
          student_id: this.id,
          department_id: this.departmentId,
          page: this.page,
          page_size: this.page_size,
          has_left: true
        })
        .then((res) => {
          if (res.data.code === 0) {
            this.tableData = res.data.data.results;
          } else {
            this.$message.error(res.data.message);
          }
        });
    }
  }
};
</script>
<style lang="less" scoped>
.attend-class {
  width: 100%;
  height: 100%;
  padding: 0 6px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  .tg-checkbox--margin {
    margin-right: 6px;
  }
  ::v-deep .el-checkbox__label {
    font-size: @text-size_small;
  }
  ::v-deep .el-checkbox__inner {
    background-color: transparent;
  }
  ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: @base-color;
  }
  .class-table-margin {
    margin-left: 0;
  }
}
</style>
