<!--选择课程-->
<template>
  <el-dialog
    :append-to-body="true"
    :visible="true"
    v-if="choose_course_visible"
    :title="title ? title : '选择课程'"
    width="1016px"
    :before-close="handleClose"
    class="choose-course"
  >
    <div class="tg-dialog__content">
      <div class="course-list">
        <div class="search tg-box--margin">
          <el-form @submit.native.prevent :inline="true" :model="form">
            <el-form-item>
              <el-input
                placeholder="请输入课程名称"
                class="search__input"
                :class="{ 'search__input--special': !has_attribute }"
                v-model="form.course_name"
              >
                <img
                  src="../../assets/图片/icon_search_grey.png"
                  alt
                  slot="prefix"
                />
                <span @click="flag = !flag" slot="suffix" class="searchBtn">
                  <img
                    :src="
                      !flag
                        ? require('../../assets/图片/icon_double_down.png')
                        : require('../../assets/图片/icon_double_up_ac.png')
                    "
                    alt
                    class="search__img"
                  />
                </span>
              </el-input>
            </el-form-item>
            <el-form-item class="search-status" v-if="has_attribute && !status">
              <el-select
                placeholder="请选择状态"
                :popper-append-to-body="false"
                v-model="form.is_enabled"
              >
                <el-option
                  v-for="(item, index) in status_list"
                  :value="item.id"
                  :label="item.name"
                  :key="index"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                class="tg-button--primary tg-button__icon"
                @click="searchVal"
              >
                <img
                  src="../../assets/图片/icon_search.png"
                  alt
                  class="tg-button__icon--normal"
                />查询
              </el-button>
              <el-button
                type="primary"
                class="tg-button--primary tg-button__icon"
                @click="reset"
              >
                <img
                  src="../../assets/图片/icon_reset.png"
                  alt
                  class="tg-button__icon--normal"
                />重置
              </el-button>
            </el-form-item>
            <el-form-item
              label="年份"
              class="tg-form-item tg-box--margin tg-form-item--right"
              v-if="flag"
            >
              <el-select
                placeholder="请选择年份"
                :popper-append-to-body="false"
                v-model="form.course_year"
              >
                <el-option
                  v-for="(item, index) in course_year"
                  :value="item.id"
                  :label="item.name"
                  :key="index"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="课程种类"
              class="tg-form-item tg-box--margin"
              v-if="flag"
            >
              <el-select
                placeholder="请选择课程种类"
                :popper-append-to-body="false"
                v-model="form.course_level"
              >
                <el-option
                  v-for="(item, index) in course_level"
                  :value="item.id"
                  :label="item.name"
                  :key="index"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="班型"
              class="tg-form-item tg-box--margin tg-form-item--right"
              v-if="flag"
            >
              <el-select
                placeholder="请选择班型"
                :popper-append-to-body="false"
                v-model="form.lesson_type"
              >
                <el-option
                  v-for="(item, index) in course_class_type"
                  :value="item.id"
                  :label="item.name"
                  :key="index"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="课程属性"
              class="tg-form-item tg-box--margin"
              v-if="flag && has_attribute"
            >
              <el-select
                placeholder="请选择课程属性"
                :popper-append-to-body="false"
                v-model="form.department_course_property"
                multiple
              >
                <el-option
                  v-for="(item, index) in course_attribute"
                  :value="item.id"
                  :label="item.name"
                  :key="index"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="状态"
              class="tg-form-item tg-box--margin"
              v-if="flag && !has_attribute && !status"
            >
              <el-select
                placeholder="请选择状态"
                :popper-append-to-body="false"
                v-model="form.is_enabled"
              >
                <el-option
                  v-for="(item, index) in status_list"
                  :value="item.id"
                  :label="item.name"
                  :key="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <div class="tg-table__box">
          <div class="tg-box--border"></div>
          <el-table
            ref="table"
            :data="course_list"
            tooltip-effect="dark"
            class="tg-table"
            :height="flag ? 353 : 449"
            @selection-change="handleSelectionChange"
            @select="checkBoxSelect"
            :row-key="getRowKeys"
            @current-change="handleCurrentChange"
            :highlight-current-row="type == 'radio' ? true : false"
            @row-click="rowClick"
            :tree-props="{ children: 'children' }"
          >
            >
            <el-table-column
              type="selection"
              width="50"
              v-if="type != 'radio'"
              label-class-name="cell_xx_hide"
              :reserve-selection="true"
            ></el-table-column>
            <el-table-column width="50" v-if="type == 'radio'">
            </el-table-column>
            <el-table-column label="课程名称" prop="course_name">
              <template slot-scope="scope">
                {{
                  typeof scope.row.product_id == "undefined"
                    ? scope.row[scope.column.property]
                    : scope.row.product_name
                }}
              </template>
            </el-table-column>
            <el-table-column label="年份" width="70" prop="course_year">
            </el-table-column>
            <el-table-column
              label="课程种类"
              width="100"
              prop="course_level_string"
              show-overflow-tooltip
            >
            </el-table-column>
            <el-table-column label="班型" width="70" prop="lesson_type">
            </el-table-column>
            <el-table-column
              label="课程属性"
              width="90"
              prop="department_course_attribute"
              v-if="has_attribute"
            >
              <template slot-scope="scope">
                {{ scope.row.course_attribute | getLabel(course_attribute) }}
              </template>
            </el-table-column>
            <el-table-column label="价格" prop="course_price" width="84">
              <template slot-scope="scope">
                {{
                  typeof scope.row.product_id == "undefined"
                    ? scope.row[scope.column.property]
                    : scope.row.original_price
                }}
              </template>
            </el-table-column>
          </el-table>
          <div class="tg-pagination">
            <span class="el-pagination__total">共 {{ total }} 条</span>
            <el-pagination
              background
              layout="prev, pager, next,jumper"
              :total="total"
              :page-size="page_size"
              :current-page="page"
              @current-change="currentChange"
            ></el-pagination>
          </div>
        </div>
      </div>
      <div class="course-list--right">
        <div class="organization__title">
          <span>
            已选 课程
            <em>{{ right_course_list.length }}</em
            >个
          </span>
          <span class="all-clear" @click="clear">
            <img src="../../assets/图片/icon_clear.png" alt />
            清空
          </span>
        </div>
        <div
          class="organization__info"
          v-for="(item, index) in right_course_list"
          :key="index"
        >
          <span>{{ item.course_name }}</span>
          <img
            src="../../assets/图片/icon_close_green.png"
            alt
            @click="delOne(index, item.id)"
          />
        </div>
        <span v-if="right_course_list.length === 0" class="is-empty"
          >暂无数据</span
        >
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button class="tg-button--plain" type="plain" @click="back"
        >取消</el-button
      >
      <el-button class="tg-button--primary" type="primary" @click="really"
        >确定</el-button
      >
    </span>
  </el-dialog>
</template>
<script>
import { course_years } from "@/public/dict.js";
import classApi from "@/api/classManagement";
import chargeApi from "@/api/charge";
import {
  getCourseConfigByType,
  getCourseListExcluded,
  getCourseList,
  getCourseRelativeList
} from "@/api/courseManagement.js";
export default {
  data() {
    return {
      right_course_list: [],
      flag: false,
      page: 1,
      page_size: 10,
      total: 0,
      list: [],
      form: {
        course_name: "",
        is_enabled: "",
        course_level: "",
        department_course_property: [],
        course_year: "",
        lesson_type: "",
        department_id: ""
      },
      course_level: [], // 课程种类
      course_attribute: [], // 课程属性
      course_class_type: [], // 班型
      course_year: [], // 年份,
      status_list: [
        { id: 1, name: "启用" },
        { id: 2, name: "停用" }
      ], // 状态
      course_list: [],
      has_attribute: false,
      default_department_id: "",
      rowSelectFlag: false
    };
  },
  computed: {
    school_id() {
      return this.$store.getters.doneGetSchoolId;
    }
  },
  filters: {
    getLabel(val, data) {
      const obj = data.find((item) => +item.id === +val);
      return typeof obj === "undefined" ? "" : obj.name;
    }
  },
  watch: {
    school_id: {
      handler(val) {
        this.default_department_id = val;
      },
      immediate: true
    }
  },
  props: {
    // 是否小程序绑定课程
    is_bind_minipro: {
      type: Number,
      default: 0
    },
    title: String,
    check_id: [String, Array],
    check_name: [String, Array],
    check_arr: Array,
    choose_course_visible: Boolean,
    course_type: Number,
    isChild: Boolean,
    // 收费时是否校验学员能否买选中的课程
    check_can_buy: {
      type: Boolean,
      default: false
    },
    hide_header_checkbox: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: "select"
    },
    // 学员信息
    stu_info: {
      type: Object,
      default: null
    },
    attribute_type: {
      type: String,
      default: "all" // all, class, sale
    },
    department_id: [String, Array],
    api_type: {
      type: String,
      default: "default" // exclude
    },
    status: {
      type: Boolean,
      default: false // true只显示启用的列表
    },
    exclude_id: {
      type: Array,
      default: () => [] // exclude
    },
    show_relative: {
      type: Boolean,
      default: false // 是否展示关联物品关联赛事
    }
  },
  mounted() {
    // 暂时隐藏全选，后续需要后台做处理
    if (this.hide_header_checkbox) {
      this.$nextTick(() => {
        // eslint-disable-next-line no-undef
        $(".choose-course .cell_xx_hide.cell").hide();
      });
    }
    this.form.department_id =
      typeof this.department_id === "undefined"
        ? this.default_department_id
        : this.department_id;
    this.getSelect("course_level");
    this.getSelect("course_class_type");
    this.getSelect("course_attribute");
    const arr = [
      {
        id: "",
        name: "不限"
      }
    ];
    course_years.map((item) => {
      arr.push({
        id: item,
        name: item
      });
    });
    this.course_year = arr;
    this.has_attribute =
      typeof this.department_id === "undefined"
        ? this.school_id.length === 1
        : true;
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    delOne(index) {
      if (this.type === "radio") {
        this.$nextTick(() => {
          this.$refs.table.setCurrentRow();
        });
        this.right_course_list.splice(index, 1);
      } else {
        setTimeout(() => {
          this.$nextTick(() => {
            this.rowSelectFlag = true;
            const id = this.right_course_list[index].id;
            this.course_list.forEach((item) => {
              if (item.id === id) {
                this.$refs.table.toggleRowSelection(item, false);
              }
            });
            this.right_course_list.splice(index, 1);
            this.rowSelectFlag = false;
          });
        }, 0);
      }
    },
    clear() {
      if (this.type === "radio") {
        this.$nextTick(() => {
          this.$refs.table.setCurrentRow();
        });
      } else {
        this.$nextTick(() => {
          this.$refs.table.clearSelection();
          this.right_course_list = [];
        });
      }
    },
    back() {
      this.$emit("close");
    },
    really() {
      const ids = [];
      const names = [];
      this.right_course_list.forEach((item) => {
        ids.push(item.id);
        names.push(item.course_name);
      });
      this.$emit(
        "update:check_id",
        this.right_course_list.length > 0 ? ids.toString() : ""
      );

      this.$emit(
        "update:check_arr",
        this.right_course_list.length > 0 ? this.right_course_list : []
      );
      this.$emit(
        "update:check_name",
        this.right_course_list.length > 0 ? names.toString() : ""
      );
      this.$emit("confirm", this.right_course_list);
      this.$emit("close");
    },
    openDialog() {},
    currentChange(val) {
      this.page = val;
      this.getCourseList();
    },
    getRowKeys(row) {
      // this.$refs.form.clearSelection();完成后需要手动清空
      return row.id;
    },
    async checkBoxSelect(selection, row) {
      if (this.check_can_buy) {
        const cna_buy = await this.checkCanBuy(row);
        if (!cna_buy) {
          this.right_course_list.map((item, index) => {
            this.right_course_list.splice(index, 1);
          });
          this.$refs.table.toggleRowSelection(row, false);
        }
      }
    },
    handleSelectionChange(val) {
      if (this.type !== "radio") {
        if (this.rowSelectFlag) return;
        const ids = this.right_course_list.map((item) => item.id);
        const arr = JSON.parse(JSON.stringify(this.right_course_list));
        const new_arr = val.map((item) => {
          if (ids.indexOf(item.id) !== -1) {
            item = arr[ids.indexOf(item.id)];
          }
          return item;
        });
        this.right_course_list = new_arr;
      }
    },
    handleCurrentChange(val) {
      if (this.type === "radio") {
        this.right_course_list = val == null ? [] : [val];
      }
    },
    reset() {
      this.form = {
        course_name: "",
        is_enabled: "",
        course_level: "",
        department_course_property: this.course_attribute.map(
          (item) => item.id
        ),
        course_year: "",
        lesson_type: "",
        department_id: this.form.department_id
      };
      this.page = 1;
      this.getCourseList();
      // this.$refs.table.clearSelection();
    },
    searchVal() {
      this.page = 1;
      this.getCourseList();
    },
    async checkCanBuy(row) {
      const { stu_info } = this;
      const parms = {
        entity: {
          entity_id: row.id,
          entity_type: row.product_type
        },
        target: {
          department_id: stu_info.department_id,
          target_id: stu_info.student_id,
          target_status: stu_info.student_type,
          target_type: stu_info.user_type
        }
      };
      const resp = await chargeApi.charge_pre_check(parms);
      const { data } = resp.data;
      if (!data.if_can_buy) {
        this.$message.info(data.reason);
      }
      return data.if_can_buy;
    },
    async rowClick(row) {
      // 如果是关联物品，不进行检验
      if (row.parent_id) {
        return;
      }
      if (this.check_can_buy) {
        const cna_buy = await this.checkCanBuy(row);
        if (!cna_buy) return;
      }
      if (this.type === "radio") return false;
      const ids = this.right_course_list.map((item) => item.id);
      const index = ids.indexOf(row.id);
      this.$refs.table.toggleRowSelection(row, index === -1);
    },
    getClassType() {
      this.class_type = [];
      classApi.GetSchoolServiceClassroomMapType().then((res) => {
        for (const key in res.data) {
          this.class_type.push({ id: key, name: res.data[key] });
        }
      });
    },
    getSelect(str) {
      getCourseConfigByType({}, str).then((res) => {
        if (res.data) {
          const arr = [];
          res.data.map((item) => {
            if (str === "course_attribute") {
              if (this.attribute_type === "all") {
                arr.push({
                  id: item.config_value,
                  name: item.config_name
                });
              } else if (
                this.attribute_type === "class" &&
                (item.config_value === "1" || item.config_value === "3")
              ) {
                arr.push({
                  id: item.config_value,
                  name: item.config_name
                });
              } else if (
                this.attribute_type === "sale" &&
                (item.config_value === "2" || item.config_value === "3")
              ) {
                arr.push({
                  id: item.config_value,
                  name: item.config_name
                });
              }
            } else {
              arr.push({
                id:
                  str === "course_level" || str === "course_class_type"
                    ? item.config_name
                    : item.config_value,
                name: item.config_name
              });
            }
          });
          this[str] = arr;
          if (str === "course_attribute") {
            this.form.department_course_property = arr.map((item1) => item1.id);
            this.getCourseList();
          }
        }
      });
    },
    async getCourseList() {
      const obj = {
        page_num: this.page,
        page_size: this.page_size,
        just_department: 1,
        course_type: this.course_type,
        is_bind_minipro: this.is_bind_minipro,
        ...this.form
      };
      // if (this.department_id && typeof this.department_id == "string") {
      //   obj.just_department = 1;
      // }
      if (!this.has_attribute) {
        delete obj.department_course_property;
      }
      let res;
      if (this.api_type === "default") {
        if (this.status) {
          obj.is_enabled = 1;
        }
        res = this.show_relative
          ? await getCourseRelativeList(obj)
          : await getCourseList(obj);
      } else if (this.api_type === "exclude") {
        if (this.status) {
          obj.is_enabled = 1;
        }
        obj.exclude_ids = this.exclude_id;
        res = await getCourseListExcluded(obj);
      }
      const { total, data } = res.data;
      this.total = total;
      this.course_list = data == null ? [] : data;
      if (this.show_relative) {
        this.course_list.forEach((row) => {
          if (row.children == null) {
            row.children = [];
          }
          row.children.forEach((item) => {
            this.$set(item, "id", `${row.id}_${item.product_id}`);
            this.$set(item, "parent_id", row.id);
          });
        });
      }
      this.$nextTick(() => {
        if (this.type === "radio") {
          this.right_course_list.forEach((row) => {
            const find_row = this.course_list.find(
              (item) => item.id === row.id
            );
            this.$refs.table.setCurrentRow(find_row);
          });
        } else {
          setTimeout(() => {
            this.rowSelectFlag = true;
            this.course_list.forEach((row) => {
              const find_index = this.right_course_list.findIndex(
                (item) => item.id === row.id
              );
              this.$refs.table.toggleRowSelection(row, find_index !== -1);
            });
            this.rowSelectFlag = false;
          }, 0);
        }
      });
    }
  },
  created() {
    this.right_course_list = JSON.parse(JSON.stringify(this.check_arr)) || [];
  }
};
</script>
<style lang="less" scoped>
.choose-course {
  .search {
    width: 100%;

    img {
      width: 14px;
      height: 14px;
      margin-right: 12px;
      margin-left: 11px;
      margin-top: -4px;
      vertical-align: middle;
    }

    img.search__img {
      width: 8px;
      height: 12px;
      margin-right: 10px;
      cursor: pointer;
    }
    .searchBtn {
      display: inline-block;
      width: 100%;
      height: 100%;
      cursor: pointer;
      user-select: none;
    }
    ::v-deep .el-form-item__content,
    ::v-deep .el-form-item__label {
      line-height: 32px;
    }

    .search__input {
      width: 280px;

      ::v-deep .el-input__inner {
        padding-left: 40px;
      }

      ::v-deep .el-input__suffix {
        right: 1px;
        background: #ebf4ff;
        height: 30px;
        top: 1px;
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
      }
    }

    .search__input--special {
      width: 413px;
    }

    .el-button {
      width: 72px;

      img {
        margin-left: 0;
        margin-right: 7px;
      }
    }

    .search-status {
      ::v-deep .el-input {
        width: 120px;

        .el-input__inner {
          padding-left: 16px;
        }
      }
    }

    ::v-deep .el-form-item {
      margin-right: 10px;
    }

    ::v-deep .el-form-item:nth-child(2) {
      margin-right: 0;
    }

    ::v-deep .el-form-item.search-status {
      margin-right: 10px;
    }

    ::v-deep .tg-form-item--right {
      margin-right: 20px;
    }

    ::v-deep .tg-form-item .el-input {
      width: 225px;
    }
  }

  ::v-deep .el-dialog__body {
    padding: 0 16px 0 16px;
  }

  .tg-dialog__content {
    display: flex;
    flex-direction: row;
    height: 589px;
  }

  .course-list {
    width: calc(100% - 368px);
    border-right: 1px solid #e0e6ed;
    padding-right: 16px;

    .tg-table__box {
      margin-left: 0;
    }

    ::v-deep .el-table {
      padding: 0;

      th {
        background: #f5f8fc;
      }

      .el-table__header {
        padding: 0 16px;
        background: #f5f8fc;
      }

      .el-table__body {
        padding: 0 16px;
      }
    }
  }

  .course-list--right {
    width: 368px;
    margin-left: 16px;
    margin-top: 16px;
    height: 500px;
    overflow: auto;

    .organization__title,
    .organization__info {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }

    .all-clear {
      // color: #157df0;
      font-size: @text-size_small;
      font-family: @text-famliy_medium;
      cursor: pointer;

      img {
        width: 14px;
        height: 14px;
        margin-right: 8px;
        vertical-align: middle;
        margin-top: -3px;
      }
    }

    .organization__title {
      em {
        font-style: normal;
        color: @base-color;
      }
    }

    .organization__info {
      border: 1px solid @base-color;
      border-radius: 4px;
      height: 40px;
      align-items: center;
      padding: 0 16px;
      margin-top: 16px;

      ::v-deep .el-input,
      ::v-deep .el-input__inner {
        height: 40px;
        line-height: 40px;
      }

      img {
        width: 16px;
        height: 16px;
        cursor: pointer;
      }

      span:nth-child(1) {
        overflow-x: scroll;
        width: calc(100% - 36px);
        white-space: nowrap;
      }
    }

    .required {
      &::before {
        content: "*";
        margin-right: 5px;
        color: #ff0317;
      }
    }
  }

  .is-empty {
    color: @text-color_third;
    width: 100%;
    display: inline-block;
    text-align: center;
    line-height: 350px;
  }

  ::v-deep .el-table__row--level-1 {
    .el-checkbox {
      display: none;
    }
  }
}
</style>
