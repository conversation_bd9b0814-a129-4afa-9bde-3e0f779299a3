<!--调班-->
<template>
  <el-dialog
    :visible="true"
    width="900px"
    :before-close="handleClose"
    class="change-class"
    :modal="has_modal"
  >
    <template slot="title">
      <span
        >调班-<em>{{ information.student_name }}</em></span
      >
    </template>
    <div class="tg-dialog__content">
      <div class="title">{{ information.student_name }}当前就读班级：</div>
      <div class="tg-table__box">
        <div class="tg-box--border"></div>
        <el-table
          ref="table"
          :data="list"
          tooltip-effect="dark"
          class="class-table"
          height="328"
        >
          <el-table-column label="班级名称" width="200" prop="classroom_name">
          </el-table-column>
          <el-table-column
            label="上课时间"
            width="120"
            prop="class_time"
            show-overflow-tooltip
          >
            <!-- <template slot-scope="scope">
              <el-dropdown>
                <span class="el-dropdown-link school_hours">
                  <img src="../../assets/图片/icon_time.png" alt="" />
                </span>
                <el-dropdown-menu
                  slot="dropdown"
                  v-if="scope.row.class_time != ''"
                >
                  <el-dropdown-item
                    v-for="(item, index) in scope.row.class_time.split(',')"
                    :key="index"
                    ><span>{{ item }}</span></el-dropdown-item
                  >
                </el-dropdown-menu>
              </el-dropdown>
            </template> -->
          </el-table-column>
          <el-table-column label="上课教室" width="120" prop="school_room_name">
          </el-table-column>
          <el-table-column label="老师" width="120" prop="teacher_name">
          </el-table-column>
          <el-table-column label="状态" width="120" prop="status_chn">
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button
                :disabled="scope.row.is_shift_pool_start"
                type="text"
                @click="changeClass(scope.row.classroom_id)"
                >调班</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button class="tg-button--plain" type="plain" @click="back"
        >关闭</el-button
      >
      <!-- <el-button class="tg-button--primary" type="primary" @click="really">确定</el-button> -->
    </span>
    <choose-school-hours
      v-if="choose_hours_visible"
      :student_id="information.student_id"
      :check_id.sync="check_class_id"
      :old_class_id="row_id"
      :filter_class_id="filterClassIds"
      :source="source"
      :operate_id="operate_id"
      :department_id="information.department_id"
      :department_name="information.department_name"
      @close="choose_hours_visible = false"
      @really="hours_tips_visible = true"
    ></choose-school-hours>
    <hours-tips
      v-if="hours_tips_visible"
      :student_id="information.student_id"
      :old_class_id="row_id"
      :class_id="check_class_id"
      @close="hoursClose"
    ></hours-tips>
  </el-dialog>
</template>
<script>
import studentInforApi from "@/api/studentInfor";
import moment from "moment";
import chooseSchoolHours from "./chooseSchoolHours.vue";
import hoursTips from "./hoursTips.vue";
import classApi from "@/api/classManagement";

export default {
  components: { chooseSchoolHours, hoursTips },
  data() {
    return {
      right_class_list: [],
      filterClassIds: [],
      flag: false,
      // page: 1,
      // page_size: 10,
      total: 0,
      list: [],
      choose_hours_visible: false,
      row_id: "-1", // 点击调班的id
      check_class_id: "",
      hours_tips_visible: false,
      information: ""
    };
  },
  props: {
    check_id: String,
    source: String,
    check_name: String,
    id: String,
    operate_id: String,
    has_modal: {
      type: Boolean,
      default: true
    }
  },
  mounted() {
    this.getStudents({ student_id: this.id });
  },
  computed: {
    isNetworkSchool() {
      return ["网校测试校区C", "聂卫平围棋网校-新"].includes(
        this.information.department_name
      );
    }
  },
  methods: {
    handleClose() {
      this.back();
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    clear() {},
    back() {
      this.clear();
      this.$emit("close");
    },
    changeClass(id) {
      this.row_id = id;
      this.filterClassIds = this.list
        .filter((i) => ["in_classroom", "wait_in_classroom"].includes(i.status))
        .map((i) => i.classroom_id);
      console.log(this.filterClassIds);
      this.choose_hours_visible = true;
    },
    hoursClose() {
      this.hours_tips_visible = false;
      this.getClass({
        student_id: this.id,
        is_shift_pool: this.isNetworkSchool ? true : undefined,
        status: ["in_classroom", "wait_in_classroom"],
        match_exists: 2 // 只看普通班，不显示赛事班
      });
    },
    getStudents(data) {
      studentInforApi.getStudentInforDetail(data).then((res) => {
        this.information = res.data;
        this.$set(
          this.information,
          "birth_day",
          moment(res.data.student_base.student_birth_day).format("YYYY-MM-DD")
        );
        this.$set(
          this.information,
          "student_gender",
          res.data.student_base.student_gender
        );
        this.$set(
          this.information,
          "student_mobile",
          res.data.student_base.student_mobile
        );
        this.$set(
          this.information,
          "student_name",
          res.data.student_base.student_name
        );
        this.$set(this.information, "id", res.data.student_base.id);
        this.getClass({
          student_id: this.id,
          is_shift_pool: this.isNetworkSchool ? true : undefined,
          status: ["in_classroom", "wait_in_classroom"],
          match_exists: 2 // 只看普通班，不显示赛事班
        });
      });
    },
    getClass(data) {
      // this.is_loading = true;
      classApi.GetSchoolServiceClassroomStudentList(data).then((res) => {
        this.list = res.data == null ? [] : res.data;
        // this.total = res.data.count;
        // this.is_loading = false;
      });
    }
  }
};
</script>
<style lang="less" scoped>
.change-class {
  //去掉最下面的那一条线
  /deep/.el-table::before {
    height: 0px;
  }
  /deep/.customer-table .el-table__fixed-right::before,
  .el-table__fixed::before {
    width: 0;
  }
  em {
    font-style: normal;
    color: @base-color;
  }
  ::v-deep .el-dialog__body {
    padding: 0 16px 0 16px;
  }
  .tg-dialog__content {
    height: 489px;
  }
  ::v-deep .class-table {
    padding: 0;
    width: 100%;
    margin: 0;
    th {
      background: #f5f8fc;
      height: 40px;
    }
    th:nth-child(1),
    td:nth-child(1) {
      .cell {
        padding-left: 42px;
      }
    }
    // td:nth-child(2) .cell{
    //   padding-left: 30px;
    // }
  }
  .tg-box--border {
    height: 38px;
  }
  .tg-table__box {
    box-shadow: none;
    margin: 0;
    margin-top: 16px;
  }
  .title {
    padding-top: 16px;
  }
  // .school_hours {
  //   display: flex;
  //   flex-direction: row;
  //   align-items: center;
  //   justify-content: center;
  //   height: 23px;
  //   img {
  //     width: 14px;
  //     height: 14px;
  //   }
  // }
}
</style>
