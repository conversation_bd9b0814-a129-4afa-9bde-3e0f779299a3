<!--班级的上课时间-->
<template>
  <el-dialog
    :visible="true"
    width="850px"
    :title="`校区：${department_name}`"
    class="choose-hours"
    :modal="false"
    :before-close="handleClose"
    top="16vh"
  >
    <div class="search tg-box--margin">
      <el-form :inline="true" @submit.native.prevent :model="form">
        <el-form-item>
          <el-input
            placeholder="请输入班级名称"
            class="search__input"
            v-model="form.name"
          >
            <img src="../../assets/图片/icon_search_grey.png" slot="prefix" />
          </el-input>
        </el-form-item>
        <el-form-item class="tg-btn--margin">
          <el-button
            type="primary"
            class="tg-button--primary tg-button__icon"
            @click="searchVal"
          >
            <img
              src="../../assets/图片/icon_search.png"
              alt=""
              class="tg-button__icon--normal"
            />查询
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tg-dialog__content">
      <div class="tg-table__box">
        <div class="tg-box--border"></div>

        <el-table
          ref="table"
          :data="list"
          tooltip-effect="dark"
          class="hours-table"
          height="328"
          highlight-current-row
          @current-change="handleCurrentChange"
          :row-style="rowStyle"
          @row-click="rowClick"
          v-loading="loading"
        >
          <el-table-column
            type="index"
            label="序号"
            width="80"
          ></el-table-column>
          <el-table-column label="班级名称" width="200" prop="name">
          </el-table-column>
          <el-table-column label="现有/预招" width="100">
            <template slot-scope="scope">
              {{
                `${scope.row.student_numb} / ${scope.row.pre_enrolment_numb} 人`
              }}
            </template>
          </el-table-column>
          <el-table-column
            label="上课时间"
            width="100"
            prop="class_time"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column label="上课教室" prop="school_room_name">
          </el-table-column>
          <el-table-column label="老师" prop="teacher_name"> </el-table-column>
        </el-table>
      </div>
    </div>

    <span slot="footer" class="dialog-footer">
      <!-- 分页 -->
      <div class="tg-pagination">
        <span class="el-pagination__total">共 {{ total }} 条</span>
        <el-pagination
          background
          layout="prev, pager, next,jumper"
          :total="total"
          :page-size="page_size"
          :current-page="page"
          @current-change="currentChange"
        >
        </el-pagination>
      </div>
      <template v-if="isNetworkSchool && source !== 'changeShiftPool'">
        <el-button
          v-has="{ m: 'userPool', o: 'shiftAdjustStudent' }"
          class="tg-button--primary"
          type="primary"
          @click="applyChangeShift"
          >进入调班池</el-button
        >
      </template>
      <el-button class="tg-button--primary" type="primary" @click="really"
        >确定</el-button
      >
    </span>
  </el-dialog>
</template>
<script>
import changeShiftPool from "@/api/changeShiftPool";
import classApi from "@/api/classManagement";
export default {
  data() {
    return {
      flag: false,
      page: 1,
      page_size: 10,
      total: 0,
      list: [],
      current_row: {},
      loading: true,
      form: {
        name: ""
      }
    };
  },
  props: {
    check_id: String,
    check_name: String,
    department_name: String,
    filter_class_id: Array,
    department_id: String,
    source: String,
    operate_id: String,
    old_class_id: String,
    student_id: String
  },
  computed: {
    isNetworkSchool() {
      return ["网校测试校区C", "聂卫平围棋网校-新"].includes(
        this.department_name
      );
    }
  },
  mounted() {
    this.getClass({
      page: this.page || 1,
      page_size: this.page_size,
      department_id: [this.department_id],
      status: ["not_start"],
      has_student_id: this.student_id,
      name: this.form.name,
      match_exists: 2 // 只看普通班，不显示赛事班
    });
  },
  methods: {
    // 分页
    currentChange(val) {
      this.page = val;
      this.getClass({
        page: this.page || 1,
        page_size: this.page_size,
        department_id: [this.department_id],
        status: ["not_start"],
        has_student_id: this.student_id,
        name: this.form.name,
        match_exists: 2 // 只看普通班，不显示赛事班
      });
    },
    searchVal() {
      this.page = 1;
      this.getClass({
        page: this.page || 1,
        page_size: this.page_size,
        department_id: [this.department_id],
        status: ["not_start"],
        has_student_id: this.student_id,
        name: this.form.name,
        match_exists: 2 // 只看普通班，不显示赛事班
      });
    },
    handleClose() {
      this.back();
    },
    clear() {
      this.current_row = {};
    },
    back() {
      this.clear();
      this.$emit("close");
    },
    async applyChangeShift() {
      const params = {
        student_ids: [this.student_id],
        remove_classroom_id: this.old_class_id,
        shift_type: "shift",
        operate_id: this.operate_id
      };
      const { code, message } = await changeShiftPool.shiftAdjustStudent(
        params
      );
      if (code === 0) {
        this.back();
      } else {
        this.$message.error(message);
      }
    },
    really() {
      if (!this.current_row || !this.current_row.id) return;
      this.$emit("update:check_id", this.current_row.id);
      this.back();
      this.$emit("really");
    },
    handleCurrentChange(val) {
      this.current_row = val;
    },
    getClass(data) {
      // this.is_loading = true;
      classApi.GetSchoolServiceClassroomList(data).then((res) => {
        console.log(this.old_class_id, res.data.results);
        this.list =
          res.data == null
            ? []
            : res.data.results.filter(
                (i) => !this.filter_class_id.includes(i.id)
              );
        this.loading = false;
        this.total = res.data.count;
        // this.is_loading = false;
      });
    },
    rowStyle({ row }) {
      if (row.student_numb === row.pre_enrolment_numb) {
        return { color: "#C0CCDA" };
      }
    },
    rowClick(row) {
      if (row.student_numb === row.pre_enrolment_numb) {
        this.$refs.table.setCurrentRow();
      }
    }
  }
};
</script>
<style lang="less" scoped>
::v-deep .el-dialog {
  max-height: 600px;
}

.choose-hours {
  ::v-deep .el-dialog__header {
    line-height: 40px;
    .el-dialog__title {
      font-size: @text-size_small;
      color: @text-color_second;
      font-family: @text-famliy_semibold;
    }
    height: 40px;
    .el-dialog__headerbtn {
      top: 12px;
    }
    &::after {
      background-color: transparent;
    }
  }
  em {
    font-style: normal;
    color: @base-color;
  }
  ::v-deep .el-dialog__body {
    padding: 0 16px 0 16px;
  }
  .tg-dialog__content {
    height: 335px;
  }
  .search {
    width: 100%;
    img {
      width: 14px;
      height: 14px;
      margin-right: 12px;
      margin-left: 11px;
      margin-top: -4px;
      vertical-align: middle;
    }
    img.search__img {
      width: 8px;
      height: 12px;
      margin-right: 10px;
      cursor: pointer;
    }
    ::v-deep .el-form-item__content,
    ::v-deep .el-form-item__label {
      line-height: 32px;
    }
    .search__input {
      width: 278px;
      ::v-deep .el-input__inner {
        padding-left: 40px;
      }
    }
    .el-button {
      width: 72px;
      img {
        margin-left: 0;
        margin-right: 7px;
      }
    }
    // .search_teacher {
    //   ::v-deep .el-input {
    //     width: 170px;
    //     .el-input__inner {
    //       padding-left: 16px;
    //     }
    //   }
    // }
    ::v-deep .el-form-item {
      margin-right: 10px;
    }
    ::v-deep .el-form-item:nth-child(2) {
      margin-right: 0;
    }
    ::v-deep .el-form-item.tg-btn--margin {
      margin-right: 10px;
    }
    ::v-deep .bottom_select {
      margin-right: 20px;
      .el-input {
        width: 296px;
      }
    }
  }
  .tg-table__box {
    box-shadow: none;
    margin: 0;
    margin-top: 16px;
  }
  ::v-deep .hours-table {
    width: 100%;
    padding: 0;
    th {
      background: #f5f8fc;
      height: 40px;
    }
    th:nth-child(1),
    td:nth-child(1) {
      .cell {
        padding-left: 10px;
      }
    }
    .el-table__body tr.current-row > td {
      background-color: #ebf4ff;
    }
  }
  .tg-box--border {
    height: 38px;
  }
}
</style>
