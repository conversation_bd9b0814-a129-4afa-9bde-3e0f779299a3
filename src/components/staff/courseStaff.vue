<!--选择员工--单选组件-->
<template>
  <div class="course-staff">
    <el-input
      v-if="isCustomize !== true"
      :value="check_name"
      readonly
      :placeholder="staff_placeholder"
      @click.native="is_disabled ? () => {} : openDialog()"
      class="tg-select tg-select--dialog"
      :disabled="is_disabled"
      @mouseenter.native="hover_flag = true"
      @mouseleave.native="hover_flag = false"
    >
      <!-- <i
        slot="suffix"
        class="el-select__caret el-input__icon el-icon-arrow-down"
      ></i>-->
      <img
        :src="
          !hover_flag
            ? require('../../assets/图片/icon_more.png')
            : require('../../assets/图片/icon_more_ac.png')
        "
        alt
        slot="suffix"
        class="btn__img--dotted"
      />
    </el-input>
    <el-dialog
      :visible="true"
      v-if="course_staff_visible"
      title="选择员工"
      width="800px"
      class="course-staff-dialog"
      :modal="has_modal"
      :append-to-body="has_modal"
      :before-close="handleClose"
    >
      <div class="tg-dialog__content">
        <div class="organization-tree-check">
          <div class="search">
            <img src="../../assets/图片/icon_search_grey.png" alt />
            <input
              v-model="filter_text"
              placeholder="请输入员工或部门名称"
              class="tg-input__inner"
            />
            <el-checkbox
              style="margin-left: 10px"
              v-model="is_leave"
              @change="leave_change"
              >显示离职员工</el-checkbox
            >
          </div>
          <el-tree
            v-if="refreshTree"
            :data="staff_list"
            node-key="node_key"
            default-expand-all
            ref="tree"
            :filter-node-method="filterNode"
            :props="default_props"
            v-loading="loading"
          >
            <div
              class="custom-tree-node_courseStall"
              :class="{
                'is-disabled':
                  data.type === 'employee' && isEmployeeDisabled(data.id)
              }"
              slot-scope="{ data }"
              @click="checkChange(data)"
            >
              <el-checkbox
                v-if="data.type === 'employee'"
                v-model="check_staff"
                :true-label="data.node_key"
                :false-label="'null'"
                class="tree__checkbox is-readonly"
                :key="data.node_key"
                :disabled="isEmployeeDisabled(data.id)"
              ></el-checkbox>
              <div
                v-if="data.type === 'employee'"
                class="title_text"
                :title="data.name + '[' + data.main_post_name + ']'"
              >
                <span> {{ data.name }}【{{ data.main_post_name }}】</span>
                <el-tag v-if="data.is_leave" type="danger" size="small"
                  >离职</el-tag
                >
              </div>
              <div v-else class="title_text" :title="data.name">
                {{ data.name }}
              </div>
            </div>
          </el-tree>
        </div>
        <div class="staff-list--right">
          <div class="organization__title">
            <span>
              已选人员
              <em>{{ right_staff_list.length }}</em>
            </span>
            <span class="all-clear" @click="clear">
              <img src="../../assets/图片/icon_clear.png" alt />
              清空
            </span>
          </div>
          <div
            class="organization__info"
            v-for="(item, index) in right_staff_list"
            :key="index"
          >
            <span style="display: flex; align-items: center"
              >{{ item.name }}
              <el-tag
                v-if="item.is_leave"
                style="width: auto; margin-left: 10px"
                type="danger"
                size="small"
                >离职</el-tag
              >
            </span>
            <img
              src="../../assets/图片/icon_close_green.png"
              alt
              @click="delOne(index, item.id)"
            />
          </div>
          <span v-if="right_staff_list.length === 0" class="is-empty"
            >暂无数据</span
          >
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="tg-button--plain" type="plain" @click="back"
          >取消</el-button
        >
        <el-button class="tg-button--primary" type="primary" @click="really"
          >确定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import staffApi from "@/api/staff";
import wmsManagementApi from "@/api/wmsManagement";
export default {
  data() {
    return {
      refreshTree: true,
      right_staff_list: [],
      check_staff: "",
      staff_list: [],
      filter_text: "",
      course_staff_visible: false,
      default_props: {
        children: "children",
        label: "name"
      },
      tree_node: [],
      select_flag: false,
      hover_flag: false,
      loading: false
    };
  },
  props: {
    check_id: [String, Array],
    check_name: [String, Array],
    // 是否显示输入框
    isCustomize: {
      type: Boolean,
      default: () => undefined
    },
    is_leave: {
      type: Boolean,
      default: () => false
    }, // 是否包含离职员工
    has_modal: {
      type: Boolean,
      default: true
    },
    staff_placeholder: String,
    department_id: String,
    is_disabled: {
      type: Boolean,
      default: false
    },
    isWithdraw: {
      type: Boolean,
      default: false
    },
    kuId: {
      type: String,
      default: ""
    },
    office_post_name: {
      type: Boolean,
      default: false
    },
    usedTeacherList: {
      type: Array,
      default: () => []
    } // 已被使用的教师ID列表，这些教师将被禁用
  },
  computed: {
    school_id() {
      return this.$store.getters.doneGetSchoolId;
    }
  },
  watch: {
    filter_text(val) {
      this.$refs.tree.filter(val);
    }
  },
  created() {
    console.log(this.isCustomize, "isCustomize");
    if (this.isCustomize !== undefined) {
      this.openDialog();
    }
  },
  methods: {
    // 判断员工是否被禁用
    isEmployeeDisabled(employeeId) {
      return this.usedTeacherList.includes(employeeId);
    },
    leave_change(val) {
      this.$emit("update:is_leave", val);
      this.clear();
      this.openDialog();
    },
    handleClose() {
      this.clear();
      this.$emit("close", false);
      this.filter_text = "";
      this.course_staff_visible = false;
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    delOne(index) {
      this.check_staff = "";
      const id = this.right_staff_list[index].id;
      for (let i = 0; i < this.tree_node.length; i++) {
        if (id === this.tree_node[i].id) {
          this.tree_node.splice(i, 1);
          i--;
        }
      }
      this.right_staff_list.splice(index, 1);
    },
    clear() {
      this.right_staff_list = [];
      this.tree_node = [];
      this.check_staff = "";
    },
    back() {
      this.clear();
      this.filter_text = "";
      this.course_staff_visible = false;
      this.$emit("close");
    },
    really() {
      console.log(this.right_staff_list);
      const check_id =
        this.right_staff_list.length > 0 ? this.right_staff_list[0].id : "";
      const check_name =
        this.right_staff_list.length > 0 ? this.right_staff_list[0].name : "";
      const check_mobile =
        this.right_staff_list.length > 0 ? this.right_staff_list[0].mobile : "";
      this.$emit("update:check_id", check_id);
      this.$emit("update:check_name", check_name);
      this.$emit("really", check_id, check_name);
      this.$emit("update:check_mobile", check_mobile);
      this.clear();
      this.filter_text = "";
      this.course_staff_visible = false;
    },
    openDialog() {
      console.log(this.department_id);
      this.getStaffTreeList();
      this.tree_node = [];
      this.filter_text = "";
      this.course_staff_visible = true;
    },
    getStaffTreeList() {
      const { department_id, is_leave } = this;
      // const is_contain_leave = is_leave;
      const params = {
        is_contain_leave: is_leave
      };
      if (department_id) {
        params.department_id = department_id;
      } else {
        params.department_id = this.school_id;
      }
      if (this.isCustomize || this.office_post_name) {
        params.office_post_name = "教务";
      }
      staffApi
        .getEmployeePreview(params)
        .then((res) => {
          this.loading = false;
          res.data.node_key = this.$uuid.v1();
          this.staff_list = [];
          console.log(this.department_id, this.isWithdraw);
          if (this.department_id) {
            if (!this.isWithdraw) {
              this.staff_list = [res.data];
              // if (typeof this.department_id === "string") {
              //   this.staff_list.push(
              //     this.departmentFilter([res.data], this.department_id)
              //   );
              // } else {
              //   this.department_id.forEach((t) => {
              //     this.staff_list.push(this.departmentFilter([res.data], t));
              //   });
              // }
            } else {
              // 进销存管理 - 创建退领订单时 获取领用过物品的人
              wmsManagementApi
                .getUsePersonList({ article_bank_id: this.kuId })
                .then((result) => {
                  if (+result.data.code === 0 && result.data.data.length > 0) {
                    this.staff_list.push(
                      this.departmentFilter([res.data], this.department_id)
                    );
                    const personList = result.data.data;
                    const filterData = this.staff_list[0].children.filter(
                      (itemA) => {
                        return personList.some(
                          (itemB) => itemB.employee_id === itemA.id
                        );
                      }
                    );
                    this.refreshTree = false;
                    this.$set(this.staff_list[0], "child", filterData);
                    this.$nextTick(() => {
                      this.$forceUpdate();
                      this.refreshTree = true;
                      this.refreshData();
                    });
                  }
                });
            }
          } else {
            // this.school_id.forEach((t) => {
            //   this.staff_list.push(this.departmentFilter([res.data], t));
            // });
            this.staff_list = [res.data];
          }
          console.log(this.staff_list);
          this.refreshData();
          this.sortChildData();
        })
        .catch(() => {
          this.loading = false;
        });
    },
    sortChildData() {
      // 先对每个节点的 children 数组按照 name 字段进行排序
      this.staff_list.forEach((node) => {
        if (node.children && node.children.length > 1) {
          node.children.sort((a, b) => {
            // 根据 name 字段进行升序排序
            return a.name.localeCompare(b.name, "zh-CN", {
              sensitivity: "accent"
            });
          });
        }
      });
    },
    refreshData() {
      this.staff_list.forEach((item) => {
        const fn = (item) => {
          item.children.forEach((item1) => {
            item1.node_key = this.$uuid.v1();
            if (
              this.check_id !== "" &&
              item1.id === this.check_id &&
              item1.type !== "department"
            ) {
              this.check_staff = item1.node_key;
              this.right_staff_list = [{ name: item1.name, id: this.check_id }];
              this.tree_node.push(item1);
            }
            if (item1.children) fn(item1);
            return item1;
          });
        };
        if (item.children) fn(item);
      });
    },
    departmentFilter(val, department_id) {
      let obj = {};
      console.log(val, department_id);
      val.forEach((t) => {
        const fn = (t) => {
          if (t.id === department_id) {
            obj = t;
          } else {
            if (t.children && t.type === "department") {
              // 对子节点数组按照 name 排序
              t.children.sort((a, b) => a.name.localeCompare(b.name));
              t.children.forEach((t1) => {
                fn(t1);
              });
            }
          }
        };
        fn(t);
      });
      return obj;
    },
    checkChange(val) {
      if (val.type !== "employee") return;

      // 如果员工被禁用，阻止选择并提示
      if (this.isEmployeeDisabled(val.id)) {
        // this.$message.warning("该教师已被使用，无法选择");
        return;
      }

      this.check_staff =
        this.check_staff === val.node_key ? "null" : val.node_key;
      this.tree_node = this.findRepeatData(val.id);
      this.right_staff_list = this.check_staff === "null" ? [] : [val];
    },
    findRepeatData(val) {
      const arr = [];
      this.staff_list.forEach((item) => {
        const fn = (item) => {
          item.children.forEach((item1) => {
            if (item1.id === val.id) {
              arr.push(item1);
            }
            if (item1.children) fn(item1);
          });
        };
        if (item.children) fn(item);
      });
      return arr;
    }
  }
};
</script>
<style lang="less" scoped>
.course-staff-dialog {
  .search {
    display: flex;
    align-items: center;
    width: 100%;
    position: relative;

    img {
      width: 14px;
      height: 14px;
      margin-right: 12px;
      margin-left: 11px;
      position: absolute;
      top: 9px;
      left: 6px;
    }

    .el-input__inner {
      padding-left: 40px;
    }
  }

  /deep/ .el-dialog__body {
    padding: 0 16px 0 16px;
  }

  .tg-dialog__content {
    display: flex;
    flex-direction: row;
    height: 430px;
  }

  .organization-tree-check {
    width: 367px;
    border-right: 1px solid #e0e6ed;
    padding-right: 16px;
    padding-top: 16px;
    height: 414px;
    overflow: auto;
  }

  .staff-list--right {
    width: 366px;
    margin-left: 16px;
    margin-top: 16px;
    height: 414px;
    overflow: auto;

    .organization__title,
    .organization__info {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }

    .all-clear {
      color: #157df0;
      font-size: @text-size_small;
      font-family: @text-famliy_medium;
      cursor: pointer;

      img {
        width: 14px;
        height: 14px;
        margin-right: 8px;
        vertical-align: middle;
        margin-top: -3px;
      }
    }

    .organization__title {
      em {
        font-style: normal;
        color: @base-color;
      }
    }

    .organization__info {
      border: 1px solid @base-color;
      border-radius: 4px;
      height: 40px;
      align-items: center;
      padding: 0 16px;
      margin-top: 16px;

      .el-input,
      .el-input__inner {
        height: 40px;
        line-height: 40px;
      }

      img {
        width: 16px;
        height: 16px;
        cursor: pointer;
      }

      span:nth-child(1) {
        overflow-x: auto;
        width: calc(100% - 36px);
        white-space: nowrap;
      }
    }

    .required {
      &::before {
        content: "*";
        margin-right: 5px;
        color: #ff0317;
      }
    }
  }

  .tree__checkbox.el-checkbox {
    margin-right: 16px;
    margin-top: -1px;
  }

  .is-empty {
    color: @text-color_third;
    width: 100%;
    display: inline-block;
    text-align: center;
    line-height: 350px;
  }
  .custom-tree-node_courseStall {
    display: flex;
    .title_text {
      // width: 150px;
      // overflow: hidden;
      // white-space: nowrap;
      // text-overflow: ellipsis;
    }
  }

  .el-tree-node__content {
    height: 30px;
  }

  .el-tree {
    padding-top: 11px;
  }

  .is-readonly {
    pointer-events: none;
  }

  // 禁用员工的样式
  .is-disabled {
    opacity: 0.6;
    cursor: not-allowed;

    .title_text span {
      color: #c0c4cc;
    }

    &:hover {
      background-color: transparent;
    }
  }

  .border--active {
    position: relative;

    &::after {
      content: "";
      position: absolute;
      width: 100%;
      height: 32px;
      left: -2px;
      top: -2px;
      border: 2px solid #ebf4ff;
      border-radius: 6px;
      z-index: 10;
    }

    .el-input__inner {
      border-color: @base-color;
    }
  }

  .tg-input__inner {
    height: 32px;
    line-height: 32px;
    -webkit-appearance: none;
    background-color: #fff;
    background-image: none;
    border-radius: 4px;
    border: 1px solid #d3dce6;
    box-sizing: border-box;
    color: #1f2d3d;
    display: inline-block;
    font-size: inherit;
    outline: 0;
    padding-right: 15px;
    padding-left: 38px;
    transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    width: 100%;

    &::placeholder {
      color: #8492a6;
    }

    &::-webkit-input-placeholder {
      /* WebKit browsers 适配谷歌 */
      color: #8492a6;
    }

    &:-moz-placeholder {
      /* Mozilla Firefox 4 to 18 适配火狐 */
      color: #8492a6;
    }

    &::-moz-placeholder {
      /* Mozilla Firefox 19+ 适配火狐 */
      color: #8492a6;
    }

    &:-ms-input-placeholder {
      /* Internet Explorer 10+  适配ie*/
      color: #8492a6;
    }
  }
}
</style>
<style lang="less" scoped>
.course-staff {
  .btn__img--dotted {
    margin-right: 10px;
  }
}
</style>
