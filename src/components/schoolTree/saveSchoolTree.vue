<template>
  <el-dialog
    :visible="true"
    v-if="flag"
    :modal="type === 'school' ? false : has_modal"
    :append-to-body="type === 'school' ? false : has_modal"
    :width="is_center ? '1016px' : type === 'school' ? '700px' : '1016px'"
    :close-on-press-escape="false"
    class="school-dialog"
    :class="{
      'school-select-dialog': type === 'school' && is_center != true,
      'multiple-dialog': type !== 'radio',
      'radio-dialog': type === 'radio' || is_center
    }"
    :show-close="type === 'school' ? false : true"
    :before-close="handleClose"
  >
    <div slot="title">
      <div v-if="type === 'school'" class="el-dialog__title school-title">
        <img src="@/assets/图片/icon_location.png" />
        <span>校区选择</span>
      </div>
      <span v-else-if="type === 'isLook'"> 已选择校区： </span>
      <span v-else class="el-dialog__title">请选择校区</span>
    </div>
    <div style="height: 100%">
      <!-- <el-input placeholder="输入关键字进行过滤" v-model="filterText">
        </el-input> -->
      <el-form
        :model="search"
        :inline="true"
        ref="school"
        v-if="type != 'isLook'"
      >
        <el-form-item label="校区名称">
          <el-input
            clearable
            placeholder="请输入校区名称"
            v-model="search.schoolName"
          />
        </el-form-item>
        <el-form-item label="区域名称">
          <el-select
            v-model="search.partName"
            :popper-append-to-body="false"
            clearable
            class="tg-input--custom"
            placeholder="请选择区域名称"
          >
            <el-option
              v-for="(item, index) in areaList"
              :label="item"
              :value="item"
              :key="index"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-button
          class="tg-button--primary btn"
          type="primary"
          @click="searchVal"
        >
          <img src="../../assets/图片/icon_search.png" />查询</el-button
        >
      </el-form>
      <el-checkbox
        class="checkbox-container"
        v-if="type !== 'radio' && type !== 'isLook'"
        :indeterminate="isIndeterminate"
        v-model="all_checked"
        @change="handleCheckAllChange"
        >全选</el-checkbox
      >

      <div class="tg-tree--custom" ref="checkboxView">
        <div v-if="schoolList.length">
          <template v-for="area in schoolList">
            <div
              :key="area.id"
              v-if="
                !area.isHide &&
                (type === 'isLook'
                  ? area.child.some((item) => item.checked)
                  : true)
              "
              class="leaf"
            >
              <div
                @click.stop="expanded(area, $event)"
                class="title"
                :class="{ 'title-ac': area.expanded }"
              >
                <span
                  class="expanded"
                  :class="{ active: area.expanded }"
                ></span>
                <span
                  v-if="type !== 'radio' && type !== 'isLook'"
                  style="height: 40px; display: flex; align-items: center"
                  @click.stop="() => {}"
                >
                  <el-checkbox
                    :indeterminate="isIndeterminate"
                    v-model="area.checked"
                    @change="handleAreaAllChange(area)"
                  ></el-checkbox>
                </span>

                <span class="pos__icon"></span>
                <span class="name">{{ area.name }}</span>
                <span class="num"
                  >({{ area.child ? area.child.length : 0 }})</span
                >
              </div>
              <div
                class="content"
                :class="{ show: area.expanded }"
                v-if="area.child && area.child.length"
              >
                <template v-for="(sch, index) in area.child">
                  <el-checkbox
                    :title="sch.name"
                    ref="checkbox"
                    :key="sch.id"
                    v-if="!sch.isHide && type !== 'isLook'"
                    :disabled="sch.disabled"
                    v-model="sch.checked"
                    @change="
                      type == 'radio'
                        ? checkChangeRadio(sch.id, area)
                        : checkChange(area)
                    "
                    >{{ sch.name | sub_str }}</el-checkbox
                  >
                  <div
                    v-else-if="sch.checked && type == 'isLook'"
                    style="width: 33%; display: inline-block; margin: 5px 0"
                    :key="index"
                  >
                    {{ sch.name | sub_str }}
                  </div>
                </template>
              </div>
              <div v-else class="content show no-data">无数据</div>
            </div></template
          >
        </div>
        <div v-else class="no-data">无数据</div>
      </div>
    </div>
    <div class="dialog-footer" slot="footer" v-if="type !== 'isLook'">
      <div class="school-choose">
        <span v-if="type == 'radio' && choose"
          ><em>已选：</em>{{ choose }}</span
        >
        <span v-else-if="type !== 'radio' && checkedNums" class="footer-label"
          >已选择校区数量：<span class="footer-num">{{ checkedNums }}</span>
          <span class="tg-school-default">
            <el-checkbox label="设置默认" v-model="isDefault"> </el-checkbox>
            <el-popover
              placement="top-start"
              title=""
              width="200"
              trigger="hover"
              content="勾选后，退出登录时会保留本次选择的校区"
            >
              <img slot="reference" src="@/assets/图片/wenhao.png" alt="" />
            </el-popover>
          </span>
        </span>
        <span v-else></span>
      </div>
      <div>
        <el-button type="plain" @click="handleClose" class="tg-button--plain"
          >取消</el-button
        >
        <el-button type="primary" @click="really" class="tg-button--primary"
          >确定</el-button
        >
      </div>
    </div>
  </el-dialog>
</template>
<script>
import organizationApi from "@/api/organization";
export default {
  data() {
    return {
      filterText: "",
      initSchoolList: [],
      schoolList: [],
      defaultProps: {
        children: "child",
        label: "name"
      },
      isDefault: true,
      choose: "",
      treeId: "",
      all_checked: false,
      isIndeterminate: false,
      search: {
        partName: "", // 区域id
        schoolName: "" // 校区名称
      },
      areaList: [],
      new_add_ids: ""
    };
  },
  props: {
    // 弹窗的显示隐藏
    flag: Boolean,
    id: [String, Array],
    name: [String, Array],
    // type有三种值：
    // 1. radio -- 单选校区，不联动右上角校区总览
    // 2. school -- 多选，联动右上角校区总览
    // 3. chooseSchool --多选校区，不联动右上角校区总览
    // 4.isLook --查看校区
    type: String,
    // 是否有遮罩层
    has_modal: {
      type: Boolean,
      default: true
    },
    // 是否控制传入的校区不能被操作
    controlSchool: {
      type: Boolean,
      default: false
    },
    // 是否控制传入的校区不能被操作且是否被勾选
    checkedControlSchool: {
      type: Boolean,
      default: false
    },
    useData: {
      type: Array
    },
    // 是否使用右上角选中校区的数据
    use_store_options: {
      type: Boolean,
      default: false
    },
    // 是否必须选中值，点击确定才能关闭
    required: {
      type: Boolean,
      default: false
    },
    // type为‘school’时，是否联动右上角校区总览数据
    interconnection: {
      type: Boolean,
      default: true
    },
    // 是否居中显示
    is_center: {
      type: Boolean,
      default: false
    },
    // 缓存校区的key
    schoolStorageKey: {
      type: String,
      default: "homeSchool"
    }
  },
  computed: {
    school_id() {
      return this.$store.getters.doneGetSchoolId;
    },
    checkedNums() {
      let n = 0;
      this.schoolList.map((item) => {
        item.child.map((child) => {
          if (child.checked) {
            n++;
          }
        });
      });
      return n;
    },
    all_school_num() {
      let n = 0;
      this.schoolList.map((item) => {
        item.child.map(() => {
          n++;
        });
      });
      return n;
    }
  },
  watch: {
    flag: {
      handler(val) {
        if (val) {
          // 打开编辑方面
          this.$nextTick(() => {
            this.use_store_options
              ? this.getStoreSchoolTree()
              : this.getSchoolTree();
            this.$refs?.school?.clearValidate();
          });
        }
      },
      immediate: true
    },
    schoolList: {
      handler(val) {
        const arr = [];
        val.map((item) => {
          item.child.map((child) => {
            if (child.checked && !child.disabled) {
              arr.push(child.id);
            }
          });
        });
        this.new_add_ids = arr;
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    const locSchool = localStorage.getItem(this.schoolStorageKey);
    this.isDefault = !!locSchool;
    // if (locSchool) {
    //   const arr = locSchool.split(",");
    //   this.$store.commit("setHeaderSchoolId", arr);
    //   sessionStorage.setItem("schoolId", arr);
    // }
  },
  methods: {
    // 所有全选
    handleCheckAllChange(check) {
      this.schoolList.map((item) => {
        item.child.map((child) => {
          if (check) {
            child.checked = true;
          } else {
            if (!child.disabled) {
              child.checked = false;
            }
          }
        });
        // 区域上的选中状态
        item.checked = check;
      });
    },
    // 区域全选
    handleAreaAllChange(area) {
      area.child.map((item) => {
        item.checked = area.checked;
      });
      const bool = this.schoolList.every((c) => c.checked === true);
      this.all_checked = bool;
    },
    expanded(area, event) {
      event.stopPropagation();
      area.expanded = !area.expanded;
      // eslint-disable-next-line no-undef
      $(event.target).parents(".leaf").children(".content").slideToggle();
    },
    handleClose() {
      this.search = {
        partName: "",
        schoolName: ""
      };
      this.$emit("update:flag", false);
      this.$emit("close");
    },
    set_property() {
      this.initSchoolList.map((item, i) => {
        item.child.map((child, j) => {
          const obj = Object.assign({}, child, {
            checked: false,
            disabled: false
          });
          this.$set(this.initSchoolList[i].child, j, obj);
        });
        this.$set(this.initSchoolList[i], "expanded", true); // 初始全展开
      });
    },
    init_school_data(ids) {
      this.set_property();
      ids = ids || this.new_add_ids || [];
      this.initSchoolList.map((item) => {
        item.child.map((child) => {
          ids.map((id) => {
            if (child.id === id) {
              if (this.controlSchool) {
                console.log(this.checkedControlSchool);
                Object.assign(child, {
                  disabled: true,
                  checked: this.checkedControlSchool
                });
              } else {
                Object.assign(child, {
                  checked: true,
                  disabled: false
                });
              }
            }
          });
        });
        // 设置区域上的复选框状态
        const bool = item.child.every((c) => c.checked === true);
        this.$set(item, "checked", bool);
      });
      console.log(this.initSchoolList);
      this.schoolList = this.initSchoolList;
      console.log(this.schoolList);
      if (this.type === "school") {
        setTimeout(() => {
          this.positionChecked();
        }, 200);
      }
    },
    // 自动滚动到选中的第1个校区位置
    positionChecked() {
      const firstCheckedElm = this.$refs.checkbox.filter(
        (i) => i._props.value
      )[0];
      const firstCheckedParentElm = firstCheckedElm.$vnode.elm.closest(".leaf");
      console.log(firstCheckedElm.$vnode.elm, firstCheckedParentElm);
      const offsetTop = firstCheckedParentElm
        ? firstCheckedParentElm.offsetTop
        : 0;
      const bodyElm = this.$refs.checkboxView;
      const marginTop = 16;
      console.log(offsetTop, bodyElm.offsetTop);
      bodyElm.scrollBy({
        top: offsetTop - bodyElm.offsetTop - marginTop
        // behavior: "smooth"
      });
    },
    getStoreSchoolTree(data) {
      this.initSchoolList = [];
      this.areaList = [];
      organizationApi.getSchoolPart(data).then((res) => {
        res.data.school_data.forEach((item) => {
          const obj = JSON.parse(JSON.stringify(item));
          obj.child = [];
          item.child.forEach((item1) => {
            if (this.school_id.indexOf(item1.id) > -1) {
              obj.child.push(item1);
            }
          });
          if (obj.child.length > 0) this.initSchoolList.push(obj);
        });
        this.initSchoolList.map((item) => {
          this.areaList.push(item.name);
        });
        if (this.id && !Array.isArray(this.id)) {
          this.new_add_ids.push(this.id);
        } else {
          this.new_add_ids = this.id;
        }
        this.init_school_data();

        this.choose = this.name;
      });
    },
    getSchoolTree(data) {
      organizationApi.getSchoolPart(data).then((res) => {
        this.areaList = res.data.area_data;
        this.initSchoolList = res.data.school_data;
        if (this.controlSchool) {
          console.log(this.useData);
          this.init_school_data(this.useData);
        } else {
          console.log(this.id, "this.id");
          this.init_school_data(this.id ? this.id.toString().split(",") : []);
        }
        this.choose = this.name;
        const { all_school_num, checkedNums } = this;
        this.all_checked = all_school_num === checkedNums;
      });
    },
    really() {
      if (this.type === "school") {
        if (this.new_add_ids.length === 0) {
          this.$message.error("请至少选择一个校区");
          return false;
        }
        const areaIdList = [];
        const name = [];
        const ids = this.new_add_ids;
        this.schoolList.forEach((el) => {
          for (let k = 0; k < el.child.length; k++) {
            if (ids.indexOf(el.child[k].id) !== -1) {
              areaIdList.push(el.id);
              return;
            }
          }
        });
        this.schoolList.map((item) => {
          name.push(
            ...item.child
              .map((child) => {
                if (child.checked) {
                  return child.name;
                }
              })
              .filter((item) => item)
          );
        });
        this.choose = name.toString();
        this.$store.commit("setHeaderSchoolId", this.new_add_ids);
        this.$store.commit("setHeaderSchoolName", this.choose);
        this.$store.commit("setHeaderAreaId", areaIdList);
        sessionStorage.setItem("schoolId", this.new_add_ids);
        sessionStorage.setItem("schoolName", this.choose);
        sessionStorage.setItem("areaId", areaIdList.toString());
        const { isDefault, new_add_ids, schoolStorageKey } = this;
        if (isDefault) {
          console.log(new_add_ids);
          localStorage.setItem(schoolStorageKey, new_add_ids);
        } else {
          localStorage.removeItem(schoolStorageKey);
        }
      }
      if (this.type === "chooseSchool") {
        const name = [];
        this.schoolList.map((item) => {
          name.push(
            ...item.child
              .map((child) => {
                if (child.checked) {
                  return child.name;
                }
              })
              .filter((item) => item)
          );
        });
        this.choose = name.toString();
      }
      if (this.new_add_ids == null) return false;
      this.$emit(
        "update:id",
        this.type === "radio"
          ? this.new_add_ids[0]
          : this.type === "chooseSchool"
          ? this.new_add_ids.toString()
          : this.new_add_ids
      );
      this.$emit("update:name", this.choose);
      this.$emit(
        "confirm",
        this.type === "radio"
          ? this.new_add_ids[0]
          : this.type === "chooseSchool"
          ? this.new_add_ids.toString()
          : this.new_add_ids,
        this.schoolList
      );
      if (!this.required) {
        this.$emit("update:flag", false);
        this.$emit("close");
      }
    },
    searchVal() {
      this.schoolList.map((item) => {
        item.is = false;
        item.child.map((child) => {
          if (this.search.schoolName || this.search.partName) {
            if (item.name === this.search.partName) {
              if (this.search.schoolName) {
                if (child.name.indexOf(this.search.schoolName) !== -1) {
                  item.is = true;
                  child.isHide = false;
                  item.isHide = false;
                } else {
                  item.isHide = !item.is;
                  child.isHide = true;
                }
              } else {
                item.isHide = false;
                child.isHide = false;
              }
            } else {
              if (this.search.partName) {
                item.isHide = true;
              } else {
                if (child.name.indexOf(this.search.schoolName) !== -1) {
                  item.is = true;
                  child.isHide = false;
                  item.isHide = false;
                } else {
                  item.isHide = !item.is;
                  child.isHide = true;
                }
              }
            }
          } else {
            item.isHide = false;
            child.isHide = false;
          }
        });
      });
      this.$forceUpdate();
      const { all_school_num, checkedNums } = this;
      this.all_checked = all_school_num === checkedNums;
    },
    checkChange(area) {
      const { all_school_num, checkedNums } = this;
      this.all_checked = all_school_num === checkedNums;
      // 设置区域上的复选框状态
      const bool = area.child.every((c) => c.checked === true);
      area.checked = bool;
    },
    checkChangeRadio(id, area) {
      this.initSchoolList.map((item) => {
        item.child.map((child) => {
          if (child.id !== id) {
            child.checked = false;
          } else {
            if (child.checked === false) {
              this.choose = "";
            } else {
              this.choose = child.name;
            }
          }
        });
      });
      // 设置区域上的复选框状态
      const bool = area.child.every((c) => c.checked === true);
      area.checked = bool;
    }
  }
};
</script>
<style lang="less">
.tg-tree--custom {
  padding-top: 16px;
  box-sizing: border-box;
  //   height: calc(100% - 86px);
  overflow: auto;
  .leaf {
    margin-bottom: 16px;
    .title {
      background-color: #f5f8fc;
      height: 40px;
      border: 1px solid #2d80ed;
      border-radius: 4px;
      padding: 10px 15px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      cursor: pointer;
      .expanded {
        background-image: url("../../assets/图片/icon_plus.png");
        width: 16px;
        height: 16px;
        display: block;
        background-size: cover;
        margin-right: 17px;
      }
      .active {
        background-image: url("../../assets/图片/icon_minus.png");
      }
      .pos__icon {
        background-image: url("../../assets/图片/icon_position.png");
        width: 10px;
        height: 14px;
        display: block;
        background-size: cover;
        margin-right: 5px;
      }
      .name {
        font-size: 14px;
        font-weight: 500;
        margin-right: 10px;
      }
      .num {
        font-size: 14px;
        color: #8492a6;
      }
    }
    .title-ac {
      background: linear-gradient(90deg, #d6eeff 0%, #e9f6ff 100%);
    }
    .content {
      width: 100%;
      box-sizing: border-box;
      margin-top: 12px;
      margin-left: 22px;
      padding: 6px 0 4px 26px;
      border-left: 2px solid #e7f0f8;
      // display: none;
      overflow: hidden;
      .el-checkbox {
        margin-right: 10px;
        // padding-bottom: 12px;
        width: 33%;
      }
    }
  }
  .no-data {
    text-align: center;
    color: #c1c3c5;
  }
}
.school-dialog {
  .school-tree {
    padding: 8px 0;
    border-top: 1px solid #ecedf2;
    height: 296px;
    overflow: auto;
  }
  .el-dialog__body {
    padding: 16px !important;
    border-bottom: 1px solid #f1f1f1;
    height: 520px;
    box-sizing: border-box;
    .tg-tree--custom {
      height: calc(100% - 33px);
    }
  }
  .dialog-footer {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    height: 32px;
    // padding-top: 16px;
  }

  .school-choose {
    font-weight: normal;
    text-align: left;
    font-size: 14px;
    width: 520px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    color: @text-color_second;
    .tg-school-default {
      display: flex;
      align-items: center;
      margin-left: 20px;
      img {
        width: 16px;
        vertical-align: sub;
      }
    }
  }

  .el-tree-node__content {
    height: 30px;
  }
  .el-form-item__label {
    line-height: 32px;
    height: 32px;
  }
  .el-tree-node__expand-icon {
    margin-right: 0 !important;
  }
  .el-checkbox {
    // margin-right: 13px !important;

    // width: 25%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    padding-right: 10px;
    box-sizing: border-box;
    margin-right: 0 !important;
    :nth-child(3n) {
      padding-right: 0;
    }

    .el-checkbox__inner {
      width: 16px;
      height: 16px;
      &:after {
        left: 5px;
        top: 2px;
      }
    }
  }
  .school-tree > .el-tree-node > .el-tree-node__content > .custom-tree-node {
    margin-left: 16px;
  }
  .el-dialog__footer {
    border-top: none !important;
  }
  .el-tree-node__expand-icon {
    background: url("../../assets/图片/icon_school_tree_down.png") !important;
    background-size: cover !important;
    width: 10px !important;
    height: 6px !important;
    padding: 0 !important;
  }
  .el-tree-node.is-expanded
    .expanded.el-tree-node__expand-icon.el-icon-caret-right {
    background: url("../../assets/图片/icon_school_tree_top.png") !important;
    background-size: cover !important;
  }
  .school-tree--select {
    height: 155px;
    overflow: auto;
  }
  .school-tree--select .el-tree-node__expand-icon.el-icon-caret-right {
    margin-right: 16px !important;
  }
  .el-dialog .el-form-item,
  .el-form-item__content,
  .el-tree-node__content {
    line-height: 32px;
  }
  .el-dialog .el-form-item {
    // height: 32px;
    margin-bottom: 0;
  }
  .el-dialog .el-form {
    line-height: 32px;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }
  .el-dialog__header {
    background: #f5f8fc;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    &:after {
      background-color: transparent !important;
    }
  }
  button.btn {
    padding: 0 11px;
    width: 72px;
    span {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    img {
      width: 11px;
      height: 12px;
      margin-right: 8px;
    }
  }
  .custom-tree-node {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  em {
    font-style: normal;
    color: @base-color;
  }
}
.school-select-dialog {
  width: 700px;
  // height: 372px;
  top: 57px;
  right: 0;
  left: unset;
  bottom: unset;
  position: absolute;
  .el-dialog {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    position: relative;
    top: 0;
    left: 0;
    transform: inherit;
    max-width: 100% !important;
    .el-dialog__title {
      font-weight: normal !important;
    }
    .el-dialog__header {
      padding-left: 16px;
      border-bottom: none !important;
    }
  }
  .el-dialog__body {
    height: 393px;
    .tg-tree--custom {
      height: calc(100% - 86px);
    }
    .content {
      line-height: 30px;
    }
  }
  .el-form {
    padding-bottom: 16px;
    border-bottom: 1px solid #ecedf2;
  }
  .school-tree--select {
    height: 294px;
    .el-tree-node__expand-icon {
      margin-left: 7px;
    }
  }
  .el-tree-node__children {
    .el-tree-node__content {
      margin-left: 12px;
    }
  }
  .el-checkbox:nth-child(4n) {
    // margin-right: 13px !important;
    padding-right: 0;
  }
  .school-choose {
    width: 480px;
    color: @text-color_second;
  }
  .tg-input--custom .el-select-dropdown.el-popper {
    left: 0 !important;
  }
}
.checkbox-container {
  height: 20px;
  width: 100%;
  display: flex;
  margin-top: 16px;
}

.footer-label {
  color: #2d80ed;
  font-size: 14px;
  display: flex;
  align-items: center;
  .footer-num {
    color: #475669;
  }
}
.radio-dialog {
  .el-dialog__body {
    .tg-tree--custom {
      height: calc(100% - 33px);
    }
  }
}
.multiple-dialog {
  .el-dialog__body {
    .tg-tree--custom {
      height: calc(100% - 86px);
    }
  }
}
.school-title {
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 55px;
  img {
    width: 24px;
    height: 24px;
    margin-right: 8px;
  }
}
</style>
