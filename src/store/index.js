import Vue from "vue";
import Vuex from "vuex";

import actions from "./actions";
import mutations from "./mutations";

import edit from "./edit/index";
import user from "./user/index";
import app from "@/store/module/app";
import login from "@/store/module/login";
import staff from "@/store/module/staff";
import channel from "@/store/module/channel";
import organization from "@/store/module/organization";
import outbound from "@/store/module/outbound";
import classroomManagement from "@/store/module/classroomManagement";
import regionalManagement from "@/store/module/regionalManagement";
import postManagement from "@/store/module/postManagement";
import roleManagement from "@/store/module/roleManagement";
import permission from "@/store/module/permission"; // 权限
import invitationManagement from "@/store/module/invitationManagement";
import marketStudent from "@/store/module/marketStudent";
import classTime from "@/store/module/classTime";
import classManagement from "@/store/module/classManagement";
import curriculum from "@/store/module/curriculum";
import offerManagement from "@/store/module/offerManagement";
import schoolServiceScheduling from "@/store/module/schoolServiceScheduling";
import audition from "@/store/module/audition";
import workflow from "@/store/module/workflow";
import workflowSet from "@/store/module/workflowSet";
import dictionary from "@/store/module/dictionary"; // 后台字典接口
import cockpit from "@/store/module/cockpit";
import weihuAudio from "@/store/module/weihuAudio"; // 外呼音频播放
import drawingBoard from "@/store/module/living/drawingBoard";
import trtc from "@/store/module/living/trtc";
import userFeedback from "@/store/module/userFeedback";
Vue.use(Vuex);

export default new Vuex.Store({
  state: {
    bannerList: []
  },
  getters: {},
  mutations,
  actions,
  modules: {
    app,
    login,
    staff,
    channel,
    organization,
    outbound,
    classroomManagement,
    regionalManagement,
    postManagement,
    roleManagement,
    edit,
    user,
    permission,
    invitationManagement,
    marketStudent,
    classTime,
    classManagement,
    curriculum,
    offerManagement,
    schoolServiceScheduling,
    audition,
    workflow,
    workflowSet,
    dictionary,
    cockpit,
    drawingBoard,
    trtc,
    weihuAudio,
    userFeedback
  }
});
