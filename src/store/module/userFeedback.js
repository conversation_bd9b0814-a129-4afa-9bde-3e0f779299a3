import userFeedbackApi from "../../api/userFeedback.js";

const state = {
  replyFeedback: {}
};
const mutations = {
  setReplyFeedback(state, replyFeedback) {
    state.replyFeedback = replyFeedback;
  }
};
const getters = {
  doneGetReplyFeedback: (state) => {
    return state.replyFeedback;
  }
};
const actions = {
  async toReplyFeedback(context, data) {
    const response = await userFeedbackApi.replyFeedback(data);
    if (response && !response.data.err) {
      context.commit(
        "setReplyFeedback",
        response.status === 200 ? "success" : response.data.message
      );
    }
  }
};
export default {
  state,
  mutations,
  getters,
  actions
};
