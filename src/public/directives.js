import permissionInfo from "./permissionFront";
import store from "../store/index";
// import { copyText } from "@/public/copyText";
export default (Vue) => {
  Vue.directive("has", {
    // 添加指令用于对点击事件节流
    inserted: function (el, binding) {
      if (!Vue.prototype.$_has(binding.value)) {
        el.parentNode.removeChild(el);
      }
    }
  });
  Vue.prototype.$_has = function (value) {
    const newVal = permissionInfo[value.m][value.o];
    let isExist = false;
    const permission_info = store.getters.doneGetPermissionInfo;
    if (permission_info.includes("is_admin")) {
      isExist = true;
    } else {
      // console.log("permission_info :>> ", permission_info);
      if (permission_info.includes(newVal)) {
        isExist = true;
      } else {
        isExist = false;
      }
    }
    return isExist;
  };
  // 注册一个全局自定义复制指令 `v-copy`
  Vue.directive("copy", {
    bind(el, { value }) {
      el.className = "copy_img";
      el.$value = value;
      el.handler = () => {
        el.style.position = "relative";
        if (!el.$value) {
          Vue.prototype.$message.success("无复制内容");
          return;
        }
        // 动态创建 textarea 标签
        const textarea = document.createElement("textarea");
        // 将该 textarea 设为 readonly 防止 iOS 下自动唤起键盘，同时将 textarea 移出可视区域
        textarea.readOnly = "readonly";
        textarea.style.position = "absolute";
        textarea.style.top = "0px";
        textarea.style.left = "-9999px";
        textarea.style.zIndex = "-9999";
        // 将要 copy 的值赋给 textarea 标签的 value 属性
        textarea.value = el.$value;
        // 将 textarea 插入到 el 中
        el.appendChild(textarea);
        console.log("textarea.select() :>> ", textarea.createTextRange);
        // 兼容IOS 没有 select() 方法
        if (textarea.createTextRange) {
          textarea.select(); // 选中值并复制
        } else {
          textarea.setSelectionRange(0, el.$value.length);
          textarea.focus();
        }
        const result = document.execCommand("Copy");
        if (result) Vue.prototype.$message.success("复制成功");
        el.removeChild(textarea);
      };
      el.addEventListener("click", el.handler); // 绑定点击事件
    },
    // 当传进来的值更新的时候触发
    componentUpdated(el, { value }) {
      el.$value = value;
    },
    // 指令与元素解绑的时候，移除事件绑定
    unbind(el) {
      el.removeEventListener("click", el.handler);
    }
  });
  // // 注册一个全局自定义复制指令 `v-copy`
  // Vue.directive("drag", {
  //   inserted: function (el) {
  //     // el.style.cursor = 'move'（用来设置鼠标作用时的样式）
  //     el.onmousedown = function (e) {
  //       // 这里是设置dom（只有鼠标在class为header的dom上时才可以拖动）

  //       if (!(e.target.className.indexOf("drag-target") > 0)) {
  //         // el.style.cursor = 'default'
  //         console.log("2222 :>> ", 2222);
  //         document.onmousemove = document.onmouseup = null;
  //         return;
  //       }
  //       // el.style.cursor = 'move'
  //       console.log(e.target);
  //       // 获取当前鼠标在dom中的位置
  //       const disx = e.clientX - el.offsetLeft;
  //       const disy = e.clientY - el.offsetTop;
  //       document.onmousemove = function (e) {
  //         // 获取移动后当前鼠标距离左边以及上边的距离
  //         let x = e.clientX - disx;
  //         let y = e.clientY - disy;
  //         // 获取鼠标在左右、上下所能移动的最大距离
  //         const maxX =
  //           document.body.clientWidth -
  //           parseInt(window.getComputedStyle(el).width);
  //         const maxY =
  //           document.body.clientHeight -
  //           parseInt(window.getComputedStyle(el).height);
  //         if (x < 0) {
  //           x = 0;
  //         } else if (x > maxX) {
  //           x = maxX;
  //         }

  //         if (y < 0) {
  //           y = 0;
  //         } else if (y > maxY) {
  //           y = maxY;
  //         }

  //         el.style.left = x + "px";
  //         el.style.top = y + "px";
  //       };
  //       document.onmouseup = function () {
  //         document.onmousemove = document.onmouseup = null;
  //       };
  //     };
  //   }
  // });
};
