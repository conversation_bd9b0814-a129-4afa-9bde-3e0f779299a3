const permissionInfo = {
  // 线索
  clue: {
    list: "/api/market-service/clue/list",
    create: "/api//market-service/clue/create",
    delete: "/api/market-service/clue/delete",
    export: "/api/market-service/clue/export",
    import: "/api/market-service/clue/import",
    info: "/api/market-service/clue/info",
    to_intention: "/api/market-service/clue/intention",
    batch_update: "/api/market-service/clue/many-update",
    update: "/api/market-service/clue/update",
    batch_delete: "/api/market-service/clue/many-delete"
  },
  // 意向客户
  intention: {
    list: "/api/market-service/customer/list",
    create: "/api/market-service/customer/create",
    delete: "/api/market-service/customer/delete",
    export: "/api/market-service/customer/export",
    import: "/api/market-service/customer/import",
    info: "/api/market-service/customer/info",
    batch_update: "/api/market-service/customer/many-update",
    batch_delete: "/api/market-service/customer/many-delete",
    update: "/api/market-service/customer/update",
    to_audition: "/api/market-service/customer/to-audition",
    to_student: "/api/market-service/customer/to-student",
    updateTag: "/api/market-service/customer/updateLabel",
    updatebindState: "/api/market-service/customer/update-addition-info",
    channel_update: "/api/market-service/customer/info/channel/update/front",
    transferSchoolApprove:
      "/api/market-service/customer/transfer-school-approve",
    transferSchoolList: "/api/market-service/customer/transfer-school-list",
    batch_channel_update:
      "/api/market-service/customer/info/channel/batch-update/front"
  },
  // 沟通记录
  communication: {
    list: "/api/market-service/communication/list",
    info: "/api/market-service/communication/info",
    create: "/api/market-service/communication/create",
    update: "/api/market-service/communication/update",
    communication_list: "/api/market-service/communication/list",
    customer_used_list: "/api/coupon-service/coupon-detail/customer-used-list",
    export: "/api/market-service/communication/export"
  },
  // 校区优惠卷管理
  campus: {
    info: "/api/coupon-service/coupon-template/info",
    check: "/api/coupon-service/coupon-detail/list"
  },
  // ai电话
  aiphone: {
    create: "/aiphone-service/group/create", // 任务组
    no_redo: "/aiphone-service/group/no-connect-redo",
    all_redo: "/aiphone-service/group/all-redo",
    task_list: "/aiphone-service/task/list", // 任务列表
    info: "/aiphone-service/group/info",
    export: "/aiphone-service/task/export",
    task_info: "/aiphone-service/task/info"
  },
  // 渠道
  channel: {
    list: "/api/market-service/channel/list",
    create: "/api/market-service/channel/create",
    update: "/api/market-service/channel/update",
    delete: "/api/market-service/channel/delete",
    sync: "/api/market-service/channel/sync"
  },
  // 子渠道
  subchannel: {
    create: "/api/market-service/subchannel/create",
    update: "/api/market-service/subchannel/update",
    delete: "/api/market-service/subchannel/delete"
  },
  // 渠道费用
  channelfee: {
    create: "/api/market-service/channelfee/create", // 费用后面拆
    delete: "/api/market-service/channelfee/delete",
    info: "/api/market-service/channelfee/info"
  },
  // subchannelfee:{
  //   create:""
  // }
  // 组织
  organization: {
    create: "/api/organization-service/department/create", // 组织管理
    update: "/api/organization-service/department/update",
    delete: "/api/organization-service/department/delete",
    school: "/api/organization-service/overr-view/school", // 获取校区
    department_list: "/api/organization-service/overr-view/info"
  },
  // 员工
  employee: {
    create: "/api/organization-service/employee/create",
    export: "/api/organization-service/employee/manage-export",
    openclose: "/api/organization-service/employee/open-or-close",
    update: "/api/organization-service/employee/update",
    delete: "/api/organization-service/employee/delete",
    permission: "/api/permission-service/employee/info",
    permission_create: "/api/permission-service/employee/update",
    info: "/api/organization-service/employee/info",
    sync_wechat: "/api/organization-service/employee/sync-corp-wechat",
    unbind_openid: "/api/organization-service/employee/unbindEmployee"
  },
  // 教室
  classroom: {
    create: "/api/school-service/schoolroom/create",
    info: "/api/school-service/schoolroom/info",
    delete: "/api/school-service/schoolroom/delete",
    update: "/api/school-service/schoolroom/update",
    divid: "/api/school-service/classroom/add-student",
    shift: "/api/school-service/classroom/shift-student"
    // sendSurvery: "/api/school-service/classroom/sendSurvery/front"
  },
  // 区域
  area: {
    create: "/api/organization-service/area/create",
    info: "/api/organization-service/area/info",
    delete: "/api/organization-service/area/delete",
    update: "/api/organization-service/area/update"
  },
  // 岗位
  post: {
    create: "/api/organization-service/office-post/create",
    info: "/api/organization-service/office-post/info",
    delete: "/api/organization-service/office-post/delete",
    update: "/api/organization-service/office-post/update"
  },
  // 角色
  role: {
    create: "/api/permission-service/role/create",
    info: "/api/permission-service/role/info",
    delete: "/api/permission-service/role/delete",
    update: "/api/permission-service/role/update",
    permission: "/api/permission-service/role-permission/info",
    permission_update: "/api/permission-service/role-permission/update",
    associated: "/api/permission-service/employee/role/list",
    associated_update: "/api/permission-service/employee/role/update"
  },
  // 财务期间设置
  financeTime: {
    create: "/api/system-service/finance-range/create",
    delete: "/api/system-service/finance-range/delete",
    update: "/api/system-service/finance-range/update",
    info: "/api/system-service/finance-range/info",
    list: "/api/system-service/finance-range/list",
    updateLock: "/api/system-service/finance-range/save-status",
    bindSchoolList: "/api/system-service/finance-department/list",
    updateBindSchool: "/api/system-service/finance-department/create"
  },
  // 优惠方案管理
  couponScheme: {
    list: "/api/coupon-service/plan/list",

    create: "/api/coupon-service/plan/create",
    update: "/api/coupon-service/plan/update",
    delete: "/api/coupon-service/plan/delete"
  },
  // 邀约
  invitation: {
    export: "/api/market-service/invitation/export",
    update: "/api/market-service/invitation/update"
  },
  // 学员信息管理
  student_infor: {
    batch_delete: "/api/student-service/student/delete",
    batch_update: "/api/student-service/student/manyUpdateEducation",
    batch_update_studentCategory:
      "/api/student-service/student/student-category-updates",
    shift: "/api/school-service/classroom/shift-student",
    info: "/api/student-service/student/info",
    update: "/api/student-service/student/update",
    create: "/api/market-service/communication/create",
    commit_create: "/api/market-service/communication-commit/create",
    show_timetable: "/api/school-service/scheduling/time-table",
    show_tuitonTable: "/api/order-service/admin/wallet/good-list",
    show_removeCourseTable: "/api/order-service/admin/deduct/deduct-list",
    show_couponTable: "/api/coupon-service/coupon-detail/student-used-list",
    export: "/api/report-center-service/admin/student/export",
    downloadTemplate: "/api/student-service/student/demoDownload",
    importTemplate: "/api/student-service/student/import",
    outSchool: "/api/student-service/student/outSchool",
    revokeStatus: "/api/student-service/student/revokeStatus",
    backToSchool: "/api/student-service/student/backToSchool",
    dropSchool: "/api/student-service/student/dropSchool",
    recoverToSchool: "/api/student-service/student/recoverToSchool",
    updateTag: "/api/student-service/student/labelUpdate",
    updateBindState: "/api/student-service/student/update-wechat-status",
    updateTutor: "/api/front/student-service/student/tutor", // 修改辅导老师
    // 修改学管师
    updateEducation: "/api/front/student-service/student/updateEducation",
    updateNieDaoLevel: "/api/front/student-service/student/updateNieDaoLevel",
    oneLevelChannel: "/api/front/student-service/student/oneLevelChannel",
    twoLevelChannel: "/api/front/student-service/student/twoLevelChannel",
    show_yuanluobo: "/api/order-service/admin/wallet/allocation-list",
    getArticleList: "/api/order-service/admin/wallet/article-list",
    stuBindList:
      "/api/questionnaire-service/admin/miniProgram/bindStudent/stuBindList",
    cusBindList:
      "/api/questionnaire-service/admin/miniProgram/bindCustomer/cusBindList"
  },
  // 班级
  class: {
    create: "/api/school-service/classroom/create",
    list: "/api/school-service/classroom-student/list",
    listy: "/api/school-service/classroom/list",
    info: "/api/school-service/classroom/info",
    export: "/api/school-service/classroom/export",
    update: "/api/school-service/classroom/update",
    delete: "/api/school-service/classroom/delete",
    divid: "/api/school-service/classroom/add-student", // 入班
    offDuty: "/api/school-service/classroom/remove-student", // 出班
    finished: "/api/school-service/classroom/finished", // 结业
    transfer: "/api/school-service/classroom/shift-student", // 转班
    shift: "/api/school-service/classroom/shift-student", // 调班
    modifyTeacher: "/api/school-service/classroom/update-header-teacher", // 修改班主任
    importTemplate: "/api/api/school-service/classroom/import",
    importStuTemplate: "/api/school-service/classroom/add-student-import",
    parent_class: "/api/feedback/send/front/parent_class", // 发送家长课堂
    class_notice: "/api/feedback/send/front/class_notice", // 发送班级通知
    course_summary: "/api/feedback/send/front/course_summary" // 发送课程总结
  },
  // 开班
  schoolService: {
    create: "/api/school-service/audit-classroom/create",
    list: "/api/school-service/audit-classroom/list",
    info: "/api/school-service/audit-classroom/info",
    export: "/api/school-service/audit-classroom/export",
    update: "/api/school-service/audit-classroom/update",
    delete: "/api/school-service/audit-classroom/delete"
  },
  // 批阅
  communication_commit: {
    create: "/api/market-service/communication-commit/create",
    update: "/api/market-service/communication-commit/update"
  },
  // 排课
  scheduling: {
    cancel: "/api/school-service/scheduling/cancelled",
    delete: "/api/school-service/scheduling/delete",
    specify_by_name: "/api/school-service/scheduling/student/list",
    add_student: "/api/school-service/scheduling/student/add-temporary", // 添加学员（试听）
    remove_trash: "/api/school-service/scheduling/student/remove-trash",
    remove_temp: "/api/school-service/scheduling/student/remove-temp",
    rollback: "/api/school-service/scheduling/student/rollback-trash",
    create: "/api/school-service/scheduling/create",
    info: "/api/school-service/scheduling/info",
    export: "/api/report-center-service/admin/school/schedulingExport",
    revocation: "/api/school-service/scheduling/revocation",
    batch_update_context: "/api/school-service/scheduling/update-context",
    batch_update: "/api/school-service/scheduling/batch-update",
    student_list: "/api/school-service/scheduling/student/list", // 学生李恩表
    temp_list: "/api/school-service/scheduling-shift/list", // 临调列表
    begin_class: "/api/school-service/scheduling/begin",
    move: "/api/school-service/scheduling/move",
    copy: "/api/school-service/scheduling/copy"
  },
  // 临调
  scheduling_shift: {
    shift: "/api/school-service/scheduling-shift/list",
    rollback: "/api/school-service/scheduling-shift/rollback"
  },
  // 试听
  audition: {
    audition: "/api/school-service/audition/list",
    new_class: "/api/school-service/audition/new-classroom",
    list_export: "/api/report-center-service/admin/audition/export",
    record: "/api/school-service/audition/student",
    add_student: "/api/school-service/scheduling/student/add-audition", // 跟班试听
    remove_adution: "/api/school-service/scheduling/student/remove-audition",
    record_export: "/api/front/audition/record/export",
    audition_detail: "/api/school-service/audition/detail-list",
    audition_detail_export: "/api/school-service/audition/detail-export"
  },
  // 补课
  redo: {
    new_class: "/api/school-service/makeup/new-classroom",
    add_student: "/api/school-service/scheduling/student/add-makeup", // 跟班补课
    remove_makeup: "/api/school-service/scheduling/student/remove-make-up",
    export: "/api/school-service/makeup/export",
    delete: "/api/school-service/makeup/delete"
  },
  // 升班
  upgrade: {
    create: "/api/course-service/course-path/create",
    batch_del: "/api/course-service/course-path/delete",
    export: "/api/course-service/course-path/export",
    update: "/api/course-service/course-path/update"
  },
  // 课程
  course: {
    department_list: "/api/course-service/course-price/department/list",
    specifications_list: "/api/course-service/course-price/detail",
    update_basic: "/api/course-service/course-mapping/update", // 修改基本信息
    update_class_basic: "/api/school-service/classroom/info",
    detail: "/api/course-service/course-mapping/detail", // 课程详情
    update_spec: "/api/course-service/course-price/update", // 修改课程规格
    update_authorized_school:
      "/api/course-service/course-price/department/update", // 修改课程授权校区
    update_related_course: "/api/course-service/course-linked/update", // 修改关联课程
    update_priority_course: "/api/course-service/course-linked/update-priority", // 修改关联课程扣款顺序
    update_rules: "/api/course-service/course-rule/update", // 修改开班规则
    add_related_course: "/api/course-service/course-linked/add", // 添加关联课程
    del_related_course: "/api/course-service/course-linked/remove", // 删除关联课程
    related_course_list: "/api/course-service/course-linked/detail",
    create: "/api/course-service/course-mapping/add", // 添加课程
    batch_enabled: "/api/course-service/course-mapping/batch/enable", // 批量启用
    batch_disabled: "/api/course-service/course-mapping/batch/disable", // 批量停用
    goods_del: "/api/course-service/course-article/delete",
    goods_update: "/api/course-service/course-article/update",
    goods_create: "/api/course-service/course-article/create",
    goods_list: "/api/course-service/course-article/list",
    match_list: "/api/course-service/course-match/list",
    match_create: "/api/course-service/course-match/create",
    match_del: "/api/course-service/course-match/delete",
    match_update: "/api/course-service/course-match/update",
    rule_info: "/api/course-service/course-rule/info",
    rule_update: "/api/course-service/course-rule/update",
    class_progress: "/api/course-service/class-progress/tab",
    pre_lesson_numb:
      "/api/course-service/class-progress/update_pre_lesson_numb",
    commonTimePeriod: "/api/school-service/classroom/common-time-period",
    infoTime: "/api/course-service/course-rule/info-time"
  },
  // 收费类型配置
  course_charge: {
    create: "/api/order-service/admin/fee-type/create", // 课程收费类型新增
    save: "/api/order-service/admin/fee-type/save", // 课程收费类型更新
    charge_status: "/api/order-service/admin/fee-type/save-is-enable", // 批量启用
    list: "/api/course-service/course-mapping/list",
    update: "/api/order-service/admin/fee-type/info"
  },
  // 默认权限
  default_permission: {
    create: "/api/permission-service/permission/default/create",
    update: "/api/permission-service/permission/default/update",
    delete: "/api/permission-service/permission/default/delete"
  },
  // 操作日志
  operation_log: {
    export: "/api/report-center-service/admin/operation-log/export"
  },
  // 物品
  goods: {
    create: "/api/course-service/article/create",
    update: "/api/course-service/article/update",
    delete: "/api/course-service/article/delete",
    batch_delete: "/api/course-service/article/batch-delete",
    export: "/api/course-service/article/export",
    import: "/api/course-service/article/import",
    list: "/api/course-service/article/list",
    batch_update: "/api/course-service/article/batch-update"
  },
  // 物品类别
  goods_categroy: {
    list: "/api/course-service/article-category/list",
    create: "/api/course-service/article-category/create",
    delete: "/api/course-service/article-category/delete",
    update: "/api/course-service/article-category/update"
  },
  // 仓库管理
  warehouse: {
    list: "/api/course-service/article-bank/list",
    create: "/api/course-service/article-bank/create",
    update: "/api/course-service/article-bank/save",
    delete: "/api/course-service/article-bank/remove"
  },
  // 进出库管理
  // 进货
  stock: {
    create: "/api/course-service/enter-leave-stock/create",
    update: "/api/course-service/enter-leave-stock/save",
    delete: "/api/course-service/enter-leave-stock/remove"
  },
  // 退货
  return: {
    create: "/api/course-service/enter-leave-stock/refund/create",
    update: "/api/course-service/enter-leave-stock/refund/save",
    delete: "/api/course-service/enter-leave-stock/refund/remove"
  },
  // 调拨
  allocate: {
    create: "/api/course-service/enter-leave-stock/dial/create",
    update: "/api/course-service/enter-leave-stock/dial/save",
    delete: "/api/course-service/enter-leave-stock/dial/remove"
  },
  // 报损
  report: {
    create: "/api/course-service/enter-leave-stock/damage/create",
    update: "/api/course-service/enter-leave-stock/damage/save",
    delete: "/api/course-service/enter-leave-stock/damage/remove"
  },
  // 领用
  use: {
    create: "/api/course-service/enter-leave-stock/use/create",
    update: "/api/course-service/enter-leave-stock/use/save",
    delete: "/api/course-service/enter-leave-stock/use/remove"
  },
  // 退领
  Withdrawal: {
    create: "/api/course-service/enter-leave-stock/withdrawal/create",
    update: "/api/course-service/enter-leave-stock/withdrawal/save",
    delete: "/api/course-service/enter-leave-stock/withdrawal/remove"
  },
  // 库存调整
  inventoryAdjustment: {
    create: "/api/course-service/enter-leave-stock/adjust/create",
    update: "/api/course-service/enter-leave-stock/adjust/save",
    delete: "/api/course-service/enter-leave-stock/adjust/remove"
  },
  // 赛事
  match: {
    create: "/api/course-service/match/create",
    update: "/api/course-service/match/update",
    delete: "/api/course-service/match/delete",
    batch_delete: "/api/course-service/match/batch-delete",
    export: "/api/course-service/match/export",
    batch_update: "/api/course-service/match/batch-update"
  },
  // 赛事类型
  match_type: {
    create: "/api/course-service/match-category/create",
    update: "/api/course-service/match-category/update",
    delete: "/api/course-service/match-category/delete"
  },
  // 学员类别
  student_type: {
    create: "/api/student-service/student-category/create",
    update: "/api/student-service/student-category/save",
    delete: "/api/student-service/student-category/delete"
  },
  // 订单管理
  order: {
    list: "/api/order-service/admin/order/list",
    // delete: "/api/enterprise/order/delete",
    cancel: "/api/order-service/admin/order/cancel",
    info: "/api/order-service/admin/order/info",
    export: "/api/order-service/admin/order/export",
    import: "/api/order-service/admin/order/import",
    manualInput: "/api/permission-service/discount/manual-input"
  },
  // 收据管理
  receipt: {
    list: "/api/order-service/admin/receipt/list",
    list_new: "/api/order-service/admin/receipt/list-new",
    invoice_apply: "/api/finance-service/invoice/apply",
    proceedsAccount: "/api/order-service/admin/receipt/account/statistics",
    export: "/api/report-center-service/admin/receipt/export",
    discard: "/api/order-service/admin/receipt/discard",
    invalid_invoice_discard: "/api/order-service/admin/invalid_invoice_discard", // 前端自定义的唯一标识，未开票作废
    after_invoicing_discard: "/api/order-service/admin/after_invoicing_discard", // 前端自定义的唯一标识，已开票作废
    transfer_update: "/api/order-service/admin/receipt/carryover/edit",
    refund_update: "/api/order-service/admin/receipt/refund/edit",
    charge_update: "/api/order-service/admin/receipt/payment/edit",
    print: "/api/order-service/admin/receipt/print",
    transfer_info: "/api/order-service/admin/transfer/transfer-info",
    refund_info: "/api/order-service/admin/receipt/carryover/detail",
    charge_info: "/api/order-service/admin/receipt/payment/detail",
    charge_type_update: "/api/order-service/admin/front/charge_type_update", // 前端自定义的唯一标识，是否有编辑收费类型的权限
    performance_info_update:
      "/api/order-service/admin/front/performance_info_update", // 前端自定义的唯一标识，是否有编辑业绩归属人的权限
    inner_remark_update: "/api/order-service/admin/front/inner_remark_update", // 前端自定义的唯一标识，是否有编辑内部备注的权限
    trackQuery: "/api/order-service/admin/jd-track/track-query", // 获取物流信息
    editJdTrack: "/api/order-service/admin/receipt/edit-jd-track", // 填写快递单号
    ylb_discard_permission: "/api/front/order-service/admin/receipt-discard", // 元萝卜收据作废
    import_express: "/api/order-service/admin/receipt/import-jd-track", // 导入快递单号
    edit_third_order_id:
      "/api/order-service/admin/receipt/order-edit-third-order-id", // 编辑第三方订单号
    edit_period: "/api/order-service/admin/receipt/edit-period" // 编辑放量期次
  },
  // 大众点评商户管理
  dianpingConfig: {
    list: "/api/order-service/admin/dianping/list",
    create: "/api/order-service/admin/dianping/add",
    unbind: "/api/order-service/admin/dianping/unbind",
    bind: "/api/order-service/admin/dianping/bind"
  },
  // 发票管理
  invoice: {
    list: "/api/finance-service/invoice/list",
    agree: "/api/finance-service/invoice/agree",
    refuse: "/api/finance-service/invoice/refuse",
    info: "/api/finance-service/invoice/info",
    discard: "/api/finance-service/invoice/discard",
    export: "/api/finance-service/invoice/export"
  },
  // 呆滞费用
  stagnant_expenses: {
    list: "/api/finance-service/idle-expense/list",
    export: "/api/finance-service/idle-expense/export",
    clear: "/api/finance-service/idle-expense/clear"
  },
  // 呆滞费用清理记录
  clean_record: {
    list: "/api/finance-service/idle-expense/clear-list",
    export: "/api/finance-service/idle-expense/clear-export"
  },
  // Pos机绑定
  Pos: {
    list: "/api/order-service/admin/yop/pos-list",
    create: "/api/order-service/admin/yop/pos-install",
    uninstall: "/api/order-service/yop/admin/pos-uninstall",
    sync: "/api/order-service/admin/yop/pos-info-sync"
  },
  // 易宝商户绑定
  Yop: {
    list: "/api/order-service/admin/yop/merchant-list",
    create: "/api/order-service/admin/yop/merchant-add",
    uninstall: "/api/order-service/admin/yop/merchant-delete",
    sync: "/api/order-service/admin/yop/merchant-update"
  },
  // 收费管理
  settlement: {
    create: "/api/order-service/admin/order/create",
    cancel: "/api/order-service/admin/order/cancel",
    chooseDate: "/api/order-service/admin/order/charge-date" // 收费日期选择权限
  },
  // 结转
  carryover: {
    carryoverManagement: "/api/order-service/admin/transfer/to-transfer-list",
    carryoverList: "/api/order-service/admin/transfer/transfer-list",
    save: "/api/order-service/admin/transfer/transfer",
    export: "/api/order-service/admin/transfer/transfer-list-export",
    chooseDate: "/api/order-service/admin/transfer/charge-date", // 收费日期选择权限
    // 元萝卜结转
    ylb_transfer: "/api/front/order-service/admin/transfer"
  },
  // 退费
  refund: {
    refundManagement: "/api/order-service/admin/refund/to-refund-list",
    refundList: "/api/order-service/admin/refund/refund-list",
    export: "/api/order-service/admin/refund/refund-list-export",
    save: "/api/order-service/admin/refund/refund",
    cost: "/api/order-service/admin/refund/good-list",
    charge: "/api/order-service/admin/receipt/list",
    cancel: "/api/order-service/admin/refund/cancel",
    pay: "/api/order-service/admin/refund/commit-pay",
    chooseDate: "/api/order-service/admin/refund/charge-date", // 收费日期选择权限,
    // 元萝卜退费
    ylb_refund: "/api/front/order-service/admin/refund",
    // 退费物品编辑
    refund_article_edit: "/api/front/order-service/admin/refund-article-edit",
    // 租赁物品退费
    goods_refund: "/api/front/order-service/admin/goods-refund"
  },
  // 课消报表
  eliminationCourse: {
    coursesList: "/api/order-service/admin/deduct/deduct-report",
    detailList: "/api/order-service/admin/deduct/student-deduct-detail",
    collectList: "/api/order-service/admin/deduct/student-deduct-summary",
    courseExport: "/api/order-service/admin/deduct/deduct-list-report",
    stuCourseExport:
      "/api/report-center-service/admin/deduct/student-deduct-detail-report",
    stuTotalCourseExport:
      "/api/report-center-service/admin/deduct/student-deduct-summary-report",
    deductReportExport: "/api/order-service/admin/deduct/deduct-report-export"
  },
  // 收费汇总报表
  chargeSummary: {
    courseCharge: "/api/order-service/admin/fee-stat/course-area-fee-list", // 课程收费汇总
    export: "/api/order-service/admin/fee-stat/course-info-list-export",
    article_list: "/api/order-service/admin/fee-stat/article-fee-list", // 物品销售汇总
    article_order_list: "/api/order-service/admin/fee-stat/article-order-list", // 物品销售明细
    teach_aid_package_collect:
      "/api/order-service/admin/teach-aid-package/package-fee-list", // 教辅包销售汇总表
    teach_aid_package_detail:
      "/api/order-service/admin/teach-aid-package/package-order-list", // 教辅包
    teach_aid_package_order_detail:
      "/api/order-service/admin/fee-stat/teach-aid-package-fee-list",
    teachAidPackageOrderExport:
      "/api/order-service/admin/fee-stat/teach-aid-package-list-export",
    package_export:
      "/api/order-service/admin/teach-aid-package/package-order-list-export",
    package_refund_export:
      "/api/order-service/admin/teach-aid-package/package-refund-list-export",
    package_order_export:
      "/api/order-service/admin/fee-stat/teach-aid-package-order-list-export",
    package_order_refund_export:
      "/api/order-service/admin/fee-stat/teach-aid-package-refund-list-export",
    article_export:
      "/api/order-service/admin/fee-stat/article-order-list-export",

    refund_list: "/api/order-service/admin/fee-stat/article-refund-list",
    refund_export:
      "/api/order-service/admin/fee-stat/article-refund-list-export",
    transfer_list: "/api/order-service/admin/fee-stat/article-transfer-list",
    transfer_export:
      "/api/order-service/admin/fee-stat/article-transfer-list-export",
    allExport: "/api/order-service/admin/fee-stat/course-area-fee-list-export",
    match_collect_list: "/api/order-service/admin/fee-stat/match-fee-list", // 赛事销售汇总表
    match_list: "/api/order-service/admin/fee-stat/match-order-list", // 赛事销售明细
    articleFeeExport:
      "/api/order-service/admin/fee-stat/article-fee-list-export", // 物品销售汇总表导出
    matchFeeExport: "/api/order-service/admin/fee-stat/match-fee-list-export", // 物品销售明细导出
    teachAidPackageOrderListExport:
      "/api/order-service/admin/fee-stat/teach-aid-package-order-list-export", // 物品销售明细导出
    matchOrderExport:
      "/api/order-service/admin/fee-stat/match-order-list-export", // 赛事销售汇总表导出
    articleOriderExport:
      "/api/order-service/admin/fee-stat/article-order-list-export" // 物品销售明细表导出
  },
  // 教辅包管理
  teachAidPackage: {
    create: "/api/course-service/teach-aid-package/create",
    edit: "/api/course-service/teach-aid-package/update",
    delete: "/api/course-service/teach-aid-package/delete",
    articleInfo: "/api/course-service/teach-aid-package/article-info"
  },
  // 学员费用报表
  studentWalletReport: {
    statisticsList: "/api/order-service/admin/wallet/statistics",
    statisticsExport: "/api/order-service/admin/wallet/statistics/export",
    walletDetail: "/api/order-service/admin/wallet/detail",
    walletDetailExport: "/api/order-service/admin/wallet/detail/export",
    walletEntryExitDetailList: "/api/order-service/admin/wallet/statistics",
    walletEntryExitDetailExport:
      "/api/order-service/admin/wallet/entry/exit/detail/export",
    walletWarnList: "/api/order-service/admin/wallet/warn/list",
    walletWarnExport: "/api/order-service/admin/wallet/warn/list/export"
  },
  // 权限组
  roleList: {
    list: "/api/permission-service/role/list",
    create: "/api/permission-service/role/create"
  },
  // 奖状管理
  citation: {
    list: "/api/organization-service/diploma/list",
    create: "/api/organization-service/diploma/create",
    update: "/api/organization-service/diploma/update",
    delete: "/api/organization-service/diploma/delete",
    generate: "/api/organization-service/diploma/generate",
    view: "/api/organization-service/diploma/view"
  },
  // 标签管理
  label: {
    create: "/api/organization-service/label/create",
    update: "/api/organization-service/label/update",
    delete: "/api/organization-service/label/delete",
    detail: "/api/organization-service/label/list",
    export: "/api/organization-service/label/export",
    enable: "/api/organization-service/label/enable"
  },
  // 个人中心任务池
  personal_center: {
    recharge_summary: "/api/taskpool-service/admin/recharge/summary",
    renewal_list: "/api/taskpool-service/admin/renewal/list",
    renewal_export: "/api/taskpool-service/admin/renewal/export",
    makeup_list: "/api/taskpool-service/admin/makeup/list",
    makeup_export: "/api/taskpool-service/admin/makeup/export",
    recharge_export: "/api/taskpool-service/admin/recharge/export",
    all_recharge_export: "/api/taskpool-service/admin/recharge/export-all",
    recharge_completion_rate_export:
      "/api/taskpool-service/admin/recharge/education-export",
    update_stars: "/api/taskpool-service/admin/recharge/update-stars",
    config_create: "/api/taskpool-service/admin/common/config-list",
    creat: "/api/taskpool-service/admin/common/config-create",
    remove: "/api/taskpool-service/admin/common/config-delete",
    save: "/api/taskpool-service/admin/common/config-update",
    setSchoolManager:
      "/api/taskpool-service/admin/recharge/single-set-school-manager", // 修改学管师
    setEducation: "/api/taskpool-service/admin/recharge/single-set-education", // 修改教务
    batchSetSchoolManager:
      "/api/taskpool-service/admin/recharge/batch-set-school-manager", // 批量修改学管师
    batchSetEducation:
      "/api/taskpool-service/admin/recharge/batch-set-education", // 批量修改教务
    rechargeRemove: "/api/taskpool-service/admin/recharge/remove", // 剔除按钮
    removeList: "/api/taskpool-service/admin/recharge/remove-list", // 剔除列表
    removeListExport: "/api/taskpool-service/admin/recharge/remove-list-export" // 导出剔除任务列表
  },
  // 课程顾问任务池
  course_consultant: {
    target_list: "/api/taskpool-service/admin/target/list",
    creat: "/api/taskpool-service/admin/target/create-person-price",
    remove: "/api/taskpool-service/admin/target/remove",
    save: "/api/taskpool-service/admin/target/save-person-price"
  },
  // 市场专员任务池
  marketing_specialist: {
    target_list: "/api/market-service/taskpool/config/list",
    creat: "/api/market-service/taskpool/config/create",
    remove: "/api/market-service/taskpool/config/remove",
    save: "/api/market-service/taskpool/config/save"
  },
  charge_report: {
    refund_list: "/api/order-service/admin/refund/item/list",
    deduct_list: "/api/order-service/admin/refund/deduct/list",
    refund_export: "/api/order-service/admin/refund/item/export",
    deduct_export: "/api/order-service/admin/refund/deduct/export"
  },
  // 优惠券
  coupon_category: {
    list: "/api/coupon-service/coupon-category/list",
    create: "/api/coupon-service/coupon-category/create",
    delete: "/api/coupon-service/coupon-category/delete",
    update: "/api/coupon-service/coupon-category/save",
    coupon_detail: "/api/coupon-service/coupon-detail/list",
    coupon_list: "/api/coupon-service/coupon-template/list",
    coupon_create: "/api/coupon-service/coupon-template/create",
    coupon_delete: "/api/coupon-service/coupon-template/delete",
    coupon_update: "/api/coupon-service/coupon-template/save",
    coupon_pool: "/api/coupon-service/coupon-pool/save",
    coupon_status: "/api/coupon-service/coupon-detail/save-available",
    // 优惠券课程规格配置
    coupon_specifications:
      "/api/course-service/course-price/course-specifications"
  },
  // 校区优惠券
  coupon_template: {
    list: "/api/coupon-service/coupon-template/department-list",
    Issue_coupons: "/api/coupon-service/coupon-assign/customer"
  },
  // 转介绍报表
  recommend: {
    list: "/api/order-service/admin/recommend/list",
    export: "/api/order-service/admin/recommend/export",
    send_cancel: "/api/order-service/admin/recommend/send-cancel",
    edit: "/api/order-service/admin/recommend/update-recommend"
  },
  // 上课时间设置 上课校区
  time_group: {
    create: "/api/school-service/class-time/group/create",
    delete: "/api/school-service/class-time/group/delete",
    list: "/api/school-service/class-time/group/list"
  },
  // 上课时间设置 上课时间
  class_time: {
    create: "/api/school-service/class-time/create",
    delete: "/api/school-service/class-time/delete",
    update: "/api/school-service/class-time/info",
    list: "/api/school-service/class-time/list"
  },
  // 加微率
  addWechat: {
    course_list: "/api/market-service/customer/count/add/wechat",
    teacher_list: "/api/school-service/classroom-student/count/add/wechat",
    course_export: "/api/market-service/customer/add/wechat/export",
    teacher_export: "/api/school-service/classroom-student/add/wechat/export"
  },
  // 图片管理
  picture: {
    create: "/api/organization-service/picture/create",
    delete: "/api/organization-service/picture/delete",
    update: "/api/organization-service/picture/info",
    list: "/api/organization-service/picture/list",
    model_create: "/api/organization-service/picture-category/create",
    model_update: "/api/organization-service/picture-category/update"
  },
  // 库存查询
  inventoryQuery: {
    export: "/api/course-service/article-bank-amount/export",
    detail: "/api/course-service/article-bank-amount/article-bank-num-list"
  },
  // 库存变动
  inventoryChange: {
    export: "/api/course-service/article-bank-amount-log/change-list-export"
  },
  // 进出库查询
  inventoryEnterOrLeave: {
    export: "/api/course-service/enter-leave-stock/info-list-export"
  },
  // 前端获客
  front: {
    // 市场渠道报表
    channelList: "/api/market-service/report/customer-channel",
    channelExport: "/api/market-service/report/customer-channel-export",
    // 客户状态分析
    statusList: "/api/market-service/report/customer-status",
    statusExport: "/api/market-service/report/customer-status-export",
    // 客户分析---市场渠道
    source_list: "/api/market-service/report/customer-source",
    source_export: "/api/market-service/report/customer-source-export",
    source_to_student_list:
      "/api/market-service/report/customer-source-to-student-list",
    source_to_student_export:
      "/api/market-service/report/customer-source-to-student-list-export",
    // 客户分析---意向级别
    intention_list: "/api/market-service/report/customer-intention",
    intention_export: "/api/market-service/report/customer-intention-export",
    // 转化率分析
    transfer_rate_list: "/api/market-service/conversion/advisor",
    transfer_rate_export: "/api/market-service/report/transfer-rate-export",
    transfer_to_student_list:
      "/api/market-service/report/transfer-rate-student",
    transfer_to_student_export:
      "/api/market-service/report/transfer-rate-student-export",
    // 转介绍统计
    transfer_introduce_list: "/api/market-service/report/transfer-introduce",
    transfer_introduce_export:
      "/api/market-service/report/transfer-introduce-export",
    introduce_to_student_list:
      "/api/market-service/report/transfer-introduce-student",
    introduce_to_student_export:
      "/api/market-service/report/transfer-introduce-student-export",
    // 邀约试听率
    audition_rate_list: "/api/market-service/report/audition-rate"
  },
  // 销售业绩报表
  salesSummary: {
    sale_list: "/api/order-service/admin/order/sale-report",
    sale_list_export: "/api/order-service/admin/order/sale-report-export"
  },
  // 续费率报表
  renewalReport: {
    rateExport: "/api/school-service/net-school/renewal/rate-export", // 续费率导出
    detail: "/api/school-service/net-school/renewal/detail", // 续费率明细
    detailExport: "/api/school-service/net-school/renewal/detail-export" // 续费率明细导出
  },
  // 转校转费
  schoolTransferFee: {
    transfer_school: "/api/sub/school-tranfer/menu",
    transfer_fee: "/api/sub/fee-tranfer/menu",
    transfer_school_fee: "/api/sub/school-transfer-fee/menu",
    transfer_school_apply_list: "/api/sub/school_transfer_apply_list/menu",
    transfer_school_apply_export: "/api/order-service/admin/approve/export",
    but_transfer_school:
      "/api/student-service/student-transfer/student-school-change",
    but_transfer_fee: "/api/order-service/admin/fee-transfer/create",
    but_transfer_School_fee:
      "/api/student-service/student-transfer/student-school-wallet-change"
  },
  // 流程审批
  workflowService: {
    approve_export: "/api/workstream-service/approve/export",
    template_create: "/api/workstream-service/template/create",
    template_update: "/api/workstream-service/template/update",
    template_list: "/api/workstream-service/template/list",
    rule_create: "/api/workstream-service/rule/create",
    rule_update: "/api/workstream-service/rule/update",
    rule_info: "/api/workstream-service/rule/info"
  },
  remittanceTransferReport: {
    list: "/api/report-center-service/admin/fee-transfer-log/list",
    export: "/api/report-center-service/admin/fee-transfer-log/export"
  },
  // 班级报表
  class_report: {
    classRoster: "/api/school-service/classroom/roster",
    attendanceDetails: "/api/order-service/admin/class/attendance", // 出勤明细表
    attendanceDetailsExport:
      "/api/report-center-service/admin/attendance/export", // 出勤明细表导出
    fullShiftRate: "/api/school-service/classroom/full-shift-rate",
    classPopulation: "/api/school-service/class/list",
    adjustClass:
      "/api/report-center-service/admin/shift-classroom-course/classroom-list",
    roster_list: "/api/order-service/admin/class/roster",
    roster_export: "/api/school-service/classroom/roster-export",
    full_shift_rate_export:
      "/api/school-service/classroom/full-shift-rate-export",
    class_population_export: "/api/school-service/class/export",
    shift_class_export:
      "/api/report-center-service/admin/shift-classroom-course/classroom-export",
    attendanceRate: "/api/order-service/admin/deduct/present-ratio", // 出勤率
    present_ratio_export:
      "/api/report-center-service/admin/deduct/present-ratio-export" // 出勤率导出
  },
  // 学员分析报表
  studentAnalysis: {
    commReport: "/api/market-service/comm-report/statistics",
    course: "/api/order-service/admin/wallet/course-statistics",
    commReportExport: "/api/market-service/comm-report/export",
    courseExport: "/api/order-service/admin/wallet/course-statistics-export"
  },
  // 操作日志
  operationLog: {
    list: "/api/report-center-service/admin/operation-log/list",
    export: ""
  },
  // 系统设置
  system_setting: {
    parentCreate:
      "/api/order-service/admin/category-configuration/parent/create",
    parentUpdate:
      "/api/order-service/admin/category-configuration/parent/update",
    parentDelete:
      "/api/order-service/admin/category-configuration/parent/delete",
    sonCreate: "/api/order-service/admin/category-configuration/son/update",
    sonDelete: "/api/order-service/admin/category-configuration/son/delete",
    sonSchool:
      "/api/order-service/admin/category-configuration/son/relation/department",
    sonStatus:
      "/api/order-service/admin/category-configuration/son/update/status"
  },
  // 学员管理
  student_management: {
    // 转介绍率
    introducer_list: "/api/student-service/day-log/introducer-list",
    // 期末人数统计
    daylog_list: "/api/student-service/day-log/list",
    daylog_export: "/api/student-service/day-log/export"
  },
  // 退交费情况
  refundFee_situation: {
    // 退费率
    refund_ratio_list: "/api/student-service/day-log/introducer-list",
    refund_ratio_export: "/api/order-service/admin/refund/refund-ratio-export",
    // 退费占比
    refund_proportion_list: "/api/order-service/admin/refund/refund-proportion",
    proportion_export:
      "/api/order-service/admin/refund/refund-proportion-export",
    refund_reason_list: "/api/order-service/admin/refund/refund-reason",
    reason_export: "/api/order-service/admin/refund/refund-reason-export"
  },
  // 全局手机号脱敏
  all_phone: {
    has_limit: "/api/show-mobile/front"
  },
  // 业绩报表
  performance_report: {
    // 老师业绩
    teacher_performance_index:
      "/api/order-service/admin/deduct/teacher/front-menu",
    // 老师业绩明细导出
    teacher_performance_detail_export:
      "/api/report-center-service/admin/deduct/teacher-deduct-detail-export",
    // 老师业绩汇总导出
    teacher_performance_summary_export:
      "/api/report-center-service/admin/deduct/teacher-deduct-summary-export",
    // 老师业绩明细
    teacher_performance_detail:
      "/api/order-service/admin/deduct/teacher-deduct-detail",
    // 老师业绩汇总
    teacher_performance_summary:
      "/api/order-service/admin/deduct/teacher-deduct-summary"
  },
  // 外呼管理
  call_manage: {
    // 外呼记录
    call_record: "/api/market-service/voip/findDialRecord",
    // 坐席列表
    extension_list: "/api/market-service/voip/extension-list",
    // 同步坐席
    extension_sync: "/api/market-service/voip/extension-sync",
    // 坐席修改
    extension_update: "/api/market-service/voip/extension-update",
    // 获取员工坐席信息
    extension_info: "/api/market-service/voip/extension-info",
    // 通话录音下载
    voice_download: "/api/market-service/voip/download/front",
    // 通话录音播放权限
    voice_play: "/api/market-service/voip/play/front"
  },
  // 直播管理
  live_manage: {
    // 直播列表
    list: "/api/live-go-service/admin/course/list",
    // 创建直播间
    create: "/api/live-go-service/admin/course/create",
    // 导出直播间列表
    live_export: "/api/live-go-service/admin/course/export",
    // 编辑直播间
    live_edit: "/api/live-go-service/admin/course/detail",
    // 学生详情
    live_student_detail: "/api/live-management/student/info",
    // 课程信息
    class_data: "/api/live-management/class/period",
    // 课堂数据
    classroom_data: "/api/live-management/class/room",
    // 结课
    end_class: "/api/live-go-service/admin/course/dismiss-room",
    // 回放
    watch_playback: "/api/live-management/class/playback",
    // 回放导出
    watch_playback_export: "/api/live-go-service/admin/transcribe/export",
    // 课件上传
    upload_courseware: "/api/live-go-service/admin/courseware/upload",
    // 新建分类
    create_category: "/api/live-go-service/admin/courseware/create",
    // 授权老师
    authorize: "/api/live-go-service/admin/courseware/auth",
    // 删除课件
    delete_courseware: "/api/live-go-service/admin/courseware/delete",
    // 删除回放
    delete_playback: "/api/live-go-service/admin/transcribe/delete",
    // 监课考勤明细
    get_attendance_detail:
      "/api/live-go-service/admin/teacher/supervision/student-list",
    // 监课考勤导出
    get_attendance_export:
      "/api/live-go-service/admin/teacher/supervision/student-list-export",
    // 取消直播
    cancel_live: "/api/live-go-service/admin/course/cancel",
    // 课后评价
    get_evaluation: "/api/live-go-service/admin/teacher/live-evaluation",
    // 下载课件
    download_courseware: "/api/live-go-service/admin/courseware/download",
    // 同步学员
    history_live_student: "/api/live-go-service/admin/history/live-student",
    // 下载回放
    download_playback: "/api/live-go-service/admin/transcribe/download",
    // 替换回放
    replace_playback: "/api/live-go-service/admin/transcribe/replace",
    // 禁用回放
    disable_playback: "/api/live-go-service/admin/transcribe/disable",
    // 上传回放
    upload_playback: "/api/live-go-service/admin/transcribe/update",
    // 清除多端登录
    clear_login: "/api/live-go-service/admin/course/del-many-cache",
    // 学生数导出
    outsideStudentExport:
      "/api/live-go-service/admin/course/outside-student-export",
    // 课堂数据导出
    classroomDataExport:
      "/api/live-go-service/admin/teacher/live-course-report-collect-export"
  },
  dataBank: {
    delete: "/api/organization-service/databank/delete"
  },
  survey: {
    list: "/api/questionnaire-service/admin/survey/getList",
    create: "/api/questionnaire-service/admin/survey/createSurvey",
    delete: "/api/questionnaire-service/admin/survey/deleteSurvey",
    studentExport:
      "/api/questionnaire-service/admin/survey/surveyStudentExport",
    updateEnabledStatus:
      "/api/questionnaire-service/admin/survey/updateEnabledStatus",
    updateConf: "/api/questionnaire-service/admin/survey/updateConf",
    updateMeta: "/api/questionnaire-service/admin/survey/updateMeta",
    getSchema: "/api/questionnaire-service/admin/survey/getSchema",
    getSurvey: "/api/questionnaire-service/admin/survey/getSurvey",
    getSurveyStudent:
      "/api/questionnaire-service/admin/survey/getSurveyStudent",
    resultExport: "/api/questionnaire-service/admin/survey/resultExport",
    // 问卷脱敏
    surveyShow: "/api/questionnaire-web-service/front/surveyShow"
  },
  restCourse: {
    list: "/api/questionnaire-service/admin/miniProgram/restCourseWebShow/list",
    edit: "/api/questionnaire-service/admin/miniProgram/restCourseWebShow/edit",
    create:
      "/api/questionnaire-service/admin/miniProgram/restCourseWebShow/add",
    delete:
      "/api/questionnaire-service/admin/miniProgram/restCourseWebShow/delete"
  },
  surveyCoupon: {
    studentList: "/api/questionnaire-service/admin/studentCoupon/list",
    studentCouponHistory: "/api/coupon-service/studentCoupon/history",
    studentCouponAdd: "/api/questionnaire-service/admin/studentCoupon/add",
    studentCouponRedeem:
      "/api/questionnaire-service/admin/studentCoupon/redeem",
    couponList: "/api/questionnaire-service/admin/coupon/list",
    couponInfo: "/api/questionnaire-service/admin/coupon/info",
    couponUpdate: "/api/questionnaire-service/admin/coupon/update",
    couponDelete: "/api/questionnaire-service/admin/coupon/delete",
    couponCreate: "/api/questionnaire-service/admin/coupon/create"
  },
  // 课程管理
  traineeReport: {
    // 课程管理
    list: "/api/school-service/admin/feedback/list",
    info: "/api/school-service/admin/feedback/info",
    export: "/api/school-service/admin/feedback/export",
    revoke: "/api/school-service/admin/feedback/cancel"
  },
  template: {
    create: "/api/questionnaire-service/admin/surveyTemplate/create", // 新增模板
    update: "/api/questionnaire-service/admin/surveyTemplate/update", // 编辑模板
    delete: "/api/questionnaire-service/admin/surveyTemplate/delete", // 删除模板
    list: "/api/questionnaire-service/admin/surveyTemplate/list", // 模板列表
    info: "/api/questionnaire-service/admin/surveyTemplate/metaInfo", // 模板详情
    allList: "/api/questionnaire-service/admin/surveyTemplate/allList", // 模板列表
    updateStatus:
      "/api/questionnaire-service/admin/surveyTemplate/updateEnabledStatus" // 更新状态
  },
  competition: {
    create: "/api/questionnaire-service/admin/match/create", // 新增赛事
    update: "/api/questionnaire-service/admin/match/update", // 编辑赛事
    delete: "/api/questionnaire-service/admin/match/del", // 删除赛事
    list: "/api/questionnaire-service/admin/match/list", // 赛事列表
    info: "/api/questionnaire-service/admin/match/detail", // 赛事详情
    updateStatus: "/api/questionnaire-service/admin/match/update/status", // 更新状态
    export: "/api/questionnaire-service/admin/match/export", // 导出赛事
    studentList: "/api/questionnaire-service/admin/match/student/list", // 学员列表
    touristList: "/api/questionnaire-service/admin/match/tourist/list", // 游客列表
    touristExport: "/api/questionnaire-service/admin/match/tourist/export", // 游客列表导出
    customizeDel: "/api/questionnaire-service/admin/match/customize/del", // 删除自定义字段
    studentUpdate: "/api/questionnaire-service/admin/match/student/update", // 编辑学员
    studentUpdateStatus:
      "/api/questionnaire-service/admin/match/student/update/status", // 更新学员状态
    studentAdd: "/api/questionnaire-service/admin/match/student/add", // 新增学员
    studentExport: "/api/questionnaire-service/admin/match/student/export", // 导出学员
    isShowCompetitionOpen:
      "/api/questionnaire-service/admin/match/isShowCompetitionOpen" // 是否展示赛事
  },
  // 小程序用户管理
  miniUserManagement: {
    list: "/api/questionnaire-service/admin/miniProgram/visitor/list",
    num: "/api/questionnaire-service/admin/miniProgram/visitor/num",
    bindList: "/api/questionnaire-service/admin/miniProgram/visitor/bindList",
    customerBindNum:
      "/api/questionnaire-service/admin/miniProgram/visitor/customerBindNum",
    studentBindNum:
      "/api/questionnaire-service/admin/miniProgram/visitor/studentBindNum",
    export:
      "/api/questionnaire-service/admin/miniProgram/visitor/visitorListExport"
  },
  nedawCircle: {
    create: "/api/questionnaire-service/admin/moments/create", // 新增聂道圈
    verify: "/api/questionnaire-service/admin/moments/update/verify", // 审核通过/驳回/发布/下架
    delist: "/api/questionnaire-service/admin/moments/update/delist", // 下架
    publish: "/api/questionnaire-service/admin/moments/update/publish", // 发布
    update: "/api/questionnaire-service/admin/moments/update", // 更新状态
    delete: "/api/questionnaire-service/admin/moments/delete", // 删除聂道圈
    detail: "/api/questionnaire-service/admin/moments/detail", // 聂道圈详情
    listStatistics: "/api/questionnaire-service/admin/moments/list-statistics", // 聂道圈列表统计
    statistics: "/api/questionnaire-service/admin/moments/statistics", // 聂道圈统计
    updateCustomCount:
      "/api/questionnaire-service/admin/moments/update/custom/count", // 聂道圈点赞数
    updateTopStatus:
      "/api/questionnaire-service/admin/moments/update/top-status" // 聂道圈置顶
  },
  // 直营校月报
  directMonthReport: {
    detail: "/api/report-center-service/admin/offline-school/detail-list",
    export: "/api/front/cockpit-service/direct-school-monthly-report/export",
    detail_export:
      "/api/report-center-service/admin/offline-school/detail-export",
    monthDetail:
      "/api/report-center-service/admin/offline-school/department-month",
    monthDetailExport: "/api/front/admin/offline-school/department-month/export"
  },
  // 设备管理
  device_manage: {
    // 获取设备类别列表
    getDeviceTypeList: "/api/student-service/device-category/list",
    // 获取设备列表
    getDeviceList: "/api/student-service/device/list",
    // 获取设备详情
    getDeviceDetail: "/api/student-service/device/info",
    // 编辑设备
    editDevice: "/api/student-service/device/update",
    // 删除设备
    deleteDevice: "/api/student-service/device/delete",
    // 删除设备类型
    deleteDeviceType: "/api/student-service/device-category/delete",
    // 添加设备类别
    addDevice: "/api/student-service/device-category/create",
    // 编辑设备类别
    editDeviceType: "/api/student-service/device-category/update",
    // 删除类别
    deleteCategory: "/api/student-service/device-category/delete"
  },
  preview: {
    feedback_cancel: "/api/school-service/miniprogram/feedback/cancel"
  },
  // 小程序资料库
  appletResource: {
    school_auth: "/api/organization-service/databank/auth",
    create: "/api/organization-service/databank/create",
    file_list: "/api/organization-service/databank/file-list",
    delete: "/api/organization-service/databank/delete",
    info: "/api/organization-service/databank/info",
    list: "/api/organization-service/databank/list",
    upload: "/api/organization-service/databank/upload"
  },
  // 电子签相关
  electronicSign: {
    // 电子签推送
    push: "/api/order-service/admin/contract/apply",
    // 获取企业列表
    getTemplateConfig: "/api/order-service/admin/contract/organization-list",
    // 获取企业详情
    getTemplateConfigDetail:
      "/api/order-service/admin/contract/organization-info",
    // 编辑电子签企业模版配置
    editTemplateConfig:
      "/api/electronic-sign-service/admin/template-config/update",
    // 新增电子签企业模版配置
    addTemplateConfig: "/api/order-service/admin/contract/organization-add",
    // 导出电子合同列表
    export: "/api/order-service/admin/contract/export"
  },
  // 阿波罗数据看板
  aboluo: {
    classList: "/api/school-service/aboluo/high-low-class",
    classDetail: "/api/school-service/aboluo/class-detail",
    total: "/api/school-service/aboluo/classroom/total",
    rankingList: "/api/school-service/aboluo/ranking-list",
    renewDetail: "/api/school-service/aboluo/renew-detail",
    totalNum: "/api/school-service/aboluo/total/num",
    export: "/api/school-service/aboluo/renew-detail/export",
    rankingInfo: "/api/school-service/aboluo/ranking-info"
  },
  userPool: {
    suspensionPoolLit: "/api/student-service/student/list",
    backToSchool: "/api/student-service/student/backToSchool",
    waitSchoolList: "/api/student-service/student/wait-school-list",
    changeShiftList: "/api/school-service/classroom/shift-pool-list",
    notRenewedList: "/api/school-service/renew-no/list",
    addStudent: "/api/school-service/classroom/add-student",
    changeShiftPoolExport:
      "/api/school-service/classroom/shift-pool-list-export",
    shiftAdjustStudent: "/api/school-service/classroom/shift-adjust-student",
    notRenewedPoolExport: "/api/school-service/renew-no/export",
    cancelShiftPool: "/api/school-service/classroom/cancel-shift-pool",
    // 休学池导出
    suspensionPoolExport:
      "/api/report-center-service/admin/student/out-school/export",
    // 待入班池导出
    waitClassPoolExport: "/api/student-service/student/wait-school-export",
    openSeaList: "/api/market-service/open-sea/list",
    openSeaAdvisorAdd: "/api/market-service/open-sea/advisor-add",
    openSeaAdvisorList: "/api/market-service/open-sea/advisor-list"
  },
  // 网校客户管理
  schoolCustomer: {
    export: "/api/market-service/tw-sale/export",
    list: "/api/market-service/tw-sale/list",
    configlist: "/api/market-service/tw-sale/field-list",
    create: "/api/market-service/tw-sale/field-create",
    delete: "/api/market-service/tw-sale/field-delete",
    addStudent: "/api/school-service/classroom/add-student"
  },
  // 网校期次管理
  periodSchool: {
    // 获取网校期次列表
    getPeriodList: "/api/market-service/period/list",
    // 批量新增
    periodBatchCreate: "/api/market-service/period/batch-create",
    // 新增网校期次
    createPeriod: "/api/market-service/period/create"
  },
  // 职级绑定配置
  rankBindConfig: {
    // 新增
    create: "/api/organization-service/employee-group/create",
    // 编辑
    update: "/api/organization-service/employee-group/update",
    // 删除
    remove: "/api/organization-service/employee-group/delete",
    // 绑定组员
    bindCrew: "/api/organization-service/employee-group/group-member-save"
  },
  // 前端数据面板
  frontendDataPanel: {
    twBoardListExport: "/api/order-service/admin/tw-board/export"
  },
  // 小程序banner管理
  bannerManagement: {
    list: "/api/questionnaire-service/admin/banner/list",
    create: "/api/questionnaire-service/admin/banner/create",
    update: "/api/questionnaire-service/admin/banner/update-status",
    delete: "/api/questionnaire-service/admin/banner/delete",
    detail: "/api/questionnaire-service/admin/banner/info",
    updateStatus: "/api/questionnaire-service/admin/banner/update-status"
  },
  // 小程序课程管理
  miniprogramCourse: {
    list: "/api/course-service/course-minpro/list",
    create: "/api/course-service/course-minpro/create",
    modify: "/api/course-service/course-minpro/modify",
    delete: "/api/course-service/course-minpro/delete",
    info: "/api/course-service/course-minpro/info",
    toFront: "/api/course-service/course-minpro/to-front",
    offShelf: "/api/course-service/course-minpro/put-on"
  },
  // 用户反馈
  userFeedback: {
    list: "/api/questionnaire-service/admin/complaint/list",
    detail: "/api/questionnaire-service/admin/complaint/detail",
    reply: "/api/questionnaire-service/admin/complaint/reply",
    export: "/api/questionnaire-service/admin/complaint/export",
    visitorShow: "/api/questionnaire-service/front/visitor/show"
  },
  // 小程序客服二维码管理
  qrCodeManagement: {
    list: "/api/questionnaire-service/admin/qRCode/list",
    create: "/api/questionnaire-service/admin/qRCode/create",
    del: "/api/questionnaire-service/admin/qRCode/del",
    updateStatus: "/api/questionnaire-service/admin/qRCode/update/status"
  },
  // 时光相册
  timeAlbum: {
    list: "/api/student-service/time-album/list",
    create: "/api/student-service/time-album/create",
    update: "/api/student-service/time-album/save",
    delete: "/api/student-service/time-album/delete",
    detail: "/api/student-service/time-album/info",
    infoList: "/api/student-service/time-album/infoList",
    typeList: "/api/student-service/public/time-album/get-album-type-list",
    export: "/api/student-service/time-album/export",
    sendExport: "/api/student-service/time-album/send-list-export",
    batchCreate: "/api/student-service/time-album/batch-add",
    sendList: "/api/student-service/time-album/send-list"
  },
  // 聂道名师
  nedawTeacher: {
    delete: "/api/questionnaire-service/admin/nieDaoTeacher/del",
    update: "/api/questionnaire-service/admin/nieDaoTeacher/update",
    updateStatus:
      "/api/questionnaire-service/admin/nieDaoTeacher/update/status",
    detail: "/api/questionnaire-service/admin/nieDaoTeacher/detail",
    create: "/api/questionnaire-service/admin/nieDaoTeacher/create"
  },
  // 校区介绍
  campusIntroduction: {
    create: "/api/questionnaire-service/admin/departmentIntroduce/create",
    update: "/api/questionnaire-service/admin/departmentIntroduce/update",
    delete: "/api/questionnaire-service/admin/departmentIntroduce/del",
    // 启用停用
    enableOrDisable:
      "/api/questionnaire-service/admin/departmentIntroduce/update/status",
    // 创建模块
    createModule:
      "/api/questionnaire-service/admin/departmentIntroduce/create-module",
    // 删除模块
    deleteModule:
      "/api/questionnaire-service/admin/departmentIntroduce/del-module",
    // 编辑模块
    updateModule:
      "/api/questionnaire-service/admin/departmentIntroduce/update-module"
  },
  // 租赁管理
  leaseManagement: {
    // 学员租赁明细列表导出
    detailExport: "/api/order-service/admin/allocation/detail-export",
    // 获取学员租赁明细列表
    detailList: "/api/order-service/admin/allocation/detail-list",
    // 结清租赁
    settlement: "/api/order-service/admin/allocation/settle-allocation",
    // 获取租赁汇总区域列表
    areaList: "/api/order-service/admin/allocation/summary-area",
    // 租赁汇总区域列表导出
    areaExport: "/api/order-service/admin/allocation/summary-area-export",
    // 获取租赁汇总校区列表
    departmentList: "/api/order-service/admin/allocation/summary-department",
    // 租赁汇总校区列表导出
    departmentExport:
      "/api/order-service/admin/allocation/summary-department-export",
    // 获取租赁汇总物品列表
    itemList: "/api/order-service/admin/allocation/summary-goods",
    // 租赁汇总物品列表导出
    itemExport: "/api/order-service/admin/allocation/summary-goods-export",
    // 学员租赁汇总表导出
    summaryExport: "/api/order-service/admin/allocation/summary-export",
    // 学员租赁汇总表
    summaryList: "/api/order-service/admin/allocation/summary-list"
  },
  // 仓库盘点
  warehouseTakeStock: {
    // 导出
    export: "/api/course-service/article-bank/article-list-export",
    // 保存
    save: "/api/course-service/enter-leave-stock/stock-take/create"
  },
  // 人力拆表
  compensationStructure: {
    // 数据导入
    importData:
      "/api/report-center-service/admin/compensation-structure/import-data"
  },
  // 精选视频
  wonderfulVideo: {
    create: "/api/questionnaire-service/admin/featuredVideos/create",
    delete: "/api/questionnaire-service/admin/featuredVideos/del",
    update: "/api/questionnaire-service/admin/featuredVideos/update",
    status: "/api/questionnaire-service/admin/featuredVideos/update/status",
    detail: "/api/questionnaire-service/admin/featuredVideos/detail",
    list: "/api/questionnaire-service/admin/featuredVideos/list",
    parentCreate:
      "/api/questionnaire-service/admin/featuredVideos/parent-create",
    parentDel: "/api/questionnaire-service/admin/featuredVideos/parent-del",
    parentList: "/api/questionnaire-service/admin/featuredVideos/parent-list",
    parentUpdate:
      "/api/questionnaire-service/admin/featuredVideos/parent-update",
    // 数据导出
    exportData:
      "/api/report-center-service/admin/compensation-structure/export-data",
    // 数据导入
    importData:
      "/api/report-center-service/admin/compensation-structure/import-data"
  },
  hoseConfig: {
    list: "/api/order-service/admin/hose/dimension-list",
    bind: "/api/order-service/admin/hose/dimension-bind",
    view: "/api/order-service/admin/hose/frontend-info"
  },
  // 市场任务指标
  marketTaskIndicator: {
    marketTaskIndicatorMenu: "/api/frontend/conversion/menu", // 菜单
    conversionList: "/api/market-service/conversion/list", // 市场周转化率列表
    indicatorList: "/api/market-service/indicator/list", // 市场任务指标列表
    conversionListExport: "/api/frontend/conversion/export", // 市场周转化率导出
    indicatorListExport: "/api/frontend/indicator/export" // 市场任务指标导出
  },
  // 在线支付对账
  onlinePayment: {
    list: "/api/order-service/admin/trade-bill/list",
    export: "/api/order-service/admin/trade-bill/list-export",
    importDianping: "/api/order-service/admin/trade-bill/import-dianping",
    importUnion: "/api/order-service/admin/trade-bill/import-union",
    importBank: "/api/order-service/admin/trade-bill/import-transfer",
    receiptDetail: "/api/order-service/admin/trade-bill/detail-receipt",
    receiptDetailExport:
      "/api/order-service/admin/trade-bill/detail-receipt-export",
    yopDetailExport: "/api/order-service/admin/trade-bill/detail-yop-export",
    yopDetail: "/api/order-service/admin/trade-bill/detail-yop",
    cftDetail: "/api/order-service/admin/trade-bill/detail-cft",
    cftDetailExport: "/api/order-service/admin/trade-bill/detail-cft-export"
  },
  // 弈客开卡
  yiKeOpenCard: {
    membershipList: "/api/student-service/yikeCard/membership-list", // 会员卡列表
    openCardCustomer: "/api/student-service/yikeCard/send-customer-membership", // 意向客户开卡
    openCardStudent: "/api/student-service/yikeCard/send-student-membership", // 学员开卡
    prepaidExport: "/api/student-service/yikeCard/prepaid-export", // 充值卡导出
    prepaidList: "/api/student-service/yikeCard/prepaid-list", // 充值卡列表
    sendMembership: "/api/student-service/yikeCard/send-membership", // 会员卡发放
    sendPrepaid: "/api/student-service/yikeCard/send-prepaid", // 充值卡发放
    createMembership: "/api/student-service/yikeCard/create-membership", // 创建会员卡
    createPrepaid: "/api/student-service/yikeCard/create-prepaid", // 创建充值卡
    entityCardRecordExport:
      "/api/student-service/yikeCard/open-entity-list-export", // 实体卡记录导出
    entityCardRecord: "/api/student-service/yikeCard/open-entity-list", // 实体卡记录
    virtualCardRecordExport:
      "/api/student-service/yikeCard/open-virtual-list-export", // 虚拟卡记录导出
    virtualCardRecord: "/api/student-service/yikeCard/open-virtual-list", // 虚拟卡记录
    customerOpenList: "/api/student-service/yikeCard/customer-open-list", // 意向客户列表开卡记录
    customerOpenListExport:
      "/api/student-service/yikeCard/customer-open-list-export", // 意向客户列表开卡记录导出
    studentOpenList: "/api/student-service/yikeCard/student-open-list", // 学员列表开卡记录
    studentOpenListExport:
      "/api/student-service/yikeCard/student-open-list-export" // 学员列表开卡记录导出
  },
  // 动态看板
  targetValueKanBan: {
    importIndicator: "/api/organization-service/dynamic-board/import-new", // 导入指标
    indicatorUpdate: "/api/organization-service/dynamic-board/update", // 更新指标
    indicatorList: "/api/organization-service/dynamic-board/list" // 指标列表
  },
  // 总览
  overview: {
    all: "/api/organization-service/drive/list"
  }
};
export default permissionInfo;
