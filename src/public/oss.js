// import OSS from "ali-oss";
import Vue from "vue";
import wareCategoryApi from "../api/uploadOss";
let region = "";
let accessKeyId = "";
let accessKeySecret = "";
let bucket = "";

// let crypto;
// 解密方法
const crypto = require("crypto");
const ALGORITHM = "aes-256-cbc";
const CIPHER_KEY = "jjNK7oAPou4tagURZjxAjymjyAoBsC39"; // Same key used in Golang
const BLOCK_SIZE = 16;
const getAliyun = function (data) {
  console.log(data);
  wareCategoryApi
    .AliyunWareCategory(data)
    .then((res) => {
      accessKeyId = decrypt(res.data.accessKeyId);
      accessKeySecret = decrypt(res.data.accessSecret);
      region = res.data.region;
      bucket = res.data.bucket;
    })
    .catch((err) => {
      console.error(err);
    });
};
// 上传文件
function decrypt(cipherText) {
  const contents = Buffer.from(cipherText, "hex");
  const iv = contents.slice(0, BLOCK_SIZE);
  const textBytes = contents.slice(BLOCK_SIZE);

  const decipher = crypto.createDecipheriv(ALGORITHM, CIPHER_KEY, iv);
  let decrypted = decipher.update(textBytes, "hex", "utf8");
  decrypted += decipher.final("utf8");
  return decrypted;
}

/**
 *
 * @param {上传是设置文件key , 一般为文件名称} objectKey
 * @param {文件file} file
 */

//  上传
const CooOss = function (file, progressCallback) {
  // eslint-disable-next-line no-undef
  const client = new OSS({
    region,
    accessKeyId,
    accessKeySecret,
    bucket
  });

  if (this instanceof CooOss) {
    const objectKey = file.name;
    return new Promise((resolve, reject) => {
      client
        .multipartUpload(objectKey, file, {
          progress: function (p, checkpoint) {
            // 返回上传进度百分比
            if (typeof progressCallback === "function") {
              // 转换为0-100的百分比值
              progressCallback(Math.floor(p * 100));
            }
          }
        })
        .then(() => {
          resolve({
            code: 0,
            objectKey,
            url: this.getOssFileUrl(objectKey),
            msg: "ok"
          });
        })
        .catch(() => {
          Vue.prototype.$message.error("上传出错了");
          const error = new Error("上传出错了");
          error.code = -1;
          error.url = "";
          error.objectKey = "";
          reject(error);
        });
    });
  } else {
    return new CooOss(file, progressCallback);
  }
};

/**
 *
 * @param {上传是设置文件key 一般是文件名} obecjtKey
 */
CooOss.prototype.getOssFileUrl = (obecjtKey) => {
  if (!obecjtKey) return new Error("object key 必须传");
  return "https://" + bucket + "." + region + ".aliyuncs.com/" + obecjtKey;
};

export default {
  install(Vue) {
    Vue.prototype.Oss = {
      uploadFile: CooOss,
      getAliyun
    };
  }
};
