// 沟通类型
const communication_type = [
  { name: "不限", id: "" },
  { name: "到店面谈", id: "face" },
  { name: "电话", id: "phone" },
  { name: "微信", id: "WeChat" }
];

// 客户状态
const customer_status = [
  { name: "未到店", id: "4" },
  { name: "已到店", id: "7" }
  // {name:"无效数据",id:"invalid"},
];
// 无效沟通原因
const invalid_reason = [
  { name: "电话未接", id: "missed_call" },
  { name: "关机", id: "shutdown" },
  { name: "空号", id: "empty_number" },
  { name: "停机", id: "downtime" },
  { name: "无法接通", id: "cannot_connect" },
  { name: "占线", id: "busy" },
  { name: "微信未回复", id: "wechat_no_reply" },
  { name: "没有学习围棋意向", id: "no_intention" },
  { name: "已报其他竞品", id: "yiba<PERSON><PERSON><PERSON>jingpin" }
];
// 下次跟进类型
const next_follow_type = [
  // {name:"QQ",id:"QQ"},
  { name: "不限", id: "" },
  { name: "到店面谈", id: "face" },
  { name: "电话", id: "phone" },
  { name: "微信", id: "WeChat" }
];
// 跟进状态
const follow_status = [
  { name: "不限", id: undefined },
  { name: "待跟进", id: "pending" },
  { name: "已跟进", id: "followed" }
]; // 下次跟进类型
const valid_status = [
  { name: "不限", id: "" },
  { name: "有效", id: "valid" },
  { name: "无效", id: "invalid" },
  { name: "待确定", id: "pending" }
];
// 承诺到访类型
const promise_visit_type = [
  // {name:"讲座",id:"lecture"},
  { name: "试听课", id: "trial_lesson" }
];
// 是否到访
const whether_to_visit = [
  { name: "不限", id: "" },
  { name: "未到访", id: false },
  { name: "已到访", id: true }
];
// 点名 缺勤原因
const leave_reason = [
  { name: "无", id: "empty" },
  { name: "事假", id: "personal_leave" },
  { name: "病假", id: "sick_leave" },
  { name: "旷课", id: "truant_leave" }
];

const teacher_level = ["N1", "N2", "N3", "N4", "N5", "N6", "N7", "N8"];
const nie_dao_level_list = [
  { name: "N1", id: "N1" },
  { name: "N2", id: "N2" },
  { name: "N3", id: "N3" },
  { name: "N4", id: "N4" },
  { name: "N5", id: "N5" },
  { name: "N6-N8", id: "N6-N8" }
];
const course_years = (function () {
  const currYear = new Date().getFullYear();
  const arr = [];
  for (let i = currYear - 10; i < currYear + 10; i++) {
    arr.push(i);
  }
  return arr;
})();

const workflow_status = [
  {
    name: "全部",
    id: ""
  },
  {
    name: "待审批",
    id: "pending"
  },
  {
    name: "审批中",
    id: "reviewing"
  },
  {
    name: "审批通过",
    id: "complete"
  },
  {
    name: "已驳回",
    id: "reject"
  },
  {
    name: "已撤销",
    id: "revoke"
  }
];
// 定义企业微信不同jump类型的跳转路由页面
const work_wechat_jump_path = {
  approval: "/workWechat/workflow" // 流程审批
};
const base_info_components_name = {
  flow_1: "OpenClassFlow", // 开班申请
  refund: "RefundApprove", // 退费申请
  transfer: "SchoolFeeTransfer", // 转校转费申请
  admin_test: "RefundApprove",
  customer_transfer: "CustomerTransfer", // 意向客户转校审批
  receipt_invoice: "InvoiceApprove" // 发票申请
};
// 前台业务-收费业务类型
const charge_business_type = {
  course: "course" // 课程
};
// 退费进度
const refund_progress = [
  {
    id: "",
    name: "不限"
  },
  {
    id: "unaudited",
    name: "待审核"
  },
  {
    id: "unpaid",
    name: "待付款"
  },
  {
    id: "paid",
    name: "已付款"
  },
  {
    id: "cancel",
    name: "审批驳回"
  },
  {
    id: "pay_cancel",
    name: "付款驳回"
  },
  {
    id: "discard",
    name: "已作废"
  },
  {
    id: "revocation",
    name: "审批撤销"
  }
];
// 退费状态
const refund_status = [
  {
    id: "",
    name: "不限"
  },
  {
    id: "pending",
    name: "待退费"
  },
  {
    id: "unaudited",
    name: "待审核"
  },
  {
    id: "unpaid",
    name: "待付款"
  },
  {
    id: "paid",
    name: "已付款"
  },
  {
    id: "cancel",
    name: "审批驳回"
  },
  {
    id: "pay_cancel",
    name: "付款驳回"
  },
  {
    id: "discard",
    name: "已作废"
  },
  {
    id: "revocation",
    name: "审批撤销"
  }
];

// 退费管理--扣款项目

const refund_project = [
  {
    key: "confirm_fee",
    label: "确认收入",
    amount: 0,
    autoOffset: false
  },
  {
    key: "manage_fee",
    label: "管理费",
    amount: 0,
    autoOffset: false
  },
  {
    key: "handling_fee",
    label: "手续费",
    amount: 0,
    autoOffset: false
  },
  {
    key: "card_fee",
    label: "刷卡费",
    amount: 0,
    autoOffset: false
  },
  {
    key: "offline_fee",
    label: "无锡八方汇-线下活动",
    amount: 0,
    autoOffset: false
  },
  {
    key: "data_fee",
    label: "资料费",
    amount: 0,
    autoOffset: false
  }
];

const channel_source = [
  { label: "网校", value: 1 },
  { label: "直营校", value: 2 }
];
// 收费汇总 表头

const approve_template_type = [
  {
    id: "refund",
    name: "退费审批"
  },
  {
    id: "open_class",
    name: "开班申请"
  },
  {
    id: "transfer",
    name: "转校转费"
  },
  {
    id: "customer_transfer",
    name: "意向客户转校审批"
  },
  {
    id: "receipt_invoice",
    name: "开票申请"
  }
];
const yop_channel_list = [
  {
    id: "OFFLINE",
    name: "线下0.27"
  },
  {
    id: "REGISTRATION",
    name: "教培0.49"
  },
  {
    id: "ONLINE",
    name: "线上0.65"
  }
];
// 网校-退费来源
const tw_remark_list = [
  { name: "自播", id: "自播" },
  { name: "达播", id: "达播" },
  { name: "低转正", id: "低转正" },
  { name: "续报", id: "续报" }
];
// 网校-渠道类型列表
const tw_channel_list = [
  { value: "feed", label: "信息流" },
  { value: "da_bo", label: "达播" },
  { value: "zi_bo", label: "自播" },
  { value: "business", label: "商务渠道" }
];
// 微信消息推送场景
const wechat_message_scene = {
  graduation: "结业",
  sign_up: "报名",
  audition: "试听后"
};
// 开票状态
const invoice_status = [
  { name: "未申请", id: 0 },
  { name: "审批中", id: 1 },
  { name: "已开票", id: 2 },
  { name: "拒绝开票", id: 3 },
  { name: "审批完成待开票", id: 4 },
  { name: "开票作废", id: 5 }
];

// 卡类型
const card_type = [
  { name: "全部", id: undefined },
  { name: "会员卡", id: "1" },
  { name: "充值卡", id: "2" }
];
// 卡有效期类型
const card_validity_type = [
  { name: "全部", id: undefined },
  { name: "月卡", id: "1" },
  { name: "半年卡", id: "2" },
  { name: "年卡", id: "3" },
  { name: "双周卡", id: "4" },
  { name: "季卡", id: "5" },
  { name: "双月卡", id: "6" },
  { name: "周卡", id: "15" }
];

// 发卡客户状态
const send_card_customer_status = [
  {
    id: undefined,
    name: "全部"
  },
  {
    name: "意向客户",
    id: "intention"
  },
  {
    name: "临时学员",
    id: "temp"
  },
  {
    name: "试听学员",
    id: "audition"
  },
  {
    name: "在读学员",
    id: "in_school"
  },
  {
    name: "休学学员",
    id: "out_school"
  },
  {
    name: "退学学员",
    id: "drop_school"
  }
];
// 发卡方式
const send_card_type = [
  {
    id: undefined,
    name: "全部"
  },
  {
    id: 1,
    name: "自动"
  },
  {
    id: 2,
    name: "手动"
  }
];
// 售卖对象
const sales_to_list = {
  audition: "试听",
  drop_school: "退学",
  in_school: "在读",
  out_school: "休学",
  temp: "临时",
  intention: "意向客户",
  visitor: "游客"
};
// 课程属性
const course_attribute = [
  {
    name: "仅开班",
    id: "1"
  },
  {
    name: "仅售卖",
    id: "2"
  },
  {
    name: "开班并售卖",
    id: "3"
  }
];
// 用户反馈类型 课程质量、服务态度、费用疑问、系统问题、教师反馈、活动建议、其他反馈

const user_feedback_type = [
  {
    id: "1",
    name: "课程质量"
  },
  {
    id: "2",
    name: "服务态度"
  },
  {
    id: "3",
    name: "费用疑问"
  },
  {
    id: "4",
    name: "系统问题"
  },
  {
    id: "5",
    name: "教师反馈"
  },
  {
    id: "6",
    name: "活动建议"
  },
  {
    id: "7",
    name: "其他反馈"
  }
];
// 可见范围
const visible_range_options = [
  { name: "公开", id: "open" },
  { name: "在读", id: "in_school" },
  { name: "休学", id: "out_school" },
  { name: "试听", id: "audition" },
  { name: "临时", id: "temp" },
  { name: "意向", id: "customer" }
];
// 发布状态
const publish_status_options = [
  {
    id: 1,
    name: "未发布"
  },
  {
    id: 2,
    name: "待审核"
  },
  {
    id: 3,
    name: "已下架"
  },
  {
    id: 4,
    name: "审核通过"
  },
  {
    id: 5,
    name: "审核驳回"
  }
];
export {
  communication_type,
  customer_status,
  invalid_reason,
  next_follow_type,
  follow_status,
  valid_status,
  promise_visit_type,
  whether_to_visit,
  leave_reason,
  teacher_level,
  course_years,
  workflow_status,
  work_wechat_jump_path,
  base_info_components_name,
  charge_business_type,
  refund_project,
  // price_type_list,
  channel_source,
  approve_template_type,
  yop_channel_list,
  tw_remark_list,
  wechat_message_scene,
  tw_channel_list,
  sales_to_list,
  course_attribute,
  user_feedback_type,
  nie_dao_level_list,
  visible_range_options,
  publish_status_options,
  invoice_status,
  refund_status,
  refund_progress,
  card_validity_type,
  card_type,
  send_card_customer_status,
  send_card_type
};
