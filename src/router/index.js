/* eslint-disable */
import Vue from "vue";
import VueRouter from "vue-router";
import store from "../store/index";
import VueCookies from "vue-cookies";
import NProgress from "nprogress"; // progress bar
import "nprogress/nprogress.css";
NProgress.configure({
  showSpinner: false
}); // NProgress Configuration
Vue.use(VueRouter);
const index = () => import("@/views/index");

const routes = [
  {
    path: "/login",
    name: "login",
    component: () => import("@/views/login"),
    meta: {
      title: "登录",
      requireAuth: false
    }
  },
  {
    path: "/teacherLiveStreaming",
    name: "teacherLiveStreaming",
    component: () => import("@/views/teacherLiveStreaming/index"),
    meta: {
      title: "直播",
      requireAuth: false
    }
  },
  {
    path: "/videoWall",
    name: "videoWall",
    component: () => import("@/views/teacherLiveStreaming/videoWall"),
    meta: {
      title: "视频墙",
      requireAuth: false
    }
  },
  {
    path: "/jointest",
    name: "jointest",
    component: () => import("@/views/teacherLiveStreaming/jointest"),
    meta: {
      title: "加入房间测试",
      requireAuth: false
    }
  },
  {
    path: "/resetPassword",
    name: "resetPassword",
    component: () => import("@/views/resetPassword"),
    meta: {
      title: "重置密码",
      requireAuth: false
    }
  },
  {
    path: "/privacy/:type",
    name: "privacy",
    component: () => import("@/views/privacy/privacy"),
    props: true,
    meta: {
      title: "隐私协议",
      requireAuth: false
    }
  },
  {
    path: "/workWechat",
    name: "workWechat",
    component: () => import("@/wap/workWechat/"),
    meta: {
      title: "正在跳转...",
      requireAuth: false
    }
  },
  {
    path: "/workWechat/workflow",
    name: "qywxWorkflow",
    component: () => import("@/wap/workWechat/workflow"),
    meta: {
      title: "",
      requireAuth: false
    }
  },
  {
    path: "/workWechat/workflow/workflowInfo",
    name: "qywxWorkflowInfo",
    component: () => import("@/wap/workWechat/workflow/workflowInfo.vue"),
    meta: {
      title: "",
      requireAuth: false
    }
  },
  {
    path: "/question",
    name: "question",
    component: () => import("@/views/questionManagement/h5/index.vue"),
    meta: {
      title: "调查问卷",
      requireAuth: false
    }
    // api: ["taskpool-service/personal_center/menu"]
  },
  {
    path: "/personalCenterDetail",
    name: "personalCenterDetail",
    component: () => import("@/views/personal/personalDetail"),
    meta: {
      title: "",
      requireAuth: false
    }
  },
  {
    path: "/qrPay",
    name: "qrPay",
    component: () => import("@/wap/eBaoPay/qrPayIndex.vue"),
    meta: {
      title: "订单详情",
      requireAuth: false
    }
  },
  {
    path: "/preview",
    name: "preview",
    component: () => import("@/wap/miniProgram/preview.vue"),
    meta: {
      title: "预览",
      requireAuth: false
    }
  },
  {
    path: "/studyReportPreview",
    name: "studyReportPreview",
    component: () => import("@/wap/miniProgram/studyReportPreview.vue"),
    meta: {
      title: "详情信息",
      requireAuth: false
    }
  },
  //小程序富文本编辑器（webview页面）
  {
    path: "/mini-richText",
    name: "miniRichText",
    show: false,
    component: () => import("@/wap/minigrammar/richText.vue"),
    meta: {
      title: "编辑内容",
      requireAuth: false
    }
  }
];
const commonRoute = [
  {
    path: "/",
    component: index,
    type: "only",
    // redirect: "/personalCenter",
    redirect: "/marketStudent",
    children: [
      {
        path: "/index",
        name: "homePage",
        component: () => import("@/views/homePage/homePage"),
        show: false,
        meta: {
          title: "首页",
          requireAuth: true
        }
      },
      {
        path: "/notFound",
        name: "notFound",
        component: () => import("@/views/notFound/notFound"),
        meta: {
          title: "404",
          requireAuth: false
        }
      },
      {
        path: "/loading",
        name: "loading",
        component: () => import("@/views/loading"),
        meta: {
          title: "加载中...",
          requireAuth: false
        }
      }
    ]
  }
];
const asyncRoute = [
  {
    path: "/market",
    component: index,
    src: require("@/assets/图片/icon_market.png"),
    src_ac: require("@/assets/图片/icon_market_ac.png"),
    meta: {
      title: "市场管理",
      requireAuth: true
    },
    children: [
      {
        name: "marketStudent",
        path: "/marketStudent",
        show: true,
        component: () => import("@/views/marketStudent/marketStudent"),
        meta: {
          title: "客户管理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["market-service/customer/menu"]
      },
      {
        name: "marketStudentWx",
        path: "/marketStudentWx",
        show: true,
        component: () => import("@/views/marketStudentWx"),
        meta: {
          title: "网校客户管理",
          requireAuth: true
        },
        api: ["marketwx-service/customer/menu"]
      },
      {
        name: "phoneRecord",
        path: "/phoneRecord",
        component: () => import("@/views/agentManagement/phoneRecord"),
        show: true,
        meta: {
          title: "通话记录",
          requireAuth: true,
          keepAlive: true
        },
        api: ["admin/market-service/phoneRecord/menu"]
      },
      // {
      //   name: "outbound",
      //   path: "/outbound",
      //   show: true,
      //   component: outbound,
      //   meta: {
      //     title: "AI电话",
      //     requireAuth: true,
      //   },
      //   api: ["aiphone-service/device/list"],
      // },
      {
        name: "invitationManagement",
        path: "/invitationManagement",
        show: true,
        component: () =>
          import("@/views/invitationManagement/invitationManagement"),
        meta: {
          title: "邀约查询",
          requireAuth: true,
          keepAlive: true
        },
        api: ["market-service/invitation/menu"]
      },
      {
        name: "audition",
        path: "/audition",
        show: true,
        component: () => import("@/views/audition/index"),
        meta: {
          title: "试听管理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["school-service/audition/menu"]
      }
    ]
  },
  {
    path: "/stage",
    component: index,
    src: require("@/assets/图片/icon_stage.png"),
    src_ac: require("@/assets/图片/icon_stage_ac.png"),
    meta: {
      title: "前台业务",
      requireAuth: true
    },
    children: [
      {
        name: "studentInfor",
        path: "/studentInfor",
        show: true,
        // component: studentInfor,
        component: () => import("@/views/studentInfor/studentInfor"),
        meta: {
          title: "学员信息管理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["student-service/student/menu"]
      },
      {
        name: "studentInforDetails",
        path: "/studentInforDetails",
        parent: "/studentInfor",
        show: false,
        component: () => import("@/views/studentInfor/studentInforDetails"),
        meta: {
          title: "学员信息明细",
          requireAuth: true,
          keepAlive: false
        },
        api: ["student-service/student/info"]
      },
      {
        name: "orderManagement",
        path: "/orderManagement",
        show: true,
        component: () => import("@/views/orderManagement/index"),
        meta: {
          title: "订单管理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["order-service/admin/order/menu"]
      },
      {
        name: "schoolTransferFee",
        path: "/schoolTransferFee",
        show: true,
        component: () => import("@/views/schoolTransferFee/index"),
        meta: {
          title: "转校转费",
          requireAuth: true,
          keepAlive: false
        },
        api: ["school-transfer-fee/menu"]
      },
      {
        name: "changeSchoolBasicInfo",
        path: "/changeSchoolBasicInfo",
        parent: "/schoolTransferFee",
        show: false,
        component: () =>
          import("@/views/schoolTransferFee/changeSchoolBasicInfo"),
        meta: {
          title: "转校转费后基础信息",
          requireAuth: true,
          keepAlive: false
        },
        api: ["order-service/admin/order/create"]
      },
      {
        name: "chargeIndex",
        path: "/chargeIndex",
        show: true,
        component: () => import("@/views/charge/index"),
        meta: {
          title: "收费管理",
          requireAuth: true,
          keepAlive: false
        },
        api: ["order-service/admin/charge/menu"]
      },
      {
        name: "carryOver",
        path: "/carryOver",
        show: true,
        component: () => import("@/views/carryOver/index"),
        meta: {
          title: "费用结转",
          requireAuth: true,
          keepAlive: false
        },
        api: ["order-service/admin/transfer/menu"]
      },
      {
        name: "refund",
        path: "/refund",
        show: true,
        component: () => import("@/views/refund/index"),
        meta: {
          title: "退费管理",
          requireAuth: true,
          keepAlive: false
        },
        api: ["order-service/admin/refund/menu"]
      },
      {
        name: "campusCouponList",
        path: "/campusCouponList",
        show: true,
        component: () => import("@/views/discountCoupon/campusCouponList"),
        meta: {
          title: "校区优惠券管理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["coupon-service/coupon-template/menu"]
      },

      {
        name: "referral",
        path: "/referral",
        show: true,
        component: () => import("@/views/business/referral/index"),
        meta: {
          title: "转介绍管理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["business/referral/menu"]
      }
    ]
  },
  {
    path: "/educational",
    component: index,
    src: require("@/assets/图片/icon_educational_ac.png"),
    src_ac: require("@/assets/图片/icon_educational.png"),
    meta: {
      title: "教务管理",
      requireAuth: true
    },
    children: [
      // 废弃开班申请，不需要了。老唐说的
      // {
      //   name: "offerManagement",
      //   path: "/offerManagement",
      //   show: true,
      //   component: () => import("@/views/offerManagement/offerManagement"),
      //   meta: {
      //     title: "开班申请",
      //     requireAuth: true,
      //     keepAlive: true
      //   },
      //   api: ["school-service/audit-classroom/menu"]
      // },
      {
        name: "classManagement",
        path: "/classManagement",
        show: true,
        component: () => import("@/views/classManagement/classManagement"),
        meta: {
          title: "班级管理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["school-service/classroom/menu"]
      },
      {
        name: "classDetail",
        path: "/classDetail",
        parent: "/classManagement",
        show: false,
        component: () => import("@/views/classDetail/index"),
        meta: {
          title: "班级详情",
          requireAuth: true,
          keepAlive: false
        },
        api: ["school-service/classroom/info"]
      },
      {
        name: "classManagementDivid",
        path: "/classManagementDivid",
        component: () => import("@/views/classManagement/dividingClasses"),
        parent: "/classManagement",
        show: false,
        meta: {
          title: "班级管理-入班",
          requireAuth: true,
          keepAlive: false
        },
        api: ["school-service/classroom/add-student"]
      },
      {
        name: "classManagementDividMatch",
        path: "/classManagementDividMatch",
        component: () => import("@/views/classManagement/dividingClassesMatch"),
        parent: "/classManagement",
        show: false,
        meta: {
          title: "班级管理-赛事入班",
          requireAuth: true,
          keepAlive: false
        },
        api: ["school-service/classroom/add-student"]
      },
      {
        name: "offDutyClasses",
        path: "/offDutyClasses",
        component: () => import("@/views/classManagement/offDutyClasses"),
        parent: "/classManagement",
        show: false,
        meta: {
          title: "班级管理-出班",
          requireAuth: true,
          keepAlive: false
        },
        api: ["school-service/classroom/remove-student"]
      },
      {
        name: "classroomAvailability",
        path: "/classroomAvailability",
        show: true,
        component: () =>
          import("@/views/schoolServiceScheduling/classroomAvailability"),
        meta: {
          title: "教师空闲时间",
          requireAuth: true
        },
        api: ["school-service/classroomAvailability/menu"]
      },
      {
        name: "schoolServiceScheduling",
        path: "/schoolServiceScheduling",
        show: true,
        component: () =>
          import("@/views/schoolServiceScheduling/schoolServiceScheduling"),
        meta: {
          title: "排课管理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["school-service/scheduling/menu"]
      },
      {
        name: "classScheduleAndRollCall",
        path: "/classScheduleAndRollCall",
        show: true,
        component: () => import("@/views/classScheduleAndRollCall/index"),
        meta: {
          title: "课表和点名",
          requireAuth: true,
          keepAlive: true
        },
        api: ["school-service/schoolroom/menu"]
      },
      {
        name: "redo",
        path: "/redo",
        show: true,
        component: () => import("@/views/redo/index"),
        meta: {
          title: "补课管理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["school-service/makeup/menu"]
      }
    ]
  },
  {
    path: "/livePage",
    component: index,
    src: require("@/assets/图片/is_vector.png"),
    src_ac: require("@/assets/图片/Vector.png"),
    meta: {
      title: "直播管理",
      requireAuth: true
    },
    children: [
      {
        name: "courseManages",
        path: "/courseManages",
        show: true,
        component: () => import("@/views/livePage/courseManage"),
        meta: {
          title: "监课管理",
          requireAuth: true
        },
        api: ["live-management/superintendent/menu"]
      },
      {
        name: "curriculumManage",
        path: "/curriculumManage",
        show: true,
        component: () => import("@/views/livePage/curriculumManage"),
        meta: {
          title: "直播管理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["live-management/live/menu"]
      },
      {
        name: "coursewareUpload",
        path: "/coursewareUpload",
        show: true,
        component: () => import("@/views/livePage/coursewareUpload"),
        meta: {
          title: "课件管理",
          requireAuth: true
        },
        api: ["live-courseware/courseware/menu"]
      },
      {
        path: "/playback",
        name: "playback",
        show: true,
        component: () => import("@/views/livePage/playback"),
        meta: {
          title: "回放管理",
          requireAuth: true
        },
        api: ["live-go-service/admin/course/playback"]
      },
      {
        path: "/liveBroadcast",
        name: "liveBroadcast",
        show: true,
        component: () => import("@/views/livePage/liveBroadcast"),
        meta: {
          title: "直播权限管理",
          requireAuth: true
        },
        api: ["live-go-service/admin/permission/get"]
      },
      {
        path: "/studentDetails",
        name: "studentDetails",
        show: false,
        component: () => import("@/views/livePage/page/studentDetails"),
        meta: {
          title: "学生详情",
          requireAuth: true
        },
        api: ["live-management/student/info"]
      },
      {
        path: "/classPeriod",
        name: "classPeriod",
        show: false,
        component: () => import("@/views/livePage/page/classPeriod"),
        meta: {
          title: "课程信息",
          requireAuth: true
        },
        api: ["live-management/class/period"]
      },
      {
        path: "/createLive",
        name: "createLive",
        show: false,
        component: () => import("@/views/livePage/page/createLive"),
        meta: {
          title: "创建直播",
          requireAuth: true
        },
        api: ["live-go-service/admin/course/create"]
      },
      {
        path: "/classroom",
        name: "classroom",
        show: false,
        component: () => import("@/views/livePage/page/classroom"),
        meta: {
          title: "课堂数据",
          requireAuth: true
        },
        api: ["live-management/class/room"]
      },

      {
        path: "/playbackDeila",
        name: "playbackDeila",
        show: false,
        component: () => import("@/views/livePage/page/playbackDeila"),
        meta: {
          title: "回放管理",
          requireAuth: true
        },
        api: ["live-go-service/admin/transcribe/detail"]
      },
      {
        path: "/classroomEvaluation",
        name: "classroomEvaluation",
        show: false,
        component: () => import("@/views/livePage/classroomEvaluation"),
        meta: {
          title: "课后评价",
          requireAuth: true
        },
        api: ["live-go-service/admin/teacher/live-evaluation"]
      }
    ]
  },
  {
    path: "/reportCenter",
    component: index,
    src: require("@/assets/图片/icon_report_center.png"),
    src_ac: require("@/assets/图片/icon_report_center_active.png"),
    meta: {
      title: "报表中心",
      requireAuth: true
    },
    children: [
      {
        name: "front",
        path: "/front",
        show: true,
        component: () => import("@/views/front/index"),
        meta: {
          title: "前端获客",
          requireAuth: true,
          keepAlive: true
        },
        api: ["market-service/report/customer-source/menu"]
      },
      {
        name: "salesSummary",
        path: "/salesSummary",
        show: true,
        component: () => import("@/views/salesSummary/index"),
        meta: {
          title: "销售统计报表",
          requireAuth: true,
          keepAlive: true
        },
        api: ["order-service/admin/order/sale-report/menu"]
      },
      {
        name: "studentManagement",
        path: "/studentManagement",
        show: true,
        component: () => import("@/views/studentManagement/index"),
        meta: {
          title: "学员管理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["market-service/admim/studentManagement/menu"]
      },
      {
        name: "studentCostReport",
        path: "/studentCostReport",
        show: true,
        component: () => import("@/views/studentCostReport/index"),
        meta: {
          title: "学员费用报表",
          requireAuth: true,
          keepAlive: true
        },
        api: ["order-service/admin/wallet/statistics/menu"]
      },
      {
        name: "studentAnalysis",
        path: "/studentAnalysis",
        show: true,
        component: () => import("@/views/studentAnalysis/index"),
        meta: {
          title: "学员分析表",
          requireAuth: true,
          keepAlive: true
        },
        api: ["workstream-service/student-analysis/menu"]
      },
      {
        name: "jionWechat",
        path: "/jionWechat",
        show: true,
        component: () => import("@/views/jionWechat/index"),
        meta: {
          title: "加微率统计",
          requireAuth: true,
          keepAlive: true
        },
        api: ["market-service/customer/count/add/wechat/menu"]
      },
      {
        name: "refundFeeSituation",
        path: "/refundFeeSituation",
        show: true,
        component: () => import("@/views/refundFeeSituation/index"),
        meta: {
          title: "退交费情况",
          requireAuth: true,
          keepAlive: true
        },
        api: ["order-service/admin/refundFeeSituation/menu"]
      },
      {
        name: "receiptManagement",
        path: "/receiptManagement",
        show: true,
        component: () => import("@/views/receiptManagement/index"),
        meta: {
          title: "收据管理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["order-service/admin/receipt/menu"]
      },
      // {
      //   name: "moneybackReport",
      //   path: "/moneybackReport",
      //   show: true,
      //   // component: () => import("@/views/financeLock/index"),
      //   meta: {
      //     title: "退费报表",
      //     requireAuth: true,
      //     keepAlive: true
      //   },
      //   api: ["moneybackReport/admin/menu"]
      // },
      {
        name: "eliminationCourse",
        path: "/eliminationCourse",
        show: true,
        component: () => import("@/views/eliminationCourse/index"),
        meta: {
          title: "课消报表",
          requireAuth: true,
          keepAlive: true
        },
        api: ["order-service/admin/deduct/courseReport/menu"]
      },
      {
        name: "chargeSummary",
        path: "/chargeSummary",
        show: true,
        component: () => import("@/views/chargeSummary/index"),
        meta: {
          title: "收费汇总表",
          requireAuth: true,
          keepAlive: true
        },
        api: ["order-service/admin/fee-stat/menu"]
      },
      {
        name: "chargeReport",
        path: "/chargeReport",
        show: true,
        component: () => import("@/views/chargeReport/index"),
        meta: {
          title: "退费报表",
          requireAuth: true,
          keepAlive: true
        },
        api: ["order-service/admin/refund/item/menu"]
      },
      {
        name: "remittanceTransferReport",
        path: "/remittanceTransferReport",
        show: true,
        component: () => import("@/views/remittanceTransferReport/index"),
        meta: {
          title: "转费明细表",
          requireAuth: true,
          keepAlive: true
        },
        api: ["order-service/admin/remittanceTransferReport/item/menu"]
      },
      // {
      //   name: "goodsReport",
      //   path: "/goodsReport",
      //   show: true,
      //   // component: () => import("@/views/financeLock/index"),
      //   meta: {
      //     title: "物品报表",
      //     requireAuth: true,
      //     keepAlive: true
      //   },
      //   api: ["order-service/admin/goods/item/menu"]
      // },
      // {
      //   name: "matchReport",
      //   path: "/matchReport",
      //   show: true,
      //   // component: () => import("@/views/financeLock/index"),
      //   meta: {
      //     title: "赛事报表",
      //     requireAuth: true,
      //     keepAlive: true
      //   },
      //   api: ["order-service/admin/matchReport/menu"]
      // },
      {
        name: "classReport",
        path: "/classReport",
        show: true,
        component: () => import("@/views/classReport/index"),
        meta: {
          title: "班级报表",
          requireAuth: true,
          keepAlive: true
        },
        api: ["school-service/classroom/roster/menu"]
      },
      {
        name: "performanceReport",
        path: "/performanceReport",
        show: true,
        component: () => import("@/views/performanceReport/index"),
        meta: {
          title: "业绩报表",
          requireAuth: true,
          keepAlive: true
        },
        api: ["order-service/admin/performanceReport/menu"]
      },
      // {
      //   name: "otherReport",
      //   path: "/otherReport",
      //   show: true,
      //   // component: () => import("@/views/financeLock/index"),
      //   meta: {
      //     title: "其他报表",
      //     requireAuth: true,
      //     keepAlive: true
      //   },
      //   api: ["order-service/admin/othersReport/menu"]
      // },
      {
        name: "renewalReport",
        path: "/renewalReport",
        show: true,
        component: () => import("@/views/renewalReport/index"),
        meta: {
          title: "续费率报表",
          requireAuth: true,
          keepAlive: true
        },
        api: ["order-service/admin/renewalReport/menu"]
      },
      {
        name: "suspensionPool",
        path: "/suspensionPool",
        show: true,
        component: () => import("@/views/suspensionPool/index"),
        meta: {
          title: "学员池",
          requireAuth: true,
          keepAlive: true
        },
        api: ["order-service/admin/suspensionPool/menu"]
      },
      // {
      //   name: "otherReport",
      //   path: "/otherReport",
      //   show: true,
      //   // component: () => import("@/views/financeLock/index"),
      //   meta: {
      //     title: "其他报表",
      //     requireAuth: true,
      //     keepAlive: true
      //   },
      //   api: ["order-service/admin/othersReport/menu"]
      // }
      {
        // 电子签管理
        name: "signManagement",
        path: "/signManagement",
        component: () => import("@/views/signManagement/index"),
        show: true,
        meta: {
          title: "电子签管理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["order-service/admin/signManagement/menu"]
      },
      {
        name: "frontendDataPanel",
        path: "/frontendDataPanel",
        show: true,
        component: () => import("@/views/frontendDataPanel/index"),
        meta: {
          title: "前端数据面板",
          requireAuth: true,
          keepAlive: true
        },
        api: ["report-center-service/frontend-data-panel/menu"]
      },
      {
        name: "leaseManagement",
        path: "/leaseManagement",
        show: true,
        component: () => import("@/views/leaseManagement/index"),
        meta: {
          title: "租赁管理",
          requireAuth: true
        },
        api: ["finance-service/lease/menu"]
      },
      {
        name: "marketTaskIndicator",
        path: "/marketTaskIndicator",
        show: true,
        component: () => import("@/views/marketTaskIndicator/index"),
        meta: {
          title: "市场专员完成度",
          requireAuth: true,
          keepAlive: true
        },
        api: ["frontend/conversion/menu"]
      }
    ]
  },

  {
    name: "questionManagement",
    path: "/questionManagement",
    component: index,
    src: require("@/assets/图片/questionManagement.png"),
    src_ac: require("@/assets/图片/questionManagement_avtive.png"),
    redirect: { name: "survey" },
    show: true,
    meta: {
      title: "小程序管理",
      requireAuth: true,
      keepAlive: true
    },
    children: [
      {
        path: "/template",
        name: "template",
        show: true,
        component: () => import("@/views/templateManagement/index.vue"),
        meta: {
          requireAuth: true,
          title: "模板管理"
        },
        api: ["questionnaire-service/admin/template/menu"]
      },
      {
        path: "/survey",
        name: "survey",
        show: true,
        component: () =>
          import("@/views/questionManagement/management/pages/list/index.vue"),
        meta: {
          requireAuth: true,
          title: "问卷列表"
        },
        api: ["questionManagement/menu"]
      },
      {
        name: "miniCoupon",
        path: "/miniCoupon",
        component: () => import("@/views/coupon/index"),
        show: true,
        meta: {
          title: "学员优惠券列表",
          requireAuth: true
        },
        api: ["order-service/admin/miniCoupon/menu"]
      },
      // 和天工优惠券打通后不需要这个菜单了
      // {
      //   name: "couponList",
      //   path: "/couponList",
      //   component: () => import("@/views/coupon/couponList"),
      //   show: true,
      //   meta: {
      //     title: "小程序优惠券",
      //     requireAuth: true
      //   },
      //   api: ["order-service/admin/couponList/menu"]
      // },
      {
        name: "surveyStatistics",
        path: "/surveyStatistics",
        component: () =>
          import(
            "@/views/questionManagement/management/pages/statistics/index.vue"
          ),
        meta: {
          title: "调查问卷统计统计",
          requireAuth: true,
          keepAlive: false,
          // 本地缓存该页面右上角校区的key
          schoolStorageKey: "courseConsultantTaskPoolSchool"
        },
        api: ["questionnaire-service/admin/survey/statistics"]
      },
      // {
      //   name: "traineeReportManagement",
      //   path: "/traineeReportManagement",
      //   component: () => import("@/views/trainee/reportManagement"),
      //   show: true,
      //   meta: {
      //     title: "学员报告管理",
      //     requireAuth: true,
      //     keepAlive: true
      //   },
      //   api: ["views/trainee/reportManagement/menu"]
      // },
      {
        name: "courseSummaryManagement",
        path: "/courseSummaryManagement",
        component: () => import("@/views/trainee/reportManagement"),
        show: true,
        meta: {
          title: "课程总结管理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["views/trainee/reportManagement/menu"]
      },
      {
        name: "parentClassManagement",
        path: "/parentClassManagement",
        component: () => import("@/views/trainee/parentClassManagement"),
        show: true,
        meta: {
          title: "家长课堂管理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["views/trainee/parentClassManagement/menu"]
      },
      {
        name: "classNoticeManagement",
        path: "/classNoticeManagement",
        component: () => import("@/views/trainee/classNoticeManagement"),
        show: true,
        meta: {
          title: "班级通知管理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["views/trainee/classNoticeManagement/menu"]
      },
      {
        name: "restCourse",
        path: "/restCourse",
        component: () => import("@/views/restCourseManagement/index"),
        show: true,
        meta: {
          title: "小程序权限配置",
          requireAuth: true
        },
        api: ["views/restCourse/restCourseManagement"]
      },
      {
        name: "appletResourceHub",
        path: "/appletResourceHub",
        component: () => import("@/views/appletResource/index"),
        show: true,
        meta: {
          title: "小程序资料库",
          requireAuth: true
        },
        api: ["views/minigram/appletResourceHub/menu"]
      },
      {
        name: "competitionManagement",
        path: "/competitionManagement",
        show: true,
        component: () => import("@/views/competitionManagement/index"),
        meta: {
          title: "赛事管理",
          requireAuth: true
        },
        api: ["questionnaire-service/competitionManagement/list/menu"]
      },
      {
        name: "competitionCreate",
        path: "/competitionCreate",
        show: false,
        component: () => import("@/views/competitionManagement/pages/create"),
        meta: {
          title: "赛事创建",
          requireAuth: true
        },
        api: ["questionnaire-service/admin/match/create"]
      },
      {
        name: "competitionDetail",
        path: "/competitionDetail",
        show: false,
        component: () => import("@/views/competitionManagement/pages/create"),
        meta: {
          title: "赛事详情",
          requireAuth: true
        },
        api: ["questionnaire-service/admin/match/detail"]
      },
      {
        name: "competitionEdit",
        path: "/competitionEdit",
        show: false,
        component: () => import("@/views/competitionManagement/pages/create"),
        meta: {
          title: "赛事编辑",
          requireAuth: true
        },
        api: ["questionnaire-service/admin/match/update"]
      },
      {
        name: "traineeReportManagementDetail",
        path: "/traineeReporDetail",
        component: () => import("@/views/trainee/detail"),
        show: false,
        meta: {
          title: "详情",
          requireAuth: true
        },
        api: ["school-service/admin/feedback/info"]
      },
      {
        path: "/survey/:id/edit",
        name: "editPage",
        meta: {
          requireAuth: true,
          meta: {
            title: "编辑优惠券",
            requireAuth: true
          }
        },
        api: ["questionnaire-service/admin/survey/getSchema"],
        component: () =>
          import("@/views/questionManagement/management/pages/edit/index.vue"),
        children: [
          {
            path: "",
            name: "QuestionEditIndex",
            meta: {
              requireAuth: true
            },
            api: ["questionnaire-service/admin/survey/QuestionEditIndex"],
            component: () =>
              import(
                "@/views/questionManagement/management/pages/edit/pages/edit.vue"
              )
          },
          {
            path: "setting",
            name: "QuestionEditSetting",
            meta: {
              requireAuth: true
            },
            api: ["questionnaire-service/admin/survey/QuestionEditSetting"],
            component: () =>
              import(
                "@/views/questionManagement/management/pages/edit/pages/setting.vue"
              )
          },
          {
            path: "resultConfig",
            name: "QuestionEditResultConfig",
            meta: {
              requireAuth: true
            },
            api: [
              "questionnaire-service/admin/survey/QuestionEditResultConfig"
            ],
            component: () =>
              import(
                "@/views/questionManagement/management/pages/edit/pages/resultConfig.vue"
              )
          }
        ]
      },
      {
        path: "survey/:id/publishResult",
        name: "publishResultPage",
        meta: {
          requireAuth: true
        },
        api: ["questionnaire-service/admin/survey/getSurvey"],
        component: () =>
          import(
            "@/views/questionManagement/management/pages/publishResult/index.vue"
          )
      },
      {
        path: "createSurvey",
        name: "createSurvey",
        meta: {
          requireAuth: false,
          // keepAlive: true,
          title: "创建问卷"
        },
        component: () =>
          import(
            "@/views/questionManagement/management/pages/create/index.vue"
          ),
        api: ["questionnaire-service/admin/survey/createSurvey"]
      },
      {
        path: "edit",
        name: "editSurvey",
        meta: {
          requireAuth: true,
          title: "修改问卷"
        },
        api: ["questionnaire-service/admin/survey/updateConf"],
        component: () =>
          import("@/views/questionManagement/management/pages/create/index.vue")
      },
      // banner管理
      {
        path: "/bannerManagement",
        name: "bannerManagement",
        show: true,
        meta: {
          requireAuth: true,
          title: "banner管理"
        },
        component: () => import("@/views/bannerManagement/index"),
        api: ["questionnaire-service/admin/banner/menu"]
      },
      // 课程产品管理
      {
        path: "/courseProductManagement",
        name: "courseProductManagement",
        show: true,
        component: () => import("@/views/courseProductManagement/index"),
        meta: {
          requireAuth: true,
          title: "课程产品管理"
        },
        api: ["course-service/course-product/menu"]
      },
      // 用户反馈
      {
        path: "/userFeedback",
        name: "userFeedback",
        show: true,
        component: () => import("@/views/userFeedback/index"),
        meta: {
          requireAuth: true,
          title: "用户反馈"
        },
        api: ["questionnaire-service/admin/survey/userFeedback/menu"]
      },
      // 时光相册
      {
        path: "/timeAlbum",
        name: "timeAlbum",
        show: true,
        component: () => import("@/views/timeAlbum/index"),
        meta: {
          requireAuth: true,
          title: "时光相册"
        },
        api: ["questionnaire-service/admin/timeAlbum/menu"]
      },
      // 客服二维码维护
      {
        path: "/qrCodeManagement",
        name: "qrCodeManagement",
        show: true,
        meta: {
          requireAuth: true,
          title: "客服二维码维护"
        },
        component: () => import("@/views/qrCodeManagement/index"),
        api: ["questionnaire-service/admin/qRCode/frontMenu"]
      },
      // 小程序用户管理
      {
        path: "/miniUserManagement",
        name: "miniUserManagement",
        show: true,
        meta: {
          requireAuth: true,
          title: "小程序用户管理"
        },
        component: () => import("@/views/miniUserManagement/index"),
        api: ["questionnaire-service/admin/miniUserManagement/menu"]
      },
      // 聂道圈
      {
        path: "/nedawCircleManagement",
        name: "nedawCircleManagement",
        show: true,
        meta: {
          requireAuth: true,
          keepAlive: true,
          title: "聂道圈管理"
        },
        component: () => import("@/views/nedawCircleManagement/index"),
        api: ["questionnaire-service/admin/nedawCircleManagement/menu"]
      },
      {
        name: "nedawCircleStatistics",
        path: "/nedawCircleStatistics",
        component: () => import("@/views/nedawCircleManagement/statistics.vue"),
        meta: {
          title: "聂道圈统计",
          requireAuth: true,
          keepAlive: false,
          // 本地缓存该页面右上角校区的key
          schoolStorageKey: "courseConsultantTaskPoolSchool"
        },
        api: ["questionnaire-service/admin/moments/statistics"]
      },

      // 聂道名师管理
      {
        name: "nedawTeacherManagement",
        path: "/nedawTeacherManagement",
        component: () => import("@/views/nedawTeacherManagement/index"),
        show: true,
        meta: {
          title: "聂道名师管理",
          requireAuth: true
        },
        api: ["questionnaire-service/admin/nedawTeacherManagement/menu"]
      },
      // 校区介绍管理
      {
        path: "/campusIntroductionManagement",
        name: "campusIntroductionManagement",
        component: () => import("@/views/campusIntroductionManagement/index"),
        show: true,
        meta: {
          title: "校区介绍管理",
          requireAuth: true
        },
        api: ["questionnaire-service/admin/campusIntroductionManagement/menu"]
      },
      {
        path: "/wonderfulVideo",
        name: "wonderfulVideo",
        show: true,
        meta: {
          requireAuth: true,
          title: "精选视频"
        },
        component: () => import("@/views/wonderfulVideo/index"),
        api: ["questionnaire-service/admin/wonderfulVideo/menu"]
      }
    ],
    api: ["questionManagement/edit"]
  },
  // {
  //   path: "/coupon",
  //   component: index,
  //   src: require("@/assets/图片/finance_grey.png"),
  //   src_ac: require("@/assets/图片/finance_green.png"),
  //   meta: {
  //     title: "优惠券管理",
  //     requireAuth: true
  //   },
  //   children: [
  //     {
  //       name: "miniCoupon",
  //       path: "/miniCoupon",
  //       component: () => import("@/views/coupon/index"),
  //       show: true,
  //       meta: {
  //         title: "学员优惠券列表",
  //         requireAuth: true
  //       },
  //       api: ["order-service/admin/miniCoupon/menu"]
  //     },
  //     {
  //       name: "couponList",
  //       path: "/couponList",
  //       component: () => import("@/views/coupon/couponList"),
  //       show: true,
  //       meta: {
  //         title: "优惠券列表",
  //         requireAuth: true
  //       },
  //       api: ["order-service/admin/couponList/menu"]
  //     }
  //   ],
  //   api: ["order-service/admin/coupon/menu"]
  // },
  {
    path: "/financial",
    component: index,
    src: require("@/assets/图片/finance_grey.png"),
    src_ac: require("@/assets/图片/finance_green.png"),
    meta: {
      title: "财务管理",
      requireAuth: true
    },
    children: [
      {
        name: "invoice",
        path: "/invoice",
        show: true,
        component: () => import("@/views/invoice/index"),
        meta: {
          title: "发票管理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["finance-service/invoice/menu"]
      },
      {
        name: "stagnantExpenses",
        path: "/stagnantExpenses",
        show: true,
        component: () => import("@/views/stagnantExpenses/index"),
        meta: {
          title: "呆滞费用清理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["finance-service/idle-expense/menu"]
      },
      {
        name: "financeLock",
        path: "/financeLock",
        show: true,
        component: () => import("@/views/financeLock/index"),
        meta: {
          title: "财务锁账期间设置",
          requireAuth: true,
          keepAlive: true
        },
        api: ["system-service/finance-range/menu"]
      },
      {
        name: "posBind",
        path: "/posBind",
        show: true,
        component: () => import("@/views/eBaoPay/bindPos.vue"),
        meta: {
          title: "Pos机绑定",
          requireAuth: true,
          keepAlive: true
        },
        api: ["order-service/yop/menu"]
      },
      {
        name: "merchantAllocation",
        path: "/merchantAllocation",
        show: true,
        component: () => import("@/views/eBaoPay/merchantAllocation.vue"),
        meta: {
          title: "易宝商户配置",
          requireAuth: true,
          keepAlive: true
        },
        api: ["order-service/merchantAllocation/menu"]
      },
      {
        name: "signTemplate",
        path: "/signTemplate",
        show: true,
        component: () => import("@/views/signTemplate/index.vue"),
        meta: {
          title: "电子签企业模版配置",
          requireAuth: true
        },
        api: ["sign-service/enterprise-template/menu"]
      },
      {
        name: "detailsFrim",
        path: "/detailsFrim",
        show: false,
        component: () => import("@/views/signTemplate/detailsFrim.vue"),
        meta: {
          title: "电子签企业模版配置",
          requireAuth: true
        },
        api: ["order-service/admin/contract/organization-info"]
      },
      {
        name: "hoseConfig",
        path: "/hoseConfig",
        show: true,
        component: () => import("@/views/hoseConfig/index.vue"),
        meta: {
          title: "合思主体配置",
          requireAuth: true,
          keepAlive: true
        },
        api: ["order-service/hoseConfig/menu"]
      },
      {
        name: "dianpingConfig",
        path: "/dianpingConfig",
        show: true,
        component: () => import("@/views/dianpingConfig/index.vue"),
        meta: {
          title: "大众点评商户配置",
          requireAuth: true,
          keepAlive: true
        },
        api: ["order-service/dianping/menu"]
      },
      {
        name: "onlinePayment",
        path: "/onlinePayment",
        show: true,
        component: () => import("@/views/onlinePayment/index"),
        meta: {
          title: "在线支付对账",
          requireAuth: true
        },
        api: ["finance-service/online-payment/menu"]
      }
    ]
  },
  {
    path: "/compensation",
    component: index,
    src: require("@/assets/图片/compensation.png"),
    src_ac: require("@/assets/图片/compensationav.png"),
    meta: {
      title: "人力管理",
      requireAuth: true
    },
    children: [
      {
        name: "compensation",
        path: "/compensation",
        show: true,
        component: () => import("@/views/compensation/index"),
        meta: {
          title: "拆表工具",
          requireAuth: true,
          keepAlive: true
        },
        api: ["order-service/admin/hose/compensation-flow-create"]
      }
    ]
  },
  {
    path: "/logistics",
    component: index,
    src: require("@/assets/图片/icon_logistics.png"),
    src_ac: require("@/assets/图片/icon_logistics_ac.png"),
    meta: {
      title: "进销存管理",
      requireAuth: true
    },
    children: [
      {
        name: "goodsManagement",
        path: "/goodsManagement",
        show: true,
        component: () => import("@/views/goods/goods"),
        meta: {
          title: "物品管理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["course-service/article/menu"]
      },
      {
        name: "warehouseSetting",
        path: "/warehouseSetting",
        show: true,
        component: () => import("@/views/inventory/warehouseSetting.vue"),
        meta: {
          title: "仓库管理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["course-service/article-bank/menu"]
      },
      {
        name: "wmsManagement",
        path: "/wmsManagement",
        show: true,
        component: () => import("@/views/wmsManagement/index.vue"),
        meta: {
          title: "进出库管理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["course-service/enter-leave-stock/menu"]
      },
      {
        name: "inventoryChangeTable",
        path: "/inventoryChangeTable",
        show: true,
        component: () => import("@/views/inventory/inventoryChangeTable.vue"),
        meta: {
          title: "库存变动表",
          requireAuth: true,
          keepAlive: true
        },
        api: ["course-service/article-bank-amount-log/menu"]
      },
      {
        name: "inventoryQuery",
        path: "/inventoryQuery",
        show: true,
        component: () => import("@/views/inventory/inventoryQuery.vue"),
        meta: {
          title: "库存查询",
          requireAuth: true,
          keepAlive: true
        },
        api: ["course-service/article-bank-amount/menu"]
      },
      {
        name: "enteringAndExitingQuery",
        path: "/enteringAndExitingQuery",
        show: true,
        component: () =>
          import("@/views/inventory/enteringAndExitingQuery.vue"),
        meta: {
          title: "进出库查询",
          requireAuth: true,
          keepAlive: true
        },
        api: ["course-service/enteringAndExitingQuery/enter-leave-stock/menu"]
      },
      {
        name: "teachingAidPackage",
        path: "/teachingAidPackage",
        show: true,
        component: () =>
          import("@/views/teachingAidPackageManagement/index.vue"),
        meta: {
          title: "教辅包管理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["course-service/teachAidPackage/menu"]
      }
    ]
  },
  {
    path: "/process",
    component: index,
    src: require("@/assets/图片/process_grey.png"),
    src_ac: require("@/assets/图片/process_green.png"),
    meta: {
      title: "流程审批",
      requireAuth: true
    },
    children: [
      {
        name: "userCenter",
        path: "/userCenter",
        show: true,
        component: () => import("@/views/userCenter/index"),
        meta: {
          title: "个人中心",
          requireAuth: true,
          keepAlive: true
        },
        api: ["all"]
      },
      {
        name: "workflowTemplate",
        path: "/workflowTemplate",
        show: true,
        component: () => import("@/views/workflowTemplate/index.vue"),
        meta: {
          title: "模板管理",
          requireAuth: true,
          keepAlive: false
        },
        api: ["workstream-service/template/list/menu"]
      },
      {
        name: "workflowTemplateEdit",
        path: "/workflowTemplateEdit",
        show: false,
        parent: "/workflowTemplate",
        component: () => import("@/views/workflowTemplate/edit.vue"),
        meta: {
          title: "模板管理-编辑",
          requireAuth: true,
          keepAlive: false
        },
        api: ["workstream-service/template/create"]
      },
      {
        name: "workflowSet",
        path: "/workflowSet",
        show: false,
        component: () => import("@/views/workflow/setting.vue"),
        meta: {
          title: "审批流配置",
          requireAuth: true,
          keepAlive: false
        },
        api: ["workstream-service/rule/create"]
      }
    ]
  },

  {
    path: "/system",
    component: index,
    src: require("@/assets/图片/icon_system.png"),
    src_ac: require("@/assets/图片/icon_system_ac.png"),
    meta: {
      title: "系统管理",
      requireAuth: true
    },
    children: [
      {
        name: "organization",
        path: "/organization",
        show: true,
        component: () => import("@/views/organization/organization"),
        meta: {
          title: "组织管理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["organization-service/overr-view/info/menu"]
      },
      {
        name: "regionalManagement",
        path: "/regionalManagement",
        show: true,
        component: () =>
          import("@/views/regionalManagement/regionalManagement"),
        meta: {
          title: "区域管理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["organization-service/area/list/menu"]
      },
      {
        name: "classroomManagement",
        path: "/classroomManagement",
        show: true,
        component: () =>
          import("@/views/classroomManagement/classroomManagement.vue"),
        meta: {
          title: "教室管理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["school-service/schoolroom/list/menu"]
      },
      {
        name: "channel",
        path: "/channel",
        show: true,
        component: () => import("@/views/channel/channel.vue"),
        meta: {
          title: "渠道管理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["market-service/channel/list/menu"]
      },
      {
        name: "roleManagement",
        path: "/roleManagement",
        show: true,
        component: () => import("@/views/roleManagement/roleManagement"),
        meta: {
          title: "权限组管理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["permission-service/role/list/menu"]
      },
      {
        name: "associated",
        path: "/associated",
        component: () => import("@/views/roleManagement/associated"),
        parent: "/roleManagement",
        show: false,
        meta: {
          title: "关联用户",
          requireAuth: true,
          keepAlive: false
        },
        api: ["permission-service/employee/role/list"]
      },
      {
        name: "permissions",
        path: "/permissions",
        component: () => import("@/views/roleManagement/permissions"),
        show: false,
        parent: "/roleManagement",
        meta: {
          title: "权限设置",
          requireAuth: true,
          keepAlive: false
        },
        api: ["permission-service/role/create"]
      },
      {
        name: "postManagement",
        path: "/postManagement",
        show: true,
        component: () => import("@/views/postManagement/postManagement"),
        meta: {
          title: "岗位管理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["organization-service/office-post/list/menu"]
      },
      {
        name: "staff",
        path: "/staff",
        show: true,
        component: () => import("@/views/staff/staff"),
        meta: {
          title: "员工管理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["organization-service/overr-view/info/menu"]
      },
      {
        name: "staffAdd",
        path: "/staffAdd",
        component: () => import("@/views/staff/staffAdd"),
        parent: "/staff", // 控制刷新显示的问题
        show: false,
        meta: {
          title: "新增员工",
          requireAuth: true,
          keepAlive: false
        },
        api: ["organization-service/employee/update"]
      },

      {
        name: "staffPermission",
        path: "/staffPermission",
        component: () => import("@/views/staff/staffPermission"),
        parent: "/staff",
        show: false,
        meta: {
          title: "设置权限",
          requireAuth: true,
          keepAlive: false
        },
        api: ["permission-service/role/create"]
      },
      {
        name: "match",
        path: "/match",
        show: true,
        component: () => import("@/views/match/match"),
        meta: {
          title: "赛事管理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["course-service/match/list/menu"]
      },
      {
        name: "courseManagement",
        path: "/courseManagement",
        show: true,
        component: () => import("@/views/courseManagement/index"),
        meta: {
          title: "课程管理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["course-service/course-mapping/list/menu"]
      },
      {
        name: "courseManagementUpdate",
        path: "/courseManagementUpdate",
        parent: "/courseManagement",
        show: false,
        component: () => import("@/views/courseManagement/update"),
        meta: {
          title: "课程管理-修改",
          requireAuth: true,
          keepAlive: false
        },
        api: ["course-service/course-mapping/detail"]
      },
      {
        name: "upgradeClass",
        path: "/upgradeClass",
        show: true,
        component: () => import("@/views/upgradeClass/upgradeClass"),
        meta: {
          title: "升班管理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["course-service/course-path/list/menu"]
      },
      {
        name: "discountCoupon",
        path: "/discountCoupon",
        show: true,
        component: () => import("@/views/discountCoupon/discountCouponList"),
        meta: {
          title: "优惠券管理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["coupon-service/coupon-template/list/menu"]
      },
      {
        name: "couponSchemeManagement",
        path: "/couponSchemeManagement",
        show: true,
        component: () => import("@/views/couponSchemeManagement/index"),
        meta: {
          title: "优惠方案管理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["coupon-service/plan/list/menu"]
      },
      {
        name: "chargeSetting",
        path: "/chargeSetting",
        show: true,
        component: () => import("@/views/charge/chargeSetting"),
        meta: {
          title: "收费类型配置",
          requireAuth: true,
          keepAlive: true
        },
        api: ["course-service/charge-setting/menu"]
      },
      {
        name: "studentCategory",
        path: "/studentCategory",
        component: () => import("@/views/studentCategory/studentCategory"),
        parent: "/studentCategory", // 控制刷新显示的问题
        show: true,
        meta: {
          title: "学员类别",
          requireAuth: true,
          keepAlive: true
        },
        api: ["student-service/student-category/list/menu"]
      },
      {
        name: "labelList",
        path: "/labelList",
        show: true,
        component: () => import("@/views/label/labelList"),
        meta: {
          title: "标签管理",
          requireAuth: true,
          keepAlive: false
        },
        api: ["organization-service/label/list/menu"]
      },
      {
        name: "label",
        path: "/label",
        parent: "/label",
        show: false,
        component: () => import("@/views/label/label"),
        meta: {
          title: "标签信息管理",
          requireAuth: true,
          keepAlive: false
        },
        api: ["organization-service/label/list"]
      },
      {
        name: "citation",
        path: "/citation",
        show: true,
        component: () => import("@/views/citation/citationManagement"),
        meta: {
          title: "奖状管理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["organization-service/diploma/list/menu"]
      },
      {
        name: "citationDetail",
        path: "/citationDetail",
        parent: "/citation",
        show: false,
        component: () => import("@/views/citation/citationDetail"),
        meta: {
          title: "奖状详情",
          requireAuth: true,
          keepAlive: false
        },
        api: ["organization-service/diploma/view"]
      },
      {
        name: "picture",
        path: "/picture",
        show: true,
        component: () => import("@/views/pictureManagement/index"),
        meta: {
          title: "图片管理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["organization-service/picture/list/menu"]
      },
      {
        name: "ClassTimeSetting",
        path: "/ClassTimeSetting",
        show: true,
        component: () => import("@/views/ClassTimeSetting/ClassTimeSetting"),
        meta: {
          title: "上课时间设置",
          requireAuth: true,
          keepAlive: true
        },
        api: ["school-service/class-time/group/list/menu"]
      },

      {
        name: "operatePermission",
        path: "/operatePermission",
        show: true,
        component: () => import("@/views/operatePermission/operatePermission"),
        meta: {
          title: "系统权限管理",
          requireAuth: true,
          keepAlive: false
        },
        api: ["permission-service/permission/group/list/menu"]
      },
      {
        name: "defaultPermission",
        path: "/defaultPermission",
        show: true,
        component: () => import("@/views/defaultPermission/defaultPermission"),
        meta: {
          title: "默认权限",
          requireAuth: true,
          keepAlive: true
        },
        api: ["permission-service/permission/default/list/menu"]
      },
      {
        name: "operationLog",
        path: "/operationLog",
        component: () => import("@/views/staff/operationLog"),
        show: true,
        meta: {
          title: "操作日志",
          requireAuth: true,
          keepAlive: true
        },
        api: ["report-center-service/admin/operation-log/list"]
      },
      {
        name: "systemSetting",
        path: "/systemSetting",
        component: () => import("@/views/systemSetting/systemSetting"),
        show: true,
        meta: {
          title: "系统设置",
          requireAuth: true,
          keepAlive: true
        },
        api: ["report-center-service/admin/systemSetting/list/menu"]
      },
      {
        name: "agentManagement",
        path: "/agentManagement",
        component: () => import("@/views/agentManagement/index"),
        show: true,
        meta: {
          title: "坐席管理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["admin/market-service/agentManagement/menu"]
      },
      {
        name: "deviceDictionary",
        path: "/deviceDictionary",
        component: () => import("@/views/deviceDictionary/index"),
        show: true,
        meta: {
          title: "设备字典",
          requireAuth: true,
          keepAlive: true
        },
        api: ["admin/device-service/deviceDictionary/menu"]
      },
      //网校期次管理
      {
        name: "onlienSchoolPeriod",
        path: "/onlienSchoolPeriod",
        component: () => import("@/views/onlienSchoolPeriod/index"),
        show: true,
        meta: {
          title: "网校期次管理",
          requireAuth: true,
          keepAlive: true
        },
        api: ["admin/school-service/onlienSchoolPeriod/menu"]
      },
      //职级绑定配置
      {
        name: "rankBindConfig",
        path: "/rankBindConfig",
        component: () => import("@/views/rankBindConfig/index"),
        show: true,
        meta: {
          title: "职级绑定配置",
          requireAuth: true,
          keepAlive: true
        },
        api: ["admin/school-service/rankBindConfig/menu"]
      },
      {
        name: "batchProcess",
        path: "/batchProcess",
        show: false,
        component: () => import("@/views/batchProcess/index"),
        meta: {
          title: "批量处理数据",
          requireAuth: true,
          keepAlive: false
        },
        api: ["admin/school-service/batch-process/menu"]
      },
      {
        name: "sendCard",
        path: "/sendCard",
        show: true,
        component: () => import("@/views/sendCard/index"),
        meta: {
          title: "发卡管理",
          requireAuth: true,
          keepAlive: false
        },
        api: ["admin/school-service/send-card/menu"]
      }
    ]
  },
  {
    path: "/cockpit_center",
    component: index,
    src: require("../assets/图片/icon_cockpit.png"),
    src_ac: require("../assets/图片/icon_cockpit_ac.png"),
    meta: {
      title: "驾驶舱",
      requireAuth: true
    },
    children: [
      {
        name: "cockpit",
        path: "/cockpit",
        show: true,
        component: () => import("@/views/cockpit/index"),
        meta: {
          title: "运营数据看板",
          requireAuth: true,
          keepAlive: false,
          fullScreen: true, // 是否全屏驾驶舱
          // 本地缓存该页面右上角校区的key
          schoolStorageKey: "cockpitSchool"
        },
        api: ["cockpit-service/operational-data-kanban/menu"]
      },

      {
        path: "/personalCenter",
        name: "personalCenter",
        show: true,
        component: () => import("@/views/personal/index"),
        meta: {
          title: "教务任务池",
          requireAuth: true,
          keepAlive: false,

          // 本地缓存该页面右上角校区的key
          schoolStorageKey: "personalCenterSchool"
        },
        api: ["taskpool-service/personal_center/menu"]
      },
      {
        name: "courseConsultantTaskPool",
        path: "/courseConsultantTaskPool",
        show: true,
        component: () => import("@/views/courseConsultantTaskPool/index"),
        meta: {
          title: "课程顾问任务池",
          requireAuth: true,
          keepAlive: false,
          // 本地缓存该页面右上角校区的key
          schoolStorageKey: "courseConsultantTaskPoolSchool"
        },
        api: ["cockpit-service/operational-courseConsultant-taskPool/menu"]
      },
      {
        name: "taskPool",
        path: "/taskPool",
        show: true,
        component: () => import("@/views/taskPool/index"),
        meta: {
          title: "市场专员任务池",
          requireAuth: true,
          keepAlive: false,
          // 本地缓存该页面右上角校区的key
          schoolStorageKey: "courseConsultantTaskPoolSchool"
        },
        api: ["cockpit-service/marketing-specialist-taskPool/menu"]
      },
      {
        name: "directSchoolMonthlyReport",
        path: "/directSchoolMonthlyReport",
        show: true,
        component: () => import("@/views/cockpit/directSchoolMonthlyReport"),
        meta: {
          title: "直营校月报",
          requireAuth: true,
          keepAlive: false,
          fullScreen: true,
          // 本地缓存该页面右上角校区的key
          schoolStorageKey: "directSchoolMonthlyReport"
        },
        api: ["cockpit-service/direct-school-monthly-report/menu"]
      },
      {
        name: "networSchoolKanBan",
        path: "/networSchoolKanBan",
        show: true,
        component: () => import("@/views/networSchoolKanBan/index"),
        meta: {
          title: "阿波罗后端数据看板",
          requireAuth: true,
          keepAlive: true,
          // 本地缓存该页面右上角校区的key
          schoolStorageKey: "networSchoolKanBanDetail"
        },
        api: ["cockpit-service/networSchoolKanBan/menu"]
      },
      {
        name: "networSchoolKanBanDetail",
        path: "/networSchoolKanBanDetail",
        show: false,
        component: () => import("@/views/networSchoolKanBan/detail"),
        meta: {
          title: "班级详情",
          requireAuth: true,
          keepAlive: false,
          // 本地缓存该页面右上角校区的key
          schoolStorageKey: "networSchoolKanBanDetail"
        },
        api: ["cockpit-service/networSchoolKanBan/networSchoolKanBanDetail"]
      },
      //教练任务池
      {
        name: "coachTaskPool",
        path: "/coachTaskPool",
        show: true,
        component: () => import("@/views/coachTaskPool/index"),
        meta: {
          title: "教练任务池",
          requireAuth: true,
          keepAlive: false,
          // 本地缓存该页面右上角校区的key
          schoolStorageKey: "coachTaskPoolSchool"
        },
        api: ["cockpit-service/coach-task-pool/menu"]
      },
      {
        name: "targetValueKanBan",
        path: "/targetValueKanBan",
        show: true,
        component: () => import("@/views/targetValueKanBan/index"),
        meta: {
          title: "目标值导入",
          requireAuth: true,
          keepAlive: false,
          // 本地缓存该页面右上角校区的key
          schoolStorageKey: "targetValueKanBanSchool"
        },
        api: ["cockpit-service/target-value-kanban/menu"]
      },
      {
        name: "overview",
        path: "/overview",
        show: true,
        component: () => import("@/views/overview/index"),
        meta: {
          title: "人力动态驱动系统",
          requireAuth: true,
          keepAlive: false,
          // 本地缓存该页面右上角校区的key
          schoolStorageKey: "overviewSchool"
        },
        api: ["cockpit-service/overview/menu"]
      },
      {
        name: "wechatOverview",
        path: "/wechatOverview",
        show: false,
        component: () => import("@/views/overview/wechatOverview"),
        meta: {
          title: "企微看板",
          requireAuth: true,
          keepAlive: false
        },
        api: ["cockpit-service/overview/menu"]
      }
    ]
  }
];
const router = new VueRouter({
  mode: "history",
  base: process.env.BASE_URL,
  routes
});
const createRouter = (routes) =>
  new VueRouter({
    mode: "history",
    base: process.env.BASE_URL,
    routes
  });
export function resetRouter() {
  router.options.routes = [];
  const newRouter = createRouter(routes);
  router.matcher = newRouter.matcher; // the relevant part
}

// * 该方法会将路由包含当前item1.path的值进行匹配，后续需要对路由起名进行注意，减少单词包含的情况产生
// 解决当前位置冗余导航===========
const originalPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push(location, resolve, reject) {
  if (resolve || reject)
    return originalPush.call(this, location, resolve, reject);
  // eslint-disable-next-line prettier/prettier
  return originalPush.call(this, location).catch((e) => {
    console.log(e);
  });
};
router.beforeEach((to, from, next) => {
  NProgress.start();
  /* 路由发生变化修改页面title */
  if (to.meta.title) {
    if (to.name === "privacy") {
      document.title = to.path.indexOf("user") > -1 ? "用户协议" : "隐私政策";
    } else {
      document.title = to.meta.title;
    }
  }
  const token = VueCookies.get("user_token");
  console.log(to, "to");
  if (to.meta.requireAuth === false) {
    next();
    NProgress.done();
  } else {
    if (token == null) {
      next({
        path: "/login"
      });
    } else {
      const doneGetPermissionInfo = store.getters.doneGetPermissionInfo;
      if (doneGetPermissionInfo.length === 0) {
        store
          .dispatch("getEmployeeAllPermission")
          .then(() => {
            const val = store.getters.doneGetPermissionInfo;
            if (val != null && val.indexOf("is_admin") > -1) {
              // commonRoute[0].redirect = asyncRoute[0].children[0].path;
              // commonRoute[0].redirect = asyncRoute[0].children[0].path;
              store.commit("setMenu", [...commonRoute, ...asyncRoute]);
            } else {
              // 设置
              const api_list = val.map((item) => {
                return item.substring(5);
              });
              // 添加权限为所有人可以查看的api
              api_list.push("all");
              const new_router = [];
              asyncRoute.forEach((r) => {
                const new_c = r.children.filter((list) => {
                  const has_api = list.api?.find((item) =>
                    api_list.includes(item)
                  );
                  return !!has_api;
                });
                console.log(new_c);
                if (new_c.length > 0) {
                  const n_r = Object.assign({}, r, {
                    children: new_c
                  });
                  new_router.push(n_r);
                }
              });
              if (new_router.length > 0)
                commonRoute[0].redirect = new_router[0].children[0].path;
              store.commit("setMenu", [...commonRoute, ...new_router]);
            }
            let getRoutes = store.getters.doneGetMenu;
            getRoutes = getRoutes.concat([
              {
                path: "*",
                redirect: "/notFound"
              }
            ]);
            resetRouter();
            getRoutes.forEach((route) => {
              router.addRoute(route);
            });
            router.options.routes = [...routes, ...getRoutes];
            next({
              ...to,
              replace: true
            });
            NProgress.done();
          })
          .catch(() => {
            NProgress.done();
          });
      } else {
        next();
        NProgress.done();
      }
    }
  }
});

router.onError((error) => {
  // 防IE缓存导致找不到js文件
  console.log(error);
  const loading = error.message.indexOf("Loading chunk");
  const failed = error.message.indexOf("failed");
  console.log(loading, failed);
  if (loading > -1 && failed > -1) {
    // 用路由的replace方法，并没有相当于F5刷新页面，失败的js文件并没有从新请求，会导致一直尝试replace页面导致死循环，而用 location.reload 方法，相当于触发F5刷新页面，虽然用户体验上来说会有刷新加载察觉，但不会导致页面卡死及死循环
    window.location.reload();
  }
});
router.afterEach(() => {
  // finish progress bar
  NProgress.done();
});
export default router;
