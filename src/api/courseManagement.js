import axios from "../http";
import Vue from "vue";
import qs from "qs";
const conf_path = `/api/course-service/course-conf`;
const specification_path = `/api/course-service/course-price/specification`;
const course_mapping_path = "/api/course-service/course-mapping";
const course_charge_path = "/api/course-service/charge";
// 获取课程管理的所有字典项
export function getCourseConfigAll(data) {
  return axios
    .get(`${conf_path}/all`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取课程管理的某一字典项
export function getCourseConfigByType(data, config_type) {
  return axios
    .get(`${conf_path}/${config_type}/list`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
export function getCourseRuleTimeInfo(data) {
  return axios
    .get(`/api/course-service/course-rule/info-time`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 对课程管理某一字典项新增
export function packageClassTypeAdd(data, config_type) {
  return axios
    .post(`/api/course-service/course-conf/package_class_type_add`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 对课程管理某一字典项新增
export function addCourseConfigByName(data, config_type) {
  return axios
    .post(`${conf_path}/${config_type}/add`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取课程管理的规格列表
export function getSpecificationList(data) {
  return axios
    .get(`${specification_path}/list`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 添加课程规格
export function addSpecification(data) {
  return axios
    .post(`${specification_path}/add`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 课程详情 基本信息 其他信息
export function getCourseInfoBasic(data) {
  return axios
    .get(`${course_mapping_path}/detail`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 课程详情 课程规格
export function getCourseInfoPriceSpec(data) {
  return axios
    .get(`/api/course-service/course-price/detail`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 课程详情 授权校区 规格
export function getAuthorizedSpec(data) {
  return axios
    .get(`/api/course-service/course-price/department/specifications`, {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 课程详情 保存基本信息其他信息
export function updateCourseInfo(data) {
  return axios
    .post(`${course_mapping_path}/update`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 课程详情 课程规格 保存
export function updateCoursePrice(data) {
  return axios
    .post(`/api/course-service/course-price/update`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error);
    })
    .finally();
}

// 课程详情 获取校区定价
export function getInfoCoursePrice(data) {
  return axios
    .get(`/api/course-service/course-price/department/list`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 课程详情 修改校区定价
export function updateInfoCoursePrice(data) {
  return axios
    .post(`/api/course-service/course-price/department/update`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error);
    })
    .finally();
}

// 课程详情 获取开班规则
export function getCourseRuleInfo(data) {
  return axios
    .get(`/api/course-service/course-rule/info?course_id=${data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 课程详情修改开班规则
export function updateCourseRuleInfo(data) {
  return axios
    .post(
      `/api/course-service/course-rule/update?id=${data.id}&course_id=${data.course_id}`,
      data
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error);
    })
    .finally();
}

// 添加课程
export function addCourse(data) {
  return (
    axios
      // .post(`http://192.168.31.77:8080${course_mapping_path}/add`, data)
      .post(`${course_mapping_path}/add`, data)
      .then((response) => {
        return response;
      })
      .catch((error) => {
        Vue.prototype.$message.error(error.err);
      })
      .finally()
  );
}
// 获取课程列表
export function getCourseList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`${course_mapping_path}/list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取课程不包括id列表
export function getCourseListExcluded(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`${course_mapping_path}/filter?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 课程详情关联课程
export function getCourseInfoRelative(data) {
  return axios
    .get(`/api/course-service/course-linked/detail`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error);
      return Promise.reject(error);
    })
    .finally();
}

// 课程详情 新增关联课程
export function addCourseInfoRelative(data) {
  return axios
    .post(`/api/course-service/course-linked/add`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error);
      return Promise.reject(error);
    })
    .finally();
}
// 课程详情 修改关联课程
export function updateCourseInfoRelative(data) {
  return axios
    .post(`/api/course-service/course-linked/update`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error);
      return Promise.reject(error);
    })
    .finally();
}

export const classroomCancel = (data) => {
  return axios
    .post(`/api/school-service/classroom/cancel-finished`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error);
      return Promise.reject(error);
    })
    .finally();
};

// 课程详情 修改被关联课程扣款顺序
export function updateCourseInfoRelated(data) {
  return axios
    .post(`/api/course-service/course-linked/update-priority`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 课程详情 删除关联课程
export function removeCourseInfoRelative(data) {
  return axios
    .get(
      `/api/course-service/course-linked/remove?id=${data.id}&course_id=${data.course_id}`,
      data
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error);
      return Promise.reject(error);
    })
    .finally();
}

// 批量启用
export function batchEnable(data) {
  return axios
    .post(`${course_mapping_path}/batch/enable`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 批量启用
export function batchDisable(data) {
  return axios
    .post(`${course_mapping_path}/batch/disable`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 关联物品赛事课程列表
export function getCourseRelativeList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/course-service/settle/course-product-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 课程收费配置列表
export function courseChargeList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios.get(`${course_charge_path}/list?${new_data}`);
}

// 新增课程收费配置
export function courseChargeCreate(data) {
  return axios
    .post(`${course_charge_path}/create`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 课程收费价格类型下拉项
export function courseChargePriceList(data) {
  return axios
    .get(`${course_charge_path}/price-list`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 课程收费类型下拉项
export function courseChargeTypeList(data) {
  return axios
    .get(`${course_charge_path}/type-list`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 课程收费配置详情
export function courseChargeInfo(data) {
  return axios
    .get(`${course_charge_path}/info`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 修改课程收费配置
export function courseChargeUpdate(data) {
  return axios
    .post(`${course_charge_path}/update`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 课程收费批量启用/停用 批量启用(opt=enable)/停用(opt=disable)
export function courseChargeOpt(data) {
  return axios
    .post(`/api/order-service/admin/fee-type/save-is-enable`, data, "")
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 课程收费授权校区
export function courseChargeAuthDepartment(data) {
  return axios
    .patch(`${course_charge_path}/auth-department`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取天弈课程列表
export function getTYCourseList(data) {
  return axios
    .get(`/api/v2/content-service/admin/course/all`, {
      params: data,
      baseURL: process.env.VUE_APP_TY_BASE_API
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
