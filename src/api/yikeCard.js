import axios from "../http";
import Vue from "vue";
import qs from "qs";

// 创建会员卡
function createMsembership(data) {
  return axios
    .post(`/api/student-service/yikeCard/create-membership`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}
// 创建充值卡
function createPrepaid(data) {
  return axios
    .post(`/api/student-service/yikeCard/create-prepaid`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}
// 会员卡发卡
function sendMembership(data) {
  return axios
    .post(`/api/student-service/yikeCard/send-membership`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}
// 充值卡发卡
function sendPrepaid(data) {
  return axios
    .post(`/api/student-service/yikeCard/send-prepaid`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}
// 导出会员卡
function membershipExport(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/student-service/yikeCard/membership-export?${new_data}`, {
      params: { exportData: 1 }
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.message);
    })
    .finally();
}
// 导出充值卡
function prepaidExport(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/student-service/yikeCard/prepaid-export?${new_data}`, {
      params: { exportData: 1 }
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.message);
    })
    .finally();
}
// 获取会员卡列表
function membershipList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/student-service/yikeCard/membership-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.message);
    })
    .finally();
}
// 获取充值卡列表
function prepaidList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/student-service/yikeCard/prepaid-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.message);
    })
    .finally();
}
// 意向客户开卡
function sendCustomerMembership(data) {
  return axios
    .post(`/api/student-service/yikeCard/send-customer-membership`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.message);
    })
    .finally();
}
// 学生开卡
function sendStudentMembership(data) {
  return axios
    .post(`/api/student-service/yikeCard/send-student-membership`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.message);
    })
    .finally();
}
// 实体卡记录
function entityCardRecord(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/student-service/yikeCard/open-entity-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.message);
    })
    .finally();
}
// 虚拟卡记录
function virtualCardRecord(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/student-service/yikeCard/open-virtual-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.message);
    })
    .finally();
}

// 导出实体卡记录
function entityCardRecordExport(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/student-service/yikeCard/open-entity-list-export?${new_data}`, {
      params: { exportData: 1 }
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.message);
    })
    .finally();
}
// 导出虚拟卡记录
function virtualCardRecordExport(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/student-service/yikeCard/open-virtual-list-export?${new_data}`, {
      params: { exportData: 1 }
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.message);
    })
    .finally();
}
// 意向客户列表开卡记录
function customerOpenList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/student-service/yikeCard/customer-open-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.message);
    })
    .finally();
}
// 意向客户列表开卡记录导出
function customerOpenListExport(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/student-service/yikeCard/customer-open-list-export?${new_data}`,
      {
        params: { exportData: 1 }
      }
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.message);
    })
    .finally();
}
// 学员列表开卡记录
function studentOpenList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/student-service/yikeCard/student-open-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.message);
    })
    .finally();
}
// 学员列表开卡记录导出
function studentOpenListExport(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/student-service/yikeCard/student-open-list-export?${new_data}`, {
      params: { exportData: 1 }
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.message);
    })
    .finally();
}
export default {
  async createMsembership(data) {
    return createMsembership(data);
  },
  async createPrepaid(data) {
    return createPrepaid(data);
  },
  async membershipList(data) {
    return membershipList(data);
  },

  async prepaidList(data) {
    return prepaidList(data);
  },
  async membershipExport(data) {
    return membershipExport(data);
  },
  async prepaidExport(data) {
    return prepaidExport(data);
  },
  async sendMembership(data) {
    return sendMembership(data);
  },
  async sendPrepaid(data) {
    return sendPrepaid(data);
  },
  async sendCustomerMembership(data) {
    return sendCustomerMembership(data);
  },
  async sendStudentMembership(data) {
    return sendStudentMembership(data);
  },
  async entityCardRecord(data) {
    return entityCardRecord(data);
  },
  async entityCardRecordExport(data) {
    return entityCardRecordExport(data);
  },
  async virtualCardRecord(data) {
    return virtualCardRecord(data);
  },
  async virtualCardRecordExport(data) {
    return virtualCardRecordExport(data);
  },
  async customerOpenList(data) {
    return customerOpenList(data);
  },
  async customerOpenListExport(data) {
    return customerOpenListExport(data);
  },
  async studentOpenList(data) {
    return studentOpenList(data);
  },
  async studentOpenListExport(data) {
    return studentOpenListExport(data);
  }
};
