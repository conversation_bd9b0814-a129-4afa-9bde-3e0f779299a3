import axios from "../http";
import Vue from "vue";
import qs from "qs";
const fetch = require("../fetch");
const path = "/api/enterprise/order";
// 经办人列表
export function getReceiptOperator(data) {
  return fetch.fetchGet(`${path}/receipt/operator`, {
    params: data
  });
}
// 收据列表
// export function getReceiptList(data) {
//   return fetch.fetchPost(
//     `${path}/receipt/page?page=${data.page}&size=${data.size}`,
//     data,
//     ""
//   );
// }
function getReceiptList(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(`/api/order-service/admin/receipt/list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
function getReceiptListNew(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(`/api/order-service/admin/receipt/list-new?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
export function getReceipType() {
  return fetch.fetchGet(`/api/order-service/admin/receipt/type`);
}

export function getReceipAtion() {
  return fetch.fetchGet(`/api/order-service/admin/receipt/log/type`);
}
// 经手人
export function getReceipAgg(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(`/api/order-service/admin/receipt/account/statistics?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 收据作废
export function getReceipDiscard(data) {
  return fetch.fetchGet(`${path}/receipt/discard`, {
    params: data
  });
}
// 修改打印状态
export function setPrintType(data) {
  return axios
    .post(`/api/order-service/admin/receipt/print`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
export function receipDiscard(data) {
  return axios
    .post(`/api/order-service/admin/receipt/discard`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 收据修改
export function receipModify(data) {
  let str = "";
  switch (data.receiptType) {
    case "order_paid":
      str = "order";
      break;
    case "refund_order":
      str = "refund";
      break;
    case "transfer_order":
      str = "transfer";
      break;
    default:
      break;
  }
  return fetch.fetchPost(
    `${path}/receipt/opt/${str}/modify?receiptType=${data.receiptType}&receiptNo=${data.receiptNo}`,
    data,
    ""
  );
}

function paymentEdit(data) {
  return axios
    .post(`/api/order-service/admin/receipt/payment/edit`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function carryoverEdit(data) {
  return axios
    .post(`/api/order-service/admin/receipt/carryover/edit`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function refundEdit(data) {
  return axios
    .post(`/api/order-service/admin/receipt/refund/edit`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function receiptTotal(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(`/api/order-service/admin/receipt/list/total?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
function orderEditChannel(data) {
  return axios
    .post(`/api/order-service/admin/receipt/order-edit-channel`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 转费转入转费转出详情
export function transferFeeDetail(data) {
  return fetch.fetchGet(
    `/api/order-service/admin/receipt/fee/transfer/detail`,
    { params: data }
  );
}

// 转费转入转费转出编辑
export function transferFeeEdit(data) {
  return fetch.fetchPost(
    `/api/order-service/admin/receipt/fee/transfer/edit`,
    data,
    ""
  );
}

//
export function receiptRoleStudent(data) {
  return fetch.fetchGet(`/api/order-service/admin/receipt/role/student`, {
    params: data
  });
}
export function editJdTrack(data) {
  return fetch.fetchPost(
    `/api/order-service/admin/receipt/edit-jd-track`,
    data,
    ""
  );
}
function jdTrackQuery(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(`/api/order-service/admin/jd-track/track-query?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 收据导入快递单号
export function importJdTrack(data) {
  return axios.post(`/api/order-service/admin/receipt/import-jd-track`, data);
}
// 收据编辑第三方订单号
export function editThirdOrderId(data) {
  return axios.post(
    `/api/order-service/admin/receipt/order-edit-third-order-id`,
    data
  );
}
// 放量转化期次修改
export function editVolumePeriod(data) {
  return axios.post(`/api/order-service/admin/receipt/edit-period`, data);
}
export default {
  getReceiptList,
  getReceiptListNew,
  getReceiptOperator,
  getReceipType,
  getReceipAtion,
  getReceipAgg,
  getReceipDiscard,
  receipDiscard,
  receipModify,
  paymentEdit,
  carryoverEdit,
  refundEdit,
  setPrintType,
  receiptTotal,
  orderEditChannel,
  transferFeeDetail,
  transferFeeEdit,
  receiptRoleStudent,
  editJdTrack,
  jdTrackQuery,
  importJdTrack,
  editThirdOrderId,
  editVolumePeriod
};
