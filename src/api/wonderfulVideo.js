import axios from "../http";
import Vue from "vue";
import qs from "qs";
// import { fetchPost } from "../fetch";

function videoDetail(data) {
  return axios
    .get(`/api/questionnaire-service/admin/featuredVideos/detail`, {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function videoCreate(data) {
  return axios
    .post(`/api/questionnaire-service/admin/featuredVideos/create`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
function parentVideoDel(data) {
  return axios
    .post(`/api/questionnaire-service/admin/featuredVideos/parent-del`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
function parentVideoUpdate(data) {
  return axios
    .post(`/api/questionnaire-service/admin/featuredVideos/parent-update`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function videoUpdate(data) {
  return axios
    .post(`/api/questionnaire-service/admin/featuredVideos/update`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function updateStatus(data) {
  return axios
    .post(`/api/questionnaire-service/admin/featuredVideos/update/status`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function videoList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/questionnaire-service/admin/featuredVideos/list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.message);
    })
    .finally();
  //   return axios
  //     .get(`/api/questionnaire-service/admin/featuredVideos/list`, {
  //       params: data
  //     })
  //     .then((response) => {
  //       return response;
  //     })
  //     .catch((error) => {
  //       Vue.prototype.$message.error(error.err);
  //     })
  //     .finally();
}

function videoParentList(data) {
  return axios
    .get(`/api/questionnaire-service/admin/featuredVideos/parent-list`, {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function parentVideoCreate(data) {
  return axios
    .post(`/api/questionnaire-service/admin/featuredVideos/parent-create`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
function videoDel(data) {
  return axios
    .post(`/api/questionnaire-service/admin/featuredVideos/del`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export default {
  async videoCreate(data) {
    return videoCreate(data);
  },
  async videoDetail(data) {
    return videoDetail(data);
  },
  async videoDel(data) {
    return videoDel(data);
  },
  async parentVideoCreate(data) {
    return parentVideoCreate(data);
  },
  async parentVideoDel(data) {
    return parentVideoDel(data);
  },
  async parentVideoUpdate(data) {
    return parentVideoUpdate(data);
  },
  async videoUpdate(data) {
    return videoUpdate(data);
  },
  async updateStatus(data) {
    return updateStatus(data);
  },
  async videoList(data) {
    return videoList(data);
  },
  async videoParentList(data) {
    return videoParentList(data);
  }
};
