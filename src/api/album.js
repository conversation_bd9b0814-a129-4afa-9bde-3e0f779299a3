import axios from "../http";
import Vue from "vue";
import qs from "qs";
const fetch = require("../fetch");
// 获取时光相册类型
function getAlbumType(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/student-service/public/time-album/get-album-type-list?${new_data}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 修改时光相册详情
function updateAlbumDetail(data) {
  return axios
    .post(`/api/student-service/time-album/create`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 删除时光相册详情
function deleteAlbumDetail(data) {
  return axios
    .post(`/api/student-service/time-album/delete`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 获取时光相册列表导出
function getAlbumListExport(data) {
  return fetch.fetchExport(`/api/student-service/time-album/export`, data);
}
// 获取时光相册列表导出
function getSendListExport(data) {
  return fetch.fetchExport(
    `/api/student-service/time-album/send-list-export`,
    data
  );
}
// 获取时光相册详情
function getAlbumDetail(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/student-service/time-album/info?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 获取学员时光相册列表
function getAlbumList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/student-service/time-album/infoList?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 获取时光相册列表
function getAlbumListPage(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/student-service/time-album/list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 修改时光相册详情
function updateAlbumDetailSave(data) {
  return axios
    .post(`/api/student-service/time-album/save`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}

function getAlbumSendList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/student-service/time-album/send-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 批量创建时光相册
function batchCreateAlbum(data) {
  return axios.post(`/api/student-service/time-album/batch-add`, data);
}
export default {
  async getAlbumType(data) {
    return await getAlbumType(data);
  },
  async updateAlbumDetail(data) {
    return await updateAlbumDetail(data);
  },
  async deleteAlbumDetail(data) {
    return await deleteAlbumDetail(data);
  },
  async getAlbumListExport(data) {
    return await getAlbumListExport(data);
  },
  async getAlbumDetail(data) {
    return await getAlbumDetail(data);
  },
  async getAlbumList(data) {
    return await getAlbumList(data);
  },
  async getAlbumListPage(data) {
    return await getAlbumListPage(data);
  },
  async updateAlbumDetailSave(data) {
    return await updateAlbumDetailSave(data);
  },
  async batchCreateAlbum(data) {
    return await batchCreateAlbum(data);
  },
  async getAlbumSendList(data) {
    return await getAlbumSendList(data);
  },
  async getSendListExport(data) {
    return await getSendListExport(data);
  }
};
