import axios from "../http";
import Vue from "vue";
import qs from "qs";

// 意向客户 创建线索，新增线索详情
// function getCreateStudents_xs(data) {
//   return axios
//     .post(`/api/market-service/customer/create`, data)
//     .then((response) => {
//       if (response.status === 200) {
//         Vue.prototype.$message.success("创建成功");
//       }
//       return response;
//     })
//     .catch((error) => {
//       console.log(error);
//       Vue.prototype.$message.error(error.err);
//       return Promise.reject(error);
//     })
//     .finally();
// }
// 意向客户列表
function visitorList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/questionnaire-service/admin/miniProgram/visitor/list?${new_data}`
    )
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      console.log(error);
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 绑定学员
function visitorBindList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/questionnaire-service/admin/miniProgram/visitor/bindList?${new_data}`
    )
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      console.log(error);
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 获取游客纯游客人数
function visitorNum(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/questionnaire-service/admin/miniProgram/visitor/num?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      console.log(error);
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 获取游客已绑定的意向人数
function visitorCustomerBindNum(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/questionnaire-service/admin/miniProgram/visitor/customerBindNum?${new_data}`
    )
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      console.log(error);
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 获取游客已绑定的学员人数
function visitorStudentBindNum(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/questionnaire-service/admin/miniProgram/visitor/studentBindNum?${new_data}`
    )
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      console.log(error);
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
export default {
  async CreateCustomerCheck(data) {
    return createCustomerCheck(data);
  },
  async VisitorList(data) {
    return visitorList(data);
  },
  async VisitorBindList(data) {
    return visitorBindList(data);
  },
  async VisitorNum(data) {
    return visitorNum(data);
  },
  async VisitorCustomerBindNum(data) {
    return visitorCustomerBindNum(data);
  },
  async VisitorStudentBindNum(data) {
    return visitorStudentBindNum(data);
  }
};
