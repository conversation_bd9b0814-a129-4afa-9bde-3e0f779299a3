import axios from "../http";
import Vue from "vue";
import qs from "qs";
// const fetch = require("../fetch");
import { fetchUploadFile } from "../fetch";
// 指标查看
function indicatorView(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/organization-service/dynamic-board/list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 导入指标
function importIndicator(data) {
  return fetchUploadFile(
    `/api/organization-service/dynamic-board/import-new`,
    data
  );
}
// 指标修改
function indicatorUpdate(data) {
  return axios
    .post(`/api/organization-service/dynamic-board/update`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
function targetList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/organization-service/dynamic-board/target-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
export default {
  async indicatorView(data) {
    return indicatorView(data);
  },
  async importIndicator(data) {
    return importIndicator(data);
  },
  async indicatorUpdate(data) {
    return indicatorUpdate(data);
  },
  async targetList(data) {
    return targetList(data);
  }
};
