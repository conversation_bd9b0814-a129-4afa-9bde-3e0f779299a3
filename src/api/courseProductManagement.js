import { fetchPost, fetchGet } from "../fetch";
import qs from "qs";
// 课程产品列表
function getCourseMinproList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/course-service/course-minpro/list?${new_data}`);
}
// 课程产品新增
function addCourseMinpro(data) {
  return fetchPost("/api/course-service/course-minpro/create", data, "");
}
// 课程产品编辑
function editCourseMinpro(data) {
  return fetchPost("/api/course-service/course-minpro/modify", data, "");
}
// 课程产品删除
function deleteCourseMinpro(data) {
  return fetchPost("/api/course-service/course-minpro/delete", data, "");
}
// 课程产品详情
function getCourseMinproDetail(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/course-service/course-minpro/info?${new_data}`);
}
// 课程产品推荐到首页
function toFront(data) {
  return fetchPost("/api/course-service/course-minpro/to-front", data, "");
}
// 课程产品下架
function offShelf(data) {
  return fetchPost("/api/course-service/course-minpro/put-on", data, "");
}

export default {
  getCourseMinproList,
  addCourseMinpro,
  editCourseMinpro,
  deleteCourseMinpro,
  getCourseMinproDetail,
  toFront,
  offShelf
};
