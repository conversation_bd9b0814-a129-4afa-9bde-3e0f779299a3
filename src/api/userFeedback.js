import qs from "qs";
const fetch = require("../fetch");

// 反馈类型列表
function getFeedbackType(data) {
  return fetch.fetchGet(
    `/api/questionnaire-service/admin/complaint/categories`,
    {
      params: data
    }
  );
}
// 反馈列表
function getFeedbackList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/questionnaire-service/admin/complaint/list?${new_data}`
  );
}
// 反馈详情
function getFeedbackDetail(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/questionnaire-service/admin/complaint/detail?${new_data}`
  );
}
function getComplaintPending(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/questionnaire-service/admin/complaint/pending?${new_data}`
  );
}
// 反馈处理
function replyFeedback(data) {
  return fetch.fetchPost(
    `/api/questionnaire-service/admin/complaint/reply`,
    data
  );
}
// 反馈处理导出
function exportDetailExcel(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/questionnaire-service/admin/complaint/export?${new_data}`,
    {
      params: { exportData: 1 }
    }
  );
}

export default {
  getFeedbackType,
  getFeedbackList,
  getFeedbackDetail,
  replyFeedback,
  exportDetailExcel,
  getComplaintPending
};
