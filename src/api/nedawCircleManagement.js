import axios from "../http";
import Vue from "vue";
import qs from "qs";

// 意向客户 创建线索，新增线索详情
// function getCreateStudents_xs(data) {
//   return axios
//     .post(`/api/market-service/customer/create`, data)
//     .then((response) => {
//       if (response.status === 200) {
//         Vue.prototype.$message.success("创建成功");
//       }
//       return response;
//     })
//     .catch((error) => {
//       console.log(error);
//       Vue.prototype.$message.error(error.err);
//       return Promise.reject(error);
//     })
//     .finally();
// }
// 聂道圈列表统计
function listStatistics(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/questionnaire-service/admin/moments/list-statistics?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      console.log(error);
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 聂道圈列表
function list(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/questionnaire-service/admin/moments/list?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      console.log(error);
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 聂道圈详情
function detail(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/questionnaire-service/admin/moments/detail?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      console.log(error);
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 聂道圈创建
function create(data) {
  return axios
    .post(`/api/questionnaire-service/admin/moments/create`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      console.log(error);
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 聂道圈删除
function deleteMoment(data) {
  return axios
    .post(`/api/questionnaire-service/admin/moments/delete`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      console.log(error);
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 聂道圈更新
function update(data) {
  return axios
    .post(`/api/questionnaire-service/admin/moments/update`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      console.log(error);
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 聂道圈更新自定义点赞数量
function updateCustomCount(data) {
  return axios
    .post(`/api/questionnaire-service/admin/moments/update/custom/count`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      console.log(error);
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 聂道圈更新置顶状态
function updateTopStatus(data) {
  return axios
    .post(`/api/questionnaire-service/admin/moments/update/top-status`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      console.log(error);
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 聂道圈更新审核状态
function updateVerify(data) {
  return axios
    .post(`/api/questionnaire-service/admin/moments/update/verify`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      console.log(error);
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 聂道圈统计
function momentsStatistics(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/questionnaire-service/admin/moments/statistics?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      console.log(error);
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 聂道圈每日统计
function dailyStatistics(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/questionnaire-service/admin/moments/daily-statistics?${new_data}`
    )
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      console.log(error);
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 聂道圈点赞列表
function studentLikeList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/questionnaire-service/admin/moments/student-like-list?${new_data}`
    )
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      console.log(error);
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 聂道圈浏览列表
function studentViewList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/questionnaire-service/admin/moments/student-view-list?${new_data}`
    )
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      console.log(error);
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 获取置顶状态
function getTopStatus(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/questionnaire-service/admin/moments/up-status?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      console.log(error);
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}

export default {
  async ListStatistics(data) {
    return listStatistics(data);
  },
  async List(data) {
    return list(data);
  },
  async Create(data) {
    return create(data);
  },
  async DeleteMoment(data) {
    return deleteMoment(data);
  },
  async Detail(data) {
    return detail(data);
  },
  async Update(data) {
    return update(data);
  },
  async UpdateCustomCount(data) {
    return updateCustomCount(data);
  },
  async UpdateTopStatus(data) {
    return updateTopStatus(data);
  },
  async UpdateVerify(data) {
    return updateVerify(data);
  },
  async MomentsStatistics(data) {
    return momentsStatistics(data);
  },
  async DailyStatistics(data) {
    return dailyStatistics(data);
  },
  async StudentLikeList(data) {
    return studentLikeList(data);
  },
  async StudentViewList(data) {
    return studentViewList(data);
  },
  async GetTopStatus(data) {
    return getTopStatus(data);
  }
};
