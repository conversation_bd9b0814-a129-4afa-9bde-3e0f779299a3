import axios from "../http";
import Vue from "vue";
import qs from "qs";

function cusBindList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/questionnaire-service/admin/miniProgram/bindCustomer/cusBindList?${new_data}`
    )
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      console.log(error);
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}

function modifyCustomerRole(data) {
  return axios
    .post(
      `/api/questionnaire-service/admin/miniProgram/changeCustomer/modifyCustomerRole`,
      data
    )
    .then((response) => {
      if (response.status === 200) {
        Vue.prototype.$message.success("创建成功");
      }
      return response;
    })
    .catch((error) => {
      console.log(error);
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
function unbindCustomer(data) {
  return axios
    .post(
      `/api/questionnaire-service/admin/miniProgram/customerOpenId/unbindCustomer`,
      data
    )
    .then((response) => {
      if (response.status === 200) {
        Vue.prototype.$message.success("创建成功");
      }
      return response;
    })
    .catch((error) => {
      console.log(error);
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 意向客户 创建线索，新增线索详情
function getCreateStudents_xs(data) {
  return axios
    .post(`/api/market-service/customer/create`, data)
    .then((response) => {
      if (response.status === 200) {
        Vue.prototype.$message.success("创建成功");
      }
      return response;
    })
    .catch((error) => {
      console.log(error);
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 校验意向客户
function createCustomerCheck(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/market-service/customer/check?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      console.log(error);
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 编辑单个员工信息，获取线索详情
function getFindStudents_xs(data) {
  return axios
    .get(`/api/market-service/customer/info?customer_id=${data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 所有线索，获取线索列表
function getStudentsList_xs(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/market-service/customer/list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 所有线索，获取线索列表
function getSearchStudentsList_xs(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/market-service/customer/search-list?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 编辑后保存接口，更新线索详情
function UpdateEmployee_xs(data) {
  return axios
    .patch(`/api/market-service/customer/update?customer_id=${data.id}`, data)
    .then((response) => {
      Vue.prototype.$message.success("修改成功");
      return response;
    })
    .catch((err) => {
      // return err;
      Vue.prototype.$message.error(err.err);
      return Promise.reject(err);
    })
    .finally();
}
// 删除线索，删除线索
function deleteStudents_xs(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .delete(`/api/market-service/customer/many-delete?${new_data}`)
    .then((response) => {
      if (response.status === 200) {
        Vue.prototype.$message.success("删除成功");
      }
      return response;
    })
    .catch(() => {
      Vue.prototype.$message.error("删除失败");
    })
    .finally();
}
function deleteStudentsOne(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .delete(`/api/market-service/clue/delete?${new_data}`)
    .then((response) => {
      if (response.status === 200) {
        Vue.prototype.$message.success("删除成功");
      }
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 责任人
function getObligator_xs(data) {
  return axios
    .get(`/api/v1/list-helper/get-obligator-list`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 同步，未体现
function getTongbu(data) {
  return axios
    .post(`/api/market-service/customer/intention`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 改变行内数据
function updateAppendData(data) {
  return axios
    .post(`/api/market-service/customer/update-addition-info`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 批量上传，线索导入
function batchImport(data) {
  return axios
    .post(`/api/market-service/customer/import`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}
// 导出，线索导出
function exportExcel(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/market-service/customer/export?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
function exportLoop(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/market-service/customer/export-rolling?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
function exportDownFile(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/market-service/customer/download?${new_data}`, {
      params: { exportData: 1 }
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function selectedExportFile(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/market-service/customer/id-export?${new_data}`, {
      params: { exportData: 1 }
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 意向级别
function getLevelList() {
  return axios
    .get(`/api/market-service/intention-level/list`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 取消转换
function cancelCustomerStatus(data) {
  return axios
    .patch(`/api/market-service/customer/cancel-customer-status`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取客户状态
function getStatusList() {
  return axios
    .get(`/api/market-service/customer/status-list`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取跟进类型
function getFollowTypeList() {
  return axios
    .get(`/api/market-service/customer/follow-type-list`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 批量修改
function batchEdit(data) {
  return axios
    .post(`/api/market-service/customer/many-update`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 变为试听
function toAudition(data) {
  return axios
    .post(`/api/market-service/customer/to-audition`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 变为正式
function toStudent(data) {
  return axios
    .post(`/api/market-service/customer/to-student`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 同步到校管家
function syncXiaogj(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/market-service/customer/to-school-manager?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 意向客户优惠券明细列表
function getCustomerUsedList(data) {
  return axios
    .get(`/api/coupon-service/coupon-detail/customer-used-list`, {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 意向客户申请转校
function transferSchoolApprove(data) {
  return axios
    .post(`/api/market-service/customer/transfer-school-approve`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export default {
  async cusBindList(data) {
    return cusBindList(data);
  },
  async modifyCustomerRole(data) {
    return modifyCustomerRole(data);
  },
  async unbindCustomer(data) {
    return unbindCustomer(data);
  },
  // 创建线索
  async GetCreateStudents_xs(data) {
    return getCreateStudents_xs(data);
  },
  // 校验意向客户
  async createCustomerCheck(data) {
    return createCustomerCheck(data);
  },
  // 编辑单个员工信息
  async GetFindStudents_xs(data) {
    return getFindStudents_xs(data);
  },
  // 所有线索
  async GetStudentsList_xs(data) {
    return getStudentsList_xs(data);
  },
  // 搜索意向客户
  async GetSearchStudentsList_xs(data) {
    return getSearchStudentsList_xs(data);
  },
  // 编辑后保存接口
  async UpdateEmployee_xs(data) {
    return UpdateEmployee_xs(data);
  },
  // 删除线索
  async DeleteStudents_xs(data) {
    return deleteStudents_xs(data);
  },
  async deleteStudentsOne(data) {
    return deleteStudentsOne(data);
  },
  // 责任人
  async GetObligator_xs(data) {
    return getObligator_xs(data);
  },
  // 同步
  async GetTongbu(data) {
    return getTongbu(data);
  },
  // 批量导入
  async BatchImport(data) {
    return batchImport(data);
  },
  // 导出
  async ExportExcel(data) {
    return exportExcel(data);
  },
  // 部分导出
  async selectedExportFile(data) {
    return selectedExportFile(data);
  },

  // 意向级别
  async GetLevelList(data) {
    return getLevelList(data);
  },
  // 取消转化
  async cancelCustomerStatus(data) {
    return cancelCustomerStatus(data);
  },
  async UpdateAppendData(data) {
    return updateAppendData(data);
  },
  // 获取客户状态
  async getStatusList(data) {
    return getStatusList(data);
  },
  async getFollowTypeList(data) {
    return getFollowTypeList(data);
  },
  async batchEdit(data) {
    return batchEdit(data);
  },
  async toAudition(data) {
    return toAudition(data);
  },
  async toStudent(data) {
    return toStudent(data);
  },
  async syncXiaogj(data) {
    return syncXiaogj(data);
  },
  // 意向客户优惠券明细列表
  async getCustomerUsedList(data) {
    return getCustomerUsedList(data);
  },
  async exportDownFile(data) {
    return exportDownFile(data);
  },
  async exportLoop(data) {
    return exportLoop(data);
  },
  async transferSchoolApprove(data) {
    return transferSchoolApprove(data);
  }
};
