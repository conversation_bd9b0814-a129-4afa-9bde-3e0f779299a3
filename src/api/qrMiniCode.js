import { fetchGet, fetchPost } from "../fetch";
import qs from "qs";
// 客服二维码
// /api/questionnaire-service/admin/qrCode/create 创建|编辑
// /api/questionnaire-service/admin/qrCode/del 删除
// /api/questionnaire-service/admin/qrCode/detail 详情
// /api/questionnaire-service/admin/qrCode/list二维码列表
// /api/questionnaire-service/admin/qrCode/update/status 修改状态

const api_path = "/api/questionnaire-service/admin/qRCode";

// 创建|编辑
function create(data) {
  return fetchPost(`${api_path}/create`, data, "");
}
// 删除
function del(data) {
  return fetchPost(`${api_path}/del`, data, "");
}
// 详情
function detail(data) {
  return fetchPost(`${api_path}/detail`, data, "");
}
// 列表
function list(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`${api_path}/list?${new_data}`);
}
// 更新状态
function updateStatus(data) {
  return fetchPost(`${api_path}/update/status`, data, "");
}

export default {
  create,
  del,
  detail,
  list,
  updateStatus
};
