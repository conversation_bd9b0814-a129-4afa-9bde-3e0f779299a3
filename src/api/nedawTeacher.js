import axios from "../http";
import Vue from "vue";
import qs from "qs";

// 创建教师
function nedawTeacherCreate(data) {
  //   const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .post(`/api/questionnaire-service/admin/nieDaoTeacher/create`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      console.log(error);
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}

// 教师详情
function nedawTeacherDetail(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/questionnaire-service/admin/nieDaoTeacher/detail?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      console.log(error);
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}

// 教师列表
function nedawTeacherList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/questionnaire-service/admin/nieDaoTeacher/list?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      console.log(error);
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}

// 获取围棋段位
function nedawTeacherWeiqiLevel(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/questionnaire-service/admin/nieDaoTeacher/level?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      console.log(error);
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}

// 更新教师
function nedawTeacherUpdate(data) {
  return axios
    .post(`/api/questionnaire-service/admin/nieDaoTeacher/update`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      console.log(error);
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}

// 启用停用
function nedawTeacherEnable(data) {
  return axios
    .post(`/api/questionnaire-service/admin/nieDaoTeacher/update/status`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      console.log(error);
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}

// 删除
function nedawTeacherDelete(data) {
  return axios
    .post(`/api/questionnaire-service/admin/nieDaoTeacher/del`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      console.log(error);
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}

// 获取已添加的教师
function nedawTeacherListBySchool(data) {
  return axios
    .get(`/api/questionnaire-service/admin/nieDaoTeacher/used-teacher`, data)
    .then((response) => {
      return response.data;
    });
}
// 获取员工信息
function getEmployeeInfo(data) {
  return axios
    .get(`/api/questionnaire-service/admin/moments/teacher/info`, {
      params: data
    })
    .then((response) => {
      return response.data;
    });
}

export default {
  async nedawTeacherCreate(data) {
    return nedawTeacherCreate(data);
  },
  async nedawTeacherDetail(data) {
    return nedawTeacherDetail(data);
  },
  async nedawTeacherList(data) {
    return nedawTeacherList(data);
  },
  async nedawTeacherWeiqiLevel(data) {
    return nedawTeacherWeiqiLevel(data);
  },
  async nedawTeacherUpdate(data) {
    return nedawTeacherUpdate(data);
  },
  async nedawTeacherEnable(data) {
    return nedawTeacherEnable(data);
  },
  async nedawTeacherDelete(data) {
    return nedawTeacherDelete(data);
  },
  async nedawTeacherListBySchool(data) {
    return nedawTeacherListBySchool(data);
  },
  async getEmployeeInfo(data) {
    return getEmployeeInfo(data);
  }
};
