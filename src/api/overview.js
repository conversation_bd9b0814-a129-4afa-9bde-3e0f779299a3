import axios from "../http";
import Vue from "vue";
import qs from "qs";

function getDriveList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/organization-service/drive/list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export default {
  async getDriveList(data) {
    return getDriveList(data);
  }
};
