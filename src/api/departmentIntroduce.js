import axios from "../http";
import Vue from "vue";
import qs from "qs";

// 创建校区介绍
function createDepartmentIntroduce(data) {
  return axios
    .post(`/api/questionnaire-service/admin/departmentIntroduce/create`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 创建模块
function createModule(data) {
  return axios
    .post(
      `/api/questionnaire-service/admin/departmentIntroduce/create-module`,
      data
    )
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 删除
function deleteDepartmentIntroduce(data) {
  return axios
    .post(`/api/questionnaire-service/admin/departmentIntroduce/del`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 删除模块
function deleteModule(data) {
  return axios
    .post(
      `/api/questionnaire-service/admin/departmentIntroduce/del-module`,
      data
    )
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取详情
function getDetail(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/questionnaire-service/admin/departmentIntroduce/detail?${new_data}`
    )
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取列表
function getList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/questionnaire-service/admin/departmentIntroduce/list?${new_data}`
    )
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取模块详情
function getModuleDetail(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/questionnaire-service/admin/departmentIntroduce/module-detail?${new_data}`
    )
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取模块列表
function getModuleList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/questionnaire-service/admin/departmentIntroduce/module-list?${new_data}`
    )
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 编辑
function editDepartmentIntroduce(data) {
  return axios
    .post(`/api/questionnaire-service/admin/departmentIntroduce/update`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 编辑模块
function editModule(data) {
  return axios
    .post(
      `/api/questionnaire-service/admin/departmentIntroduce/update-module`,
      data
    )
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 启用停用
function enableOrDisable(data) {
  return axios
    .post(
      `/api/questionnaire-service/admin/departmentIntroduce/update/status`,
      data
    )
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取校区信息
function getCampusInfo(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/questionnaire-service/admin/departmentIntroduce/department-info?${new_data}`
    )
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 编辑模块内容
function updateModule(data) {
  return axios
    .post(
      `/api/questionnaire-service/admin/departmentIntroduce/update-module`,
      data
    )
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取已有校区列表
function getFilledCampusList(data) {
  return axios
    .get(
      `/api/questionnaire-service/admin/departmentIntroduce/used-department`,
      data
    )
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export default {
  createDepartmentIntroduce,
  createModule,
  deleteDepartmentIntroduce,
  editModule,
  getDetail,
  getList,
  getModuleDetail,
  getModuleList,
  editDepartmentIntroduce,
  enableOrDisable,
  deleteModule,
  getCampusInfo,
  updateModule,
  getFilledCampusList
};
