<template>
  <div class="user-feedback-create">
    <el-dialog
      title="处理反馈内容"
      :visible="true"
      width="800px"
      class="custom_edit_dialogs"
      :show-close="true"
      :before-close="back"
    >
      <div class="dialogs-content">
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
          <!-- 基本信息 -->
          <div class="basic-info module--margin">
            <div class="section-title">反馈内容</div>
            <div class="section-content">
              <el-form-item label="校区">
                <el-input v-model="info.department_name" disabled></el-input>
              </el-form-item>
              <el-form-item label="反馈类型">
                <el-input v-model="info.category_name" disabled></el-input>
              </el-form-item>
              <el-form-item label="提交方式">
                <el-checkbox :checked="true" disabled>{{
                  info.is_anonymous ? "匿名" : "非匿名"
                }}</el-checkbox>
              </el-form-item>
              <el-form-item label="联系方式">
                <el-input v-model="info.contact_phone" disabled></el-input>
              </el-form-item>
              <el-form-item label="意见或建议">
                <el-input
                  type="textarea"
                  v-model="info.feedback"
                  :rows="4"
                  disabled
                ></el-input>
              </el-form-item>
              <el-form-item
                v-if="info?.feedback_attachment?.length > 0"
                label="上传图片"
              >
                <div class="image-list">
                  <div
                    v-for="(url, index) in info.feedback_attachment"
                    :key="index"
                    class="image-item"
                  >
                    <el-image
                      :src="url"
                      fit="cover"
                      :preview-src-list="info.feedback_attachment"
                    >
                    </el-image>
                  </div>
                </div>
              </el-form-item>
            </div>
          </div>

          <!-- 处理详情 -->
          <div class="handle-info module--margin">
            <div class="section-title">处理详情</div>
            <div class="section-content">
              <el-form-item label="答复内容" prop="reply">
                <el-input
                  type="textarea"
                  v-model="form.reply"
                  :rows="4"
                  maxlength="200"
                  placeholder="请输入答复内容"
                ></el-input>
              </el-form-item>
              <el-form-item label="备注">
                <el-input
                  type="textarea"
                  v-model="form.remark"
                  :rows="4"
                  maxlength="200"
                  placeholder="请输入备注信息"
                ></el-input>
              </el-form-item>
              <el-form-item class="form-item-cover" label="上传图片">
                <el-upload
                  action="#"
                  list-type="picture-card"
                  accept="image/*"
                  :file-list="coverFileList"
                  :limit="4"
                  ref="uploadImgsBody"
                  :http-request="(file) => uploadImg(file, 'coverFileList')"
                >
                  <i slot="default" class="el-icon-plus"></i>
                  <div
                    class="el-upload-listitem"
                    slot="file"
                    slot-scope="{ file }"
                  >
                    <el-image
                      class="el-upload-list__item-thumbnail"
                      :src="file.url"
                      fit="cover"
                      :preview-src-list="[file.url]"
                    >
                    </el-image>
                    <span
                      class="el-upload-list__item-delete"
                      @click="handleRemove(file, 'coverFileList')"
                    >
                      <i class="el-icon-delete"></i>
                    </span>
                  </div>
                </el-upload>
              </el-form-item>
            </div>
          </div>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="tg-button--plain" type="plain" @click="back"
          >返回</el-button
        >
        <el-button
          :loading="loading"
          v-throttle="reply"
          class="tg-button--primary"
          type="primary"
          >保存</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { v4 as uuidv4 } from "uuid";
import userFeedbackApi from "@/api/userFeedback.js";
export default {
  name: "create",
  props: {
    feedbackData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      form: {
        remark: "",
        remark_attachment: [],
        reply: ""
      },
      info: {},
      rules: {
        reply: [{ required: true, message: "请输入答复内容", trigger: "blur" }]
      },
      coverFileList: []
    };
  },
  created() {
    this.Oss.getAliyun();
    this.initData();
  },
  watch: {
    coverFileList(val) {
      if (val && val.length >= 4) {
        this.$nextTick(() => {
          $(".form-item-cover .el-upload--picture-card").hide();
        });
      } else {
        this.$nextTick(() => {
          $(".form-item-cover .el-upload--picture-card").show();
        });
      }
      this.form.remark_attachment = this.coverFileList.map((item) => item.url);
    }
  },
  methods: {
    initData() {
      // TODO: 初始化数据，从props中获取
      if (this.feedbackData) {
        this.info = this.feedbackData;
      }
    },
    // 回复
    reply() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true;
          // this.$store.dispatch("toReplyFeedback", {
          //   ...this.form,
          //   order_no: this.info.order_no
          // });
          userFeedbackApi
            .replyFeedback({
              ...this.form,
              order_no: this.info.order_no
            })
            .then((res) => {
              if (res.data.code === 0) {
                this.$message.success("回复成功");
                this.$emit("getList");
                this.$emit("close");
              } else {
                this.$message.error(res.data.message || "回复失败");
              }
            })
            .catch(() => {
              this.$message.error("回复失败");
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    },
    back() {
      this.$emit("close", false);
    },
    uploadImg(upload, type) {
      const file = upload.file;
      const img = new Image();
      const reader = new FileReader();

      reader.onload = (e) => {
        img.src = e.target.result;
        img.onload = () => {
          let width = img.width;
          let height = img.height;

          // 如果图片宽度大于750px，进行等比例压缩
          if (width > 750) {
            const scale = width / 750;
            width = 750;
            height = Math.floor(height / scale);
          }

          // 使用canvas进行图片压缩
          const canvas = document.createElement("canvas");
          const ctx = canvas.getContext("2d");
          canvas.width = width;
          canvas.height = height;

          // 在canvas上绘制图片
          ctx.drawImage(img, 0, 0, width, height);

          // 获取压缩后的图片数据并转为blob
          canvas.toBlob(
            (blob) => {
              // 生成文件名
              const suffix = file.name.substring(
                file.name.lastIndexOf(".") + 1
              );
              const fileName = `${uuidv4()}.${suffix}`;

              // 创建新的File对象
              const compressedFile = new File([blob], fileName, {
                type: `image/${suffix === "jpg" ? "jpeg" : suffix}`,
                lastModified: Date.now()
              });

              // 上传到阿里云OSS
              this.Oss.uploadFile(compressedFile)
                .then((res) => {
                  if (res.code === 0) {
                    this[type].push({
                      name: res.objectKey,
                      url: res.url,
                      uid: file.uid
                    });
                  } else {
                    this.$message.error("上传图片失败");
                  }
                })
                .catch((err) => {
                  console.error("上传失败:", err);
                  this.$message.error("上传图片失败");
                });
            },
            file.type,
            0.9
          ); // 压缩质量0.9
        };
      };

      reader.readAsDataURL(file);
    },
    handleRemove(file, type) {
      this[type].map((item, index) => {
        if (file.uid === item.uid) {
          this[type].splice(index, 1);
        }
      });
    },
    submitData() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true;
          // TODO: 调用保存接口
          setTimeout(() => {
            this.$message.success("处理成功");
            this.loading = false;
            this.$emit("close", true);
          }, 1000);
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.user-feedback-create {
  .section-title {
    padding: 0 0 0 15px;
    position: relative;
    font-size: 15px;
    margin-bottom: 20px;
    &::before {
      content: "";
      position: absolute;
      background-color: @base-color;
      top: 8px;
      left: 3px;
      height: 6px;
      width: 6px;
      z-index: 1;
      border-radius: 100%;
    }
  }

  .dialogs-content {
    padding: 16px 16px 16px 16px;
    max-height: 560px;
    overflow-y: scroll;
  }

  .module--margin {
    margin-bottom: 32px;
  }

  .image-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;

    .image-item {
      width: 100px;
      height: 100px;

      .el-image {
        width: 100%;
        height: 100%;
      }
    }
  }

  .custom_edit_dialogs {
    /deep/ .el-dialog {
      height: 700px;
    }
    ::v-deep .el-dialog__body {
      padding: 0;
    }
  }

  .section-content {
    padding: 0 12px;
  }
  .el-form-item {
    margin-bottom: 16px;
  }
  /deep/ .el-upload-listitem {
    position: relative;
    height: 100%;
    .el-upload-list__item-delete {
      position: absolute;
      right: 10px;
      top: inherit;
      bottom: 10px;
      font-size: 12px;
      color: #f44336;
      width: 24px;
      height: 24px;
      line-height: 24px;
      text-align: center;
      background: #ffffffb8;
      border-radius: 100%;
      display: block;
      cursor: pointer;
      &:hover {
        background: rgba(175, 174, 174, 0.69);
      }
    }
  }
}
</style>
