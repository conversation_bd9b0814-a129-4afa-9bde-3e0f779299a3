<template>
  <el-dialog
    title="新增节点"
    :visible.sync="dialogVisibleComputed"
    width="600px"
    v-if="dialogVisibleComputed"
    :close-on-click-modal="false"
    :before-close="beforeClose"
  >
    <div>
      <el-button type="primary" @click="openResourceHub">资料库</el-button>
      <!-- 表单内容 -->
      <el-form
        ref="albumForm"
        :model="form"
        :rules="rules"
        label-position="left"
        label-width="120px"
      >
        <!-- 选择时间 - 添加限制只能选择当天及之前的日期 -->
        <el-form-item label="选择时间" prop="event_time">
          <el-date-picker
            v-model="form.event_time"
            type="date"
            placeholder="选择时间"
            value-format="yyyy-MM-dd"
            :picker-options="pickerOptions"
          ></el-date-picker>
        </el-form-item>
        <!-- 节点标题 -->
        <el-form-item label="节点标题" prop="album_title">
          <el-input
            v-model="form.album_title"
            placeholder="请输入节点标题"
            maxlength="25"
            show-word-limit
          ></el-input>
        </el-form-item>
        <!-- 标签 -->
        <el-form-item label="标签" prop="album_type">
          <!-- 下拉 -->
          <el-select v-model="form.album_type" placeholder="请选择标签">
            <el-option
              v-for="tag in albumTypeList"
              :key="tag.id"
              :label="tag.name"
              :value="tag.id"
              :disabled="tag.status === 1"
            ></el-option>
          </el-select>
        </el-form-item>
        <!-- 内容 -->
        <el-form-item label="内容" prop="album_values">
          <el-input
            v-model="form.album_values"
            type="textarea"
            placeholder="请输入内容"
            maxlength="1000"
            show-word-limit
            autosize
          ></el-input>
        </el-form-item>

        <el-form-item label="图片/视频" prop="album_picture_video">
          <div class="album-content">
            <!-- 上传区域 - 只显示已有媒体和一个新的添加框 -->
            <div
              class="upload-container"
              v-for="item in visibleAlbumItems"
              :key="`album-item-${item.originalIndex}-${item.type || 'empty'}-${
                item.image_url || item.video_url || 'empty'
              }`"
            >
              <!-- 图片上传 -->
              <div class="mediaitem">
                <el-upload
                  class="avatar-uploader"
                  action="#"
                  :show-file-list="false"
                  ref="uploadImgs"
                  :data="{ index: item.originalIndex, type: true }"
                  :before-upload="beforeUpload"
                  :http-request="uploadImg"
                >
                  <!-- 上传进度圆环 -->
                  <el-progress
                    v-if="item.uploading"
                    type="circle"
                    :percentage="item.uploadPercentage"
                    :width="128"
                    class="upload-progress"
                  >
                    <template #default>
                      <div class="progress-content">
                        <span>{{ item.uploadPercentage }}%</span>
                        <span class="progress-text">上传中</span>
                      </div>
                    </template>
                  </el-progress>

                  <div
                    class="media-wrapper"
                    v-else-if="item.image_url || item.type === 'video'"
                  >
                    <img
                      v-if="item.image_url && item.type === 'image'"
                      :src="item.image_url"
                      class="avatar"
                    />
                    <img
                      v-else-if="item.type === 'video'"
                      src="https://tg-prod.oss-cn-beijing.aliyuncs.com/16d607c6-6cfd-43e4-b9f6-a7454c5d1b60.png"
                      alt=""
                      class="avatar"
                    />

                    <!-- 预览和删除按钮 -->
                    <div class="media-actions">
                      <el-button
                        type="primary"
                        icon="el-icon-view"
                        circle
                        size="mini"
                        @click.stop="previewMedia(item)"
                      ></el-button>
                      <el-button
                        type="danger"
                        icon="el-icon-delete"
                        circle
                        size="mini"
                        @click.stop="deleteMedia(item.originalIndex)"
                      ></el-button>
                    </div>
                  </div>
                  <i v-else class="avatar-uploader-icon">
                    <img src="@/assets/图片/upload.png" alt="" />
                  </i>
                </el-upload>
                <!-- 描述 -->
                <div class="mediaitem-desc">
                  请上传图片或视频（仅支持上传png,jpg,jpeg,webp,mp4,mpeg,webm,mov）
                </div>
              </div>

              <!-- 视频封面 -->
              <div v-if="item.type === 'video'" class="mediaitem">
                <el-upload
                  class="avatar-uploader"
                  action="#"
                  :show-file-list="false"
                  ref="uploadVideos"
                  :data="{ index: item.originalIndex, type: false }"
                  :before-upload="beforeUploadPoster"
                  :http-request="uploadImg"
                >
                  <!-- 上传进度圆环 -->
                  <el-progress
                    v-if="item.posterUploading"
                    type="circle"
                    :percentage="item.posterUploadPercentage"
                    :width="128"
                    class="upload-progress"
                  >
                    <template #default>
                      <div class="progress-content">
                        <span>{{ item.posterUploadPercentage }}%</span>
                        <span class="progress-text">上传中</span>
                      </div>
                    </template>
                  </el-progress>

                  <div class="media-wrapper" v-else-if="item.image_url">
                    <img :src="item.image_url" class="avatar" />

                    <!-- 预览和删除按钮 -->
                    <div class="media-actions">
                      <el-button
                        type="primary"
                        icon="el-icon-view"
                        circle
                        size="mini"
                        @click.stop="previewImage(item.image_url)"
                      ></el-button>
                      <el-button
                        type="danger"
                        icon="el-icon-delete"
                        circle
                        size="mini"
                        @click.stop="deletePoster(item.originalIndex)"
                      ></el-button>
                    </div>
                  </div>
                  <i v-else class="avatar-uploader-icon">
                    <img src="@/assets/图片/upload.png" alt="" />
                  </i>
                </el-upload>
                <div class="mediaitem-desc">
                  请上传视频封面（仅支持上传png,jpg,jpeg,webp）
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button size="medium" @click="beforeClose">关闭</el-button>
      <el-button
        size="medium"
        type="primary"
        v-throttle="submitForm"
        :loading="submitting"
        >保存</el-button
      >
    </div>

    <!-- 使用现有的视频预览组件 -->
    <video-dialog
      :visible.sync="videoVisible"
      :videoUrl="currentVideoUrl"
      :videPoster="currentVideoPoster"
    />

    <!-- 图片预览对话框 -->
    <el-dialog
      :visible.sync="imageVisible"
      title="预览"
      width="70%"
      center
      append-to-body
    >
      <div class="image-preview-container">
        <img :src="currentImageUrl" class="preview-image" alt="预览图片" />
      </div>
    </el-dialog>

    <resourceHub
      ref="resourceHub"
      v-if="resource_hub_visible"
      @choose="resourceHubChoose"
      @close="resource_hub_visible = false"
      :department_id="department_id"
    />
  </el-dialog>
</template>

<script>
import { v4 as uuidv4 } from "uuid";
import videoDialog from "./videoDialog.vue";
import resourceHub from "@/views/classManagement/resourceHub.vue";
export default {
  name: "AlbumDetailDialog",
  components: {
    videoDialog,
    resourceHub
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    recordData: {
      type: Object,
      default: null
    },
    albumTypeList: {
      type: Array,
      default: () => []
    },
    department_id: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      // 添加日期选择器配置
      pickerOptions: {
        disabledDate: (time) => {
          // 禁用当前日期之后的所有日期
          return time.getTime() > Date.now();
        }
      },
      submitting: false,
      form: {
        event_time: "",
        album_title: "",
        album_type: "",
        album_values: "",
        album_picture_video: []
      },
      rules: {
        event_time: [
          { required: true, message: "请选择时间", trigger: "blur" }
        ],
        album_title: [
          { required: true, message: "请输入节点标题", trigger: "blur" }
        ],
        album_type: [
          { required: true, message: "请选择标签", trigger: "blur" }
        ],
        album_values: [
          { required: true, message: "请输入内容", trigger: "blur" }
        ],
        album_picture_video: [
          { required: true, message: "请上传图片/视频", trigger: "blur" }
        ]
      },
      album_picture_video: [],
      images: ["jpeg", "png", "webp", "jpg"],
      videos: ["mp4", "mpeg", "webm", "mov"],
      videoVisible: false,
      currentVideoUrl: "",
      currentVideoPoster: "",
      imageVisible: false,
      currentImageUrl: "",
      maxAlbumItems: 5, // 最大媒体数量
      currentMediaCount: 0, // 当前有内容的媒体数量
      resource_hub_visible: false
    };
  },
  computed: {
    dialogVisibleComputed: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      }
    },
    visibleAlbumItems() {
      // 创建一个新的数组来存放要显示的项目
      const result = [];

      // 添加所有已上传的媒体项目（按顺序）
      for (let i = 0; i < this.album_picture_video.length; i++) {
        const item = this.album_picture_video[i];
        result.push({
          ...item,
          originalIndex: i // 保存原始索引，用于删除操作
        });
      }

      // 如果还没有达到最大数量，添加一个空白项用于上传
      if (this.album_picture_video.length < this.maxAlbumItems) {
        result.push({
          id: this.album_picture_video.length + 1,
          name: "",
          image_url: "",
          video_url: "",
          type: "",
          width: 0,
          height: 0,
          uploading: false,
          uploadPercentage: 0,
          posterUploading: false,
          posterUploadPercentage: 0,
          size: 0,
          originalIndex: this.album_picture_video.length // 下一个要添加的位置
        });
      }

      return result;
    }
  },
  watch: {
    visible(val) {
      if (val) {
        console.log("visible", val);
        this.initFormData();
      }
    }
  },
  mounted() {
    this.Oss.getAliyun();
  },
  methods: {
    initFormData() {
      this.$refs.albumForm && this.$refs.albumForm.resetFields();
      this.form = {
        event_time: "",
        album_title: "",
        album_type: "",
        album_values: "",
        album_picture_video: []
      };

      // 重置为空数组
      this.album_picture_video = [];
    },
    beforeClose() {
      this.$emit("update:visible", false);
      this.album_picture_video = [];
      this.currentMediaCount = 0;
      this.form.album_picture_video = [];
      this.initFormData();
    },
    beforeUpload(file) {
      // 检查是否已达到最大数量
      if (this.album_picture_video.length >= this.maxAlbumItems) {
        this.$message.error("最多上传" + this.maxAlbumItems + "个媒体");
        return false;
      }

      const suffix = file.name.match(/[^.]+$/)[0];
      const isImage = this.images.includes(suffix);
      const isVideo = this.videos.includes(suffix);
      if (!isImage && !isVideo) {
        this.$message.error(
          `文件类型不支持,请上传${this.images.join(",")}或${this.videos.join(
            ","
          )}`
        );
        return false;
      }
      const isLt2M = file.size / 1024 / 1024 < 200;
      if (!isLt2M) {
        this.$message.error("上传文件大小不能超过 200MB!");
        return false;
      }
      return true;
    },
    beforeUploadPoster(file) {
      const suffix = file.name.match(/[^.]+$/)[0];
      const isImage = this.images.includes(suffix);
      if (!isImage) {
        this.$message.error(`文件类型不支持,请上传${this.images.join(",")}`);
        return false;
      }
      const isLt2M = file.size / 1024 / 1024 < 20;
      if (!isLt2M) {
        this.$message.error("上传文件大小不能超过 20MB!");
        return false;
      }
      return true;
    },
    uploadImg(upload) {
      console.log(upload);
      const f = upload.file;
      const suffix = f.name.match(/[^.]+$/)[0];
      const name = `${uuidv4()}.${suffix}`;
      const copyFile = new File([f], name);
      const size = f.size;

      const isMediaUpload = upload.data.type;
      let currentIndex;

      // 根据是否是主媒体上传决定处理方式
      if (isMediaUpload) {
        // 主媒体上传：创建新项目并添加到数组末尾
        const newItem = {
          id: this.album_picture_video.length + 1,
          name: "",
          image_url: "",
          video_url: "",
          type: "",
          width: 0,
          height: 0,
          uploading: true,
          uploadPercentage: 0,
          posterUploading: false,
          posterUploadPercentage: 0,
          size: 0
        };
        this.album_picture_video.push(newItem);
        currentIndex = this.album_picture_video.length - 1;
      } else {
        // 封面上传：使用传入的索引
        currentIndex = upload.data.index;
        this.album_picture_video[currentIndex].posterUploading = true;
        this.album_picture_video[currentIndex].posterUploadPercentage = 0;
      }

      // 创建进度回调函数
      const progressCallback = (percentage) => {
        if (isMediaUpload) {
          this.album_picture_video[currentIndex].uploadPercentage = percentage;
        } else {
          this.album_picture_video[currentIndex].posterUploadPercentage =
            percentage;
        }
      };

      // 执行上传操作，传入进度回调
      this.Oss.uploadFile(copyFile, progressCallback)
        .then(
          this.handleUploadSuccess(
            currentIndex,
            isMediaUpload,
            name,
            suffix,
            size
          )
        )
        .catch(this.handleUploadError(currentIndex, isMediaUpload));
    },

    // 处理上传成功回调
    handleUploadSuccess(index, isMediaUpload, name, suffix, size) {
      return (res) => {
        console.log(res);

        if (res.code === 0) {
          // 上传成功，设置进度为100%
          if (isMediaUpload) {
            this.album_picture_video[index].uploadPercentage = 100;

            // 延迟关闭进度条显示
            setTimeout(() => {
              this.album_picture_video[index].uploading = false;

              // 根据文件类型处理上传结果
              this.album_picture_video[index].name = name;
              if (this.images.includes(suffix)) {
                this.getImageDimensions(res.url).then((dimensions) => {
                  this.album_picture_video[index].image_url = res.url;
                  this.album_picture_video[index].width = dimensions.width;
                  this.album_picture_video[index].height = dimensions.height;
                  this.album_picture_video[index].type = "image";
                  this.album_picture_video[index].size = size;

                  // 更新媒体计数
                  this.updateMediaCount();
                });
              } else if (this.videos.includes(suffix)) {
                this.album_picture_video[index].image_url = "";
                this.album_picture_video[index].video_url = res.url;
                this.album_picture_video[index].type = "video";

                // 更新媒体计数
                this.updateMediaCount();
              }
            }, 500);
          } else {
            this.album_picture_video[index].posterUploadPercentage = 100;

            // 延迟关闭进度条显示
            setTimeout(() => {
              this.album_picture_video[index].posterUploading = false;

              // 处理封面上传结果
              this.getImageDimensions(res.url).then((dimensions) => {
                this.album_picture_video[index].image_url = res.url;
                this.album_picture_video[index].width = dimensions.width;
                this.album_picture_video[index].height = dimensions.height;
                this.album_picture_video[index].size = size;
              });
            }, 500);
          }
        } else {
          // 上传失败
          if (isMediaUpload) {
            this.album_picture_video[index].uploading = false;
          } else {
            this.album_picture_video[index].posterUploading = false;
          }
          this.$message.error("上传失败");
        }
      };
    },

    // 处理上传错误回调
    handleUploadError(index, isMediaUpload) {
      return (error) => {
        // 处理上传错误
        if (isMediaUpload) {
          this.album_picture_video[index].uploading = false;
        } else {
          this.album_picture_video[index].posterUploading = false;
        }
        this.$message.error("上传失败: " + error.message);
      };
    },
    getImageDimensions(url) {
      return new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = () => {
          resolve({
            width: img.naturalWidth,
            height: img.naturalHeight
          });
        };
        img.onerror = reject;
        img.src = url;
      });
    },
    removeImage(index) {
      this.form.images.splice(index, 1);
    },
    deleteMedia(index) {
      // 直接从数组中删除该项目
      this.album_picture_video.splice(index, 1);

      // 删除后更新媒体计数，触发计算属性更新
      this.updateMediaCount();
    },
    deletePoster(index) {
      this.album_picture_video[index].image_url = "";
      this.album_picture_video[index].width = 0;
      this.album_picture_video[index].height = 0;
    },
    previewMedia(item) {
      if (item.type === "video" && item.video_url) {
        this.currentVideoUrl = item.video_url;
        this.currentVideoPoster = item.image_url || "";
        this.videoVisible = true;
      } else if (item.type === "image" && item.image_url) {
        this.previewImage(item.image_url);
      }
    },
    previewImage(imageUrl) {
      this.currentImageUrl = imageUrl;
      this.imageVisible = true;
    },
    submitForm() {
      const validationErrors = [];
      this.submitting = true;
      this.form.album_picture_video = this.album_picture_video;
      // 验证表单
      this.$refs.albumForm.validate((valid) => {
        if (!valid) {
          // 遍历所有表单字段，查找验证失败的字段
          if (this.$refs.albumForm.fields) {
            this.$refs.albumForm.fields.forEach((field) => {
              if (field.validateState === "error") {
                validationErrors.push(field.validateMessage);
              }
            });
          }

          // 显示第一个验证错误信息
          if (validationErrors.length > 0) {
            this.$message.error(validationErrors[0]);
          } else {
            this.$message.error("表单验证失败，请检查必填项");
          }
          this.submitting = false;
          return false;
        }

        // 验证媒体内容
        const validMedia = this.album_picture_video.filter(
          (item) => item.type && (item.image_url || item.video_url)
        );

        if (validMedia.length === 0) {
          this.$message.error("请至少上传一张图片或一个视频");
          this.submitting = false;
          return false;
        }

        // 处理视频封面
        const processedMedia = validMedia.map((item) => {
          if (item.type === "video" && !item.image_url) {
            return {
              ...item,
              image_url:
                item.video_url +
                "?x-oss-process=video/snapshot,t_1000,f_jpg,w_0,h_0,m_fast"
            };
          }
          return item;
        });

        this.form.album_picture_video = processedMedia;
        this.$emit("submit", this.form);
      });
    },
    // 添加新方法，用于更新媒体计数
    updateMediaCount() {
      this.currentMediaCount = this.album_picture_video.filter(
        (item) => item.type === "image" || item.type === "video"
      ).length;
    },
    openResourceHub() {
      this.resource_hub_visible = true;
    },
    resourceHubChoose(data, type, poster) {
      console.log(type, poster);
      if (type === 1) {
        if (this.currentMediaCount >= this.maxAlbumItems) {
          this.$message.error("最多上传" + this.maxAlbumItems + "个媒体");
          return;
        }
        this.getImageDimensions(data).then((dimensions) => {
          // 按顺序添加到数组末尾
          this.album_picture_video.push({
            id: this.album_picture_video.length + 1,
            type: "image",
            image_url: data,
            name: data,
            width: dimensions.width,
            height: dimensions.height,
            video_url: "",
            uploading: false,
            uploadPercentage: 0,
            posterUploading: false,
            posterUploadPercentage: 0,
            size: 0
          });
          // 更新媒体计数
          this.updateMediaCount();
        });
      } else if (type === 2) {
        if (this.currentMediaCount >= this.maxAlbumItems) {
          this.$message.error("最多上传" + this.maxAlbumItems + "个媒体");
          return;
        }
        this.getImageDimensions(
          poster ||
            data + "?x-oss-process=video/snapshot,t_1000,f_jpg,w_0,h_0,m_fast"
        ).then((dimensions) => {
          // 按顺序添加到数组末尾
          this.album_picture_video.push({
            id: this.album_picture_video.length + 1,
            type: "video",
            video_url: data,
            image_url:
              poster ||
              data +
                "?x-oss-process=video/snapshot,t_1000,f_jpg,w_0,h_0,m_fast",
            name: data,
            width: dimensions.width,
            height: dimensions.height,
            uploading: false,
            uploadPercentage: 0,
            posterUploading: false,
            posterUploadPercentage: 0,
            size: 0
          });
          // 更新媒体计数
          this.updateMediaCount();
        });
      } else if (type === 3) {
        this.form.album_values = data;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.album-content {
  .image-list {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;

    .image-item {
      position: relative;
      width: 150px;
      height: 150px;
      border: 1px solid #eee;
      border-radius: 4px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .image-actions {
        position: absolute;
        top: 5px;
        right: 5px;

        .el-button {
          background-color: rgba(0, 0, 0, 0.5);
          border: none;
          color: #fff;
          padding: 5px;
        }
      }
    }
  }

  .upload-container {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;

    .mediaitem {
      padding: 0 !important;
      // margin-bottom: 20px;
    }

    .mediaitem-desc {
      text-align: center;
      margin-top: 10px;
      color: #8c939d;
      font-size: 12px;
      line-height: 20px;
      width: 128px;
    }
  }
}

::v-deep .el-input__inner {
  width: 300px;
}

::v-deep .el-form-item__content {
  width: 300px;
}

::v-deep .el-input {
  width: 300px !important;
}

::v-deep .avatar-uploader {
  width: 128px;
  height: 128px;
  display: block;

  .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  .el-upload:hover {
    border-color: #409eff;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 128px;
    height: 128px;
    line-height: 150px;
    text-align: center;
    display: block;

    img {
      width: 56px;
      height: 47px;
    }
  }

  .avatar {
    width: 128px;
    height: 128px;
    display: block;
  }
}

::v-deep .el-input::after {
  display: none;
}

::v-deep .el-input__prefix {
  top: 4px;
}

.media-wrapper {
  position: relative;
  width: 100%;
  height: 100%;

  &:hover .media-actions {
    opacity: 1;
  }
}

.media-actions {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s;
  gap: 10px;
}

.image-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;

  .preview-image {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
  }
}

::v-deep .el-textarea__inner {
  min-height: 120px !important;
}

::v-deep .el-input__count {
  background: transparent !important;
}

/* 添加进度条相关样式 */
.upload-progress {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 128px;
  height: 128px;
}

.progress-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  span {
    font-size: 18px;
    font-weight: bold;
  }

  .progress-text {
    font-size: 12px;
    margin-top: 5px;
    color: #8c939d;
  }
}

::v-deep .el-progress__text {
  display: none;
}

::v-deep .el-progress-circle {
  width: 100% !important;
  height: 100% !important;
}
</style>
