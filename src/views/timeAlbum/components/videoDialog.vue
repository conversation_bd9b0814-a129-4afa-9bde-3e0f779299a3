<template>
  <el-dialog
    :visible.sync="videoDialog"
    title="预览"
    width="70%"
    center
    class="videoPlayer"
    append-to-body
    v-if="videoDialog"
  >
    <div>
      <video
        style="width: 100%; height: 500px"
        class="video-player vjs-custom-skin"
        ref="videoPlayer"
        :playsline="false"
        :controls="true"
        :poster="videPoster"
        :src="videoUrl"
        :autoplay="true"
      ></video>
    </div>
  </el-dialog>
</template>
<script>
export default {
  data() {
    return {};
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    videoUrl: {
      type: String,
      default: ""
    },
    videPoster: {
      type: String,
      default: ""
    }
  },
  computed: {
    videoDialog: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      }
    }
  },
  methods: {
    // 播放回调
    onPlayerPlay(player) {},

    // 暂停回调
    onPlayerPause(player) {},

    // 视频播完回调
    onPlayerEnded($event) {},

    // DOM元素上的readyState更改导致播放停止
    onPlayerWaiting($event) {},

    // 已开始播放回调
    onPlayerPlaying($event) {},

    // 当播放器在当前播放位置下载数据时触发
    onPlayerLoadeddata($event) {},

    // 当前播放位置发生变化时触发。
    onPlayerTimeupdate($event) {},

    // 媒体的readyState为HAVE_FUTURE_DATA或更高
    onPlayerCanplay(player) {},

    // 媒体的readyState为HAVE_ENOUGH_DATA或更高。这意味着可以在不缓冲的情况下播放整个媒体文件。
    onPlayerCanplaythrough(player) {},

    // 播放状态改变回调
    playerStateChanged(playerCurrentState) {},

    // 将侦听器绑定到组件的就绪状态。与事件监听器的不同之处在于，如果ready事件已经发生，它将立即触发该函数。。
    playerReadied(player) {}
  }
};
</script>
<style lang="sass" scoped></style>
