<template>
  <div class="uplod">
    <el-dialog
      :visible.sync="dialogVisible"
      width="400px"
      :before-close="handleClose"
      title="上传图片或视频"
      append-to-body
      v-if="dialogVisible"
    >
      <div class="mediaitem">
        <el-upload
          class="avatar-uploader"
          action="#"
          :show-file-list="false"
          ref="uploadImgs"
          :data="{ type: true }"
          :before-upload="beforeUpload"
          :http-request="uploadImg"
        >
          <!-- 上传进度圆环 -->
          <el-progress
            v-if="uploading"
            type="circle"
            :percentage="uploadPercentage"
            :width="128"
            class="upload-progress"
          >
            <template #default>
              <div class="progress-content">
                <span>{{ uploadPercentage }}%</span>
                <span class="progress-text">上传中</span>
              </div>
            </template>
          </el-progress>

          <div
            class="media-wrapper"
            v-else-if="updateInfo.image_url || updateInfo.type === 'video'"
          >
            <img
              v-if="updateInfo.image_url && updateInfo.type === 'image'"
              :src="updateInfo.image_url"
              class="avatar"
            />
            <img
              v-else-if="updateInfo.type === 'video'"
              src="https://tg-prod.oss-cn-beijing.aliyuncs.com/16d607c6-6cfd-43e4-b9f6-a7454c5d1b60.png"
              alt=""
              class="avatar"
            />

            <div class="media-actions">
              <el-button
                type="primary"
                icon="el-icon-view"
                circle
                size="mini"
                @click.stop="previewMedia(updateInfo)"
              ></el-button>
              <el-button
                type="danger"
                icon="el-icon-delete"
                circle
                size="mini"
                @click.stop="deleteMedia()"
              ></el-button>
            </div>
          </div>
          <i v-else class="avatar-uploader-icon">
            <img src="@/assets/图片/upload.png" alt="" />
          </i>
        </el-upload>
        <!-- 描述 -->
        <div class="mediaitem-desc">
          请上传图片或视频（仅支持上传png,jpg,jpeg,webp,mp4,mpeg,webm,mov）
        </div>
      </div>

      <!-- 视频封面上传 -->
      <div
        v-if="updateInfo.video_url && updateInfo.type === 'video'"
        class="mediaitem"
      >
        <el-upload
          class="avatar-uploader"
          action="#"
          :show-file-list="false"
          ref="uploadVideos"
          :data="{ type: false }"
          :before-upload="beforeUploadPoster"
          :http-request="uploadImg"
        >
          <!-- 上传进度圆环 -->
          <el-progress
            v-if="posterUploading"
            type="circle"
            :percentage="posterUploadPercentage"
            :width="128"
            class="upload-progress"
          >
            <template #default>
              <div class="progress-content">
                <span>{{ posterUploadPercentage }}%</span>
                <span class="progress-text">上传中</span>
              </div>
            </template>
          </el-progress>

          <div class="media-wrapper" v-else-if="updateInfo.image_url">
            <img :src="updateInfo.image_url" class="avatar" />

            <div class="media-actions">
              <el-button
                type="primary"
                icon="el-icon-view"
                circle
                size="mini"
                @click.stop="previewImage(updateInfo.image_url)"
              ></el-button>
              <el-button
                type="danger"
                icon="el-icon-delete"
                circle
                size="mini"
                @click.stop="deletePoster()"
              ></el-button>
            </div>
          </div>
          <i v-else class="avatar-uploader-icon">
            <img src="@/assets/图片/upload.png" alt="" />
          </i>
        </el-upload>
        <div class="mediaitem-desc">
          请上传视频封面（仅支持上传png,jpg,jpeg,webp）
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" plain @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          v-throttle="handleUpload"
          :loading="loading"
          :disabled="butLoading"
        >
          上传
        </el-button>
      </div>
    </el-dialog>

    <!-- 添加视频预览对话框 -->
    <video-dialog
      :visible.sync="videoVisible"
      :videoUrl="currentVideoUrl"
      :videPoster="currentVideoPoster"
    />

    <!-- 添加图片预览对话框 -->
    <el-dialog
      :visible.sync="imageVisible"
      title="预览"
      width="70%"
      center
      append-to-body
    >
      <div class="image-preview-container">
        <img :src="currentImageUrl" class="preview-image" alt="预览图片" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { v4 as uuidv4 } from "uuid";
import videoDialog from "./videoDialog.vue";

export default {
  name: "uploadCom",
  components: {
    videoDialog
  },
  data() {
    return {
      images: ["jpeg", "png", "webp", "jpg"],
      videos: ["mp4", "mpeg", "webm", "mov"],
      updateInfo: {
        type: "",
        image_url: "",
        video_url: "",
        width: 0,
        height: 0,
        size: 0
      },
      videoVisible: false,
      currentVideoUrl: "",
      currentVideoPoster: "",
      imageVisible: false,
      currentImageUrl: "",
      loading: false,
      butLoading: false,
      uploading: false,
      uploadPercentage: 0,
      posterUploading: false,
      posterUploadPercentage: 0
    };
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit("update:visible", false);
      this.updateInfo = {
        type: "",
        image_url: "",
        video_url: "",
        width: 0,
        height: 0,
        size: 0
      };
      this.videoVisible = false;
      this.imageVisible = false;
      this.currentVideoUrl = "";
      this.currentVideoPoster = "";
      this.currentImageUrl = "";
    },

    previewMedia(item) {
      if (item.type === "video" && item.video_url) {
        this.currentVideoUrl = item.video_url;
        this.currentVideoPoster = item.image_url || "";
        this.videoVisible = true;
      } else if (item.type === "image" && item.image_url) {
        this.previewImage(item.image_url);
      }
    },

    previewImage(imageUrl) {
      this.currentImageUrl = imageUrl;
      this.imageVisible = true;
    },

    deleteMedia() {
      this.updateInfo.image_url = "";
      this.updateInfo.video_url = "";
      this.updateInfo.type = "";
      this.updateInfo.width = 0;
      this.updateInfo.height = 0;
      this.updateInfo.size = 0;
    },

    deletePoster() {
      this.updateInfo.image_url = "";
      this.updateInfo.width = 0;
      this.updateInfo.height = 0;
    },

    handleUpload() {
      this.loading = true;
      if (!this.updateInfo.type) {
        this.loading = false;
        this.$message.error("请上传图片或视频");
        return;
      }
      if (this.updateInfo.video_url && !this.updateInfo.image_url) {
        this.updateInfo.image_url =
          this.updateInfo.video_url +
          "?x-oss-process=video/snapshot,t_1000,f_jpg,w_0,h_0,m_fast";
        this.$emit("handleUpload", this.updateInfo);
        this.handleClose();
        this.loading = false;
      } else {
        this.$emit("handleUpload", this.updateInfo);
        this.handleClose();
        this.loading = false;
      }
    },

    beforeUpload(file) {
      const suffix = file.name.match(/[^.]+$/)[0];
      const isImage = this.images.includes(suffix);
      const isVideo = this.videos.includes(suffix);
      if (!isImage && !isVideo) {
        this.$message.error(
          `文件类型不支持，请上传${this.images.join(",")}或${this.videos.join(
            ","
          )}`
        );
        return false;
      }
      // 如果上传的是视频
      if (isVideo) {
        this.updateInfo.image_url = "";
      }
      const isLt2M = file.size / 1024 / 1024 < 200;
      if (!isLt2M) {
        this.$message.error("上传文件大小不能超过 200MB!");
        return false;
      }
      return true;
    },

    beforeUploadPoster(file) {
      const suffix = file.name.match(/[^.]+$/)[0];
      const isImage = this.images.includes(suffix);
      if (!isImage) {
        this.$message.error(`文件类型不支持，请上传${this.images.join(",")}`);
        return false;
      }
      const isLt2M = file.size / 1024 / 1024 < 20;
      if (!isLt2M) {
        this.$message.error("上传文件大小不能超过 20MB!");
        return false;
      }
      return true;
    },

    uploadImg(upload) {
      this.butLoading = true;
      const f = upload.file;
      const suffix = f.name.match(/[^.]+$/)[0];
      const name = `${uuidv4()}.${suffix}`;
      const copyFile = new File([f], name);

      const isMediaUpload = upload.data.type;

      // 设置上传状态和初始进度
      if (isMediaUpload) {
        this.uploading = true;
        this.uploadPercentage = 0;
      } else {
        this.posterUploading = true;
        this.posterUploadPercentage = 0;
      }

      // 创建进度回调函数
      const progressCallback = (percentage) => {
        if (isMediaUpload) {
          this.uploadPercentage = percentage;
        } else {
          this.posterUploadPercentage = percentage;
        }
      };

      // 执行上传操作，传入进度回调
      this.Oss.uploadFile(copyFile, progressCallback)
        .then((res) => {
          this.butLoading = false;
          console.log(res);

          if (res.code === 0) {
            // 上传成功，设置进度为100%
            if (isMediaUpload) {
              this.uploadPercentage = 100;

              // 延迟关闭进度条显示
              setTimeout(() => {
                this.uploading = false;

                // 根据文件类型处理上传结果
                if (this.images.includes(suffix)) {
                  this.getImageDimensions(res.url).then((dimensions) => {
                    this.updateInfo.type = "image";
                    this.updateInfo.image_url = res.url;
                    this.updateInfo.width = dimensions.width;
                    this.updateInfo.height = dimensions.height;
                    this.updateInfo.size = f.size;
                  });
                } else if (this.videos.includes(suffix)) {
                  this.updateInfo.type = "video";
                  this.updateInfo.video_url = res.url;
                }
              }, 500);
            } else {
              this.posterUploadPercentage = 100;

              // 延迟关闭进度条显示
              setTimeout(() => {
                this.posterUploading = false;

                // 处理封面上传结果
                this.getImageDimensions(res.url).then((dimensions) => {
                  this.updateInfo.image_url = res.url;
                  this.updateInfo.width = dimensions.width;
                  this.updateInfo.height = dimensions.height;
                  this.updateInfo.size = f.size;
                });
              }, 500);
            }
          } else {
            // 上传失败
            if (isMediaUpload) {
              this.uploading = false;
            } else {
              this.posterUploading = false;
            }
            this.$message.error("上传失败");
          }
        })
        .catch((error) => {
          this.butLoading = false;

          // 处理上传错误
          if (isMediaUpload) {
            this.uploading = false;
          } else {
            this.posterUploading = false;
          }
          this.$message.error("上传失败: " + error.message);
        });
    },

    getImageDimensions(url) {
      return new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = () => {
          resolve({
            width: img.naturalWidth,
            height: img.naturalHeight
          });
        };
        img.onerror = reject;
        img.src = url;
      });
    },

    getVideoFirstFrame(videoUrl) {
      return new Promise((resolve, reject) => {
        const video = document.createElement("video");
        const canvas = document.createElement("canvas");
        const context = canvas.getContext("2d");

        video.onloadeddata = () => {
          try {
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;

            context.drawImage(video, 0, 0, canvas.width, canvas.height);

            const base64Image = canvas.toDataURL("image/jpeg", 0.8);

            this.uploadBase64Image(base64Image)
              .then((url) => {
                resolve(url);
              })
              .catch(reject);
          } catch (error) {
            reject(error);
          }
        };

        video.onerror = () => {
          reject(new Error("视频加载失败"));
        };

        video.src = videoUrl;
        video.currentTime = 0;
      });
    },
    uploadBase64Image(base64Data) {
      return new Promise((resolve, reject) => {
        const arr = base64Data.split(",");
        const mime = arr[0].match(/:(.*?);/)[1];
        const bstr = atob(arr[1]);
        let n = bstr.length;
        const u8arr = new Uint8Array(n);
        while (n--) {
          u8arr[n] = bstr.charCodeAt(n);
        }
        const file = new File([u8arr], `video_cover_${Date.now()}.jpg`, {
          type: mime
        });

        this.Oss.uploadFile(file)
          .then((res) => {
            if (res.code === 0) {
              this.getImageDimensions(res.url)
                .then((dimensions) => {
                  this.updateInfo.width = dimensions.width;
                  this.updateInfo.height = dimensions.height;
                  this.updateInfo.size = file.size;
                  resolve(res.url);
                })
                .catch(reject);
            } else {
              reject(new Error("上传失败"));
            }
          })
          .catch(reject);
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.mediaitem {
  padding: 0 !important;
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  align-content: space-around;
}

.mediaitem-desc {
  text-align: center;
  margin-top: 10px;
  color: #8c939d;
  font-size: 12px;
}

::v-deep .avatar-uploader {
  width: 128px;
  height: 128px;
  display: block;

  .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  .el-upload:hover {
    border-color: #409eff;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 128px;
    height: 128px;
    line-height: 150px;
    text-align: center;
    display: block;

    img {
      width: 56px;
      height: 47px;
    }
  }

  .avatar {
    width: 128px;
    height: 128px;
    display: block;
  }
}

.media-wrapper {
  position: relative;
  width: 100%;
  height: 100%;

  &:hover .media-actions {
    opacity: 1;
  }
}

.media-actions {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s;
  gap: 10px;
}

.image-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;

  .preview-image {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
  }
}

/* 添加进度条相关样式 */
.upload-progress {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 128px;
  height: 128px;
}

.progress-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  span {
    font-size: 18px;
    font-weight: bold;
  }

  .progress-text {
    font-size: 12px;
    margin-top: 5px;
    color: #8c939d;
  }
}

::v-deep .el-progress__text {
  display: none;
}

::v-deep .el-progress-circle {
  width: 100% !important;
  height: 100% !important;
}
</style>
