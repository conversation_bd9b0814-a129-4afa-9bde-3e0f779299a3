<!--选择学员-->
<template>
  <div>
    <el-dialog
      :visible="true"
      title="选择学员"
      width="1016px"
      :before-close="handleClose"
      class="choose-student"
    >
      <div class="tg-dialog__content">
        <div class="class-list">
          <div class="search tg-box--margin">
            <el-form @submit.native.prevent :inline="true" :model="form">
              <el-form-item>
                <el-input
                  placeholder="请输入学员姓名/学号/手机号"
                  class="search__input"
                  v-model="form.name"
                >
                  <img
                    src="@/assets/图片/icon_search_grey.png"
                    alt=""
                    slot="prefix"
                  />
                  <span class="searchBtn" slot="suffix" @click="flag = !flag">
                    <img
                      :src="
                        !flag
                          ? require('@/assets/图片/icon_double_down.png')
                          : require('@/assets/图片/icon_double_up_ac.png')
                      "
                      alt=""
                      class="search__img"
                    />
                  </span>
                </el-input>
              </el-form-item>
              <el-form-item
                label="学员状态"
                class="search-teacher tg-btn--margin"
              >
                <el-select
                  v-model="form.student_type"
                  :popper-append-to-body="false"
                  placeholder="请选择学员状态"
                  multiple
                >
                  <template v-for="(item, index) in student_state_list">
                    <el-option
                      v-if="item.show"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </template>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  class="tg-button--primary tg-button__icon"
                  @click="searchVal"
                >
                  <img
                    src="@/assets/图片/icon_search.png"
                    alt=""
                    class="tg-button__icon--normal"
                  />查询
                </el-button>
                <el-button
                  type="primary"
                  class="tg-button--primary tg-button__icon"
                  @click="reset"
                >
                  <img
                    src="@/assets/图片/icon_reset.png"
                    alt=""
                    class="tg-button__icon--normal"
                  />重置
                </el-button>
              </el-form-item>

              <el-form-item
                label="校区"
                class="tg-form-item tg-box--margin"
                v-if="flag"
              >
                <el-input
                  placeholder="请选择所属校区"
                  readonly
                  v-model="form.department_name"
                >
                </el-input>
                <!-- <school-tree
                  :flag.sync="school_tree_visible"
                  :id.sync="form.department_id"
                  :name.sync="form.department_name"
                  :type="'radio'"
                  class="stu-school-tree"
                >
                </school-tree> -->
              </el-form-item>
              <el-form-item
                label="课程"
                class="tg-form-item tg-box--margin custom--select"
                v-if="flag"
              >
                <el-input
                  v-model="form.course_name"
                  readonly
                  placeholder="请选择课程"
                  @click.native="choose_course_visible = true"
                  @mouseenter.native="course_flag = true"
                  @mouseleave.native="course_flag = false"
                  :class="{ 'border--active': course_flag }"
                >
                  <img
                    :src="
                      !course_flag
                        ? require('@/assets/图片/icon_more.png')
                        : require('@/assets/图片/icon_more_ac.png')
                    "
                    slot="suffix"
                    alt=""
                    class="more"
                  />
                </el-input>
              </el-form-item>
              <el-form-item
                label="班级"
                class="tg-form-item tg-box--margin custom--select"
                v-if="flag"
              >
                <el-input
                  v-model="form.classroom_name"
                  readonly
                  placeholder="请选择班级"
                  @click.native="choose_class_visible = true"
                  @mouseenter.native="class_flag = true"
                  @mouseleave.native="class_flag = false"
                  :class="{ 'border--active': class_flag }"
                >
                  <img
                    :src="
                      !class_flag
                        ? require('@/assets/图片/icon_more.png')
                        : require('@/assets/图片/icon_more_ac.png')
                    "
                    slot="suffix"
                    alt=""
                    class="more"
                  />
                </el-input>
              </el-form-item>
              <el-form-item
                label="报名"
                class="tg-form-item tg-box--margin"
                v-if="flag"
              >
                <el-date-picker
                  style="width: 296px"
                  v-model="form.date"
                  type="daterange"
                  range-separator="至"
                  format="yyyy-MM-dd"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :pickerOptions="pickerOptions"
                  popper-class="tg-date-picker tg-date--range"
                >
                </el-date-picker>
              </el-form-item>
            </el-form>
          </div>
          <div class="tg-table__box">
            <div class="tg-box--border"></div>
            <el-table
              ref="table"
              :data="list"
              tooltip-effect="dark"
              class="tg-table"
              :height="flag ? 353 : 449"
              @selection-change="handleSelectionChange"
              :row-key="getRowKeys"
            >
              <el-table-column
                type="selection"
                width="50"
                :reserve-selection="true"
              ></el-table-column>
              <el-table-column
                label="学号"
                prop="student_base.student_number"
                width="130"
                sortable
              ></el-table-column>
              <el-table-column
                label="学员姓名"
                prop="student_base.student_name"
                width="100"
                sortable
              ></el-table-column>
              <el-table-column label="性别" width="86" sortable>
                <template slot-scope="scope">
                  <div>
                    {{ scope.row.student_base.gender }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="学员状态"
                prop="student_type_chn"
                width="110"
                sortable
              ></el-table-column>
              <el-table-column label="手机号" width="110" sortable>
                <template slot-scope="scope">
                  <span>{{
                    scope.row.student_base.student_mobile | formatPhoneNumber
                  }}</span>
                </template>
              </el-table-column>
              <!-- <el-table-column
                  label="剩余课(课时)"
                  width="100"
                  prop="course_hours"
                >
                </el-table-column> -->
              <el-table-column
                label="报名校区"
                prop="department_name"
                width="100"
                sortable
                show-overflow-tooltip
              ></el-table-column>
            </el-table>
            <div class="tg-pagination">
              <span class="el-pagination__total">共 {{ total }} 条</span>
              <el-pagination
                background
                layout="prev, pager, next,jumper"
                :total="total"
                :page-size="page_size"
                :current-page="page"
                @current-change="currentChange"
              >
              </el-pagination>
            </div>
          </div>
        </div>
        <div class="class-list--right">
          <div class="organization__title">
            <span
              >已选 学员<em>{{ right_stu_list.length }}</em
              >个</span
            >
            <span class="all-clear" @click="clear">
              <img src="@/assets/图片/icon_clear.png" alt="" />
              清空
            </span>
          </div>
          <div class="right-list-container">
            <div
              class="organization__info"
              v-for="(item, index) in right_stu_list"
              :key="index"
            >
              <span
                >{{ item.student_base.student_name }}-{{
                  item.student_base.student_number
                }}</span
              >
              <img
                src="@/assets/图片/icon_close_green.png"
                alt=""
                @click="delOne(index, item)"
              />
            </div>
            <span v-if="right_stu_list.length === 0" class="is-empty"
              >暂无数据</span
            >
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="tg-button--plain" type="plain" @click="back"
          >取消</el-button
        >
        <el-button class="tg-button--primary" type="primary" @click="really"
          >确定</el-button
        >
      </span>
    </el-dialog>
    <choose-course
      :check_id.sync="form.course_id"
      :check_name.sync="form.course_name"
      :check_arr.sync="course_check_arr"
      :choose_course_visible="choose_course_visible"
      v-if="choose_course_visible"
      :status="true"
      @close="choose_course_visible = false"
    ></choose-course>
    <choose-class
      :check_id.sync="form.classroom_id"
      :check_name.sync="form.classroom_name"
      :check_arr.sync="class_check_arr"
      :choose_class_visible="choose_class_visible"
      v-if="choose_class_visible"
      @close="choose_class_visible = false"
      :has_modal="false"
    ></choose-class>
    <!-- 重复数据弹出 -->
    <el-dialog
      title="提示"
      :visible.sync="dialogFinished"
      width="700px"
      class="rep-dialog"
    >
      <div v-if="repeatData.length" class="tip-box">
        <div class="text">
          <img src="@/assets/图片/icon_info.png" />
          以下学员是您已添加过的学员，您可以点击取消按钮重新选择，或者点击确定按钮系统将自动过滤重复的学员
        </div>
        <div class="box-info">
          <el-tag v-for="item in repeatData" :key="item.student_id" type="">{{
            item.student_base.student_number +
            "-" +
            item.student_base.student_name
          }}</el-tag>
        </div>
      </div>
      <div v-if="lackData.length" class="tip-box">
        <div class="text">
          <img src="@/assets/图片/icon_info.png" />
          以下学员剩余课时不足，您可以点击取消按钮重新选择，或者点击确定按钮系统将自动添加
        </div>
        <div class="box-info">
          <el-tag v-for="item in lackData" :key="item.student_number" type="">{{
            item.student_number + "-" + item.student_name
          }}</el-tag>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="tg-button--plain" @click="dialogFinished = false"
          >取消</el-button
        >
        <el-button class="tg-button--primary" type="primary" @click="addData"
          >确定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import timeFormat from "@/public/timeFormat";
import studentInforApi from "@/api/studentInfor";
import classManagementApi from "@/api/classManagement";
import ChooseCourse from "@/components/studentInfo/chooseCourse.vue";
import ChooseClass from "@/components/studentInfo/chooseClass.vue";
import schoolServiceSchedulingApi from "@/api/schoolServiceScheduling";
import { picker_options } from "@/public/datePickerOptions";

export default {
  data() {
    return {
      right_stu_list: [],
      flag: false,
      page: 1,
      page_size: 10,
      total: 0,
      list: [],
      form: {
        name: "",
        date: "",
        course_name: "",
        sign_up_begin_time: "",
        sign_up_over_time: "",
        classroom_name: "",
        student_type: [],
        department_id: "",
        department_name: "",
        classroom_id: "",
        course_id: ""
      },
      student_state_list: [],
      dialogFinished: false,
      repeatData: [],
      filterData: [],
      lackData: [],
      // school_tree_visible: false
      choose_class_visible: false,
      choose_course_visible: false,
      class_flag: false,
      course_flag: false,
      course_check_arr: [],
      class_check_arr: [],
      pickerOptions: picker_options
    };
  },
  props: {
    department_id: String,
    course_id: String,
    department_name: String,
    has_modal: {
      type: Boolean,
      default: true
    },
    type: String,
    dividStu: {
      type: Boolean,
      default: false
    },
    student_type: {
      default: () => [],
      type: Array
    }
  },
  components: {
    ChooseCourse,
    ChooseClass
  },
  mounted() {
    this.form.department_id = this.department_id;
    this.form.department_name = this.department_name;
    this.getStudentStateList();
    if (this.student_type.length > 0)
      this.$set(this.form, "student_type", this.student_type);
    this.getList({
      page: 1,
      page_size: 10,
      ...this.form
    });
  },
  watch: {
    "form.date": {
      handler(newObj) {
        if (newObj && newObj.length > 1) {
          this.form.sign_up_begin_time = this.moment(newObj[0]).format(
            "YYYY-MM-DD"
          );
          this.form.sign_up_over_time = this.moment(newObj[1]).format(
            "YYYY-MM-DD"
          );
        } else {
          this.form.sign_up_begin_time = "";
          this.form.sign_up_over_time = "";
        }
      },
      immediate: false
    }
  },
  methods: {
    getTime(time) {
      return timeFormat.GetTime(time);
    },
    handleClose() {
      this.clear();
      this.$emit("close");
    },
    delOne(index, item) {
      console.log(index, item);
      this.$nextTick(() => {
        this.$refs.table.toggleRowSelection(item);
      });
      this.right_stu_list.splice(index, 1);
    },
    clear() {
      this.right_stu_list = [];
      this.$refs.table.clearSelection();
    },
    back() {
      this.$emit("close");
    },
    async really() {
      //   this.findRepeatStu();
      if (this.repeatData.length || this.lackData.length) {
        this.dialogFinished = true;
      } else {
        if (this.type === "basic") {
          this.$emit("really", this.right_stu_list);
        } else {
          this.$parent.addToTemp(this.right_stu_list);
          this.$emit("close");
        }
        // this.$parent.tempTable.data = this.right_stu_list.concat(data);
      }
    },
    // 通过接口获取课时包不足的学员
    findClassPackageLack() {
      const student_discounts = this.right_stu_list.map((item) => {
        return {
          dynamic_discount: 1,
          student_id: item.student_id
        };
      });
      const { department_id } = this.form;
      const params = {
        course_id: this.course_id,
        department_id,
        student_discounts
      };
      return schoolServiceSchedulingApi.GetRelatedCourse(params);
    },
    findRepeatStu() {
      const { tempTable } =
        typeof this.$parent.tempTable === "undefined"
          ? this.$parent.$parent
          : this.$parent;
      const other = this.$lodash.cloneDeep(this.right_stu_list);
      const repeat = [];
      for (let j = 0; j < tempTable.data.length; j++) {
        for (let i = 0; i < other.length; i++) {
          const rs = other[i];
          const td = tempTable.data[j];
          if (td.student_id === rs.student_id) {
            repeat.push(rs);
            other.splice(i, 1);
            break;
          }
        }
      }
      // 重复的学员
      this.repeatData = repeat;
      // 去除重复学员的数组
      this.filterData = other;
    },

    currentChange(val) {
      this.page = val;
      this.getList({
        page: val,
        page_size: this.page_size,
        ...this.form
      });
    },
    getRowKeys(row) {
      return row.student_id;
    },
    handleSelectionChange(val) {
      this.right_stu_list = val;
      // let ids = this.right_stu_list.map((item) => item.id);
      // let arr = JSON.parse(JSON.stringify(this.right_stu_list));
      // let new_arr = val.map((item) => {
      //   if (ids.indexOf(item.id) != -1) {
      //     item = arr[ids.indexOf(item.id)];
      //   }
      //   return item;
      // });
      // this.right_stu_list = new_arr;
    },
    reset() {
      this.page = 1;
      this.form = {
        name: "",
        date: "",
        course_name: "",
        student_type: this.student_type,
        sign_up_begin_time: "",
        sign_up_over_time: "",
        classroom_name: "",
        department_id: this.department_id,
        department_name: this.department_name
      };
      this.searchVal();
    },
    searchVal() {
      if (this.form.student_type.length === 0)
        this.$set(this.form, "student_type", ["audition", "in_school", "temp"]);
      this.getList({ page: 1, page_size: this.page_size, ...this.form });
    },
    getClassInfo() {
      const { classroom_id } =
        typeof this.$parent.form1 === "undefined"
          ? this.$parent.$parent.form1
          : this.$parent.form1;
      return classManagementApi.GetSchoolServiceClassroomInfo({
        id: classroom_id
      });
    },
    async getList(d) {
      // 关闭筛选条件-在班 & 待入班
      // console.log(d);
      // const promise = await this.getClassInfo();
      // const { data, status } = promise;
      // if (status == 200) {
      // this.exclude_ids = data.student_ids;
      // d.exclude_ids = data.student_ids;
      if (this.dividStu) {
        d.out_school = true;
        d.drop_school = true;
      }
      studentInforApi.getNewStudentInforList(d).then((res) => {
        if (+res.data.code === 0) {
          this.total = res.data.data.count;
          this.list = res.data.data.results || [];
          this.is_loading = false;
        }
      });
      // }
    },
    getStudentStateList(data) {
      this.student_state_list = [];
      studentInforApi.getStudentState(data).then((res) => {
        for (const key in res.data) {
          if (this.student_type.length > 0 && !this.dividStu) {
            this.student_state_list.push({
              label: res.data[key],
              value: key,
              show: this.student_type.indexOf(key) > -1
            });
          } else {
            this.student_state_list.push({
              label: res.data[key],
              value: key,
              show: true
            });
          }
        }
        if (this.dividStu) {
          const arr = ["退学", "休学"];
          arr.forEach((item) => {
            const indexToDelete = this.student_state_list.findIndex(
              (obj) => obj.label === item
            );
            if (indexToDelete !== -1) {
              this.student_state_list.splice(indexToDelete, 1);
            }
          });
        }
      });
    },
    addData() {
      if (!(this.repeatData.length && this.lackData.length === 0)) {
        typeof this.$parent.addToTemp === "undefined"
          ? this.$parent.$parent.addToTemp(this.filterData)
          : this.$parent.addToTemp(this.filterData);
      }
      this.dialogFinished = false;
      this.$emit("close");
    }
  }
};
</script>
<style lang="less" scoped>
.choose-student {
  .search {
    width: 100%;
    // margin-bottom:16px;
    .search__input img {
      width: 14px;
      // height: 14px;
      margin-right: 12px;
      margin-left: 11px;
      margin-top: -4px;
      vertical-align: middle;
    }

    img.search__img {
      width: 8px;
      height: 12px;
      margin-right: 10px;
      cursor: pointer;
    }
    .searchBtn {
      display: inline-block;
      width: 100%;
      height: 100%;
      cursor: pointer;
      user-select: none;
    }
    ::v-deep .el-form-item__content,
    ::v-deep .el-form-item__label {
      line-height: 32px;
    }
    .search__input {
      width: 278px;
      ::v-deep .el-input__inner {
        padding-left: 40px;
      }
    }
    .el-button {
      width: 72px;
      img {
        margin-left: 0;
        margin-right: 7px;
      }
    }
    .search-teacher {
      ::v-deep .el-input {
        width: 160px;
        .el-input__inner {
          padding-left: 16px;
        }
      }
    }
    ::v-deep .el-form-item {
      margin-right: 10px;
    }
    ::v-deep .el-form-item:nth-child(2) {
      margin-right: 0;
    }
    ::v-deep .el-form-item:nth-child(3) {
      margin-right: 0;
    }
    ::v-deep .el-form-item.tg-btn--margin {
      margin-right: 10px;
    }
    ::v-deep .tg-form-item {
      margin-right: 20px;
      .el-input {
        width: 296px;
      }
    }
  }
  ::v-deep .el-dialog__body {
    padding: 0 16px 0 16px;
    overflow: auto;
  }
  .tg-dialog__content {
    display: flex;
    flex-direction: row;
    height: 622px;
  }
  .class-list {
    width: calc(100% - 257px);
    border-right: 1px solid #e0e6ed;
    padding-right: 16px;
    display: flex;
    flex-direction: column;
    .tg-table__box {
      margin-left: 0;
      margin-right: 16px;
      margin-top: 16px;
    }
    ::v-deep .el-table {
      padding: 0;
      th {
        background: #f5f8fc;
      }
      .el-table__header {
        padding: 0 16px;
        background: #f5f8fc;
        width: 100% !important;
      }
      .el-table__body {
        padding: 0 16px;
        width: 100% !important;
      }
    }
  }
  .class-list--right {
    width: 257px;
    padding-left: 16px;
    padding-top: 16px;
    // overflow-y: scroll;
    box-sizing: border-box;
    height: 100%;
    padding-bottom: 10px;
    display: flex;
    flex-direction: column;

    .organization__title,
    .organization__info {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }
    .all-clear {
      // color: #157df0;
      font-size: @text-size_small;
      font-family: @text-famliy_medium;
      cursor: pointer;
      img {
        width: 14px;
        height: 14px;
        margin-right: 8px;
        vertical-align: middle;
        margin-top: -3px;
      }
    }
    .right-list-container {
      width: 100%;
      flex: auto;
      overflow: auto;
    }
    .organization__title {
      em {
        font-style: normal;
        color: @base-color;
      }
    }
    .organization__info {
      border: 1px solid @base-color;
      border-radius: 4px;
      height: 40px;
      align-items: center;
      padding: 0 16px;
      margin-top: 16px;
      ::v-deep .el-input,
      ::v-deep .el-input__inner {
        height: 40px;
        line-height: 40px;
      }
      img {
        width: 16px;
        height: 16px;
        cursor: pointer;
      }
      span:nth-child(1) {
        overflow-x: scroll;
        width: calc(100% - 36px);
        white-space: nowrap;
      }
    }
    .required {
      &::before {
        content: "*";
        margin-right: 5px;
        color: #ff0317;
      }
    }
  }
  .is-empty {
    color: @text-color_third;
    width: 100%;
    display: inline-block;
    text-align: center;
    line-height: 350px;
  }
  ::v-deep .stu-school-tree {
    color: #000;
    .el-dialog {
      .el-form {
        padding-top: 20px;
        .el-input {
          width: 210px;
        }
        button {
          margin-left: 30px;
        }
      }
    }
  }
  .custom--select {
    ::v-deep .el-input .el-input__inner {
      cursor: pointer;
    }
    ::v-deep .el-input .el-input__suffix-inner {
      cursor: pointer;
    }
    img {
      width: 16px;
    }
    ::v-deep .el-input__suffix {
      top: -2px;
      right: 12px;
    }
  }
}

.rep-dialog {
  .tip-box {
    padding: 10px;
    background-color: #f5f8fc;
    .text {
      line-height: 24px;
      display: flex;
      align-items: center;
      img {
        width: 16px;
        margin-right: 10px;
      }
    }
    .box-info {
      padding: 20px 0px;
      padding-left: 26px;
      max-height: 400px;
      overflow: scroll;
      span {
        margin-right: 10px;
        margin-bottom: 10px;
      }
    }
  }
  /deep/ .el-dialog__body {
    border-bottom: 1px solid #f1f1f1;
    border-top: 1px solid #f1f1f1;
  }
}
</style>
