<template>
  <div class="time-album">
    <el-dialog
      :title="dialogType ? '查看' : '编辑'"
      :visible.sync="dialogVisible"
      width="800px"
      :before-close="handleClose"
    >
      <div class="timeline-container" ref="scrollContainer">
        <el-timeline
          v-infinite-scroll="load"
          :infinite-scroll-immediate="false"
          :infinite-scroll-disabled="disabled"
          :infinite-scroll-distance="20"
          :infinite-scroll-container="scrollRef"
          class="timeline-content"
        >
          <el-timeline-item
            v-for="(item, index) in currentRecord"
            :key="index"
            placement="top"
            v-loading="item.loading"
          >
            <el-card
              class="timeline-card"
              :class="{ editing: item.id === currentEditId }"
            >
              <div class="card-header">
                <span class="date" v-if="!item.edit">{{
                  item.event_time | formatDate
                }}</span>
                <el-date-picker
                  v-else
                  v-model="item.event_time"
                  type="date"
                  placeholder="选择日期"
                  value-format="yyyy-MM-dd"
                  :picker-options="pickerOptions"
                >
                </el-date-picker>
                <div class="but" v-if="item.amt !== 1 || is_admin">
                  <el-button
                    type="text"
                    class="edit-btn"
                    @click="handleEdit(item)"
                    v-if="isSelf(item) || is_admin"
                  >
                    <i
                      :class="
                        !item.edit ? 'el-icon-edit' : 'el-icon-circle-check'
                      "
                    ></i>
                  </el-button>
                  <el-button
                    type="text"
                    class="delete-btn"
                    @click="handleDelete(item)"
                    v-if="isDelete(item) || is_admin"
                  >
                    <i class="el-icon-delete"></i>
                  </el-button>
                </div>
              </div>
              <span class="title" v-if="!item.edit">{{
                item.album_title
              }}</span>
              <el-input
                class="title"
                v-else
                v-model="item.album_title"
                style="width: 200px"
                maxlength="10"
              ></el-input>
              <el-tag
                v-if="
                  !item.edit || albumTypeStatus(item.album_type).status === 1
                "
                size="medium"
                >{{
                  albumTypeList.find((tag) => tag.id === item.album_type)?.name
                }}</el-tag
              >
              <el-select
                v-model="item.album_type"
                placeholder="请选择标签"
                v-else
              >
                <el-option
                  v-for="tag in albumTypeList"
                  :key="tag.id"
                  :label="tag.name"
                  :value="tag.id"
                  :disabled="tag.status === 1"
                ></el-option>
              </el-select>
              <div
                class="content"
                v-if="!item.edit"
                v-html="parsedAlbumContent(item.album_values)"
              ></div>
              <el-input
                class="content"
                type="textarea"
                :maxlength="200"
                v-model="item.album_values"
                v-else
              ></el-input>
              <div class="media-preview">
                <div
                  v-for="(media, mediaIndex) in item.album_picture_video"
                  :key="mediaIndex"
                  class="preview-item"
                  @click="handlePreview(media)"
                >
                  <i
                    v-if="item.edit"
                    class="el-icon-close delete"
                    @click="handleMediaDelete(item, media)"
                  ></i>
                  <el-image
                    style="width: 100%; height: 100%"
                    v-if="!media.video_url"
                    :preview-src-list="[media.image_url]"
                    fit="fill"
                    :src="media.image_url"
                  />
                  <div v-else class="video-thumbnail" @click="openVideo(media)">
                    <el-image
                      style="width: 100%; height: 100%"
                      :src="
                        media.image_url ||
                        media.video_url +
                          '?x-oss-process=video/snapshot,t_1000,f_jpg,w_0,h_0,m_fast'
                      "
                      alt=""
                      fit="fill"
                    />
                    <div class="play-button">
                      <i class="el-icon-video-play"></i>
                    </div>
                  </div>
                </div>
                <div
                  class="upda"
                  v-if="item.album_picture_video?.length < 5 && item.edit"
                  @click="openUpload"
                >
                  <img src="@/assets/图片/upload.png" alt="" />
                </div>
              </div>
              <div class="operator" v-if="!item.edit">
                记录人：{{ item.final_employee_name }}
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
        <div class="timeline-loading" v-if="loading">
          <!-- <el-loading-spinner></el-loading-spinner> -->
          <span>加载中...</span>
        </div>
        <div class="timeline-no-more" v-if="noMore">没有更多了</div>
      </div>
      <div
        v-show="show_add_button"
        slot="footer"
        class="dialog-footer"
        v-if="$_has({ m: 'timeAlbum', o: 'create' })"
      >
        <div class="dialog-footer">
          <el-button type="primary" @click="handleAddNode">新增节点</el-button>
        </div>
      </div>

      <video-dialog
        :visible.sync="videoDialogVisible"
        :videoUrl="currentVideoUrl"
        :videPoster="currentVideoPoster"
        @handleClose="handleVideoDialogClose"
      />
      <uploadCom
        :visible.sync="uploadDialogVisible"
        @handleClose="handleVideoDialogClose"
        @handleUpload="handleUpload"
      />
    </el-dialog>
  </div>
</template>

<script>
import videoDialog from "./videoDialog.vue";
import uploadCom from "./uploadCom.vue";

export default {
  name: "LineAlbum",
  components: { videoDialog, uploadCom },
  data() {
    return {
      timelineData: [],
      videoDialogVisible: false,
      currentVideoUrl: "",
      currentVideoPoster: "",
      dialogType: true,
      uploadDialogVisible: false,
      linInfo: {},
      currentEditId: null,
      page: 1,
      scrollRef: null,
      isFirstLoad: true,
      pickerOptions: {
        disabledDate: (time) => {
          return time.getTime() > Date.now();
        }
      },
      tempItemData: null,
      is_admin: false,
      employee_id: ""
    };
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    currentRecord: {
      type: Array,
      default: () => []
    },
    currentRecordDetail: {
      type: Object,
      default: () => ({})
    },
    loading: {
      type: Boolean,
      default: false
    },
    noMore: {
      type: Boolean,
      default: false
    },
    albumTypeList: {
      type: Array,
      default: () => []
    },
    show_add_button: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      }
    },
    disabled() {
      return this.loading || this.noMore || this.isFirstLoad;
    },
    albumTypeStatus() {
      return (type) => {
        return this.albumTypeList.find((tag) => tag.id === type);
      };
    },
    all_permission() {
      return this.$store.getters.doneGetPermissionInfo;
    }
  },
  filters: {
    formatDate(value) {
      return value ? moment(value).format("YYYY-MM-DD") : "";
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.page = 1;
        this.isFirstLoad = true;
        this.$nextTick(() => {
          this.scrollRef = this.$refs.scrollContainer;
          this.$emit("load", this.currentRecordDetail, this.page);
          this.isFirstLoad = false;
        });
      }
    },
    currentRecord(val) {
      if (val && val?.length > 0) {
        this.isFirstLoad = false;
      }
    },
    all_permission: {
      handler(val) {
        this.is_admin = val != null && val.indexOf("is_admin") > -1;
      },
      immediate: true
    }
  },
  mounted() {
    const userInfo = localStorage.getItem("user_info");
    if (userInfo) {
      const { employee_id } = JSON.parse(userInfo);
      this.employee_id = employee_id;
    }
    this.$nextTick(() => {
      this.scrollRef = this.$refs.scrollContainer;
    });
  },
  methods: {
    parsedAlbumContent(data) {
      return data.replace(/\n/g, "<br>");
    },
    handleClose() {
      const hasEditingItem =
        this.currentRecord && this.currentRecord.some((record) => record.edit);

      if (hasEditingItem) {
        this.$confirm(
          "您有未保存的编辑内容，关闭后将丢失所有更改",
          "确认关闭",
          {
            confirmButtonText: "确认关闭",
            cancelButtonText: "取消",
            type: "warning"
          }
        )
          .then(() => {
            this.resetEditState();
            this.page = 1;
            this.isFirstLoad = true;
            this.dialogVisible = false;
          })
          .catch(() => {
            // 用户取消关闭，不做任何操作
          });
      } else {
        this.resetEditState();
        this.page = 1;
        this.isFirstLoad = true;
        this.dialogVisible = false;
      }
    },
    resetEditState() {
      this.currentEditId = null;
      this.tempItemData = null;

      if (this.currentRecord && this.currentRecord.forEach) {
        this.currentRecord.forEach((record) => {
          record.edit = false;
        });
      }
    },
    handleDelete(item) {
      this.$emit("handleDelete", item);
    },
    handleAddNode() {
      const hasEditingItem =
        this.currentRecord && this.currentRecord.some((record) => record.edit);

      if (hasEditingItem) {
        this.$confirm(
          "您有未保存的编辑内容，新增节点前需要放弃当前编辑",
          "确认操作",
          {
            confirmButtonText: "继续新增",
            cancelButtonText: "返回编辑",
            type: "warning",
            customClass: "custom-confirm-box"
          }
        )
          .then(() => {
            this.resetEditState();
            this.$emit("handleAdd", this.currentRecordDetail);
          })
          .catch(() => {
            // 用户取消，返回编辑状态，不做任何操作
          });
      } else {
        this.$emit("handleAdd", this.currentRecordDetail);
      }
    },
    handlePreview(media) {
      // 实现预览逻辑
    },
    openVideo(item) {
      this.videoDialogVisible = true;
      this.currentVideoUrl = item.video_url;
      this.currentVideoPoster = item.image_url;
    },
    handleVideoDialogClose() {
      this.videoDialogVisible = false;
    },
    load() {
      if (this.disabled) return;

      console.log("加载更多, 当前页码:", this.page);
      this.page++;
      this.$emit("load", this.currentRecordDetail, this.page);
    },
    handleEdit(item) {
      console.log(item);

      if (item.edit) {
        const validMedia = item.album_picture_video.filter(
          (items) => items.image_url || items.video_url
        );

        if (validMedia?.length === 0) {
          this.$message.error("请至少上传一张图片或一个视频");
          return false;
        }
        this.page = 1;
        this.$emit("handleEdit", item);
        this.currentEditId = null;
        this.tempItemData = null;
      } else {
        if (this.currentEditId !== null) {
          const currentEditingItem = this.currentRecord.find(
            (r) => r.id === this.currentEditId
          );

          if (currentEditingItem) {
            this.$confirm("您有未保存的编辑内容，是否放弃?", "提示", {
              confirmButtonText: "放弃编辑",
              cancelButtonText: "继续编辑",
              type: "warning"
            })
              .then(() => {
                if (this.tempItemData) {
                  const editingItem = this.currentRecord.find(
                    (r) => r.id === this.tempItemData.id
                  );
                  if (editingItem) {
                    Object.keys(this.tempItemData).forEach((key) => {
                      editingItem[key] = this.tempItemData[key];
                    });
                    editingItem.edit = false;
                  }
                }

                this.resetEditState();
                this.startEditing(item);
              })
              .catch(() => {
                // 用户选择继续编辑，不做任何操作
              });
          } else {
            this.resetEditState();
            this.startEditing(item);
          }
        } else {
          this.startEditing(item);
        }
      }
    },
    startEditing(item) {
      this.tempItemData = JSON.parse(JSON.stringify(item));

      this.currentRecord.forEach((record) => {
        record.edit = false;
      });

      item.edit = true;
      this.currentEditId = item.id;
      this.linInfo = item;
    },
    openUpload() {
      this.uploadDialogVisible = true;
    },
    handleMediaDelete(item, media) {
      item.album_picture_video = item.album_picture_video.filter(
        (item) => item.image_url !== media.image_url
      );
    },
    handleUpload(updateInfo) {
      console.log(updateInfo);
      this.linInfo.album_picture_video.push(updateInfo);
    },
    isSelf(item) {
      console.log(
        item.employee_id,
        this.employee_id,
        this.$_has({ m: "timeAlbum", o: "update" })
      );
      return item.employee_id === this.employee_id
        ? true
        : this.$_has({ m: "timeAlbum", o: "update" });
    },
    isDelete(item) {
      return item.employee_id === this.employee_id
        ? true
        : this.$_has({ m: "timeAlbum", o: "delete" });
    }
  }
};
</script>

<style lang="less" scoped>
.time-album {
  .timeline-container {
    padding: 20px;
    height: 500px;
    overflow-y: scroll;
    position: relative;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #909399;
      border-radius: 3px;
    }
  }

  .timeline-content {
    min-height: 100%;
  }

  .timeline-loading,
  .timeline-no-more {
    text-align: center;
    padding: 10px 0;
    color: #909399;
    font-size: 14px;
  }

  .timeline-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      .delete-btn {
        padding: 0;
        font-size: 16px;
        color: #f56c6c;
      }

      .edit-btn {
        padding: 0;
        font-size: 16px;
        color: #409eff;
      }
    }

    .title {
      font-weight: bold;
      font-size: 16px;
      margin-bottom: 15px;
      display: inline-block;
      margin-right: 10px;
    }

    .operator {
      font-size: 14px;
      color: #666;
      margin-top: 10px;
    }

    .content {
      margin-bottom: 15px;
      line-height: 1.5;
    }

    .media-preview {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;

      .preview-item {
        width: 120px;
        height: 120px;
        overflow: hidden;
        border-radius: 4px;
        cursor: pointer;
        position: relative;

        .delete {
          position: absolute;
          top: 0;
          right: 0;
          color: red;
          font-size: 16px;
          padding: 5px;
          cursor: pointer;
          z-index: 99999;
          margin: 10px;
        }

        .video-thumbnail {
          position: relative;
          width: 100%;
          height: 100%;

          .play-button {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 40px;
            height: 40px;
            background-color: rgba(0, 0, 0, 0.6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;

            i {
              color: #fff;
              font-size: 24px;
            }
          }
        }
      }
    }

    .upda {
      width: 120px;
      height: 120px;
      line-height: 120px;
      text-align: center;
      border: 1px dashed #ccc;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      transition: background-color 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 50px;
        height: 50px;
      }
    }

    &.editing {
      border: 1px solid #409eff;
      box-shadow: 0 0 8px rgba(64, 158, 255, 0.2);
    }
  }

  .dialog-footer {
    text-align: center;
  }
}

.custom-confirm-box {
  .el-message-box__message {
    word-break: break-word;
    white-space: normal;
    line-height: 1.5;
  }

  .el-message-box__title {
    word-break: break-word;
    font-size: 16px;
  }

  .el-message-box__content {
    padding: 15px 15px 10px;
  }

  @media (max-width: 768px) {
    width: 85% !important;
    max-width: 420px;

    .el-message-box__title {
      font-size: 15px;
    }

    .el-message-box__message {
      font-size: 14px;
    }
  }
}
</style>
