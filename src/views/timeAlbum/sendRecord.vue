<template>
  <div class="time-album-container">
    <!-- 搜索区域 -->
    <div class="tg-search__box">
      <tg-search
        :searchTitle.sync="searchTitle"
        :form.sync="searchParams"
        @reset="resetSearch"
        @search="searchRecords"
        :showNum="3"
        :hasDefaultDate="true"
        class="tg-box--margin"
        :isExport="isExport"
        @educe="exportExcel"
        :loadingState="exportLoading"
      ></tg-search>
    </div>
    <!-- 数据表格 -->
    <div class="tg-table__box course-statistics-table">
      <el-table
        :data="tableData"
        border
        ref="table"
        tooltip-effect="dark"
        style="width: 100%"
        v-loading="loading"
        class="tg-table"
      >
        <el-table-column prop="student_name" label="学生姓名" width="120">
          <template slot-scope="scope">
            <div class="copy_name">
              <el-button type="text" @click="handleEdit(scope.row)">{{
                scope.row.student_name
              }}</el-button>
              <div v-copy="scope.row.student_name"></div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="student_number"
          label="学号"
          width="120"
        ></el-table-column>
        <el-table-column
          prop="department_name"
          label="所属校区"
          width="180"
        ></el-table-column>
        <el-table-column prop="student_type" label="学员状态" width="180">
          <template slot-scope="scope">
            {{
              scope.row.student_type === "drop_school"
                ? "退学"
                : scope.row.student_type === "in_school"
                ? "在读"
                : "休学"
            }}
          </template>
        </el-table-column>
        <el-table-column
          prop="student_category_name"
          label="学员类别"
          width="150"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            {{ scope.row.student_category_name?.join(",") }}
          </template>
        </el-table-column>
        <el-table-column prop="education_name" label="教务" width="180">
          <template slot-scope="scope">
            {{ scope.row.education_name?.join(",") }}
          </template></el-table-column
        >
        <el-table-column prop="school_manager_name" label="学管师" width="180">
          <template slot-scope="scope">
            {{ scope.row.school_manager_name?.join(",") }}
          </template>
        </el-table-column>
        <el-table-column
          prop="event_time"
          label="节点时间"
          width="180"
        ></el-table-column>
        <el-table-column
          prop="album_type_chn"
          label="标签类型"
          width="180"
        ></el-table-column>
        <el-table-column
          prop="album_employee_name"
          label="发送人"
          width="180"
        ></el-table-column>
        <el-table-column prop="created_at" label="发送时间" width="180">
          <template slot-scope="scope">
            {{ scope.row.created_at | formatDate }}
          </template>
        </el-table-column>
        <el-table-column
          prop="album_updated_at"
          label="最后一次更新时间"
          width="180"
        >
          <template slot-scope="scope">
            {{ scope.row.album_updated_at | formatDate }}
          </template>
        </el-table-column>
        <el-table-column
          prop="final_employee_name"
          label="最后一次操作人"
          width="150"
        ></el-table-column>
        <el-table-column prop="deleted_at" label="消息状态" width="180">
          <template slot-scope="scope">
            {{ scope.row.deleted_at ? "已删除" : "未删除" }}
          </template>
        </el-table-column>
        <el-table-column prop="deleted_at" label="删除时间" width="180">
          <template slot-scope="scope">
            {{ scope.row.deleted_at | formatDate }}
          </template>
        </el-table-column>
        <el-table-column prop="final_employee_name" label="删除人" width="180">
          <template slot-scope="scope">
            {{ scope.row.deleted_at ? scope.row.final_employee_name : "" }}
          </template>
        </el-table-column>

        <el-table-column label="操作" fixed="right" width="220">
          <template slot-scope="scope">
            <el-button
              v-show="!scope.row.deleted_at"
              type="text"
              @click="handleEdit(scope.row)"
              v-if="$_has({ m: 'timeAlbum', o: 'update' })"
              >查看</el-button
            >
            <el-button
              v-show="!scope.row.deleted_at"
              type="text"
              @click="handleDelete(scope.row)"
              v-if="$_has({ m: 'timeAlbum', o: 'delete' })"
              >删除</el-button
            >
          </template>
        </el-table-column>
        <template slot="empty">
          <div style="margin-top: 15%">
            <TgLoading v-if="loading"></TgLoading>
            <div class="empty-container" v-else>暂无数据～</div>
          </div>
        </template>
      </el-table>

      <!-- 分页 -->
      <div class="tg-pagination">
        <span class="el-pagination__total">共 {{ pagination.total }} 条</span>
        <el-pagination
          background
          layout="sizes,prev,pager,next,jumper"
          :total="pagination.total"
          :page-size="pagination.page_size"
          :current-page="pagination.page"
          @current-change="currentChange"
          @size-change="sizeChange"
          :page-sizes="[10, 20, 50, 100]"
        ></el-pagination>
      </div>
    </div>

    <!-- 查看/编辑弹窗组件 -->
    <!-- <album-detail-dialog
      ref="detailDialog"
      :visible.sync="dialogVisible"
      :albumTypeList="albumTypeList"
      @submit="detailDialogSubmit"
      @refresh="getTableData"
      :department_id="schoolIds"
    ></album-detail-dialog> -->
    <!-- 新增/编辑弹窗组件 -->
    <line-album-dialog
      :visible.sync="dialogVisibleLine"
      :dialogType="dialogType"
      :currentRecord="currentRecord"
      @handleAdd="handleAdd"
      @handleDelete="handleDelete"
      @refresh="getTableData"
      @handleEdit="handleEditAlbum"
      @load="handleEdit"
      :currentRecordDetail="currentRecordDetail"
      :loading="loadingLine"
      :noMore="noMore"
      :albumTypeList="albumTypeList"
      :show_add_button="false"
    ></line-album-dialog>
    <!-- <choosStudent
      @close="add_student_visible = false"
      :department_id="schoolIds"
      :department_name="department_name"
      v-if="add_student_visible"
      :has_modal="false"
      type="basic"
      @really="addStudentList"
      :student_type="student_type"
    ></choosStudent> -->
  </div>
</template>

<script>
// import AlbumDetailDialog from "./components/AlbumDetailDialog.vue";
import studentInforApi from "@/api/studentInfor";
import { downLoadFile } from "@/public/downLoadFile";
import lineAlbumDialog from "./components/lineAlbum.vue";
import timeAlbumApi from "@/api/album";
import timeFormat from "@/public/timeFormat";
// import choosStudent from "./components/choosStudent.vue";
// import moment from "moment";
export default {
  name: "TimeAlbum",
  components: {
    // AlbumDetailDialog,
    lineAlbumDialog
  },
  data() {
    return {
      // 搜索相关
      searchTitle: [
        // 学员信息
        {
          label: "学员信息",
          props: "name",
          type: "input",
          placeholder: "请输入学员姓名/学号/手机号",
          show: true
        },
        // 学员类别
        {
          label: "学员类别",
          props: "student_category",
          type: "select",
          selectOptions: [
            // { name: "全部", id: undefined },
            // { name: "正式学员", id: "is_student" },
            // { name: "非正式", id: "not_student" }
          ],
          placeholder: "请选择学员类别",
          show: true
        },
        // 学员状态
        {
          label: "学员状态",
          props: "student_type",
          type: "select",
          selectOptions: [],
          placeholder: "请选择学员状态",
          show: true
        },
        {
          label: "标签类型",
          props: "album_type",
          type: "select",
          selectOptions: [],
          placeholder: "请选择标签类型",
          show: false
        },

        {
          props: "first_employee_id",
          label: "发送人",
          type: "course_staff",
          show: false,
          has_modal: false
        },
        {
          label: "发送时间",
          props: "album_time",
          type: "date",
          tooltip: true,
          sort: "custom",
          placeholder: "请选择更新时间",
          show: false
        },
        {
          label: "消息状态",
          props: "mes_status",
          type: "select",
          selectOptions: [
            { name: "全部", id: undefined },
            { name: "已删除", id: 2 },
            { name: "未删除", id: 1 }
          ],
          placeholder: "请选择学员状态",
          show: false
        }
      ],
      searchParams: {
        name: "",
        student_category: undefined,
        student_type: undefined,
        album_type: undefined,
        first_employee_id: "",
        album_time: null,
        mes_status: undefined
      },
      isExport: false,
      // 表格相关
      loading: false,
      tableData: [],

      // 分页相关
      pagination: {
        page: 1,
        page_size: 10,
        total: 0
      },

      // 弹窗相关
      dialogVisible: false,
      dialogType: "add", // add/edit
      currentRecord: [],
      dialogVisibleLine: false,
      albumTypeList: [],
      currentRecordAdd: null,
      currentRecordDetail: null,
      pageInfo: {
        page: 1,
        page_size: 10,
        total: 0
      },
      loadingLine: false,
      noMore: false,
      exportLoading: false,
      //   add_student_visible: false,
      student_type: ["drop_school", "in_school", "out_school"],
      department_name: [],
      student_list: [],
      department_id: "",
      employee_id: ""
    };
  },
  async mounted() {
    const userInfo = localStorage.getItem("user_info");
    if (userInfo) {
      const { employee_id } = JSON.parse(userInfo);
      this.employee_id = employee_id;
    }
    if (this.$_has({ m: "timeAlbum", o: "sendExport" })) {
      this.isExport = true;
    }
    await this.getInitialData();
    await this.getTableData();
  },
  computed: {
    schoolIds() {
      return this.$store.getters.doneGetSchoolId;
    }
  },
  filters: {
    formatDate(value) {
      return timeFormat.GetTime(value);
    }
  },
  watch: {
    schoolIds() {
      this.searchParams.page = 1;
      this.getTableData();
    }
  },
  methods: {
    getStudentStyleList() {
      studentInforApi.getStudentType().then((res) => {
        if (+res.status === 200 && +res.data.code === 0) {
          const obj = this.searchTitle.find(
            (item) => item.props === "student_category"
          );
          obj.selectOptions = [
            { id: "unspecified", name: "未分类" },
            ...res.data.data
          ];
          obj.selectOptions.unshift({ name: "不限", id: undefined });
        }
      });
    },
    async getStudentStateList() {
      const student_state_list = [];
      const { data } = await studentInforApi.getStudentState();
      for (const key in data) {
        const key_id = ["audition", "temp"];
        if (!key_id.includes(key)) {
          student_state_list.push({ name: data[key], id: key });
        }
      }
      const obj = this.searchTitle.find(
        (item) => item.props === "student_type"
      );
      obj.selectOptions = student_state_list;
      obj.selectOptions.unshift({ name: "不限", id: "" });
    },
    // 初始化数据
    getInitialData() {
      this.getStudentStyleList();
      this.getStudentStateList();
      this.getAlbumTypeList();
    },

    // 获取表格数据
    async getTableData() {
      this.loading = true;
      if (this.searchParams.album_time) {
        this.searchParams.album_time_start = this.searchParams.album_time[0];
        this.searchParams.album_time_end = this.searchParams.album_time[1];
      } else {
        this.searchParams.album_time_start = null;
        this.searchParams.album_time_end = null;
      }
      timeAlbumApi
        .getAlbumSendList({
          ...this.searchParams,
          department_id: this.schoolIds,
          ...this.pagination
        })
        .then((res) => {
          if (res.data.code === 0) {
            this.tableData = res.data.data.results;
            this.pagination.total = res.data.data.count;
          } else {
            this.$message.error(res.message);
          }
          this.loading = false;
        });
    },

    // 搜索相关
    searchRecords() {
      this.pagination.page = 1;
      this.getTableData();
    },

    resetSearch() {
      this.searchParams = {
        name: "",
        student_category: undefined,
        student_type: undefined,
        album_type: undefined,
        first_employee_id: "",
        album_time: null,
        mes_status: undefined
      };
      this.pagination.page = 1;
      this.getTableData();
    },

    // 分页相关
    currentChange(val) {
      this.pagination.page = val;
      this.getTableData();
    },
    sizeChange(val) {
      this.pagination.page = 1;
      this.pagination.page_size = val;
      this.resetSearch();
    },

    handleAdd(row) {
      this.currentRecordAdd = row;
      this.department_id = row.department_id;
      this.dialogVisible = true;
    },

    handleEdit(row, page = 1) {
      if (row.deleted_at) {
        this.$message.info("数据已删除，不支持查看！");
        return;
      }
      this.pageInfo.page = page;
      this.currentRecordDetail = row;
      this.loadingLine = true;
      timeAlbumApi
        .getAlbumDetail({
          id: row.id,
          department_id: row.department_id
        })
        .then((res) => {
          if (res.data.code === 0) {
            this.noMore = true;
            this.loadingLine = false;
            const data = res.data.data;
            data.edit = false;
            data.loading = false;
            this.currentRecord = [data];
            this.dialogType = "view";
            this.dialogVisibleLine = true;
          } else {
            this.$message.error(res.data.message);
          }
        });
    },

    handleDelete(row) {
      this.$confirm("确认删除该记录?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          timeAlbumApi
            .deleteAlbumDetail({
              id: row.id,
              create_employee_id: this.employee_id,
              department_id: [row.department_id]
            })
            .then((res) => {
              if (res.data.code === 0) {
                this.$message.success("删除成功");
                this.dialogVisibleLine = false;
                this.currentRecordDetail = {};
                this.currentRecord = [];
                this.getTableData();
              } else {
                this.$message.error(res.data.message);
              }
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },
    async exportExcel() {
      this.exportLoading = true;
      timeAlbumApi
        .getSendListExport({
          ...this.searchParams,
          department_id: this.schoolIds,
          ...this.pagination
        })
        .then((res) => {
          downLoadFile(res, "时光相册发送记录");
          this.exportLoading = false;
        });
    },
    // 弹窗相关
    // async detailDialogSubmit(form) {
    //   if (this.student_list.length) {
    //     if (!this.student_list || this.student_list.length === 0) {
    //       this.$message.warning("没有选择任何学生");
    //       this.student_list = [];
    //     }

    //     const results = {
    //       total: this.student_list.length,
    //       success: [],
    //       failed: []
    //     };

    //     try {
    //       // 遍历学生列表，为每个学生调用创建接口
    //       for (let i = 0; i < this.student_list.length; i++) {
    //         const student = this.student_list[i];

    //         try {
    //           console.log(
    //             `正在为学生 ${student.student_name}(${student.student_number}) 创建时光相册...`
    //           );

    //           // 调用创建时光相册的API
    //           const response = await timeAlbumApi.batchCreateAlbum({
    //             ...form,
    //             department_id: [student.department_id],
    //             student_id: student.student_id,
    //             student_number: student.student_base.student_number
    //           });

    //           if (+response.status === 200 && +response.data.code === 0) {
    //             results.success.push({
    //               student_name: student.student_base.student_name,
    //               student_number: student.student_base.student_number,
    //               student_id: student.student_id
    //             });
    //             console.log(`✓ 学生 ${student.student_name} 时光相册创建成功`);
    //           } else {
    //             results.failed.push({
    //               student_name: student.student_base.student_name,
    //               student_number: student.student_base.student_number,
    //               student_id: student.student_id,
    //               error: response.data.message || "创建失败"
    //             });
    //             console.error(
    //               `✗ 学生 ${student.student_name} 时光相册创建失败:`,
    //               response.data.message
    //             );
    //           }
    //         } catch (error) {
    //           results.failed.push({
    //             student_name: student.student_base.student_name,
    //             student_number: student.student_base.student_number,
    //             student_id: student.student_id,
    //             error: error.message || "网络错误"
    //           });
    //           console.error(
    //             `✗ 学生 ${student.student_name} 时光相册创建失败:`,
    //             error
    //           );
    //         }
    //       }
    //     } finally {
    //       // 处理完成，移除到下面的结果显示逻辑
    //     }

    //     // 显示最终结果
    //     const successCount = results.success.length;
    //     const failedCount = results.failed.length;

    //     console.log("批量添加结果:", results);

    //     // 构建结果信息
    //     let resultMessage = "";

    //     if (successCount > 0) {
    //       resultMessage += `成功创建的学生: ${successCount}位`;
    //     }

    //     if (failedCount > 0) {
    //       const failedNames = results.failed
    //         .map((item) => `${item.student_name}(${item.error})`)
    //         .join("、");
    //       if (successCount > 0) {
    //         resultMessage += "<br>"; // 使用HTML换行
    //       }
    //       resultMessage += `创建失败的学生: ${failedNames}`;
    //     }

    //     // 显示结果弹窗
    //     this.$alert(resultMessage, "批量创建结果", {
    //       confirmButtonText: "确定",
    //       showClose: false,
    //       dangerouslyUseHTMLString: true, // 启用HTML解析
    //       type:
    //         successCount === this.student_list.length
    //           ? "success"
    //           : failedCount === this.student_list.length
    //           ? "error"
    //           : "warning"
    //     })
    //       .then(() => {
    //         this.dialogVisible = false;
    //         this.getTableData();
    //       })
    //       .catch(() => {
    //         console.log("出错");
    //       });

    //     // 清空学生列表
    //     this.student_list = [];
    //     this.$refs.detailDialog.submitting = false;
    //   } else {
    //     timeAlbumApi
    //       .updateAlbumDetail({
    //         ...form,
    //         department_id: [this.currentRecordAdd.department_id],
    //         student_id: this.currentRecordAdd.student_id,
    //         student_number: this.currentRecordAdd.student_number
    //       })
    //       .then((res) => {
    //         if (+res.status === 200 && +res.data.code === 0) {
    //           this.$message.success("保存成功");
    //           this.dialogVisible = false;
    //           this.$refs.detailDialog.beforeClose();
    //           if (this.dialogVisibleLine) {
    //             this.handleEdit(this.currentRecordDetail);
    //           } else {
    //             this.getTableData();
    //           }
    //         } else {
    //           this.dialogVisible = false;
    //           this.$message.error(res.data.message);
    //         }
    //         this.$refs.detailDialog.submitting = false;
    //       })
    //       .catch(() => {
    //         this.$refs.detailDialog.submitting = false;
    //       });
    //   }
    // },
    // 获取时光相册类型
    getAlbumTypeList() {
      timeAlbumApi.getAlbumType().then((res) => {
        if (+res.status === 200 && +res.data.code === 0) {
          const obj = this.searchTitle.find(
            (item) => item.props === "album_type"
          );
          obj.selectOptions = [...res.data.data];
          obj.selectOptions.unshift({ name: "不限", id: undefined });
          this.albumTypeList = res.data.data;
        }
      });
    },
    handleEditAlbum(item) {
      item.loading = true;
      timeAlbumApi
        .updateAlbumDetailSave({
          ...item,
          department_id: [item.department_id],
          create_employee_id: item.employee_id
        })
        .then((res) => {
          if (+res.status === 200 && +res.data.code === 0) {
            this.$message.success("修改成功");
            // this.getTableData();
            this.handleEdit(this.currentRecordDetail);
            item.edit = false;
          } else {
            this.$message.error(res.data.message);
          }
          item.loading = false;
        })
        .catch(() => {
          item.loading = false;
        });
    }
    // onCreate() {
    //   this.add_student_visible = true;
    // },
    // addStudentList(list) {
    //   this.add_student_visible = false;
    //   this.student_list = list;
    //   this.dialogVisible = true;
    //   console.log(list);
    // }
  }
};
</script>

<style lang="less" scoped>
.time-album-container {
  margin-top: 16px;
}

::v-deep .tg-search {
  width: calc(100% - 12px);
  margin: 0 6px;

  div.tg-search__box:first-child .el-input {
    width: 280px;
  }
}

::v-deep .course-statistics-table {
  width: calc(100% - 12px);
  border: 1px solid @base-color;
  min-height: 100px;

  .el-table__footer td {
    border: none;
  }

  .el-table {
    padding: 0;
  }

  th {
    background: @light-color;
  }

  .el-table th:first-child > .cell {
    padding-left: 26px;
  }

  .el-table td:first-child > .cell {
    padding-left: 26px;
  }

  .el-table__footer-wrapper tbody td {
    background: @light-color;
  }

  .el-table__footer-wrapper {
    td {
      border-top: 1px solid @base-color;
    }
  }

  .el-table::before {
    background-color: @base-color;
  }
}

::v-deep .summary-table {
  .el-table::before {
    width: 0;
  }

  .el-table {
    border-radius: 4px;
  }
}

::v-deep .el-table__fixed-right {
  .el-table__fixed-header-wrapper,
  .el-table__fixed-body-wrapper {
    right: 0 !important;
  }
}

::v-deep .el-table__fixed-body-wrapper {
  right: 0 !important;
}
::v-deep .el-table__fixed-right th:last-child .cell {
  padding-left: 12px !important;
}

::v-deep button.tg-span__divide-line {
  &::after {
    top: 12px;
  }
}

::v-deep .el-table__body-wrapper {
  height: calc(100% - 46px);

  // height: 100% !important;
  table {
    // min-height: 100px;
  }

  .empty-container {
    position: absolute;
    top: 50%;
    left: 50%;
  }

  .loading-container {
    position: absolute;
    top: 15%;
    left: 1%;
    background: transparent;

    .box {
      height: 100%;
    }
  }
}
</style>
