<template>
  <div class="front">
    <div class="student-tab">
      <template v-for="(item, index) in menuList">
        <span
          :key="index"
          :class="{ 'student-tab--active': tab === index }"
          @click="changeTab(index)"
          v-has="{ m: 'timeAlbum', o: item.control }"
          >{{ item.name }}</span
        ></template
      >
    </div>
    <management
      v-show="tab === 0"
      v-if="$_has({ m: 'timeAlbum', o: 'list' })"
    ></management>
    <sendRecord
      v-show="tab === 1"
      v-if="$_has({ m: 'timeAlbum', o: 'sendList' })"
    ></sendRecord>
  </div>
</template>

<script>
import management from "./photoManagement.vue";
import sendRecord from "./sendRecord.vue";

export default {
  data() {
    return {
      menuList: [
        {
          name: "时光相册管理",
          control: "list"
        },
        {
          name: "时光相册发送记录",
          control: "sendList"
        }
      ],
      tab: 0
    };
  },
  components: {
    management,
    sendRecord
  },
  created() {
    for (let k = 0; k < this.menuList.length; k++) {
      if (this.$_has({ m: "timeAlbum", o: this.menuList[k].control })) {
        this.tab = k;
        break;
      }
    }
  },
  methods: {
    changeTab(i) {
      this.tab = i;
    }
  }
};
</script>

<style lang="less" scoped>
.front {
  height: 100%;

  .student-tab {
    width: 100%;
    height: 46px;
    background: #fff;
    border-radius: 4px;
    padding: 0 16px;
    // border-top: 1px solid #e0e6ed;
    margin-bottom: 16px;
    margin-left: -10px;
    box-shadow: 0 2px 6px 0 rgba(204, 208, 217, 0.35);
    span {
      font-family: @text-famliy_semibold;
      color: @text-color_second;
      font-size: @text-size_normal;
      display: inline-block;
      height: 44px;
      border-bottom: 2px solid transparent;
      line-height: 44px;
      cursor: pointer;
      font-weight: bold;
    }
    span + span {
      margin-left: 32px;
    }
    span.student-tab--active {
      color: #2d80ed;
      border-color: #2d80ed;
    }
  }
}
</style>
