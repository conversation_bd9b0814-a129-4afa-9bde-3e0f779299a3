<template>
  <div class="course-management-create">
    <el-dialog
      title="新增课程"
      :visible="true"
      width="1020px"
      class="custom_edit_dialogs"
      :show-close="true"
      :before-close="back"
    >
      <div class="dialogs-content">
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <div class="course-types module--margin">
            <el-form-item label="产品类型" required prop="type"> </el-form-item>
            <div class="radioGroup">
              <el-radio-group v-model="form.type">
                <el-radio
                  class="js_course-type radio--tick"
                  v-for="item in course_type_list"
                  :key="item.config_value"
                  :label="item.config_value"
                  >{{ item.config_name }}</el-radio
                >
              </el-radio-group>
              <span class="desc">创建后产品类型不支持修改</span>
            </div>
          </div>
          <!-- 课程基本信息 -->
          <base-info
            :type="form.type"
            :formData="form.base_info"
            class="module--margin"
          ></base-info>
          <!-- 课程定价 -->
          <price-info
            :type="form.type"
            :formData="form.price_info"
            :baseInfo="form.base_info"
            ref="priceInfo"
            class="module--margin"
          ></price-info>
          <!-- 绑定物品 -->
          <bind-goods
            :check_goods.sync="form.check_goods"
            class="module--margin"
          ></bind-goods>
          <!-- 绑定比赛 -->
          <bind-match
            :check_matches.sync="form.check_matches"
            class="module--margin"
          ></bind-match>
          <!-- 其他信息 -->
          <other-info
            :formData="form.other_info"
            class="module--margin"
          ></other-info>
          <!-- 排课规则 -->
          <arrange-rule
            :formData="form.arrange_rule"
            v-show="form.type == 2"
            :type="form.type"
            pageType="edit"
            class="module--margin"
          ></arrange-rule>
          <!-- 排课规则 -->
          <!-- <schedule-rule v-show="form.type == 2"></schedule-rule> -->
          <!-- 开班规则 -->
          <open-classroom-rule
            :formData="form.open_classroom_rule"
            v-show="form.type == 3"
            :type="form.type"
            pageType="edit"
            class="module--margin"
          ></open-classroom-rule>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="tg-button--plain" type="plain" @click="back"
          >返回</el-button
        >
        <el-button
          :loading="loading"
          v-throttle="submitData"
          class="tg-button--primary"
          type="primary"
          >保存</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import BaseInfo from "@/components/courseManagement/baseInfo.vue";
import BindGoods from "@/components/courseManagement/bindGoods.vue";
import BindMatch from "@/components/courseManagement/bindMatch.vue";
import OpenClassroomRule from "@/components/courseManagement/openClassroomRule.vue";
import PriceInfo from "@/components/courseManagement/priceInfo.vue";
// import ScheduleRule from "@/components/courseManagement/scheduleRule.vue";
import OtherInfo from "@/components/courseManagement/otherInfo.vue";
import ArrangeRule from "@/components/courseManagement/arrangeRule.vue";
import { validNum1, validNum2 } from "@/public/validate.js";
import { addCourse, getCourseConfigByType } from "@/api/courseManagement.js";
export default {
  components: {
    BaseInfo,
    BindGoods,
    BindMatch,
    OpenClassroomRule,
    PriceInfo,
    // ScheduleRule,
    OtherInfo,
    ArrangeRule
  },
  data() {
    return {
      loading: false,
      course_type_list: [],
      form: {
        type: "1",
        base_info: {
          name: "",
          type: "1",
          elimination: false,
          hours: "",
          attrs: {
            year: new Date().getFullYear(),
            type: "",
            rank: "",
            prop: ""
          },
          stu_sell_type: []
        },
        price_info: {
          standard_price: "",
          stage_hour: "",
          is_whole_buy: false,
          specs_actived: false,
          price_type: 2,
          amountTable: [],
          ladderTabel: [],
          relevanceTable: [],
          relatedTable: [],
          schoolsPriceTable: [],
          ladderPriceTable: []
        },
        other_info: {
          settlement: 1,
          refund: 1,
          desc: "",
          is_enabled: 1
        },
        arrange_rule: {
          classroom_name: "",
          lesson_duration: "",
          teacher_level: []
        },
        open_classroom_rule: {
          classroom_name: "",
          mini_student_numb: "",
          max_student_numb: "",
          lesson_duration: "",
          scheduling_frequency: "",
          frequency_type: "周",
          plan_lesson_numb: "",
          teacher_level: []
        },
        check_goods: [],
        check_matches: []
      },
      rules: {
        "base_info.hours": [
          { validator: this.checkBaseInfoHours, trigger: "blur" }
        ],
        "price_info.standard_price": [
          { validator: this.checkPriceInfoStandard, trigger: "blur" }
        ],
        "price_info.stage_hour": [
          { validator: this.checkPriceInfoStageHour, trigger: "blur" }
        ],
        "arrange_rule.lesson_duration": [
          { validator: this.checkArrangelessonDuration, trigger: "blur" }
        ],
        "open_classroom_rule.lesson_duration": [
          { validator: this.checkOpenlessonDuration, trigger: "blur" }
        ],
        "open_classroom_rule.mini_student_numb": [
          { validator: this.openRulesCheck, trigger: "blur" }
        ],
        "open_classroom_rule.max_student_numb": [
          { validator: this.openRulesCheck, trigger: "blur" }
        ],
        "open_classroom_rule.scheduling_frequency": [
          { validator: this.openRulesCheck, trigger: "blur" }
        ],
        "open_classroom_rule.plan_lesson_numb": [
          { validator: this.openRulesCheck, trigger: "blur" }
        ]
      }
    };
  },
  // provide() {
  //   return {
  //     course_type: this.form.type,
  //     course_prop: this.form.base_info.attrs.prop,
  //     standard_price: this.form.price_info.standard_price
  //   };
  // },
  created() {
    // 获取产品类型
    getCourseConfigByType({}, "course_type").then((res) => {
      if (res.data) {
        this.course_type_list = res.data;
      }
    });
  },
  mounted() {
    // this.$bus.on();
  },
  watch: {
    // "form.base_info.attrs": {
    //   handler(curr, old) {
    //     const { type, rank } = curr;
    //     this.form.arrange_rule.classroom_name = `${rank}${type}`;
    //     this.form.open_classroom_rule.classroom_name = `${rank}${type}`;
    //   },
    //   deep: true,
    //   immediate: true
    // }
    "form.base_info.name": {
      handler(curr) {
        this.form.arrange_rule.classroom_name = curr;
        this.form.open_classroom_rule.classroom_name = curr;
      },
      deep: true,
      immediate: true
    },
    "form.type": {
      handler() {
        this.form.base_info.attrs.type = "";
      },
      deep: true
    }
  },
  methods: {
    checkBaseInfoHours(rule, value, callback) {
      if (this.form.base_info.elimination) {
        if (!value) {
          return callback(new Error());
        }
        if (!Number.isFinite(+value)) {
          this.$message.error("动态课消课时请输入数字");
          callback(new Error());
        } else {
          if (value < 0) {
            this.$message.error("动态课消课时不能小于0");
            callback(new Error());
          } else if (value > 999) {
            this.$message.error("动态课消课时不能大于999");
            callback(new Error());
          } else {
            callback();
          }
        }
      } else {
        callback();
      }
    },
    checkPriceInfoStandard(rule, value, callback) {
      if (!value) {
        return callback(new Error());
      }
      if (!validNum2(value)) {
        this.$message.error(
          "标准单价格式不正确，格式不能为负数，小数点后最多为两位数字"
        );
        callback(new Error());
      } else {
        if (value >= 1000000) {
          this.$message.error("标准单价最大为6位数字");
          callback(new Error());
        } else {
          callback();
        }
      }
    },
    checkPriceInfoStageHour(rule, value, callback) {
      if (!value) {
        return callback(new Error());
      }
      if (!validNum2(value)) {
        this.$message.error(
          "1期课时数格式不正确，格式不能为负数，小数点后最多为两位数字"
        );
        callback(new Error());
      } else {
        if (value >= 1000) {
          this.$message.error("1期课时数最大为3位数字");
          callback(new Error());
        } else {
          callback();
        }
      }
    },
    checkArrangelessonDuration(rule, value, callback) {
      if (+this.form.type === 2) {
        if (!value) {
          return callback(new Error());
        }
        if (!validNum1(value)) {
          this.$message.error("排课规则单次课时长格式不正确,请输入大于0的数字");
          callback(new Error());
        } else {
          if (value >= 10000) {
            this.$message.error("单次课时长为4位数字");
            callback(new Error());
          } else {
            callback();
          }
        }
      } else {
        callback();
      }
    },
    checkOpenlessonDuration(rule, value, callback) {
      if (+this.form.type === 3) {
        if (!value) {
          return callback(new Error());
        }
        if (!validNum1(value)) {
          this.$message.error("开班规则单次课时长格式不正确,请输入大于0的数字");
          callback(new Error());
        } else {
          if (value >= 10000) {
            this.$message.error("单次课时长为4位数字");
            callback(new Error());
          } else {
            callback();
          }
        }
      } else {
        callback();
      }
    },
    openRulesCheck(rule, value, callback) {
      if (+this.form.type === 3) {
        // 开班输入相关规则
        const reg = /^\+?[1-9]\d*$/;
        if (value && !reg.test(value)) {
          this.$message.error("开班规则输入项格式有误，请输入大于0的数字");
          callback(new Error());
        } else {
          callback();
        }
      } else {
        callback();
      }
    },
    back() {
      this.$emit("close", false);
    },
    // 组装提交的form表单数据
    formatFormdata() {
      const obj = {};
      const {
        type,
        base_info,
        price_info,
        other_info,
        arrange_rule,
        open_classroom_rule,
        check_goods,
        check_matches
      } = this.form;
      // var abb;
      // if (base_info.ty_link_course && base_info.ty_link_course.length > 0) {
      //   base_info.ty_link_course = base_info.ty_link_course.map((t) => {
      //     return t.name;
      //   });
      // }
      // -------------------课程基本信息，其他信息
      const sales_to = this.$lodash.without(
        base_info.stu_sell_type,
        "customer"
      );
      obj.course_base_info = {
        ty_link_course: base_info.ty_link_course,
        ty_link_course_id: base_info.ty_link_course_id,
        course_name: base_info.name,
        course_type: +type,
        course_year: base_info.attrs.year.toString(),
        course_levels: +type === 1 ? null : base_info.attrs.rank,
        course_attribute: +type === 1 ? null : +base_info.attrs.prop,
        lesson_type: base_info.attrs.type,
        course_genre: +base_info.type,
        dynamic_discount: base_info.elimination ? base_info.hours : null,
        sales_to: base_info.stu_sell_type.length ? sales_to : null,
        allow_intention_sale: base_info.stu_sell_type.includes("customer")
          ? 1
          : 0,
        allow_visitor_sale: base_info.stu_sell_type.includes("visitor") ? 1 : 2,
        ...other_info
      };
      // ----------------课程定价信息
      const purchase_type = price_info.is_whole_buy ? 1 : price_info.price_type;
      obj.course_price_info = {
        standard_price: +price_info.standard_price,
        standard_numb: +price_info.stage_hour,
        purchase_type: +purchase_type
      };
      let checkedSchollArr = [];
      let price_table = "schoolsPriceTable";
      // +price_info.price_type === 2 ? "schoolsPriceTable" : "ladderPriceTable";
      if (+purchase_type === 3) {
        price_table = "ladderPriceTable";
      }
      checkedSchollArr = price_info[price_table].filter(
        (item) => item.checked && item.pid
      );

      const course_departments_standard = [];
      checkedSchollArr.map((item) => {
        course_departments_standard.push({
          department_id: item.id,
          department_price: +item.unit_price,
          department_attribute:
            +type === 1 ? null : +item.course_attr ? +item.course_attr : null,
          is_forced_cross_out: item.is_forced_cross_out
        });
      });
      console.log(checkedSchollArr);
      obj.course_price_info.course_departments_standard =
        course_departments_standard;

      const specList = this.$refs.priceInfo.spec; // 规格列表
      if (+purchase_type === 1) {
        // console.log(purchase_type);
      } else if (+purchase_type === 2) {
        // 按数量定价
        const course_count_prices = [];
        specList.map((item) => {
          const course_department_price = [];
          checkedSchollArr.map((item2) => {
            course_department_price.push({
              department_id: item2.id,
              department_price: +item2[`prop_${item.id}`].price
            });
          });
          // obj.course_department_price = course_department_price;
          course_count_prices.push({
            specification_id: item.id,
            price_count: +item.course_hour,
            price: +item.price,
            course_department_price
          });
        });
        obj.course_price_info.course_count_prices = course_count_prices;
      } else if (+purchase_type === 3) {
        // 按阶级定价
        const course_stair_prices = [];
        specList.map((item) => {
          const course_department_price = [];
          checkedSchollArr.map((item2) => {
            course_department_price.push({
              department_id: item2.id,
              department_price: +item2[`prop_${item.id}`].price
            });
          });
          // obj.course_department_price = course_department_price;
          course_stair_prices.push({
            specification_id: item.id,
            max_price_count: +item.max_hour,
            min_price_count: +item.min_hour,
            price: +item.price,
            course_department_price
          });
        });
        obj.course_price_info.course_stair_prices = course_stair_prices;
      }
      // ----------关联课程
      const { relevanceTable } = this.form.price_info;
      const course_linked_infos = [];
      relevanceTable.map((item) => {
        course_linked_infos.push({
          target_course_id: item.id,
          positive_ratio_source: "1",
          positive_ratio_target: item.discount,
          allow_negative_ratio: item.two_way ? 2 : 1
        });
      });
      obj.course_linked_infos = course_linked_infos;
      // ----------排课规则，开班规则
      if (+type === 2) {
        obj.course_rule_info = {
          classroom_name: arrange_rule.classroom_name,
          teacher_level: arrange_rule.teacher_level.join(),
          lesson_duration: +arrange_rule.lesson_duration
        };
      } else if (+type === 3) {
        obj.course_rule_info = {
          classroom_name: open_classroom_rule.classroom_name,
          teacher_level: open_classroom_rule.teacher_level.join(),
          mini_student_numb: +open_classroom_rule.mini_student_numb,
          max_student_numb: +open_classroom_rule.max_student_numb,
          lesson_duration: +open_classroom_rule.lesson_duration,
          scheduling_frequency: +open_classroom_rule.scheduling_frequency,
          frequency_type: open_classroom_rule.frequency_type,
          plan_lesson_numb: +open_classroom_rule.plan_lesson_numb
        };
      }
      // 绑定物品
      obj.course_article_info = {
        data: []
      };
      check_goods.map((item) => {
        obj.course_article_info.data.push({
          course_id: "",
          article_id: item.id, // 物品id
          is_forced_sell: item.is_forced_sell ? 1 : 2 // 是否强制售卖 1 强制 2 不强制
        });
      });
      // 绑定赛事
      obj.course_match_info = {
        data: []
      };
      check_matches.map((item) => {
        obj.course_match_info.data.push({
          course_id: "",
          match_id: item.id, // 物品id
          is_forced_sell: item.is_forced_sell ? 1 : 2 // 是否强制售卖 1 强制 2 不强制
        });
      });
      return obj;
    },
    submitData() {
      const data = this.formatFormdata();
      console.log(data);

      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true;
          addCourse(data)
            .then((res) => {
              if (res.data === "success") {
                this.$message.success("新增成功");
                this.$parent.reset();
                this.$emit("close", false);
              } else {
                this.$message.error("新增失败");
              }
              this.loading = false;
            })
            .catch(() => {
              this.loading = false;
            });
        } else {
          this.$message.error("必填项未录入或输入项格式有误");
        }
      });
    }
  }
};
</script>
<style lang="less">
.course-management-create {
  .section-title {
    padding: 0 0 0 15px;
    position: relative;
    font-size: 15px;
    &::before {
      content: "";
      position: absolute;
      background-color: @base-color;
      top: 8px;
      left: 3px;
      height: 6px;
      width: 6px;
      z-index: 1;
      border-radius: 100%;
    }
  }
  .desc {
    color: #b3b7c6;
    font-size: 12px;
    padding-left: 100px;
    background-image: url("~@/assets/图片/icon_info.png");
    background-size: 10px 10px;
    background-repeat: no-repeat;
    background-position: 82px center;
  }
  .section-content {
    padding: 0 12px;
  }
}
</style>

<style lang="less" scoped>
.dialogs-content {
  padding: 4px 16px 16px 16px;
  max-height: 560px;
  overflow-y: scroll;
}
.module--margin {
  margin-bottom: 32px;
}
.custom_edit_dialogs {
  /deep/ .el-dialog {
    height: 700px;
  }
  ::v-deep .el-dialog__body {
    padding: 0;
  }
}
.radioGroup {
  margin: 3px 0px 12px 15px;
}
</style>
