<template>
  <div class="container vessel">
    <tg-search
      :searchTitle.sync="searchTitle"
      :form.sync="search"
      @reset="reset"
      @search="searchStaff"
      :showNum="3"
      class="tg-box--margin"
    ></tg-search>
    <el-row class="tg-box--margin tg-shadow--margin tg-row--height">
      <el-button
        type="plain"
        class="tg-button--plain"
        @click="addCourse"
        v-has="{ m: 'course', o: 'create' }"
        >新增</el-button
      >
      <!-- <el-button type="plain" class="tg-button--plain" @click="del"
        >批量删除</el-button
      > -->
      <el-button
        type="plain"
        :disabled="stop_use"
        class="tg-button--plain"
        :class="{ disabled: stop_use }"
        @click="stopUsing"
        v-has="{ m: 'course', o: 'batch_disabled' }"
        >批量停用</el-button
      >
      <el-button
        type="plain"
        :disabled="start_use"
        :class="{ disabled: start_use }"
        class="tg-button--plain"
        @click="startUsing"
        v-has="{ m: 'course', o: 'batch_enabled' }"
        >批量启用</el-button
      >
    </el-row>
    <div class="tg-table__box">
      <div class="tg-box--border"></div>
      <el-table
        ref="table"
        :data="list"
        tooltip-effect="dark"
        class="tg-table"
        @selection-change="handleSelectionChange"
        :row-key="getRowKeys"
        :cell-style="{ borderRightColor: '#e0e6ed75' }"
        :header-cell-style="{ borderRightColor: '#e0e6ed75' }"
        border
      >
        <el-table-column
          type="selection"
          width="50"
          :reserve-selection="true"
        ></el-table-column>
        <el-table-column show-overflow-tooltip label="课程名称" width="200">
          <template slot-scope="scope">
            <div class="copy_name">
              <span
                style="color: #157df0; cursor: pointer"
                @click="toInfo(scope.row)"
                >{{ scope.row.course_name }}</span
              >
              <div v-copy="scope.row.course_name"></div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="课程编号"
          width="140"
          prop="course_no"
        ></el-table-column>
        <el-table-column label="年份" prop="course_year"></el-table-column>
        <el-table-column label="标准单价" prop="course_price"></el-table-column>
        <el-table-column label="单位" prop="unit">
          <template> 课时 </template>
        </el-table-column>

        <el-table-column label="产品类型" prop="course_type">
          <template slot-scope="scope">
            {{ scope.row.course_type | courseTypeFilter(course_type_list) }}
          </template>
        </el-table-column>
        <el-table-column label="课程规格/课程属性" width="120">
          <template slot-scope="scope">
            <img
              v-has="{ m: 'course', o: 'department_list' }"
              src="../../assets/图片/message.png"
              class="attribute"
              @click="goCourseAttribute(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="课程种类"
          prop="course_level_string"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column label="班型" prop="lesson_type"></el-table-column>
        <el-table-column label="课程类型" prop="course_genre">
          <template slot-scope="scope">
            {{ scope.row.course_genre | courseGenreFilter(course_genre_list) }}
          </template>
        </el-table-column>
        <el-table-column label="启用状态">
          <template slot-scope="scope">
            <div class="on_type">
              <div class="green_pie" v-if="scope.row.is_enabled == 1"></div>
              <div class="gray_pie" v-else></div>
              <span>{{ scope.row.is_enabled == 1 ? "已启用" : "已停用" }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column width="140" label="授权校区">
          <template slot-scope="scope">
            <span
              @click="goCourseAttribute(scope.row)"
              style="color: #157df0; cursor: pointer"
              >{{ scope.row.access_department_numb }}校区</span
            >
          </template>
        </el-table-column>
        <template slot="empty">
          <div style="margin-top: 15%">
            <TgLoading v-if="loading"></TgLoading>
            <div class="empty-container" v-else>暂无数据～</div>
          </div>
        </template>
      </el-table>
      <div class="tg-pagination">
        <span class="el-pagination__total">共 {{ total }} 条</span>
        <el-pagination
          background
          layout="sizes,prev,pager,next,jumper"
          :total="total"
          :page-size="pageSize"
          :current-page.sync="page"
          @size-change="sizeChange"
          @current-change="currentChange"
          :page-sizes="[10, 20, 50, 100]"
        >
        </el-pagination>
      </div>
    </div>

    <create @close="dialogCreate = false" v-if="dialogCreate"></create>
    <dialog-spec
      v-if="dialogSpecShow"
      @close="dialogSpecShow = false"
      :course_attribute="course_attribute_list"
      :attribute="dialogSpecAttr"
      :com_course_id="com_course_id"
    ></dialog-spec>
  </div>
</template>
<script>
import {
  getCourseList,
  getCourseConfigByType,
  batchEnable,
  batchDisable
} from "@/api/courseManagement.js";
import studentInforApi from "@/api/studentInfor";
import tgSearch from "@/components/search/search.vue";
import Create from "./create.vue";
import DialogSpec from "@/components/courseManagement/dialogSpec.vue";
import { course_years } from "@/public/dict.js";

export default {
  name: "courseManagement",
  components: {
    tgSearch,
    Create,
    DialogSpec
  },
  data() {
    return {
      pageSize: 10,
      dialogCreate: false,
      dialogSpecShow: false,
      dialogSpecAttr: "",
      course_attribute_list: [],
      course_genre_list: [],
      course_type_list: [],
      com_course_id: "",
      searchTitle: [
        {
          props: "course_name",
          label: "课程名称",
          type: "input",
          width: 400,
          show: true,
          placeholder: "请输入课程名称"
        },
        {
          props: "course_no",
          label: "课程编号",
          type: "input",
          width: 400,
          show: true,
          placeholder: "请输入课程编号"
        },
        {
          props: "course_year",
          label: "年份",
          type: "select",
          width: 400,
          show: true,
          placeholder: "请选择",
          selectOptions: []
        },
        {
          props: "course_type",
          label: "产品类型",
          type: "select",
          width: 400,
          show: false,
          placeholder: "请选择",
          selectOptions: []
        },
        {
          props: "course_level",
          label: "课程种类",
          type: "select",
          width: 400,
          show: false,
          placeholder: "请选择",
          selectOptions: []
        },
        {
          props: "lesson_type",
          label: "班型",
          type: "select",
          width: 400,
          show: false,
          placeholder: "请选择",
          selectOptions: []
        },
        {
          props: "sales_to",
          label: "售卖对象",
          type: "select",
          width: 400,
          show: false,
          placeholder: "请选择",
          selectOptions: []
        },
        {
          props: "is_enabled",
          label: "启用状态",
          type: "select",
          width: 400,
          show: false,
          placeholder: "请选择",
          selectOptions: [
            {
              id: 0,
              name: "不限"
            },
            {
              id: 1,
              name: "已启用"
            },
            {
              id: 2,
              name: "已停用"
            }
          ]
        },
        {
          props: "justDepartment",
          label: "",
          type: "bool",
          show: false,
          content: "只查看授权给当前校区的课程"
        }
      ],
      search: {
        course_name: "",
        course_no: "",
        course_year: "",
        course_level: "",
        lesson_type: "",
        is_enabled: 0,
        department_id: "",
        sales_to: "",
        course_type: undefined,
        course_class_type: undefined,
        justDepartment: false
      },
      height: window.innerHeight - 370,
      loading: false,
      list: [],
      page: 1,
      total: 0,
      selectionsIds: [],
      stop_use: false,
      start_use: false
    };
  },
  beforeRouteEnter(to, from, next) {
    if (from.name === "courseManagementUpdate") {
      next((vm) => {
        vm.getList();
      });
    } else {
      // 不是从详情进入的到该页面的路由，需要重置页面
      next((vm) => {
        vm.reset();
      });
    }
  },
  computed: {
    school_id() {
      return this.$store.getters.doneGetSchoolId;
    }
  },

  created() {
    this.getSelect("course_level");
    this.getSelect("course_class_type");
    this.getSelect("course_attribute");
    this.getSelect("course_type");
    this.getSelect("course_genre");
    this.getStudentStateList();
  },

  filters: {
    courseTypeFilter: function (value, data) {
      const arr = data.filter((item) => +item.id === +value);
      return arr.length ? arr[0].name : "";
    },
    courseGenreFilter: function (value, data) {
      const arr = data.filter((item) => +item.config_value === +value);
      return arr.length ? arr[0].config_name : "";
    }
  },
  mounted() {
    const years = [
      {
        id: "",
        name: "不限"
      }
    ];
    course_years.map((item) => {
      years.push({
        id: item,
        name: item
      });
    });
    this.searchTitle[2].selectOptions = years;
    // this.getList();
    this.clearSelection();
  },
  methods: {
    async getSelect(str) {
      const { data } = await getCourseConfigByType({}, str);
      if (data) {
        const arr = [];
        data.map((item) => {
          arr.push({
            id: str === "course_type" ? item.config_value : item.config_name,
            name: item.config_name
          });
        });
        if (str === "course_type") {
          this.searchTitle[3].selectOptions = arr;
          this.searchTitle[3].selectOptions.unshift({
            name: "不限",
            id: undefined
          });
          this.course_type_list = arr;
        }
        if (str === "course_level") {
          this.searchTitle[4].selectOptions = arr;
          this.searchTitle[4].selectOptions.unshift({ name: "不限", id: "" });
        }
        if (str === "course_class_type") {
          this.searchTitle[5].selectOptions = arr;
          this.searchTitle[5].selectOptions.unshift({
            name: "不限",
            id: ""
          });
        }
        if (str === "course_attribute") {
          this.course_attribute_list = data;
        }
        if (str === "course_genre") {
          this.course_genre_list = data;
        }
      }
    },
    getStudentStateList(data) {
      const arr = [];
      studentInforApi.getStudentState(data).then((res) => {
        for (const key in res.data) {
          arr.push({ name: res.data[key], id: key });
        }
        arr.push({ name: "游客", id: "visitor" });
        arr.push({ name: "意向客户", id: "intention" });
        this.searchTitle[6].selectOptions = arr;
        this.searchTitle[6].selectOptions.unshift({ name: "不限", id: "" });
      });
    },
    getList() {
      const { justDepartment, sales_to } = this.search;
      this.loading = true;
      this.list = [];
      getCourseList({
        page_num: this.page,
        page_size: this.pageSize,
        just_department: justDepartment ? 1 : 0,

        // just_department: 1,
        ...this.search,
        allow_visitor_sale: sales_to === "visitor" ? 1 : 2,
        allow_intention_sale: sales_to === "intention" ? 1 : 2,
        department_id: justDepartment ? this.school_id : undefined
      }).then((res) => {
        if (res.data) {
          this.list = res.data.data || [];
          this.total = res.data.total;
        }
        this.loading = false;
      });
    },
    // 分页
    currentChange(val) {
      this.page = val;
      this.getList();
    },
    sizeChange(val) {
      this.page = 1;
      this.pageSize = val;
      this.getList();
    },
    handleSelectionChange(list) {
      // this.selections = list;
      this.selectionsIds = list.map((item) => item.id);
      this.start_use = list.filter((item) => item.is_enabled === 1).length;
      this.stop_use = list.filter((item) => item.is_enabled === 2).length;
      console.log(this.selectionsIds);
    },
    reset() {
      this.search = {
        course_name: "",
        course_no: "",
        course_year: "",
        course_level: "",
        lesson_type: "",
        is_enabled: 0,
        sales_to: "",
        allow_visitor_sale: 0,
        allow_intention_sale: 0,
        justDepartment: false
      };
      this.page = 1;
      this.pageSize = 10;
      this.clearSelection();
      this.getList();
    },
    searchStaff() {
      this.page = 1;
      this.clearSelection();
      this.getList();
    },
    clearSelection() {
      this.$nextTick(() => {
        this.$refs.table.clearSelection();
      });
    },
    addCourse() {
      this.dialogCreate = true;
    },
    stopUsing() {
      if (this.selectionsIds.length === 0) return false;
      this.$confirm("是否确认停用选中的课程?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        batchDisable({
          ids: this.selectionsIds
        }).then((res) => {
          if (res.data === "success") {
            this.clearSelection();
            this.$message.success("批量停用成功");
            this.getList();
          } else {
            this.$message.error("批量停用失败");
          }
        });
      });
    },
    startUsing() {
      if (this.selectionsIds.length === 0) return false;
      this.$confirm("是否确认启用选中的课程?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        batchEnable({
          ids: this.selectionsIds
        }).then((res) => {
          if (res.data === "success") {
            this.clearSelection();
            this.$message.success("批量启用成功");
            this.getList();
          } else {
            this.$message.error("批量启用失败");
          }
        });
      });
    },
    del() {},
    getRowKeys(row) {
      return row.id;
    },
    goCourseAttribute(row) {
      if (this.$_has({ m: "course", o: "department_list" })) {
        this.dialogSpecAttr = row.course_attribute;
        this.com_course_id = row.id;
        this.dialogSpecShow = true;
      }
    },
    toInfo(row) {
      if (this.$_has({ m: "course", o: "detail" })) {
        this.$router.push({
          path: `/courseManagementUpdate`,
          query: { id: row.id }
        });
      }
    }
  }
};
</script>
<style lang="less" scoped>
/deep/ .el-message-box__message {
  padding: 40px;
}
/deep/ .el-message-box {
  height: 100px;
  width: 400px !important;
}
.vessel {
  ::v-deep .el-table td:last-child {
    border-right-color: transparent !important;
  }
  ::v-deep .el-table__header th:last-child {
    border-right-color: transparent !important;
  }
}
.attribute {
  width: 29px;
  height: 23px;
  cursor: pointer;
  vertical-align: middle;
}
.tg-row--height {
  width: 100%;
}
.on_type {
  display: flex;
  align-items: center;
  .green_pie {
    width: 6px;
    height: 6px;
    background: #2d80ed;
    border-radius: 3px;
    margin-right: 7px;
  }
  .gray_pie {
    width: 6px;
    height: 6px;
    background: #96a7bd;
    border-radius: 3px;
    margin-right: 7px;
  }
}

/deep/ button.disabled {
  background-color: #ccc;
  border: 1px solid #ccc;
  color: #fff;
}
::v-deep .el-table__body-wrapper {
  height: calc(100% - 46px);
  .empty-container {
    position: absolute;
    top: 50%;
    left: 50%;
  }
  .loading-container {
    position: absolute;
    top: 30%;
    left: 1%;
    background: transparent;
    .box {
      height: 100%;
    }
  }
}
</style>
