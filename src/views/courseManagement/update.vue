<template>
  <div class="course-update tg-box--margin">
    <div class="tg-header__subtitle tg-info__back">
      <img
        src="../../assets/图片/icon_menu_down_ac.png"
        alt
        @click="$router.go(-1)"
      />
      <span @click="$router.go(-1)">返回</span>
    </div>
    <div class="tg-info__content tg-box--margin tg-table__box">
      <div class="course-info__title">
        <div class="course-info__title--left">
          <img alt src="../../assets/图片/course_infor.png" />
          <span class="course-info__label" v-if="!info_can_update">
            {{ info.course_name }}
          </span>
          <el-input v-model="info.course_name" v-if="info_can_update">
            {{ info.course_name }}
          </el-input>
          <span class="tg-tips__box">
            {{ info.course_type | getLabel(course_type) }}
          </span>
          <span class="tg-tips__box">{{ info.is_enabled | getEnabled }}</span>
        </div>
        <div>
          <el-button
            type="primary"
            class="tg-button--plain"
            @click="openInfoUpdate()"
            v-if="!info_can_update"
            v-has="{ m: 'course', o: 'update_basic' }"
            >修改</el-button
          >
          <el-button
            type="primary"
            class="tg-button--plain"
            v-if="info_can_update"
            @click="saveInfo"
            >确认修改</el-button
          >
          <el-button
            type="primary"
            class="tg-button--plain"
            v-if="info_can_update"
            @click="cancelInfo"
            >取消</el-button
          >
        </div>
      </div>
      <div class="course-info__list" :class="isSpeard ? 'down' : 'up'">
        <div class="info tg-box--margin">
          <div class="info__content">
            <div class="info-box__border">
              <span class="special-span--top">年份</span>
              <span v-if="!info_can_update">{{ info.course_year }}</span>
              <el-select class="is-edit" v-model="info.course_year" v-else>
                <el-option
                  :label="item"
                  :value="item"
                  v-for="(item, index) in course_year"
                  :key="index"
                ></el-option>
              </el-select>
            </div>
            <div class="info-box__border" v-if="info.course_type !== 1">
              <span>课程种类</span>
              <span v-if="!info_can_update">
                {{ info.course_level_string }}
              </span>
              <el-select
                class="is-edit"
                multiple
                v-else
                v-model="info.course_levels"
              >
                <el-option
                  :label="item.config_name"
                  :value="item.config_name"
                  v-for="(item, index) in course_level"
                  :key="index"
                ></el-option>
              </el-select>
            </div>
            <div class="info-box__border" v-if="info.course_type !== 1">
              <span>课程属性</span>
              <span v-if="!info_can_update">
                {{ info.course_attribute | getLabel(course_attribute) }}
              </span>
              <el-select class="is-edit" v-model="info.course_attribute" v-else>
                <el-option
                  :label="item.config_name"
                  :value="Number(item.config_value)"
                  v-for="(item, index) in course_attribute"
                  :key="index"
                ></el-option>
              </el-select>
            </div>
            <div class="info-box__border">
              <span>{{
                info.course_type === 3
                  ? "班型"
                  : info.course_type === 1
                  ? "课时包"
                  : ""
              }}</span>
              <span v-if="!info_can_update">{{ info.lesson_type }}</span>
              <el-select class="is-edit" v-model="info.lesson_type" v-else>
                <el-option
                  v-show="info.course_type === 1"
                  :label="item.config_name"
                  :value="item.config_name"
                  v-for="(item, index) in course_package_class_type"
                  :key="index + 'course_package_class_type'"
                ></el-option>
                <el-option
                  v-show="info.course_type === 2 || info.course_type === 3"
                  :label="item.config_name"
                  :value="item.config_name"
                  v-for="(item, index) in course_class_type"
                  :key="index + 'course_class_type'"
                ></el-option>
              </el-select>
            </div>
            <div
              v-if="info.course_type == 2 || info.course_type == 3"
              class="info-box__border"
            >
              <span>课程类型</span>
              <span v-if="!info_can_update">
                {{ info.course_genre | getLabel(course_genre) }}
              </span>
              <el-select
                class="is-edit"
                disabled
                v-model="info.course_genre"
                v-else
              >
                <el-option
                  :label="item.config_name"
                  :value="Number(item.config_value)"
                  v-for="(item, index) in course_genre"
                  :key="index"
                ></el-option>
              </el-select>
            </div>
            <div
              class="info-box__border"
              v-if="
                info.course_type == 1 ||
                info.course_attribute == 2 ||
                info.course_attribute == 3
              "
            >
              <span>售卖对象</span>
              <span v-if="!info_can_update">
                {{ info.sales_to | getStuState(student_state_list) }}
              </span>
              <el-select
                class="is-edit"
                v-model="info.sales_to"
                multiple
                v-else
              >
                <el-option
                  :label="item.label"
                  :value="item.value"
                  v-for="(item, index) in student_state_list"
                  :key="index"
                  :disabled="['out_school', 'drop_school'].includes(item.value)"
                ></el-option>
              </el-select>
            </div>
            <div class="info-box__border info-box__border--special">
              <span>计费形式</span>
              <span v-if="!info_can_update">按课时</span>
              <div class="is-edit" v-else>
                <el-input value="按课时" :disabled="true" />
              </div>
            </div>
            <div
              class="info-box__border info-box--special info-box__border--special"
            >
              <span>动态课消</span>
              <span v-if="!info_can_update">
                {{
                  info.dynamic_discount === ""
                    ? ""
                    : `每排一次课，计${info.dynamic_discount}课时`
                }}
              </span>
              <div class="is-edit" v-else>
                <el-switch v-model="is_dynamic_discount"></el-switch>
                <span v-if="is_dynamic_discount">每排一次课，计</span>
                <el-input-number
                  :controls="false"
                  class="tg-input__number"
                  v-model="info.dynamic_discount"
                  v-if="is_dynamic_discount"
                  :precision="2"
                  :max="9.99"
                  :min="1"
                />
                <span v-if="is_dynamic_discount">课时</span>
              </div>
            </div>
            <div class="info-box__border info-box__border--special">
              <span>创建时间</span>
              <span v-if="!info_can_update">
                {{ info.created_at | getTime }}
              </span>
              <div class="is-edit" v-else>
                <el-input :disabled="true" :value="info.created_at | getTime" />
              </div>
            </div>
            <div
              class="info-box__border info-box--special info-box__border--special"
            >
              <span>天弈课程名称</span>
              <span v-if="!info_can_update">
                <el-tooltip
                  :content="ty_link_course"
                  placement="bottom"
                  effect="light"
                  popper-class="tg-tooltip"
                >
                  <div class="tg_ellipsis">{{ ty_link_course }}</div>
                </el-tooltip>
              </span>
              <div class="is-edit" v-else>
                <!-- <el-input
                  v-model="ty_link_course"
                /> -->
                <div class="ty_link_ul">
                  <div
                    class="font_ty"
                    v-for="(item, index) in info.ty_link_course"
                    :key="index"
                  >
                    {{ item }}
                    <i
                      class="el-icon-circle-close"
                      @click="delTYLinkCourse(index)"
                    ></i>
                  </div>
                  <el-button
                    type="text"
                    @click="
                      tyLinkShow = true;
                      ty_value = '';
                    "
                    >添加</el-button
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="course-info__list--hidden"
          :class="isSpeard ? 'down' : 'up'"
        >
          <!-- <div class="info__line tg-box--margin" v-if="isSpeard">
            <span class="info__subtitle">定价信息</span>
            <div class="info tg-box--margin">
              <div class="info__content">
                <div class="info__content--special">
                  <span>标准单价</span>
                  <span>r4</span>
                  <span class="info__label">r4</span>
                </div>
                <div>
                  <span>只能整期购买</span>
                  <span>90</span>
                </div>
                <div>
                  <span>课程规格</span>
                  <span class="tg-text--blue">查看</span>
                </div>
              </div>
            </div>
          </div>-->
          <div class="info__line tg-box--margin" v-if="isSpeard">
            <span class="info__subtitle">其他信息</span>
            <div class="info tg-box--margin">
              <div class="info__content other-info">
                <div class="info-box__border--right">
                  <span>结转</span>
                  <span v-if="!info_can_update">
                    {{ info.settlement | getSelect }}
                  </span>
                  <el-switch
                    :inactive-value="0"
                    :active-value="1"
                    class="is-edit--margin"
                    v-model="info.settlement"
                    v-else
                  ></el-switch>
                </div>
                <div class="info-box__border--right">
                  <span>退费</span>
                  <span v-if="!info_can_update">
                    {{ info.refund | getSelect }}
                  </span>
                  <el-switch
                    :inactive-value="0"
                    :active-value="1"
                    class="is-edit--margin"
                    v-model="info.refund"
                    v-else
                  ></el-switch>
                </div>
                <div>
                  <span>描述</span>
                  <span v-if="!info_can_update">{{ info.desc }}</span>
                  <el-input
                    class="is-edit is-edit--special"
                    v-model="info.desc"
                    v-else
                  />
                </div>
              </div>
            </div>
          </div>
          <div
            class="search-speard tg-box--margin"
            @click="show(false)"
            @mouseenter="hoverFlag = true"
            @mouseleave="hoverFlag = false"
            v-if="isSpeard"
          >
            <div class="search-speard__btn">
              <span>收起</span>
              <img
                :src="
                  hoverFlag
                    ? require('../../assets/图片/icon_top_ac.png')
                    : require('../../assets/图片/icon_top.png')
                "
                alt
              />
            </div>
          </div>
        </div>
        <div
          class="search-speard tg-box--margin"
          @click="show(true)"
          @mouseenter="hoverFlag = true"
          @mouseleave="hoverFlag = false"
          v-if="!isSpeard"
        >
          <div class="search-speard__btn">
            <span>展开</span>
            <img
              :src="
                hoverFlag
                  ? require('../../assets/图片/icon_down_ac.png')
                  : require('../../assets/图片/icon_down.png')
              "
              alt
            />
          </div>
        </div>
      </div>
    </div>
    <el-tabs
      v-model="tab"
      class="student-tab course-tab--margin"
      @tab-click="handleClick"
    >
      <el-tab-pane label="课程规格" name="first">
        <price-info
          v-if="tab === 'first'"
          :height="height"
          ref="price_info"
        ></price-info>
      </el-tab-pane>
      <el-tab-pane label="授权校区" name="second">
        <authorized-school
          v-if="tab === 'second'"
          :course_attribute="course_attribute"
          :attribute="info.course_attribute"
          :course_type="info.course_type"
        ></authorized-school>
      </el-tab-pane>
      <el-tab-pane
        v-if="$_has({ m: 'course', o: 'class_progress' })"
        label="上课进度"
        name="third"
      >
        <class-progress v-if="tab === 'third'"></class-progress>
      </el-tab-pane>
      <el-tab-pane label="绑定物品" name="fourth">
        <bind-goods v-if="tab === 'fourth'"></bind-goods>
      </el-tab-pane>
      <el-tab-pane label="绑定赛事" name="fifth">
        <bind-match v-if="tab === 'fifth'"></bind-match>
      </el-tab-pane>
      <el-tab-pane label="关联课程" name="sixth">
        <div class="related-courses tg-box--margin tg-shadow--margin">
          <el-form>
            <related-courses
              :course_name="info.course_name"
              ref="relative_course"
              v-if="tab === 'sixth'"
              :height="height"
              :pageType="'edit'"
              :relevance.sync="course_relative_info.be_linked"
              :related.sync="course_relative_info.linked"
              :course_attribute="course_attribute"
              :course_genre="course_genre"
              @add="relativeAdd"
              @remove="relativeRemove"
              @batchDel="relativeBatchDel"
              @update="relativeUpdate"
              :id="course_id"
            ></related-courses>
          </el-form>
          <!-- <div class="tg-pagination">
            <span class="el-pagination__total">共 {{ total }} 条</span>
            <el-pagination
              background
              layout="prev, pager, next,jumper"
              :total="total"
              :page-size="pageSize"
              :current-page="page"
              @current-change="currentChange"
            >
            </el-pagination>
          </div>-->
        </div>
      </el-tab-pane>
      <el-tab-pane
        v-if="info.course_type != 1"
        :label="info.course_type == 3 ? '开班规则' : '排课规则'"
        name="seventh"
      >
        <el-row
          class="btn--special tg-box--margin"
          v-has="{ m: 'course', o: 'update_rules' }"
        >
          <el-button
            type="primary"
            class="tg-button--plain"
            @click="openSeventhUpdate"
            v-if="!seventh_can_update"
            v-has="{ m: 'course', o: 'rule_update' }"
            >修改</el-button
          >
          <el-button
            type="primary"
            class="tg-button--plain"
            v-if="seventh_can_update"
            @click="cancelSeventh"
            >取消</el-button
          >
          <el-button
            type="primary"
            class="tg-button--plain"
            v-if="seventh_can_update"
            @click="saveSeventh"
            >确认修改</el-button
          >
        </el-row>
        <el-form
          v-if="tab === 'seventh'"
          class="arrange-rule tg-box--margin tg-shadow--margin tg-box--shadow"
          ref="ruleForm"
          :rules="rules"
          :model="seventh_info"
          :style="{ height: height + 40 + 'px' }"
        >
          <open-classroom-rule
            v-if="info.course_type == 3"
            :type="info.course_type"
            :pageType="seventh_can_update ? 'edit' : 'info'"
            ref="classroom"
            :formData="seventh_info.open_classroom_rule"
          ></open-classroom-rule>
          <arrange-rule
            v-else
            :type="info.course_type"
            :pageType="seventh_can_update ? 'edit' : 'info'"
            ref="arrange"
            :formData="seventh_info.arrange_rule"
          ></arrange-rule>
        </el-form>
      </el-tab-pane>
    </el-tabs>
    <el-dialog title="天弈课程名称添加" :visible.sync="tyLinkShow" width="30%">
      <!-- <el-input
        v-model="ty_value"
        placeholder="请输入天弈课程名称"
        style="width: 100%"
      ></el-input> -->
      <el-select
        class="ty_course"
        v-model="ty_value"
        filterable
        value-key="id"
        placeholder="请选择天弈课程"
      >
        <el-option
          v-for="(item, index) in ty_course_list"
          :key="index"
          :label="item.name"
          :value="item"
          :disabled="init_info?.ty_link_course?.includes(item.name)"
        >
        </el-option>
      </el-select>
      <span slot="footer" class="dialog-footer">
        <el-button @click="tyLinkShow = false">取 消</el-button>
        <el-button type="primary" @click="tyCourseInput">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { validNum2 } from "@/public/validate.js";
import ArrangeRule from "@/components/courseManagement/arrangeRule.vue";
import BindGoods from "@/components/courseManagement/infoBindGoods.vue";
import BindMatch from "@/components/courseManagement/infoBindMatch.vue";
import classProgress from "@/components/courseManagement/classProgress.vue";
import relatedCourses from "@/components/courseManagement/infoRelevanceCourse";
import AuthorizedSchool from "@/components/courseManagement/infoAuthorizedSchool.vue";
import OpenClassroomRule from "@/components/courseManagement/openClassroomRule.vue";
import PriceInfo from "@/components/courseManagement/infoPriceInfo";
import studentInforApi from "@/api/studentInfor";
import { course_years } from "@/public/dict.js";
import {
  getCourseInfoBasic,
  getCourseConfigAll,
  updateCourseInfo,
  getCourseInfoRelative,
  getCourseRuleInfo,
  addCourseInfoRelative,
  removeCourseInfoRelative,
  updateCourseRuleInfo,
  getTYCourseList
} from "@/api/courseManagement";

export default {
  data() {
    return {
      hoverFlag: false,
      isSpeard: false,
      tab: "",
      total: 0,
      pageSize: 10,
      page: 1,
      height: 0,
      update_flag: false,
      seventh_can_update: false,
      course_id: "",
      info: {},
      init_info: {},
      info_can_update: false,
      course_genre: [],
      course_level: [],
      course_class_type: [],
      course_attribute: [],
      course_year: [],
      course_type: [],
      is_dynamic_discount: false,
      course_relative_info: {},
      seventh_info: {
        arrange_rule: {
          classroom_name: "",
          lesson_duration: "",
          teacher_level: []
        },
        open_classroom_rule: {
          classroom_name: "",
          mini_student_numb: "",
          max_student_numb: "",
          lesson_duration: "",
          scheduling_frequency: "",
          frequency_type: "周",
          plan_lesson_numb: "",
          teacher_level: []
        }
      },
      rules: {
        "arrange_rule.lesson_duration": [
          { validator: this.checklessonDuration, trigger: "blur" }
        ],
        "open_classroom_rule.lesson_duration": [
          { validator: this.checklessonDuration, trigger: "blur" }
        ],
        "open_classroom_rule.mini_student_numb": [
          { validator: this.openRulesCheck, trigger: "blur" }
        ],
        "open_classroom_rule.max_student_numb": [
          { validator: this.openRulesCheck, trigger: "blur" }
        ],
        "open_classroom_rule.scheduling_frequency": [
          { validator: this.openRulesCheck, trigger: "blur" }
        ],
        "open_classroom_rule.plan_lesson_numb": [
          { validator: this.openRulesCheck, trigger: "blur" }
        ]
      },
      student_state_list: [],
      ty_link_course: "",
      tyLinkShow: false,
      ty_value: "",
      ty_course_list: []
    };
  },
  mounted() {
    this.tab = "first";
    this.course_id = this.$route.query.id;
    this.getDetails({ id: this.course_id });
    this.getCourseConfig();
    this.getYear();
    this.getStudentStateList();
    this.height =
      document.body.clientWidth > 1675
        ? window.innerHeight - 592
        : window.innerHeight - 638;
    if (this.height < 338) {
      this.height = 338;
    }
    this.getTYCourse();
  },
  watch: {
    tab: {
      handler(val) {
        if (val === "sixth") {
          this.getCourseInfoRelative({ course_id: this.course_id });
        } else if (val === "seventh") {
          this.getCourseRuleInfo(this.course_id);
        } else if (val === "first") {
          this.$nextTick(() => {
            this.$refs.price_info.getList();
          });
        }
      },
      immediate: true
    },
    is_dynamic_discount(val) {
      if (val) {
        this.info.dynamic_discount = this.info.dynamic_discount
          ? this.info.dynamic_discount
          : 1;
      }
    }
  },
  filters: {
    getSelect(val) {
      return val === 0 ? "不允许" : val === 1 ? "允许" : "";
    },
    getLabel(val, data) {
      const item = data.find((item1) => +item1.config_value === val);
      return typeof item === "undefined" ? "" : item.config_name;
    },
    getEnabled(val) {
      return val === 1 ? "已开启" : val === 2 ? "已停用" : "";
    },
    getStuState(val, data) {
      const label = [];
      if (typeof val === "undefined") return "";
      for (let i = 0; i < val.length; i++) {
        const item = data.find((item1) => item1.value === val[i]);
        label.push(typeof item === "undefined" ? "" : item.label);
      }
      return label.join("，");
    }
  },
  methods: {
    getTYCourse() {
      getTYCourseList().then((res) => {
        const arr = res.data ?? [];
        // const arrName = arr.map((item) => item.name);
        // this.ty_course_list = [...new Set(arrName)];
        this.ty_course_list = arr;
      });
    },
    getYear() {
      const arr = [];
      course_years.map((item) => {
        arr.push("" + item);
      });
      this.course_year = arr;
    },
    getStudentStateList(data) {
      this.student_state_list = [];
      studentInforApi.getStudentState(data).then((res) => {
        for (const key in res.data) {
          this.student_state_list.push({ label: res.data[key], value: key });
        }
        this.student_state_list.push({
          label: "意向客户",
          value: "intention_frontend"
        });
        this.student_state_list.push({
          label: "游客",
          value: "visitor"
        });
      });
    },
    // currentChange(val) {
    //   this.page = val;
    // },
    show(bool) {
      this.isSpeard = bool;
      this.hoverFlag = false;
      if (bool) {
        if (document.body.clientWidth > 1675) {
          this.height = window.innerHeight - 697;
        } else {
          this.height = window.innerHeight - 743;
        }
      } else {
        if (document.body.clientWidth > 1675) {
          this.height = window.innerHeight - 592;
        } else {
          this.height = window.innerHeight - 638;
        }
      }
    },
    handleClick() {},
    openDialog() {
      this.update_flag = true;
    },
    openInfoUpdate() {
      this.info_can_update = true;
      const { course_level_string } = this.info;
      const arr =
        course_level_string === "" ? [] : course_level_string.split(",");
      this.$set(this.info, "course_levels", arr);
    },
    openSeventhUpdate() {
      this.seventh_can_update = true;
    },
    saveSeventh() {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          let d = {};
          const { open_classroom_rule, arrange_rule } = this.seventh_info;
          if (+this.info.course_type === 3) {
            d = {
              id: open_classroom_rule.id,
              course_id: this.course_id,
              classroom_name: open_classroom_rule.classroom_name,
              teacher_level: open_classroom_rule.teacher_level.join(),
              mini_student_numb: +open_classroom_rule.mini_student_numb,
              max_student_numb: +open_classroom_rule.max_student_numb,
              lesson_duration: +open_classroom_rule.lesson_duration,
              scheduling_frequency: +open_classroom_rule.scheduling_frequency,
              frequency_type: open_classroom_rule.frequency_type,
              plan_lesson_numb: +open_classroom_rule.plan_lesson_numb
            };
          } else {
            d = {
              id: arrange_rule.id,
              course_id: this.course_id,
              classroom_name: arrange_rule.classroom_name,
              teacher_level: arrange_rule.teacher_level.join(),
              lesson_duration: +arrange_rule.lesson_duration
            };
          }
          const { data } = await updateCourseRuleInfo(d);
          if (typeof data.err === "undefined") {
            this.$message.success("保存成功");
            this.seventh_can_update = false;
          }
        }
      });
    },
    cancelSeventh() {
      this.seventh_can_update = false;
      this.getCourseRuleInfo(this.course_id);
    },
    // 保存基本信息
    async saveInfo() {
      const dynamic_discount_valid = this.checkDynamicDiscount();
      if (!dynamic_discount_valid) {
        this.$message.error("请填写动态课消");
        return false;
      }
      console.log(this.info.course_attribute, this.info.sales_to);
      if (this.info.course_attribute !== 1 && this.info.sales_to.length === 0) {
        this.$message.error("请选择售卖对象");
        return false;
      }
      const info = {
        ...this.info,
        dynamic_discount: this.info.dynamic_discount.toString()
      };
      delete info.deleted_at;
      delete info.created_at;
      delete info.course_no;
      delete info.sales_to_string;
      delete info.multi_props;
      if (info.sales_to.includes("intention_frontend")) {
        info.sales_to.splice(info.sales_to.indexOf("intention_frontend"), 1);
        info.allow_intention_sale = 1;
      } else {
        info.allow_intention_sale = 0;
      }
      // if (info.sales_to.includes("visitor")) {
      //   info.sales_to.splice(info.sales_to.indexOf("visitor"), 1);
      //   info.allow_visitor_sale = 1;
      // } else {
      //   info.allow_visitor_sale = 2;
      // }
      if (!this.is_dynamic_discount) delete info.dynamic_discount;
      const data = {
        id: this.course_id,
        course_base_info: info
      };
      console.log(data);
      this.updateCourseInfo(data);
      if (
        this.init_info.course_name.trim() !== this.info.course_name.trim() &&
        (+this.info.course_type === 3 || +this.info.course_type === 2)
      ) {
        const { open_classroom_rule, arrange_rule } = this.seventh_info;
        let d = {};
        if (+this.info.course_type === 3) {
          d = {
            id: open_classroom_rule.id,
            course_id: this.course_id,
            classroom_name: this.info.course_name
          };
        } else if (+this.info.course_type === 2) {
          d = {
            id: arrange_rule.id,
            course_id: this.course_id,
            classroom_name: this.info.course_name
          };
        }
        const { data } = await updateCourseRuleInfo(d);
        if (data === "success") {
          if (+this.info.course_type === 3) {
            this.$set(
              this.seventh_info.open_classroom_rule,
              "classroom_name",
              this.info.course_name
            );
          } else if (+this.info.course_type === 2) {
            this.$set(
              this.seventh_info.arrange_rule,
              "classroom_name",
              this.info.course_name
            );
          }
        }
      }
    },
    cancelInfo() {
      this.info_can_update = false;
      this.info = { ...this.init_info };
      this.is_dynamic_discount = !!this.init_info.dynamic_discount;
    },
    checkDynamicDiscount() {
      if (!this.is_dynamic_discount) return true;
      return this.info.dynamic_discount !== "";
    },
    async getDetails(id) {
      try {
        const { data } = await getCourseInfoBasic(id);
        const sales_to =
          typeof data.multi_props.sales_to === "undefined"
            ? []
            : data.multi_props.sales_to;
        if (+data.allow_intention_sale === 1) {
          sales_to.push("intention_frontend");
        }
        // if (+data.allow_visitor_sale === 1) {
        //   sales_to.push("visitor");
        // }
        this.info = { ...data, sales_to };

        // 初始化 ty_link_course_id 数组，如果后端没有返回的话
        if (!this.info.ty_link_course_id) {
          this.info.ty_link_course_id = [];
        }

        if (this.info.ty_link_course && this.info.ty_link_course.length > 0) {
          this.ty_link_course = this.info.ty_link_course.join(",");
        } else {
          this.ty_link_course = "";
        }
        this.init_info = { ...data, sales_to };
        this.is_dynamic_discount = !!this.init_info.dynamic_discount;
        this.getCourseRuleInfo(this.course_id);
      } catch (e) {
        console.error(e);
      }
    },
    getConfigListByType(data, config_type) {
      data.map((item) => {
        if (config_type === item.config_type) {
          this[config_type] = item.config_list;
        }
      });
    },
    getCourseConfig() {
      getCourseConfigAll().then((res) => {
        if (res.data) {
          const config_name_list = [
            "course_genre",
            "course_level",
            "course_class_type",
            "course_attribute",
            "course_type",
            "course_package_class_type"
          ];
          config_name_list.map((item) => {
            this.getConfigListByType(res.data, item);
          });
        }
      });
    },
    async updateCourseInfo(d) {
      const { data } = await updateCourseInfo(d);
      if (data === "success") {
        this.$message.success("修改成功");
        this.info_can_update = false;
        this.getDetails({ id: this.course_id });
      }
    },
    // 获取关联课程
    async getCourseInfoRelative(d) {
      const { data } = await getCourseInfoRelative(d);
      data?.linked?.forEach((element) => {
        const new_priority = element.priority === 9999 ? "" : element.priority;
        this.$set(element, "new_priority", new_priority);
      });
      this.course_relative_info = {
        ...data,
        linked: data.linked == null ? [] : data.linked,
        be_linked: data.be_linked == null ? [] : data.be_linked
      };
    },
    // 新增关系
    async relativeAdd(obj) {
      const d = {
        course_id: this.course_id,
        items: [
          {
            target_course_id: obj.id,
            positive_ratio_source: "1",
            positive_ratio_target: obj.positive_ratio_target.toString(),
            allow_negative_ratio: Number(obj.allow_negative_ratio)
          }
        ]
      };
      const { data } = await addCourseInfoRelative(d);
      if (data === "success") {
        this.$message.success("关联成功");
        this.getCourseInfoRelative({ course_id: this.course_id });
      }
    },

    // 关联关系删除
    async relativeRemove(obj) {
      const d = {
        id: obj.id,
        course_id: this.course_id
      };
      const { data } = await removeCourseInfoRelative(d);
      if (data === "success") {
        this.$message.success("移除成功");
        this.getCourseInfoRelative({ course_id: this.course_id });
      }
    },
    relativeUpdate() {
      this.getCourseInfoRelative({ course_id: this.course_id });
    },
    // 关联关系批量删除
    relativeBatchDel() {},
    // 规则
    async getCourseRuleInfo(d) {
      const { course_type } = this.info;
      const { data } = await getCourseRuleInfo(d);
      if (data) {
        if (+course_type === 2) {
          data.teacher_level = data.teacher_level.split(",");
          this.seventh_info.arrange_rule = data;
        }
        if (+course_type === 3) {
          data.teacher_level = data.teacher_level.split(",");
          this.seventh_info.open_classroom_rule = data;
        }
        // console.log(this.seventh_info);
      }
    },
    checklessonDuration(rule, value, callback) {
      if (!value) {
        return callback(new Error());
      }
      if (!validNum2(value)) {
        this.$message.error("单次课时长格式不正确,请输入大于0的数字");
        callback(new Error());
      } else {
        if (value >= 10000) {
          this.$message.error("单次课时长为4位数字");
          callback(new Error());
        } else {
          callback();
        }
      }
    },
    openRulesCheck(rule, value, callback) {
      const reg = /^\+?[1-9]\d*$/;
      if (value && !reg.test(value)) {
        this.$message.error("开班规则输入项格式有误，请输入大于0的数字");
        callback(new Error());
      } else {
        callback();
      }
    },
    tyCourseInput() {
      if (!this.ty_value || !this.ty_value.id || !this.ty_value.name) {
        this.$message.warning("请选择有效的天弈课程");
        return;
      }

      if (!this.info.ty_link_course) {
        this.info.ty_link_course = [];
        this.info.ty_link_course_id = [];
      }

      // 检查是否已经添加过该课程
      if (
        this.info.ty_link_course_id &&
        this.info.ty_link_course_id.includes(this.ty_value.id.toString())
      ) {
        this.$message.warning("该课程已添加，请选择其他课程");
        return;
      }

      // 添加课程名称和ID
      this.info.ty_link_course.push(this.ty_value.name);
      this.info.ty_link_course_id.push(this.ty_value.id.toString());

      this.tyLinkShow = false;
      this.ty_value = null; // 重置选择
    },
    delTYLinkCourse(index) {
      this.info.ty_link_course.splice(index, 1);
      if (this.info.ty_link_course_id) {
        this.info.ty_link_course_id.splice(index, 1);
      }
    }
  },
  components: {
    classProgress,
    BindGoods,
    BindMatch,
    relatedCourses,
    ArrangeRule,
    AuthorizedSchool,
    OpenClassroomRule,
    PriceInfo
  }
};
</script>
<style lang="less" scoped>
.course-update {
  /deep/ .ty_course {
    width: 500px;
    .el-input {
      width: 100%;
    }
  }
  .tg-info__back {
    img {
      width: 10px;
      height: 6px;
      margin-right: 10px;
      transform: rotate(90deg);
      -moz-transform: rotate(90deg);
      -ms-transform: rotate(90deg);
      -o-transform: rotate(90deg);
      -webkit-transform: rotate(90deg);
      cursor: pointer;
    }

    span {
      cursor: pointer;
    }
  }
  .ty_link_ul {
    // display: flex;
    .font_ty {
      all: initial;
      font-size: @text-size_normal;
      color: @text-color_second;
      font-family: @text-famliy_medium;
      // line-height: 46px;
      margin: 0 5px;
      i {
        color: red;
        cursor: pointer;
      }
    }
  }
  .tg-info__content {
    background: #fff;
    padding: 16px;
    box-sizing: border-box;
    width: auto;
  }

  span.info__subtitle {
    height: 22px;
    position: relative;
    line-height: 22px;
    color: @text-color_first;
    font-family: @text-famliy_semibold;
    font-size: @text-size_medium;
    display: inline-block;
    padding: 0 16px;
    background-color: #fff;
  }

  .info__subtitle::after {
    content: "";
    position: absolute;
    left: 0;
    top: 2px;
    height: 18px;
    width: 4px;
    background-color: @base-color;
  }

  .info__line {
    position: relative;
  }

  .info__line::before {
    content: "";
    background-color: #e0e6ed;
    height: 1px;
    width: 100%;
    position: absolute;
    top: 11px;
    left: 0;
  }

  .info {
    border: 1px solid @base-color;
    border-radius: 4px;
    overflow: hidden;
  }

  .info__content {
    // line-height: 46px;
    // height: 46px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;

    & > div {
      width: 33.33%;
      display: flex;
      align-items: center;
    }

    span {
      font-size: @text-size_normal;
      color: @text-color_second;
      font-family: @text-famliy_medium;
      line-height: 46px;
    }

    span:nth-child(1) {
      background-color: #f5f8fc;
      min-width: 100px;
      display: inline-block;
      text-align: right;
      padding-right: 16px;
    }

    span:nth-child(2) {
      width: calc(100% - 132px);
      padding-left: 16px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    span.tg-text--blue {
      color: #157df0;
    }

    .special-span--top {
      border-top-left-radius: 4px;
    }
  }

  .other-info {
    & > div {
      width: calc((100% - 2px) / 3);
    }
  }

  .search-speard {
    display: inline-flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    width: 100%;
    cursor: pointer;

    &:hover {
      .search-speard__btn span {
        color: #157df0;
      }
    }

    span {
      font-family: @text-famliy_medium;
      font-size: 13px;
      color: @text-color_third;
      margin-right: 12px;
    }

    img {
      height: 4px;
      width: 8px;
    }
  }

  .search-speard__btn {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: 100px;
  }

  .is-expand {
    padding: 16px 0;
  }

  .course-info__title {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;

    img {
      width: 32px;
      margin-right: 10px;
    }

    .course-info__label {
      font-size: @text-size_medium;
      font-family: @text-famliy_medium;
      color: @text-color_second;
    }

    .course-info__title--left {
      display: flex;
      flex-direction: row;
      align-items: center;
    }
  }

  .tg-tips__box {
    background: #eff5ff;
    border: 1px solid #b6d1ff;
    border-radius: 3px;
    height: 20px;
    line-height: 20px;
    padding: 0 12px;
    font-size: @text-size_special;
    font-family: @text-famliy_normal;
    color: #478bff;
    margin-left: 10px;
  }

  .info-box__border {
    box-sizing: border-box;
    border-bottom: 1px solid #e0e6ed;
    border-right: 1px solid #e0e6ed;

    &:nth-child(3n) {
      border-right: 0;
    }
  }

  .info-box__border--right {
    border-right: 1px solid #e0e6ed;
  }

  div.info__content--special {
    width: 50%;

    span:nth-child(2),
    span:nth-child(3) {
      width: calc((100% - 116px) / 2);
    }

    span:nth-child(2) {
      border-right: 1px solid #e0e6ed;
    }

    span:nth-child(3) {
      text-indent: 1rem;
    }
  }

  .course-info__list--hidden {
    &.up {
      transform: rotateX(90deg);
      transform-origin: center 0;
      transition: transform 0.5s;
    }

    &.down {
      transform: rotateX(0deg);
      transform-origin: center 0;
      transition: transform 0.5s;
    }
  }

  .course-info__list {
    transition: height 0.5s;

    // &.up {
    //   height: 228px;
    // }

    // &.down {
    //   height: 333px;
    // }
  }

  .tg-info__content.tg-table__box {
    margin-bottom: 16px;
  }

  .student-tab {
    height: 46px;
    position: relative;

    ::v-deep .el-tabs__item {
      font-family: @text-famliy_semibold;
      color: @text-color_second;
      font-size: @text-size_normal;
      height: 46px;
      line-height: 46px;
    }

    ::v-deep .is-active {
      color: @base-color;
    }

    ::v-deep .el-tabs__header {
      margin: 0 6px;
      padding: 0 16px;
      background: #fff;
      box-shadow: 0 2px 6px 0 rgba(204, 208, 217, 0.35);
      border-radius: 4px;
    }

    ::v-deep .arrange-rule {
      background: #fff;
      border-radius: 4px;
      padding: 0 6px 6px 6px;
      margin-bottom: 6px;
      min-height: 378px;
      overflow-y: scroll;

      .desc {
        font-size: @text-size_special;
        font-family: @text-famliy_medium;
        margin-left: 16px !important;
        color: #b3b7c6;

        &::before {
          content: "";
          display: inline-block;
          width: 10px;
          height: 10px;
          background: url("../../assets/图片/icon_info.png");
          background-size: cover;
          margin-right: 8px;
        }
      }

      .section-title {
        display: none;
      }

      .section-content {
        padding: 0 10px 10px 10px;
      }
    }
  }

  ::v-deep .related-courses {
    margin-bottom: 6px;
    overflow-y: scroll;

    .relevance-course-moudle .course-tables .relevance {
      margin-top: 0;
    }

    .course-tables {
      padding-left: 16px;
      padding-right: 16px;
      background: #fff;
      box-shadow: 0 2px 6px 0 rgba(204, 208, 217, 0.35);
      border-radius: 4px;
      margin-top: 16px;
      min-height: 358px;
    }

    .desc {
      margin-left: 0 !important;
      color: #b3b7c6;
      font-family: @text-famliy_medium;
      font-size: @text-size_special;

      &::before {
        content: "";
        display: inline-block;
        width: 10px;
        height: 10px;
        background: url("../../assets/图片/icon_info.png");
        background-size: cover;
        margin-right: 8px;
      }
    }

    .course-tables .title {
      padding: 16px 0 16px 16px;

      &::before {
        top: 19px;
      }
    }

    .tg-pagination {
      border-top: 1px solid #e0e6ed;
      box-shadow: 0 2px 6px 0 rgba(204, 208, 217, 0.35);
    }

    .relevance:last-child {
      margin-top: 0;
    }
  }

  .btn--special {
    margin-left: 6px;
    margin-right: 6px;
  }

  ::v-deep .is-edit {
    margin: 0 16px;
    width: calc(100% - 32px);

    .el-input {
      width: 100%;
    }

    .tg-input__number {
      width: 100px;
      margin: 0 16px;
    }
  }

  ::v-deep .is-edit--special {
    width: 100%;
  }

  .is-edit--margin {
    margin-left: 16px;
  }
}
</style>
