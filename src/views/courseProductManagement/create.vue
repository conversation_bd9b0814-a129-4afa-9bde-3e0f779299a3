<template>
  <el-dialog
    :visible="true"
    :title="dialogTitle"
    width="1000px"
    @close="handleClose"
  >
    <div class="course-product-management-create">
      <div style="margin-top: 0px" class="step-title step1">基础设置</div>
      <el-form
        label-position="left"
        label-width="120px"
        :model="form"
        ref="form"
      >
        <el-form-item required label="课程分类" prop="minpro_course_type">
          <el-select
            v-model="form.minpro_course_type"
            placeholder="请选择课程分类"
            :disabled="mode === 'view' || mode === 'edit' || formLoading"
          >
            <el-option label="试听课" :value="2"></el-option>
            <el-option label="公开课" :value="4"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="form.minpro_course_type === 4"
          required
          label="课程名称"
          prop="minpro_course_name"
        >
          <el-input
            style="width: 400px"
            v-model.trim="form.minpro_course_name"
            maxlength="20"
            :disabled="mode === 'view' || formLoading"
          ></el-input>
        </el-form-item>
        <el-form-item
          v-if="form.minpro_course_type === 4"
          required
          label="H5视频链接"
          prop="html_url"
        >
          <el-input
            style="width: 400px"
            maxlength="200"
            @blur="handleBlurHtmlUrl"
            v-model.trim="form.html_url"
            :disabled="mode === 'view' || formLoading"
          ></el-input>
        </el-form-item>
        <el-form-item
          v-if="form.minpro_course_type === 2"
          required
          label="选择课程"
          prop="link_course_id"
        >
          <el-button
            icon="el-icon-plus"
            size="small"
            type="primary"
            @click="selectCourse"
            :disabled="mode === 'view' || formLoading"
            >选择课程</el-button
          >
        </el-form-item>
        <el-form-item
          v-if="course_check_arr?.length && form.minpro_course_type === 2"
          label=""
          prop="xxx"
        >
          <div style="margin-left: 0" class="tg-table__box">
            <div class="tg-box--border"></div>
            <el-table
              ref="course_table"
              :data="course_check_arr"
              tooltip-effect="dark"
              class="tg-table"
              :row-key="getRowKeys"
            >
              <el-table-column label="课程名称" prop="course_name">
                <template slot-scope="scope">
                  {{
                    scope.row.product_id
                      ? scope.row.product_name
                      : scope.row.course_name
                  }}
                </template>
              </el-table-column>
              <el-table-column label="年份" width="80" prop="course_year">
              </el-table-column>
              <el-table-column
                label="课程种类"
                width="100"
                prop="course_level_string"
                show-overflow-tooltip
              >
              </el-table-column>
              <el-table-column label="班型" width="120" prop="lesson_type">
              </el-table-column>
              <el-table-column label="课程属性" width="120" prop="">
                <template slot-scope="scope">
                  {{ scope.row.course_attribute | getLabel(course_attribute) }}
                </template>
              </el-table-column>
              <el-table-column label="价格" prop="course_price" width="84">
                <template slot-scope="scope">
                  {{
                    typeof scope.row.product_id == "undefined"
                      ? scope.row[scope.column.property].toFixed(2)
                      : scope.row.original_price.toFixed(2)
                  }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form-item>
        <!-- 售卖对象 -->
        <el-form-item
          v-if="form.minpro_course_type === 2"
          required
          label="售卖对象"
          prop="sale_to"
        >
          <el-input
            style="width: 820px"
            v-model="sale_to_format"
            disabled
          ></el-input>
        </el-form-item>
        <!-- 可见范围 -->
        <el-form-item required label="可见范围" prop="view_to">
          <el-select
            v-model="form.view_to"
            placeholder="请选择可见范围"
            multiple
            class="select-view-to"
            :disabled="mode === 'view' || formLoading"
          >
            <el-option
              :label="item.name"
              :value="item.id"
              v-for="item in viewToOptionsFormat"
              :key="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="form.minpro_course_type === 2"
          required
          label="是否可用优惠券"
          prop="can_coupon_use"
        >
          <el-radio
            v-model="form.can_coupon_use"
            :label="1"
            :disabled="mode === 'view' || formLoading"
            >是</el-radio
          >
          <el-radio
            v-model="form.can_coupon_use"
            :label="2"
            :disabled="mode === 'view' || formLoading"
            >否</el-radio
          >
        </el-form-item>
        <el-form-item required label="课程封面" prop="cover_url">
          <div class="form-item-cover">
            <el-upload
              action="#"
              list-type="picture-card"
              accept="image/*"
              :file-list="coverFileList"
              :limit="1"
              ref="uploadImgsCover"
              :http-request="(file) => uploadImg(file, 'coverFileList')"
              :disabled="mode === 'view' || formLoading"
            >
              <i slot="default" class="el-icon-plus"></i>
              <div class="el-upload-listitem" slot="file" slot-scope="{ file }">
                <el-image
                  class="el-upload-list__item-thumbnail"
                  fit="cover"
                  :src="file.url"
                  :preview-src-list="[file.url]"
                >
                </el-image>

                <span
                  class="el-upload-list__item-delete"
                  @click="handleRemove(file, 'coverFileList')"
                  v-if="mode !== 'view'"
                >
                  <i class="el-icon-delete"></i>
                </span>
              </div>
            </el-upload>
          </div>
          <div v-if="mode !== 'view'" class="form-item-cover-tips">
            请上传160*160尺寸的图片
          </div>
        </el-form-item>
        <el-form-item
          style="margin: 16px 0"
          required
          label="课程头图"
          prop="header_url"
        >
          <div class="form-item-header">
            <el-upload
              action="#"
              list-type="picture-card"
              accept="image/*"
              :file-list="headerFileList"
              :limit="1"
              ref="uploadImgsHeader"
              :http-request="(file) => uploadImg(file, 'headerFileList')"
              :disabled="mode === 'view' || formLoading"
            >
              <i slot="default" class="el-icon-plus"></i>
              <div class="el-upload-listitem" slot="file" slot-scope="{ file }">
                <el-image
                  class="el-upload-list__item-thumbnail"
                  :src="file.url"
                  fit="cover"
                  :preview-src-list="[file.url]"
                >
                </el-image>
                <span
                  class="el-upload-list__item-delete"
                  @click="handleRemove(file, 'headerFileList')"
                  v-if="mode !== 'view'"
                >
                  <i class="el-icon-delete"></i>
                </span>
              </div>
            </el-upload>
          </div>
          <div v-if="mode !== 'view'" class="form-item-cover-tips">
            请上传750*422尺寸的图片
          </div>
        </el-form-item>
        <el-form-item required label="课程详情图" prop="body_url">
          <div class="form-item-body">
            <el-upload
              action="#"
              list-type="picture-card"
              accept="image/*"
              :file-list="bodyFileList"
              :limit="20"
              ref="uploadImgsBody"
              :http-request="(file) => uploadImg(file, 'bodyFileList')"
              :disabled="mode === 'view' || formLoading"
            >
              <i slot="default" class="el-icon-plus"></i>
              <div class="el-upload-listitem" slot="file" slot-scope="{ file }">
                <el-image
                  class="el-upload-list__item-thumbnail"
                  :src="file.url"
                  fit="cover"
                  :preview-src-list="[file.url]"
                >
                </el-image>
                <span
                  class="el-upload-list__item-delete"
                  @click="handleRemove(file, 'bodyFileList')"
                  v-if="mode !== 'view'"
                >
                  <i class="el-icon-delete"></i>
                </span>
              </div>
            </el-upload>
          </div>
        </el-form-item>
        <!-- 设置授权校区 -->
        <!-- <el-form-item
          v-if="form.minpro_course_type === 4"
          required
          label="授权校区"
          prop="view_department"
        >
          <el-button type="primary" size="small" @click="setSchool"
            >设置授权校区</el-button
          >
        </el-form-item> -->
      </el-form>
      <div v-if="form.minpro_course_type === 2" class="step-title step2">
        价格设置
      </div>
      <el-form
        v-if="form.minpro_course_type === 2"
        label-position="left"
        label-width="120px"
        :model="form"
        ref="form"
      >
        <el-form-item required label="小程序显示原价" prop="is_show_price">
          <el-radio
            v-model="form.is_show_price"
            :label="1"
            :disabled="mode === 'view' || formLoading"
            >是</el-radio
          >
          <el-radio
            v-model="form.is_show_price"
            :label="2"
            :disabled="mode === 'view' || formLoading"
            >否</el-radio
          >
        </el-form-item>
        <el-form-item
          v-if="form.is_show_price === 1"
          required
          label="原价"
          prop="standard_price"
        >
          <el-input
            v-model="form.standard_price"
            placeholder="请输入原价"
            type="text"
            @blur="handleBlurStandardPrice"
            :disabled="mode === 'view' || formLoading"
          ></el-input>
        </el-form-item>
        <el-form-item v-if="course_check_arr.length > 0" label="" prop="xxx">
          <div style="margin-left: 0" class="tg-table__box">
            <div class="tg-box--border"></div>
            <el-table
              ref="course_table2"
              :data="course_check_arr"
              tooltip-effect="dark"
              class="tg-table"
              :row-key="getRowKeys"
            >
              <el-table-column label="课程名称" prop="course_name">
                <template slot-scope="scope">
                  {{
                    scope.row.product_id
                      ? scope.row.product_name
                      : scope.row.course_name
                  }}
                </template>
              </el-table-column>
              <el-table-column
                label="售卖规格"
                width="100"
                prop="standard_numb"
              >
                <template slot-scope="scope">
                  {{ scope.row.standard_numb + "课时" }}
                </template>
              </el-table-column>
              <el-table-column label="售卖价格" prop="course_price" width="100">
                <template slot-scope="scope">
                  {{
                    scope.row.product_id
                      ? scope.row.original_price.toFixed(2)
                      : scope.row[scope.column.property].toFixed(2)
                  }}
                </template>
              </el-table-column>
              <el-table-column label="整期价格" prop="course_price" width="100">
                <template slot-scope="scope">
                  {{
                    (scope.row.course_price * scope.row.standard_numb).toFixed(
                      2
                    )
                  }}
                </template>
              </el-table-column>
              <el-table-column
                v-if="form.is_show_price === 1"
                label="显示原价"
                width="120"
                prop=""
              >
                <template slot-scope="scope">
                  {{
                    scope.row.product_id
                      ? ""
                      : form.standard_price
                      ? parseFloat(form.standard_price).toFixed(2)
                      : ""
                  }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button class="tg-button--plain" type="plain" @click="handleClose">{{
        mode === "view" ? "关闭" : "取消"
      }}</el-button>
      <template v-if="mode !== 'view'">
        <el-button
          class="tg-button--primary"
          :loading="loading1"
          type="primary"
          v-throttle="() => really(1)"
          >上架</el-button
        >
        <el-button
          class="tg-button--primary"
          :loading="loading2"
          type="primary"
          v-throttle="() => really(2)"
          >保存</el-button
        >
      </template>
    </span>
    <choose-course
      v-if="course_visible"
      :choose_course_visible="course_visible"
      :check_arr.sync="course_check_arr"
      type="radio"
      @close="course_visible = false"
      @confirm="courseConfirm"
      attribute_type="sale"
      :status="true"
      :course_type="2"
      :is_bind_minipro="1"
      :department_id="departmentIdList"
    ></choose-course>
    <!-- <saveSchoolTree
      v-if="schoolTreeVisible"
      :flag.sync="schoolTreeVisible"
      :id.sync="form.view_department"
      :name.sync="form.view_department_name"
      :use_store_options="false"
      :checkedControlSchool="true"
      :type="mode === 'view' ? 'isLook' : 'chooseSchool'"
      :required="true"
      @confirm="confirmSchool"
    ></saveSchoolTree> -->
  </el-dialog>
</template>

<script>
import { course_attribute } from "@/public/dict.js";
import { v4 as uuidv4 } from "uuid";
import courseProductManagementApi from "@/api/courseProductManagement.js";
import { getCourseList } from "@/api/courseManagement.js";
// import saveSchoolTree from "@/components/schoolTree/saveSchoolTree.vue";
export default {
  name: "miniprogramCourseProductCreate",
  // components: {
  //   saveSchoolTree
  // },
  props: {
    viewToOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      course_visible: false,
      course_check_arr: [],
      schoolTreeVisible: false,
      form: {
        body_url: [],
        can_coupon_use: 1,
        cover_url: "",
        header_url: "",
        html_url: "",
        is_show_price: 1,
        link_course_id: "",
        minpro_course_name: "",
        minpro_course_type: 2,
        standard_price: "",
        view_to: []
        // view_department: [],
        // view_department_name: ""
      },
      coverFileList: [],
      headerFileList: [],
      bodyFileList: [],
      course_attribute,
      loading1: false,
      loading2: false,
      mode: "create", // create, view, edit
      minpro_course_id: null,
      formLoading: false,
      originalForm: null
    };
  },
  filters: {
    getLabel(val, data) {
      const obj = data.find((item) => +item.id === +val);
      return typeof obj === "undefined" ? "" : obj.name;
    }
  },
  computed: {
    sale_to() {
      if (this.course_check_arr.length) {
        const sale_to = this.course_check_arr[0].sales_to || [];
        return sale_to;
      }
      return [];
    },
    sale_to_format() {
      const arr = [];
      this.viewToOptions.map((item) => {
        if (this.sale_to.includes(item.id)) {
          arr.push(item.name);
        }
      });
      return arr.join(",");
    },
    viewToOptionsFormat() {
      return this.viewToOptions.filter((item) => item.id !== "");
    },
    departmentIdList() {
      return this.$store.getters.doneGetSchoolId;
    },
    dialogTitle() {
      const titles = {
        create: "新增小程序课程",
        view: "查看小程序课程",
        edit: "编辑小程序课程"
      };
      return titles[this.mode] || "小程序课程";
    }
  },
  watch: {
    coverFileList(val) {
      if (val && val.length < 1) {
        this.form.cover_url = "";
        $(".form-item-cover .el-upload--picture-card").show();
      } else {
        this.form.cover_url = this.coverFileList[0].url;
        this.$nextTick(() => {
          $(".form-item-cover .el-upload--picture-card").hide();
        });
      }
    },
    headerFileList(val) {
      if (val && val.length < 1) {
        this.form.header_url = "";
        $(".form-item-header .el-upload--picture-card").show();
      } else {
        this.form.header_url = this.headerFileList[0].url;
        this.$nextTick(() => {
          $(".form-item-header .el-upload--picture-card").hide();
        });
      }
    },
    bodyFileList(val) {
      if (val && val.length < 1) {
        this.form.body_url = [];
        $(".form-item-body .el-upload--picture-card").show();
      } else {
        this.form.body_url = this.bodyFileList.map((item) => item.url);
        if (this.form.body_url.length >= 20 || this.mode === "view") {
          this.$nextTick(() => {
            $(".form-item-body .el-upload--picture-card").hide();
          });
        }
      }
    }
  },
  created() {
    this.Oss.getAliyun();
  },
  methods: {
    // setSchool() {
    //   this.schoolTreeVisible = true;
    // },
    // confirmSchool(val) {
    //   console.log(val);
    //   if (val) {
    //     this.form.view_department = val;
    //   }
    //   this.schoolTreeVisible = false;
    // },
    initForm(id, mode) {
      // 重置状态
      this.resetState();

      this.mode = mode || "create";
      this.minpro_course_id = id;

      if (id && (mode === "view" || mode === "edit")) {
        this.formLoading = true;
        // 获取课程详情
        courseProductManagementApi
          .getCourseMinproDetail({ minpro_course_id: id })
          .then((res) => {
            if (res.data.code === 0) {
              const courseData = res.data.data;
              // 填充表单数据
              this.fillFormData(courseData);
            } else {
              this.$message.error(res.data.message || "获取课程详情失败");
            }
          })
          .catch((err) => {
            console.error(err);
            this.$message.error("获取课程详情失败");
          })
          .finally(() => {
            this.formLoading = false;
          });
      }
    },

    resetState() {
      // 重置表单数据
      this.form = {
        body_url: [],
        can_coupon_use: 1,
        cover_url: "",
        header_url: "",
        html_url: "",
        is_show_price: 1,
        link_course_id: "",
        minpro_course_name: "",
        minpro_course_type: 2,
        standard_price: "",
        view_to: []
      };

      // 重置文件列表
      this.coverFileList = [];
      this.headerFileList = [];
      this.bodyFileList = [];

      // 重置课程选择
      this.course_check_arr = [];

      // 重置状态变量
      this.loading = false;
      this.formLoading = false;
      this.originalForm = null;
      this.mode = "create";
      this.minpro_course_id = null;
    },
    getLinkCourse(link_course_id) {
      getCourseList({
        id: link_course_id,
        course_type: 2,
        just_department: 1,
        is_enabled: 1,
        // department_course_property: [2, 3],
        department_id: this.departmentIdList
      }).then((res) => {
        if (res.status === 200) {
          this.course_check_arr = res.data.data;
        }
      });
    },
    fillFormData(data) {
      // 填充表单数据
      this.form = {
        body_url: data.body_url || [],
        can_coupon_use: data.can_coupon_use,
        cover_url: data.cover_url || "",
        header_url: data.header_url || "",
        html_url: data.html_url || "",
        is_show_price: data.is_show_price,
        link_course_id: data.link_course_id,
        minpro_course_name: data.minpro_course_name,
        minpro_course_type: data.minpro_course_type,
        standard_price: data?.standard_price?.toFixed(2) || "",
        view_to: data.view_to || [],
        minpro_course_id: data.minpro_course_id
        // view_department: data.view_department || [],
        // view_department_name: data.view_department_name || ""
      };

      // 保存原始数据，以便比较是否有修改
      this.originalForm = JSON.parse(JSON.stringify(this.form));
      // 填充图片列表
      if (data.cover_url) {
        this.coverFileList = [
          {
            name: "封面图",
            url: data.cover_url
          }
        ];
      }

      if (data.header_url) {
        this.headerFileList = [
          {
            name: "头图",
            url: data.header_url
          }
        ];
      }

      if (data.body_url && data.body_url.length) {
        this.bodyFileList = data.body_url.map((url) => ({
          name: "详情图",
          url
        }));
      }

      // 如果有关联课程，获取课程详情
      if (data.link_course_id && data.minpro_course_type === 2) {
        this.getLinkCourse(data.link_course_id);
      }
    },
    uploadImg(upload, type) {
      const file = upload.file;
      const img = new Image();
      const reader = new FileReader();

      reader.onload = (e) => {
        img.src = e.target.result;
        img.onload = () => {
          let width = img.width;
          let height = img.height;
          if (type === "coverFileList") {
            if (width !== 160 || height !== 160) {
              this.$message.error("请上传160*160尺寸的图片");
              this.coverFileList = [];
              return false;
            }
          } else if (type === "headerFileList") {
            if (width !== 750 || height !== 422) {
              this.$message.error("请上传750*422尺寸的图片");
              this.headerFileList = [];
              return false;
            }
          }
          // 如果图片宽度大于750px，进行等比例压缩
          if (width > 750) {
            const scale = width / 750;
            width = 750;
            height = Math.floor(height / scale);
          }

          // 使用canvas进行图片压缩
          const canvas = document.createElement("canvas");
          const ctx = canvas.getContext("2d");
          canvas.width = width;
          canvas.height = height;

          // 在canvas上绘制图片
          ctx.drawImage(img, 0, 0, width, height);

          // 获取压缩后的图片数据并转为blob
          canvas.toBlob(
            (blob) => {
              // 生成文件名
              const suffix = file.name.substring(
                file.name.lastIndexOf(".") + 1
              );
              const fileName = `${uuidv4()}.${suffix}`;

              // 创建新的File对象
              const compressedFile = new File([blob], fileName, {
                type: `image/${suffix === "jpg" ? "jpeg" : suffix}`,
                lastModified: Date.now()
              });

              // 上传到阿里云OSS
              this.Oss.uploadFile(compressedFile)
                .then((res) => {
                  if (res.code === 0) {
                    this[type].push({
                      name: res.objectKey,
                      url: res.url,
                      uid: file.uid
                    });
                  } else {
                    this.$message.error("上传图片失败");
                  }
                })
                .catch((err) => {
                  console.error("上传失败:", err);
                  this.$message.error("上传图片失败");
                });
            },
            file.type,
            0.9
          ); // 压缩质量0.9
        };
      };

      reader.readAsDataURL(file);
    },
    handleRemove(file, type) {
      this[type].map((item, index) => {
        if (file.uid === item.uid) {
          this[type].splice(index, 1);
        }
      });
    },
    courseConfirm() {
      this.course_visible = false;
    },
    handleClose() {
      this.$emit("close");
    },
    selectCourse() {
      this.course_visible = true;
    },
    getRowKeys(row) {
      return row.id;
    },
    handleBlurStandardPrice() {
      if (this.form.standard_price) {
        // 正则表达式验证是否为两位小数，或者整数
        if (!/^\d+(\.\d{1,2})?$/.test(this.form.standard_price)) {
          this.$message.error("原价必须为数字，且最多两位小数");
          this.form.standard_price = "";
        }
      }
    },
    handleBlurHtmlUrl() {
      if (this.form.html_url) {
        const reg =
          /^(((ht|f)tps?):\/\/)?([^!@#$%^&*?.\s-]([^!@#$%^&*?.\s]{0,63}[^!@#$%^&*?.\s])?\.)+[a-z]{2,6}\/?/;
        if (!reg.test(this.form.html_url)) {
          this.$message.error("请输入正确的H5链接");
          this.form.html_url = "";
        }
      }
    },
    really(type) {
      const { view_to, minpro_course_name, html_url } = this.form;
      // 公开课
      if (this.form.minpro_course_type === 4) {
        if (!minpro_course_name) {
          this.$message.error("请输入课程名称");
          return;
        }
        if (!html_url) {
          this.$message.error("请输入H5链接");
          return;
        }
      }
      // 试听课
      if (this.form.minpro_course_type === 2) {
        if (!this.course_check_arr || this.course_check_arr.length === 0) {
          this.$message.error("请选择课程");
          return;
        }
        if (this.sale_to.length === 0 || !this.sale_to) {
          this.$message.error("请选择售卖对象");
          return;
        }
      }

      if (!this.form.view_to || view_to.length === 0) {
        this.$message.error("请选择可见范围");
        return;
      }
      if (!this.coverFileList || this.coverFileList.length === 0) {
        this.$message.error("请上传课程封面");
        return;
      }
      if (!this.headerFileList || this.headerFileList.length === 0) {
        this.$message.error("请上传课程头图");
        return;
      }
      if (!this.bodyFileList || this.bodyFileList.length === 0) {
        this.$message.error("请上传课程详情图");
        return;
      }
      // if (this.form.minpro_course_type === 4) {
      //   if (
      //     !this.form.view_department ||
      //     this.form.view_department.length === 0
      //   ) {
      //     this.$message.error("请设置授权校区");
      //     return;
      //   }
      // }
      if (this.form.minpro_course_type === 2 && this.form.is_show_price === 1) {
        if (!this.form.standard_price) {
          this.$message.error("请输入原价");
          return;
        }
      }
      this.submitData(type);
    },
    submitData(type) {
      const formData = JSON.parse(JSON.stringify(this.form));
      formData.status = type;
      formData.standard_price = parseFloat(formData.standard_price);
      // if (formData?.view_department?.length) {
      //   formData.view_department =
      //     typeof formData?.view_department === "string"
      //       ? formData?.view_department?.split(",")
      //       : formData.view_department;
      // }
      if (this.course_check_arr?.length) {
        formData.link_course_id = this.course_check_arr[0].id;
      }

      if (type === 1) {
        this.loading1 = true;
      } else {
        this.loading2 = true;
      }

      let apiMethod = courseProductManagementApi.addCourseMinpro;
      if (this.mode === "edit") {
        apiMethod = courseProductManagementApi.editCourseMinpro;
        formData.minpro_course_id = this.minpro_course_id;
      }

      apiMethod(formData)
        .then((res) => {
          if (res.data.code === 0) {
            this.$message.success(
              this.mode === "edit" ? "修改成功" : "提交成功"
            );
            this.$emit("getList");
            this.$emit("close");
          } else {
            this.$message.error(
              res.data.message ||
                (this.mode === "edit" ? "修改失败" : "提交失败")
            );
          }
        })
        .catch(() => {
          this.$message.error(this.mode === "edit" ? "修改失败" : "提交失败");
        })
        .finally(() => {
          if (type === 1) {
            this.loading1 = false;
          } else {
            this.loading2 = false;
          }
        });
    }
  }
};
</script>

<style lang="less" scoped>
.course-product-management-create {
  .el-table {
    padding: 0;
  }
  .step-title {
    padding: 0 0 0 15px;
    position: relative;
    font-size: 15px;
    font-weight: 500;
    margin: 20px 0;
    &::before {
      content: "";
      position: absolute;
      background-color: #2d80ed;
      top: 8px;
      left: 3px;
      height: 6px;
      width: 6px;
      z-index: 1;
      border-radius: 100%;
    }
  }
  .el-form {
    padding-left: 20px;
    .el-form-item {
      margin-bottom: 10px;
    }
    .form-item-cover-tips {
      font-size: 12px;
      color: #ff0000;
    }
  }
  /deep/ .select-view-to {
    .el-input {
      width: 820px;
    }
  }
  /deep/ .el-upload-listitem {
    position: relative;
    height: 100%;
    .el-upload-list__item-delete {
      position: absolute;
      right: 10px;
      top: inherit;
      bottom: 10px;
      font-size: 12px;
      color: #f44336;
      width: 24px;
      height: 24px;
      line-height: 24px;
      text-align: center;
      background: #ffffffb8;
      border-radius: 100%;
      display: block;
      cursor: pointer;
      &:hover {
        background: rgba(175, 174, 174, 0.69);
      }
    }
  }
  /deep/ .el-input.is-focus::after {
    display: none;
  }
}
</style>
