<template>
  <el-dialog
    title="推荐到首页"
    :visible.sync="dialogVisible"
    width="500px"
    @close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form class="front-priority-form" label-width="100px">
      <el-form-item required label="课程名称">
        <el-input v-model="courseName" disabled></el-input>
      </el-form-item>
      <el-form-item required label="课程分类">
        <el-input v-model="courseCategory" disabled></el-input>
      </el-form-item>
      <el-form-item required label="显示优先级">
        <el-input-number
          v-model="frontIndex"
          :min="1"
          :max="999"
        ></el-input-number>
      </el-form-item>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button size="small" @click="handleClose">取消</el-button>
      <el-button size="small" type="primary" @click="handleConfirm"
        >保存</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: "FrontPriorityDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    course: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: false,
      courseName: "",
      courseCategory: "",
      frontIndex: 1
    };
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val) {
        this.initData();
      }
    }
  },
  methods: {
    initData() {
      if (this.course) {
        this.courseName = this.course.course_name || "";
        // 这里根据实际数据确定课程分类字段，可能是course_level_string或其他
        this.courseCategory =
          this.course.minpro_course_type === 2 ? "试听课" : "公开课";
        this.frontIndex = this.course.front_index || 1;
      }
    },
    handleClose() {
      this.$emit("close");
    },
    handleConfirm() {
      this.$emit("confirm", {
        frontIndex: this.frontIndex
      });
    }
  }
};
</script>

<style lang="less" scoped>
.front-priority-form {
  /deep/ .el-input,
  /deep/ .el-input-number {
    position: relative;
    display: inline-block;
    width: 168px;
    line-height: 30px;
  }
}
</style>
