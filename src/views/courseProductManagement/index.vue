<template>
  <div class="container vessel">
    <tg-search
      :searchTitle.sync="searchTitle"
      :form.sync="search"
      @reset="reset"
      @search="searchStaff"
      :showNum="3"
      class="tg-box--margin"
    ></tg-search>
    <el-row class="tg-box--margin tg-shadow--margin tg-row--height">
      <el-button
        type="plain"
        class="tg-button--plain"
        @click="addCourse"
        v-has="{ m: 'miniprogramCourse', o: 'create' }"
        >新增</el-button
      >
      <el-button
        v-has="{ m: 'miniprogramCourse', o: 'delete' }"
        type="plain"
        class="tg-button--plain"
        @click="handleBatchDelete"
        >批量删除</el-button
      >
    </el-row>
    <div class="tg-table__box">
      <div class="tg-box--border"></div>
      <el-table
        ref="table"
        :data="list"
        class="tg-table"
        style="width: 100%"
        @selection-change="handleSelectionChange"
        :row-key="getRowKeys"
        :cell-style="{ borderRightColor: '#e0e6ed75' }"
        :header-cell-style="{ borderRightColor: '#e0e6ed75' }"
        border
      >
        <el-table-column
          type="selection"
          width="50"
          :reserve-selection="true"
          :selectable="checkSelectable"
        ></el-table-column>
        <el-table-column
          label="小程序课程编号"
          prop="minpro_course_no"
          width="130"
        ></el-table-column>
        <el-table-column
          show-overflow-tooltip
          prop="course_name"
          label="课程名称"
          width="200"
        >
          <!-- <template slot-scope="scope">
            <div class="copy_name">
              <span
                style="color: #157df0; cursor: pointer"
                @click="toInfo(scope.row)"
                >{{ scope.row.course_name }}</span
              >
              <div v-copy="scope.row.course_name"></div>
            </div>
          </template> -->
        </el-table-column>
        <el-table-column label="课程编号" width="140" prop="course_no">
          <template slot-scope="scope">
            <span v-if="scope.row.course_no">{{ scope.row.course_no }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="年份" prop="course_year">
          <template slot-scope="scope">
            <span v-if="scope.row.course_year">{{
              scope.row.course_year
            }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column
          label="标准单价"
          prop="standard_single_price"
        ></el-table-column>
        <el-table-column label="单位" prop="unit">
          <template> 课时 </template>
        </el-table-column>
        <el-table-column label="一期" prop="standard_numb">
          <template slot-scope="scope">
            {{ scope.row.standard_numb }}课时
          </template>
        </el-table-column>
        <el-table-column label="产品类型" prop="minpro_course_type">
          <template slot-scope="scope">
            {{ scope.row.minpro_course_type === 2 ? "试听课" : "公开课" }}
          </template>
        </el-table-column>
        <el-table-column width="120" label="课程规格/课程属性">
          <template slot-scope="scope">
            <img
              v-if="scope.row.minpro_course_type === 2"
              src="../../assets/图片/message.png"
              class="attribute"
              @click="goCourseAttribute(scope.row)"
            />
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column
          label="课程种类"
          prop="course_level_string"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span v-if="scope.row.minpro_course_type === 2">{{
              scope.row.course_level_string
            }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="班型" prop="lesson_type"></el-table-column>
        <el-table-column label="课程类型" prop="course_genre">
          <template slot-scope="scope">
            {{ scope.row.course_genre | courseGenreFilter(course_genre_list) }}
          </template>
        </el-table-column>
        <el-table-column label="启用状态">
          <template slot-scope="scope">
            <div class="on_type">
              <div class="green_pie" v-if="scope.row.is_enabled == 1"></div>
              <div class="gray_pie" v-else></div>
              <span>{{ scope.row.is_enabled == 1 ? "已启用" : "已停用" }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="授权校区">
          <template slot-scope="scope">
            <span v-if="scope.row.minpro_course_type === 4">-</span>
            <span
              v-else
              @click="goCourseAttribute(scope.row)"
              style="color: #157df0; cursor: pointer"
              >{{ scope.row.access_department_numb || 0 }}校区</span
            >
          </template>
        </el-table-column>
        <el-table-column width="120" label="售卖对象">
          <template slot-scope="scope">
            {{ scope.row?.sales_to_text }}
          </template>
        </el-table-column>
        <el-table-column width="120" label="可见范围">
          <template slot-scope="scope">
            {{ scope.row?.view_to_text }}
          </template>
        </el-table-column>
        <el-table-column label="是否可用优惠券">
          <!-- 是否使用优惠券 1 是 2 否 -->
          <template slot-scope="scope">
            {{ scope.row.can_coupon_use === 1 ? "是" : "否" }}
          </template>
        </el-table-column>
        <el-table-column width="100" label="封面图">
          <template slot-scope="scope">
            <el-image
              v-if="scope.row?.cover_url"
              fit="cover"
              :src="scope.row.cover_url"
              :preview-src-list="[scope.row.cover_url]"
              style="width: 60px; height: 60px"
            ></el-image>
          </template>
        </el-table-column>
        <el-table-column width="100" label="头图">
          <template slot-scope="scope">
            <el-image
              v-if="scope.row?.header_url"
              fit="cover"
              :src="scope.row.header_url"
              :preview-src-list="[scope.row.header_url]"
              style="width: 60px; height: 60px"
            ></el-image>
          </template>
        </el-table-column>
        <el-table-column width="100" label="详情图">
          <template slot-scope="scope">
            <el-image
              v-if="scope.row?.body_url?.length"
              fit="cover"
              :src="scope.row.body_url[0]"
              :preview-src-list="scope.row.body_url"
              style="width: 60px; height: 60px"
            ></el-image>
          </template>
        </el-table-column>
        <el-table-column width="100" label="推荐到首页">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.is_front"
              :active-value="1"
              :inactive-value="2"
              @change="handleToFront(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column width="80" label="展示顺序">
          <template slot-scope="scope">
            {{ scope.row?.front_index || "-" }}
          </template>
        </el-table-column>
        <el-table-column label="上架状态">
          <template slot-scope="scope">
            {{ scope.row.status | statusFilter }}
          </template>
        </el-table-column>
        <el-table-column width="120" label="销售数量/观看次数">
          <template slot-scope="scope">
            {{ scope.row.sales_volume }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" align="center" width="220" label="操作">
          <template slot-scope="scope">
            <el-button
              v-has="{ m: 'miniprogramCourse', o: 'info' }"
              type="text"
              size="small"
              class="tg-text--blue tg-span__divide-line"
              @click="handleView(scope.row)"
              >查看</el-button
            >
            <el-button
              v-has="{ m: 'miniprogramCourse', o: 'modify' }"
              v-if="scope.row.status !== 1"
              type="text"
              size="small"
              class="tg-text--blue tg-span__divide-line"
              @click="handleEdit(scope.row)"
              >修改</el-button
            >

            <el-button
              v-has="{ m: 'miniprogramCourse', o: 'offShelf' }"
              v-if="scope.row.status === 1"
              type="text"
              size="small"
              class="tg-text--red tg-span__divide-line"
              @click="handleOffShelf(scope.row)"
              >下架</el-button
            >
            <el-button
              v-has="{ m: 'miniprogramCourse', o: 'offShelf' }"
              v-if="scope.row.status === 2 || scope.row.status === 3"
              type="text"
              size="small"
              class="tg-text--blue tg-span__divide-line"
              @click="handleOffShelf(scope.row)"
              >上架</el-button
            >
            <el-button
              v-has="{ m: 'miniprogramCourse', o: 'delete' }"
              v-if="scope.row.status !== 1"
              type="text"
              size="small"
              class="tg-text--red"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>

        <template slot="empty">
          <div style="margin-top: 15%">
            <TgLoading v-if="loading"></TgLoading>
            <div class="empty-container" v-else>暂无数据～</div>
          </div>
        </template>
      </el-table>
      <div class="tg-pagination">
        <span class="el-pagination__total">共 {{ total }} 条</span>
        <el-pagination
          background
          layout="sizes,prev,pager,next,jumper"
          :total="total"
          :page-size="pageSize"
          :current-page.sync="page"
          @size-change="sizeChange"
          @current-change="currentChange"
          :page-sizes="[10, 20, 50, 100]"
        >
        </el-pagination>
      </div>
    </div>

    <create
      @close="dialogCreate = false"
      @getList="getList"
      v-if="dialogCreate"
      :viewToOptions="viewToOptions"
      ref="createComponent"
    ></create>
    <dialog-spec
      v-if="dialogSpecShow"
      @close="dialogSpecShow = false"
      :course_attribute="course_attribute_list"
      :attribute="dialogSpecAttr"
      :com_course_id="com_course_id"
    ></dialog-spec>
    <front-priority-dialog
      :visible="frontPriorityDialogVisible"
      :course="currentCourse"
      @close="closeFrontPriorityDialog"
      @confirm="confirmFrontPriority"
    />
    <saveSchoolTree
      v-if="schoolTreeVisible"
      :flag.sync="schoolTreeVisible"
      :id="currentCourse.view_department"
      :use_store_options="false"
      :checkedControlSchool="true"
      type="isLook"
      :required="true"
    ></saveSchoolTree>
  </div>
</template>
<script>
import courseProductManagementApi from "@/api/courseProductManagement.js";
import { getCourseConfigByType } from "@/api/courseManagement.js";
import studentInforApi from "@/api/studentInfor";
import tgSearch from "@/components/search/search.vue";
import Create from "./create.vue";
import DialogSpec from "@/components/courseManagement/dialogSpec.vue";
import { course_years, sales_to_list } from "@/public/dict.js";
import FrontPriorityDialog from "./components/FrontPriorityDialog.vue";
import saveSchoolTree from "@/components/schoolTree/saveSchoolTree.vue";

export default {
  name: "courseProductManagement",
  components: {
    tgSearch,
    Create,
    DialogSpec,
    FrontPriorityDialog,
    saveSchoolTree
  },
  data() {
    return {
      page: 1,
      pageSize: 10,
      dialogCreate: false,
      dialogSpecShow: false,
      schoolTreeVisible: false,

      dialogSpecAttr: "",
      course_attribute_list: [],
      course_genre_list: [],
      // course_type_list: [],
      com_course_id: "",
      sales_to_list,
      searchTitle: [
        {
          props: "minpro_course_no",
          label: "小程序课程编号",
          type: "input",
          width: 400,
          show: true,
          placeholder: "请输入小程序课程编号"
        },
        {
          props: "course_name",
          label: "课程名称",
          type: "input",
          width: 400,
          show: true,
          placeholder: "请输入课程名称"
        },
        {
          props: "course_no",
          label: "课程编号",
          type: "input",
          width: 400,
          show: true,
          placeholder: "请输入课程编号"
        },
        {
          props: "minpro_course_type",
          label: "课程分类",
          type: "select",
          width: 400,
          show: false,
          placeholder: "请选择",
          selectOptions: [
            {
              id: "",
              name: "不限"
            },
            {
              id: 2,
              name: "试听课"
            },
            {
              id: 4,
              name: "公开课"
            }
          ]
        },
        {
          props: "course_year",
          label: "年份",
          type: "select",
          width: 400,
          show: true,
          placeholder: "请选择",
          selectOptions: []
        },

        {
          props: "course_level",
          label: "课程种类",
          type: "select",
          width: 400,
          show: false,
          placeholder: "请选择",
          selectOptions: []
        },
        {
          props: "lesson_type",
          label: "班型",
          type: "select",
          width: 400,
          show: false,
          placeholder: "请选择",
          selectOptions: []
        },
        {
          props: "sales_to",
          label: "售卖对象",
          type: "select",
          width: 400,
          show: false,
          placeholder: "请选择",
          selectOptions: []
        },
        {
          props: "is_enabled",
          label: "启用状态",
          type: "select",
          width: 400,
          show: false,
          placeholder: "请选择",
          selectOptions: [
            {
              id: "",
              name: "不限"
            },
            {
              id: 1,
              name: "已启用"
            },
            {
              id: 2,
              name: "已停用"
            }
          ]
        },
        {
          props: "view_to",
          label: "可见范围",
          type: "select",
          width: 400,
          show: false,
          placeholder: "请选择可见范围",
          selectOptions: []
        },
        {
          props: "is_front",
          label: "推荐到首页",
          type: "select",
          width: 400,
          show: false,
          placeholder: "请选择",
          selectOptions: [
            {
              id: "",
              name: "不限"
            },
            {
              id: 1,
              name: "是"
            },
            {
              id: 2,
              name: "否"
            }
          ]
        },
        {
          props: "status",
          label: "上架状态",
          type: "select",
          width: 400,
          show: false,
          placeholder: "请选择",
          selectOptions: [
            {
              id: "",
              name: "不限"
            },
            {
              id: 1,
              name: "已上架"
            },
            {
              id: 2,
              name: "未上架"
            },
            {
              id: 3,
              name: "已下架"
            }
          ]
        },
        {
          props: "justDepartment",
          label: "",
          type: "bool",
          show: false,
          content: "只查看授权给当前校区的课程"
        }
      ],
      search: {
        status: "",
        is_front: "",
        course_name: "",
        course_no: "",
        course_year: "",
        course_level: "",
        lesson_type: "",
        is_enabled: "",
        department_id: "",
        sales_to: "",
        course_type: undefined,
        course_class_type: undefined,
        justDepartment: false,
        view_to: ""
      },
      height: window.innerHeight - 370,
      loading: false,
      list: [],
      total: 0,
      selectionsIds: [],
      frontPriorityDialogVisible: false,
      currentCourse: {}
      // stop_use: false,
      // start_use: false
    };
  },
  computed: {
    school_id() {
      return this.$store.getters.doneGetSchoolId;
    },
    viewToOptions() {
      return this.searchTitle[9].selectOptions.filter((item) => item.id !== "");
    }
  },

  created() {
    this.getSelect("course_level");
    this.getSelect("course_class_type");
    this.getSelect("course_attribute");
    this.getSelect("course_type");
    this.getSelect("course_genre");
    this.getStudentStateList();
  },

  filters: {
    // courseTypeFilter: function (value, data) {
    //   const arr = data.filter((item) => +item.id === +value);
    //   return arr.length ? arr[0].name : "";
    // },
    courseGenreFilter: function (value, data) {
      const arr = data.filter((item) => +item.config_value === +value);
      return arr.length ? arr[0].config_name : "";
    },
    statusFilter: function (value) {
      return value === 1 ? "已上架" : value === 2 ? "未上架" : "已下架";
    }
  },
  mounted() {
    const years = [
      {
        id: "",
        name: "不限"
      }
    ];
    course_years.map((item) => {
      years.push({
        id: item,
        name: item
      });
    });
    this.searchTitle[4].selectOptions = years;
    this.getList();
    this.clearSelection();
  },
  methods: {
    goSchoolTree(row) {
      this.currentCourse = row;
      if (row.view_department && row.view_department.length) {
        this.schoolTreeVisible = true;
      }
    },
    handleView(row) {
      this.dialogCreate = true;
      this.$nextTick(() => {
        this.$refs.createComponent.initForm(row.minpro_course_id, "view");
      });
    },
    handleEdit(row) {
      this.dialogCreate = true;
      this.$nextTick(() => {
        this.$refs.createComponent.initForm(row.minpro_course_id, "edit");
      });
    },
    checkSelectable(row) {
      return row.status !== 1;
    },
    handleBatchDelete() {
      if (this.selectionsIds.length === 0) return false;
      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        courseProductManagementApi
          .deleteCourseMinpro({
            minpro_course_ids: this.selectionsIds
          })
          .then((res) => {
            if (res.data.code === 0) {
              this.$message.success("操作成功");
              this.getList();
            } else {
              this.$message.error(res.data?.message || "操作失败");
            }
          });
      });
    },
    handleDelete(row) {
      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        courseProductManagementApi
          .deleteCourseMinpro({
            minpro_course_ids: [row.minpro_course_id]
          })
          .then((res) => {
            if (res.data.code === 0) {
              this.$message.success("操作成功");
              this.getList();
            } else {
              this.$message.error(res.data?.message || "操作失败");
            }
          });
      });
    },
    handleOffShelf(row) {
      // 询问是否下架 1 上架 2 未上架 3 已下架
      const comfirmTxt = row.status === 1 ? "是否下架?" : "是否上架?";
      this.$confirm(comfirmTxt, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        courseProductManagementApi
          .offShelf({
            minpro_course_id: row.minpro_course_id,
            status: row.status === 1 ? 3 : 1
          })
          .then((res) => {
            if (res.data.code === 0) {
              this.$message.success("操作成功");
              this.getList();
            } else {
              this.$message.error(res.data?.message || "操作失败");
            }
          });
      });
    },
    handleToFront(row) {
      // 询问是否推荐到首页 1 推荐 2 不推荐
      if (row.is_front === 2) {
        // 取消推荐到首页，直接弹出确认窗口
        this.$confirm("是否取消推荐到首页?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            this.submitToFront(row);
          })
          .catch(() => {
            row.is_front = 1; // 恢复开关状态
          });
      } else {
        // 推荐到首页，打开设置优先级弹窗
        this.currentCourse = row;
        this.frontPriorityDialogVisible = true;
      }
    },

    // 关闭弹窗
    closeFrontPriorityDialog() {
      this.frontPriorityDialogVisible = false;
      // 恢复开关状态
      if (this.currentCourse.is_front === 1) {
        this.currentCourse.is_front = 2;
      }
    },

    // 确认设置优先级
    confirmFrontPriority(data) {
      this.frontPriorityDialogVisible = false;
      this.currentCourse.front_index = data.frontIndex;
      this.submitToFront(this.currentCourse);
    },

    // 提交推荐到首页的API请求
    submitToFront(row) {
      const currentIsFront = row.is_front; // 保存当前的状态值
      courseProductManagementApi
        .toFront({
          is_front: row.is_front,
          minpro_course_id: row.minpro_course_id,
          front_index: row.front_index
        })
        .then((res) => {
          if (res.data.code === 0) {
            this.$message.success("操作成功");
            row.is_front = currentIsFront;
          } else {
            this.$message.error(res.data?.message || "操作失败");
            // 操作失败，恢复开关状态
            row.is_front = currentIsFront === 1 ? 2 : 1;
          }
        })
        .catch(() => {
          // 请求异常，恢复开关状态
          row.is_front = row.is_front === 1 ? 2 : 1;
          this.$message.error("操作失败");
        });
    },

    async getSelect(str) {
      const { data } = await getCourseConfigByType({}, str);
      if (data) {
        const arr = [];
        data.map((item) => {
          arr.push({
            id: str === "course_type" ? item.config_value : item.config_name,
            name: item.config_name
          });
        });
        // if (str === "course_type") {
        //   this.searchTitle[5].selectOptions = arr;
        //   this.searchTitle[5].selectOptions.unshift({
        //     name: "不限",
        //     id: undefined
        //   });
        //   this.course_type_list = arr;
        // }
        if (str === "course_level") {
          this.searchTitle[5].selectOptions = arr;
          this.searchTitle[5].selectOptions.unshift({ name: "不限", id: "" });
        }
        if (str === "course_class_type") {
          this.searchTitle[6].selectOptions = arr;
          this.searchTitle[6].selectOptions.unshift({
            name: "不限",
            id: ""
          });
        }
        if (str === "course_attribute") {
          this.course_attribute_list = data;
        }
        if (str === "course_genre") {
          this.course_genre_list = data;
        }
      }
    },
    getStudentStateList(data) {
      const arr = [];
      const arr2 = [];
      studentInforApi.getStudentState(data).then((res) => {
        for (const key in res.data) {
          arr.push({ name: res.data[key], id: key });
          arr2.push({ name: res.data[key], id: key });
        }
        this.searchTitle[7].selectOptions = arr;
        arr.push({ name: "意向客户", id: "intention" });
        arr.push({ name: "游客", id: "visitor" });
        this.searchTitle[7].selectOptions.unshift({ name: "不限", id: "" });

        this.searchTitle[9].selectOptions = arr2;
        arr2.push({ name: "意向客户", id: "intention" });
        arr2.push({ name: "游客", id: "visitor" });
        this.searchTitle[9].selectOptions.unshift({ name: "不限", id: "" });
      });
    },
    getList() {
      const { justDepartment } = this.search;
      this.loading = true;
      // this.list = [];
      // this.total = 0;
      courseProductManagementApi
        .getCourseMinproList({
          page: this.page,
          page_size: this.pageSize,
          just_department: justDepartment ? 1 : 0,
          ...this.search,
          department_id: justDepartment ? this.school_id : undefined,
          allowIntentionSale: this.search.sales_to === "intention" ? 1 : 2,
          allowVisitorSale: this.search.sales_to === "visitor" ? 1 : 2
        })
        .then((res) => {
          if (res.data.code === 0) {
            const list = res.data?.data?.results || [];
            list.map((item) => {
              item.sales_to_text = item.sales_to
                .map((item2) => {
                  if (sales_to_list[item2]) {
                    return sales_to_list[item2];
                  }
                })
                .join("、");
              item.view_to_text = item.view_to
                .map((item2) => {
                  if (sales_to_list[item2]) {
                    return sales_to_list[item2];
                  }
                })
                .join("、");
            });
            this.list = list;
            this.total = res.data?.data?.count || 0;
            // this.$nextTick(() => {
            //   this.$refs.table.doLayout();
            // });
          } else {
            this.$message.error(res.data?.message || "获取列表失败");
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 分页
    currentChange(val) {
      console.log(val);
      this.page = val;
      this.getList();
    },
    sizeChange(val) {
      this.page = 1;
      this.pageSize = val;
      this.getList();
    },
    handleSelectionChange(list) {
      // this.selections = list;
      this.selectionsIds = list.map((item) => item.minpro_course_id);
      // this.start_use = list.filter((item) => item.is_enabled === 1).length;
      // this.stop_use = list.filter((item) => item.is_enabled === 2).length;
      console.log(this.selectionsIds);
    },
    reset() {
      this.search = {
        status: "",
        is_front: "",
        course_name: "",
        course_no: "",
        course_year: "",
        course_level: "",
        lesson_type: "",
        is_enabled: "",
        sales_to: "",
        view_to: "",
        justDepartment: false
      };
      this.page = 1;
      this.pageSize = 10;
      this.clearSelection();
      this.getList();
    },
    searchStaff() {
      this.page = 1;
      this.clearSelection();
      this.getList();
    },
    clearSelection() {
      this.$nextTick(() => {
        this.$refs.table.clearSelection();
      });
    },
    addCourse() {
      this.dialogCreate = true;
    },

    getRowKeys(row) {
      return row.id;
    },
    goCourseAttribute(row) {
      if (this.$_has({ m: "course", o: "department_list" })) {
        this.dialogSpecAttr = row.course_attribute;
        this.com_course_id = row.link_course_id;
        this.dialogSpecShow = true;
      }
    },
    toInfo(row) {
      if (this.$_has({ m: "course", o: "detail" })) {
        this.$router.push({
          path: `/courseManagementUpdate`,
          query: { id: row.id }
        });
      }
    }
  }
};
</script>
<style lang="less" scoped>
/deep/ .el-message-box__message {
  padding: 40px;
}
/deep/ .el-message-box {
  height: 100px;
  width: 400px !important;
}
.vessel {
  ::v-deep .el-table td:last-child {
    border-right-color: transparent !important;
  }
  ::v-deep .el-table__header th:last-child {
    border-right-color: transparent !important;
  }
}
.attribute {
  width: 29px;
  height: 23px;
  cursor: pointer;
  vertical-align: middle;
}
.tg-row--height {
  width: 100%;
}
.on_type {
  display: flex;
  align-items: center;
  .green_pie {
    width: 6px;
    height: 6px;
    background: #2d80ed;
    border-radius: 3px;
    margin-right: 7px;
  }
  .gray_pie {
    width: 6px;
    height: 6px;
    background: #96a7bd;
    border-radius: 3px;
    margin-right: 7px;
  }
}

/deep/ button.disabled {
  background-color: #ccc;
  border: 1px solid #ccc;
  color: #fff;
}
::v-deep .el-table__body-wrapper {
  height: calc(100% - 46px);
  .empty-container {
    position: absolute;
    top: 50%;
    left: 50%;
  }
  .loading-container {
    position: absolute;
    top: 30%;
    left: 1%;
    background: transparent;
    .box {
      height: 100%;
    }
  }
}
</style>
