<template>
  <div class="connect-record-statistics">
    <tg-search
      class="connect-search"
      :searchTitle.sync="search_title"
      :form.sync="searchForm"
      :showNum="1"
      :isExport="isExport"
      @reset="reset"
      @educe="exportExcel"
      @search="search"
      :loadingState="exportLoading"
      :defaultSpread="false"
      ref="search"
    ></tg-search>
    <div class="tg-table__box statistics-table">
      <el-table
        ref="table"
        :data="processedList"
        tooltip-effect="dark"
        class="tg-table conversion-table"
        v-loading="false"
        :height="height + 'px'"
        border
        :span-method="objectSpanMethod"
        :row-class-name="tableRowClassName"
      >
        <el-table-column
          align="center"
          prop="area_name"
          label="区域"
          min-width="100"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.area_name }}</span>
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          prop="department_name"
          label="校区"
          min-width="100"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.department_name }}</span>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          v-for="(item, index) in table_header"
          :key="index"
          :prop="item.prop"
          :label="item.label"
          min-width="100"
        >
          <template v-if="item.children">
            <el-table-column
              v-for="(item1, index) in item.children"
              :key="index"
              :prop="item1.prop"
              :label="item1.label"
              min-width="100"
            >
              <el-table-column
                v-for="(item2, index) in table_header_children"
                :key="index"
                :prop="item2.prop"
                :label="item2.label"
                min-width="100"
              >
                <template slot-scope="scope">
                  <span>{{
                    getChannelValue(scope.row, item1.prop, item2.prop)
                  }}</span>
                </template>
              </el-table-column>
            </el-table-column>
          </template>
          <template v-else>
            <el-table-column
              v-for="(item2, index) in table_header_children"
              :key="index"
              :prop="item2.prop"
              :label="item2.label"
              min-width="100"
            >
              <template slot-scope="scope">
                <span>{{
                  getChannelValue(scope.row, item.prop, item2.prop)
                }}</span>
              </template>
            </el-table-column>
          </template>
        </el-table-column>
        <template slot="empty">
          <div style="margin-top: 2%">
            <loading v-if="loading"></loading>
            <div class="empty-container" v-else>暂无数据～</div>
          </div>
        </template>
      </el-table>
    </div>
  </div>
</template>
<script>
import quickTime from "@/public/quickTime";
import loading from "../loading";
export default {
  data() {
    return {
      exportLoading: false,
      searchForm: {
        date_range: []
      },
      isExport: false,
      search_title: [
        {
          props: "date_range",
          label: "时间范围",
          type: "date",
          show: true,
          selectOptions: [],
          has_options: true
        }
      ],

      page: 1,
      list: [],
      processedList: [],
      spanMap: {},
      staffConnectVisible: false,
      connectSearch: {},
      loading: false,
      height: 0,
      table_header_children: [
        {
          label: "有效信息",
          prop: "valid_num"
        },
        {
          label: "到店",
          prop: "audition_num"
        },
        {
          label: "试听",
          prop: "audition_num"
        },
        {
          label: "转化",
          prop: "new_num"
        }
      ],
      table_header: [
        {
          label: "线上推广",
          prop: "online_promotion",
          children: [
            {
              label: "线上推广-大众点评（美团）",
              prop: "dianping"
            },
            {
              label: "线上推广-品牌推广",
              prop: "brand"
            },
            {
              label: "线上渠道平台",
              prop: "platform"
            },
            {
              label: "线上腾讯推广",
              prop: "tencent"
            }
          ]
        },
        {
          label: "线下-市场地面推广",
          prop: "market_ground",
          children: [
            {
              label: "线下-市场地面推广-线下市场地面推广",
              prop: "market_ground"
            }
          ]
        },
        {
          label: "线下-市场地面推广",
          prop: "market_ground"
        },
        {
          label: "线下-校区自主活动",
          prop: "campus_activity"
        },
        {
          label: "线下-户外广告",
          prop: "outdoor_ad"
        },
        {
          label: "线下-转介绍",
          prop: "referral"
        },
        {
          label: "线下-自然进店",
          prop: "natural_in"
        },
        {
          label: "线下-内部员工推荐",
          prop: "other"
        },
        {
          label: "其他",
          prop: "other"
        },
        {
          label: "合计",
          prop: "total"
        }
      ]
    };
  },
  components: {
    loading
  },
  watch: {
    school_id: {
      handler(val) {
        if (this.$_has({ m: "front", o: "channelList" })) {
          this.search();
        }
      },
      deep: true
    }
  },
  created() {
    this.height = window.innerHeight - 230;
  },
  mounted() {
    if (this.$_has({ m: "front", o: "channelList" })) {
      this.setSearchDefault();
      this.search();
    }
    if (this.$_has({ m: "front", o: "channelExport" })) {
      this.isExport = true;
    }
  },
  computed: {
    school_id() {
      return this.$store.getters.doneGetSchoolId;
    }
  },
  methods: {
    search() {
      this.page = 1;
      const q = this.getSearch();
      if (
        this.searchForm.date_range === null ||
        this.searchForm.date_range.length === 0
      ) {
        this.$message.error("请选择日期");
        return;
      }
      this.getStatisticsList(q);
    },

    async getStatisticsList(search) {
      this.loading = true;
      // const res = await reportApi.getConversionList(search);
      // this.list = res.data.data || [];

      // 模拟数据
      this.list = [
        {
          area_id: "1",
          area_name: "华东区",
          department_id: "101",
          department_name: "模拟数据",
          // 线上推广渠道数据
          dianping_valid_num: 45,
          dianping_audition_num: 32,
          dianping_new_num: 18,
          brand_valid_num: 28,
          brand_audition_num: 22,
          brand_new_num: 12,
          platform_valid_num: 35,
          platform_audition_num: 28,
          platform_new_num: 15,
          tencent_valid_num: 52,
          tencent_audition_num: 38,
          tencent_new_num: 22,
          // 线下推广渠道数据
          market_ground_valid_num: 38,
          market_ground_audition_num: 30,
          market_ground_new_num: 16,
          campus_activity_valid_num: 42,
          campus_activity_audition_num: 35,
          campus_activity_new_num: 20,
          outdoor_ad_valid_num: 25,
          outdoor_ad_audition_num: 18,
          outdoor_ad_new_num: 8,
          referral_valid_num: 65,
          referral_audition_num: 58,
          referral_new_num: 45,
          natural_in_valid_num: 32,
          natural_in_audition_num: 25,
          natural_in_new_num: 12,
          other_valid_num: 15,
          other_audition_num: 12,
          other_new_num: 6,
          // 区域合计
          dept_valid_total: 377,
          dept_audition_total: 298,
          dept_new_total: 174,
          // 全区域合计
          region_valid_total: 755,
          region_audition_total: 598,
          region_new_total: 348
        },
        {
          area_id: "1",
          area_name: "华东区",
          department_id: "102",
          department_name: "模拟数据1",
          // 线上推广渠道数据
          dianping_valid_num: 38,
          dianping_audition_num: 28,
          dianping_new_num: 15,
          brand_valid_num: 22,
          brand_audition_num: 18,
          brand_new_num: 10,
          platform_valid_num: 30,
          platform_audition_num: 24,
          platform_new_num: 13,
          tencent_valid_num: 45,
          tencent_audition_num: 32,
          tencent_new_num: 18,
          // 线下推广渠道数据
          market_ground_valid_num: 32,
          market_ground_audition_num: 25,
          market_ground_new_num: 14,
          campus_activity_valid_num: 35,
          campus_activity_audition_num: 28,
          campus_activity_new_num: 16,
          outdoor_ad_valid_num: 20,
          outdoor_ad_audition_num: 15,
          outdoor_ad_new_num: 7,
          referral_valid_num: 55,
          referral_audition_num: 48,
          referral_new_num: 38,
          natural_in_valid_num: 28,
          natural_in_audition_num: 22,
          natural_in_new_num: 10,
          other_valid_num: 12,
          other_audition_num: 10,
          other_new_num: 5,
          // 区域合计
          dept_valid_total: 377,
          dept_audition_total: 298,
          dept_new_total: 174,
          // 全区域合计
          region_valid_total: 755,
          region_audition_total: 598,
          region_new_total: 348
        },
        {
          area_id: "2",
          area_name: "华南区",
          department_id: "201",
          department_name: "模拟数据2",
          // 线上推广渠道数据
          dianping_valid_num: 42,
          dianping_audition_num: 35,
          dianping_new_num: 20,
          brand_valid_num: 25,
          brand_audition_num: 20,
          brand_new_num: 11,
          platform_valid_num: 33,
          platform_audition_num: 26,
          platform_new_num: 14,
          tencent_valid_num: 48,
          tencent_audition_num: 36,
          tencent_new_num: 21,
          // 线下推广渠道数据
          market_ground_valid_num: 35,
          market_ground_audition_num: 28,
          market_ground_new_num: 15,
          campus_activity_valid_num: 40,
          campus_activity_audition_num: 32,
          campus_activity_new_num: 18,
          outdoor_ad_valid_num: 22,
          outdoor_ad_audition_num: 16,
          outdoor_ad_new_num: 8,
          referral_valid_num: 58,
          referral_audition_num: 52,
          referral_new_num: 42,
          natural_in_valid_num: 30,
          natural_in_audition_num: 24,
          natural_in_new_num: 11,
          other_valid_num: 13,
          other_audition_num: 11,
          other_new_num: 5,
          // 区域合计
          dept_valid_total: 378,
          dept_audition_total: 300,
          dept_new_total: 174,
          // 全区域合计
          region_valid_total: 755,
          region_audition_total: 598,
          region_new_total: 348
        }
      ];

      this.processTableData();
      this.loading = false;
    },

    // 处理表格数据，添加合计行
    processTableData() {
      if (!this.list || this.list.length === 0) {
        this.processedList = [];
        return;
      }

      // 按区域分组
      const groupedData = {};
      this.list.forEach((item) => {
        const areaId = item.area_id;
        if (!groupedData[areaId]) {
          groupedData[areaId] = [];
        }
        groupedData[areaId].push(item);
      });

      const processedData = [];
      // 总合计数据从任意一条记录中获取（因为所有记录的region_*_total都是相同的）
      const firstRecord = this.list[0];
      const totalValidNum = firstRecord.region_valid_total || 0;
      const totalAuditionNum = firstRecord.region_audition_total || 0;
      const totalNewNum = firstRecord.region_new_total || 0;
      const totalTrainingNewNum = firstRecord.region_training_new_total || 0;

      // 处理每个区域的数据
      Object.keys(groupedData).forEach((areaId) => {
        const areaData = groupedData[areaId];

        // 添加该区域的所有数据行
        areaData.forEach((item) => {
          processedData.push({
            ...item,
            rowType: "data"
          });
        });

        // 区域合计使用dept_*_total字段
        // const areaValidTotal = areaData[0].dept_valid_total || 0;
        // const areaAuditionTotal = areaData[0].dept_audition_total || 0;
        // const areaNewTotal = areaData[0].dept_new_total || 0;
        // const areaTrainingNewTotal = areaData[0].dept_training_new_total || 0;

        // 添加区域合计行
        // processedData.push({
        //   area_id: areaId,
        //   area_name: areaData[0].area_name,
        //   department_name: "合计",
        //   employee_name: "",
        //   valid_num: areaValidTotal,
        //   audition_num: areaAuditionTotal,
        //   new_num: areaNewTotal,
        //   training_new_num: areaTrainingNewTotal,
        //   rowType: "areaTotal"
        // });
      });

      // 添加总合计行（使用region_*_total字段）
      processedData.push({
        area_name: "合计",
        department_name: "",
        employee_name: "",
        valid_num: totalValidNum,
        audition_num: totalAuditionNum,
        new_num: totalNewNum,
        training_new_num: totalTrainingNewNum,
        rowType: "grandTotal"
      });

      this.processedList = processedData;
      this.calculateSpanMap();
    },

    // 计算合并单元格的映射
    calculateSpanMap() {
      this.spanMap = {};
      const areaGroupedData = {};
      const deptGroupedData = {};

      // 按区域和校区分组计算
      this.processedList.forEach((item, index) => {
        if (item.rowType === "data") {
          const areaId = item.area_id;
          const deptId = item.department_id;

          // 区域分组
          if (!areaGroupedData[areaId]) {
            areaGroupedData[areaId] = [];
          }
          areaGroupedData[areaId].push(index);

          // 校区分组
          if (!deptGroupedData[deptId]) {
            deptGroupedData[deptId] = [];
          }
          deptGroupedData[deptId].push(index);
        }
      });

      // 设置区域列的合并（第0列）
      Object.values(areaGroupedData).forEach((indexes) => {
        if (indexes.length > 0) {
          // 第一行显示区域名称，后续行隐藏
          this.spanMap[`${indexes[0]}_0`] = [indexes.length, 1];
          for (let i = 1; i < indexes.length; i++) {
            this.spanMap[`${indexes[i]}_0`] = [0, 0];
          }
        }
      });

      // 设置校区列的合并（第1列）
      Object.values(deptGroupedData).forEach((indexes) => {
        if (indexes.length > 0) {
          // 第一行显示校区名称，后续行隐藏
          this.spanMap[`${indexes[0]}_1`] = [indexes.length, 1];
          for (let i = 1; i < indexes.length; i++) {
            this.spanMap[`${indexes[i]}_1`] = [0, 0];
          }
        }
      });
    },

    // 合并单元格的方法
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      const key = `${rowIndex}_${columnIndex}`;

      // 区域合计行的合并逻辑
      if (row.rowType === "areaTotal") {
        if (columnIndex === 1) {
          // 第二列（校区）和第三列（市场）合并
          return [1, 2];
        } else if (columnIndex === 2) {
          // 第三列被合并到第二列，隐藏
          return [0, 0];
        }
        return [1, 1];
      }

      // 总合计行的合并逻辑
      if (row.rowType === "grandTotal") {
        if (columnIndex === 0) {
          // 前三列（区域、校区、市场）合并
          return [1, 3];
        } else if (columnIndex === 1 || columnIndex === 2) {
          // 第二列和第三列被合并到第一列，隐藏
          return [0, 0];
        }
        return [1, 1];
      }

      // 普通数据行的区域列和校区列合并
      if (columnIndex === 0 || columnIndex === 1) {
        const span = this.spanMap[key];
        if (span) {
          return span;
        }
      }

      return [1, 1];
    },

    // 表格行类名
    tableRowClassName({ row, rowIndex }) {
      if (row.rowType === "areaTotal") {
        return "area-total-row";
      } else if (row.rowType === "grandTotal") {
        return "grand-total-row";
      }
      return "";
    },

    async exportExcel() {
      // 前端通过html导出表格
      // 创建不可见的虚拟表格
      this.exportLoading = true;
      const table = document.createElement("table");
      table.id = "virtualTable";
      table.style.display = "none";
      const head_html = $(".conversion-table .el-table__header-wrapper>table")
        .children("thead")
        .html();
      const body_html = $(".conversion-table .el-table__body-wrapper>table")
        .children("tbody")
        .html();
      table.innerHTML = head_html + body_html;
      // 将表格转换为工作表，使用 { raw: true } 选项来保留原始值
      const worksheet = XLSX.utils.table_to_sheet(table, { raw: true });
      // 设置统一的列宽
      const columnWidth = { wpx: 120 }; // 120像素宽度，每列相同
      worksheet["!cols"] = Array(Object.keys(worksheet).length).fill(
        columnWidth
      );

      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, `市场周转化率`);
      XLSX.writeFile(workbook, `市场周转化率.xlsx`);
      this.exportLoading = false;
      this.$message.success("导出成功");
      table.remove();
    },
    reset() {
      this.searchForm = {
        date_range: []
      };
      this.setSearchDefault();
      const q = this.getSearch();
      this.getStatisticsList(q);
    },
    currentChange(val) {
      this.page = val;
      const q = this.getSearch();
      this.getStatisticsList(q);
    },

    getSearch() {
      const newForm = JSON.parse(JSON.stringify(this.searchForm));
      const query = {
        page: this.page,
        department_id: this.school_id,
        ...newForm
      };

      if (this.searchForm.date_range && this.searchForm.date_range.length) {
        const [start, end] = query.date_range;
        query.start_time = start || "";
        query.end_time = end || "";
        delete query.date_range;
      }
      return query;
    },

    setSearchDefault() {
      const week = quickTime.GetDate("month");
      this.$set(this.searchForm, "date_range", week);
    },
    getChannelValue(row, channelProp, metricProp) {
      // 处理合计行
      if (row.rowType === "areaTotal" || row.rowType === "grandTotal") {
        if (channelProp === "total") {
          if (metricProp === "valid_num") {
            return row.valid_num || 0;
          } else if (metricProp === "audition_num") {
            return row.audition_num || 0;
          } else if (metricProp === "new_num") {
            return row.new_num || 0;
          }
        }
        return 0;
      }

      // 处理普通数据行
      if (channelProp === "total") {
        // 合计列：计算所有渠道的合计
        let total = 0;
        const channels = [
          "dianping",
          "brand",
          "platform",
          "tencent",
          "market_ground",
          "campus_activity",
          "outdoor_ad",
          "referral",
          "natural_in",
          "other"
        ];

        channels.forEach((channel) => {
          const fieldName = `${channel}_${metricProp}`;
          total += row[fieldName] || 0;
        });

        return total;
      } else {
        // 具体渠道列：直接获取对应字段的值
        const fieldName = `${channelProp}_${metricProp}`;
        return row[fieldName] || 0;
      }
    }
  }
};
</script>
<style lang="less" scoped>
.connect-record-statistics {
  margin-top: 16px;
}
.connect-search {
  margin: 0 6px;
  width: calc(100% - 12px);
}
::v-deep .statistics-table {
  width: calc(100% - 12px);
  border: 1px solid @base-color;
  // .el-table th.is-leaf {
  //   border: none;
  // }
  .el-table td:last-child {
    border-right: none;
  }
  .el-table {
    padding: 0;
    border: none;
  }
  th {
    background: @light-color;
  }
  .el-table th:first-child > .cell {
    padding-left: 26px;
  }
  .el-table td:first-child > .cell {
    padding-left: 26px;
  }
  .el-table__body-wrapper {
    height: calc(100% - 46px);
    .empty-container {
      // position: absolute;
      top: 50%;
      left: 50%;
      height: 80px;
    }
    .loading-container {
      position: absolute;
      top: 15%;
      left: 1%;
      width: 100%;
      background: transparent;
      .box {
        height: 100%;
      }
    }
  }

  // 区域合计行样式
  .area-total-row {
    background-color: #f5f7fa !important;
    font-weight: bold !important;

    td {
      background-color: #f5f7fa !important;
      font-weight: bold !important;
    }
  }

  // 总合计行样式
  .grand-total-row {
    background-color: #e6f7ff !important;
    font-weight: bold !important;
    color: #1890ff !important;

    td {
      background-color: #e6f7ff !important;
      font-weight: bold !important;
      color: #1890ff !important;
    }
  }
}

// Tooltip 自定义样式
::v-deep .custom-tooltip {
  max-width: 400px;

  .el-tooltip__popper {
    max-width: 400px !important;

    .el-tooltip__popper[x-placement^="top"] {
      margin-bottom: 8px;
    }
  }
}

// 表头问号图标样式
::v-deep .el-table th .cell {
  display: flex;
  align-items: center;
  justify-content: center;

  .el-icon-question {
    font-size: 14px;
    margin-left: 4px;
    color: #409eff;
    cursor: pointer;

    &:hover {
      color: #66b1ff;
    }
  }
}
</style>
