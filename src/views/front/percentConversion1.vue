<template>
  <div class="container">
    <tg-search
      :searchTitle.sync="search_title"
      :form.sync="searchForm"
      :showNum="2"
      :isExport="isExport"
      @reset="reset"
      @educe="exportExcel"
      @search="searchVal"
      :loadingState="exportLoading"
      :searchLoadingState="searchLoading"
      :hasDefaultDate="true"
    ></tg-search>
    <chartTable
      ref="chartTable"
      :type="'transform'"
      :newData="newData"
      :searchDate="searchForm.date_range"
      :requestData="getSearch()"
      v-if="newData"
    ></chartTable>
  </div>
</template>

<script>
import chartTable from "@/components/front/chartTable.vue";
import quickTime from "@/public/quickTime";
import frontApi from "@/api/front";
import { downLoadFile } from "@/public/downLoadFile";

export default {
  components: { chartTable },
  props: ["tabindex"],
  data() {
    return {
      search_title: [
        {
          props: "advisor_id",
          label: "销售负责人",
          type: "course_staff",
          show: true,
          selectOptions: []
        },
        {
          props: "date_range",
          label: "日期范围",
          type: "date",
          show: true,
          selectOptions: [],
          has_options: true
        },
        {
          props: "transfer_end_status",
          label: "转化过程",
          type: "select",
          show: false,
          selectOptions: [
            { name: "不限", id: "no_limit" },
            { name: "未到店", id: "4" },
            { name: "已到店", id: "7" },
            { name: "无效数据", id: "pending" },
            { name: "转化成功", id: "end" }
          ]
        },
        {
          props: "transfer_start_status",
          label: "到",
          type: "select",
          show: false,
          selectOptions: [
            { name: "不限", id: "no_limit" },
            { name: "未到店", id: "4" },
            { name: "已到店", id: "7" },
            { name: "无效数据", id: "pending" },
            { name: "转化成功", id: "end" }
          ]
        },
        {
          props: "valid_status",
          label: "客户有效性",
          type: "select",
          show: true,
          selectOptions: [
            { name: "不限", id: undefined },
            { name: "待定", id: "pending" },
            { name: "有效", id: "valid" },
            { name: "无效", id: "invalid" }
          ]
        }
      ],
      searchForm: {
        advisor_id: "",
        date_range: [],
        transfer_start_status: "",
        transfer_end_status: "",
        valid_status: undefined
      },
      newData: "",
      isExport: false,
      exportLoading: false,
      searchLoading: false
    };
  },
  computed: {
    ids() {
      return this.$store.getters.doneGetSchoolId;
    }
  },
  watch: {
    ids(val) {
      if (this.$_has({ m: "front", o: "transfer_rate_list" })) {
        this.searchVal();
      }
    },
    tabindex(val) {
      if (+val === 3) {
        this.$refs.chartTable.resize();
      }
    }
  },
  mounted() {
    if (this.$_has({ m: "front", o: "transfer_rate_list" })) {
      this.setSearchDefault();
      this.searchVal();
      if (this.$_has({ m: "front", o: "transfer_rate_export" })) {
        this.isExport = true;
      }
    }
  },
  methods: {
    reset() {
      this.searchForm = {
        advisor_id: "",
        date_range: [],
        transfer_start_status: "",
        transfer_end_status: "",
        valid_status: undefined
      };
      this.newData = [];
      this.setSearchDefault();
      this.searchVal();
    },
    exportExcel() {
      this.exportLoading = true;
      frontApi
        .exportCustomerTransferRateExport(this.getSearch())
        .then((res) => {
          downLoadFile(res, "转化率统计列表");
          this.exportLoading = false;
        });
    },
    setSearchDefault() {
      const pastThirty = quickTime.GetDate("pastThirty");
      this.$set(this.searchForm, "date_range", pastThirty);
    },
    getSearch() {
      const data = {
        department_id: this.ids,
        valid_status: this.searchForm.valid_status,
        start_time:
          this.searchForm.date_range && this.searchForm.date_range.length > 0
            ? this.searchForm.date_range[0]
            : "",
        end_time:
          this.searchForm.date_range && this.searchForm.date_range.length > 0
            ? this.searchForm.date_range[1]
            : "",
        transfer_start_status: this.searchForm.transfer_start_status,
        transfer_end_status: this.searchForm.transfer_end_status
      };
      if (this.searchForm.advisor_id) {
        data.advisor_id = [this.searchForm.advisor_id];
      }
      return data;
    },
    searchVal() {
      this.searchLoading = true;
      frontApi.getCustomerTransferRate(this.getSearch()).then((res) => {
        if (+res.status === 200 && +res.data.code === 0) {
          this.newData = res.data.data ?? [];
          this.searchLoading = false;
        }
      });
    }
  }
};
</script>

<style></style>
