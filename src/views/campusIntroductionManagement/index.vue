<template>
  <div class="campusIntroductionManagement">
    <tg-search
      :searchTitle.sync="search_title"
      :form.sync="params"
      :showNum="3"
      @reset="reset"
      @search="searchVal"
    ></tg-search>
    <el-row class="tg-box--margin tg-shadow--margin tg-row--height">
      <el-button
        type="plain"
        class="tg-button--plain"
        @click="handleAddTeacher"
        v-has="{ m: 'campusIntroduction', o: 'create' }"
        >新增</el-button
      >
      <!-- 批量启用 -->
      <el-button
        type="plain"
        class="tg-button--plain"
        @click="handleBatchEnable"
        :disabled="selectedTeachers.length === 0"
        v-has="{ m: 'campusIntroduction', o: 'enableOrDisable' }"
        >批量启用
        {{
          selectedTeachers.length > 0 ? `(${selectedTeachers.length})` : ""
        }}</el-button
      >
      <!-- 批量停用 -->
      <el-button
        type="plain"
        class="tg-button--plain"
        @click="handleBatchDisable"
        :disabled="selectedTeachers.length === 0"
        v-has="{ m: 'campusIntroduction', o: 'enableOrDisable' }"
        >批量停用
        {{
          selectedTeachers.length > 0 ? `(${selectedTeachers.length})` : ""
        }}</el-button
      >
    </el-row>
    <div class="tg-table__box">
      <div class="tg-box--border"></div>
      <el-table
        ref="table"
        :data="list"
        tooltip-effect="dark"
        class="tg-table"
        :cell-style="{ borderRightColor: '#e0e6ed75' }"
        :header-cell-style="{ borderRightColor: '#e0e6ed75' }"
        border
        @selection-change="handleSelectionChange"
      >
        <!-- 多选框列 -->
        <el-table-column
          type="selection"
          width="55"
          align="center"
        ></el-table-column>
        <el-table-column
          v-for="item in table_title"
          :key="item.prop"
          :prop="item.prop"
          :label="item.label"
          :width="item.width"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <span v-if="item.prop === 'status'">
              {{ scope.row.status === 1 ? "已启用" : "已停用" }}
            </span>
            <span v-else-if="item.prop === 'teacher_team_count'">
              <el-button type="text" @click="handleTeacherTeam(scope.row)">
                {{ scope.row.teacher_team_count }}
              </el-button>
            </span>
            <span v-else-if="item.prop === 'highlight_count'">
              <el-button
                type="text"
                @click="
                  (student_evaluation_type = 2), handleCampusImage(scope.row)
                "
              >
                {{ scope.row.highlight_count }}
              </el-button>
            </span>
            <span v-else-if="item.prop === 'student_review_count'">
              <el-button
                type="text"
                @click="
                  (student_evaluation_type = 3),
                    handleStudentEvaluationImage(scope.row)
                "
              >
                {{ scope.row.student_review_count }}
              </el-button>
            </span>
            <span v-else>{{ scope.row[item.prop] }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="260" fixed="right">
          <template slot-scope="scope">
            <el-button
              type="text"
              @click="handleToggleStatus(scope.row)"
              v-has="{ m: 'campusIntroduction', o: 'enableOrDisable' }"
            >
              {{ scope.row.status === 1 ? "停用" : "启用" }}
            </el-button>
            <el-button
              type="text"
              @click="handleEdit(scope.row)"
              v-if="scope.row.status === 2"
              v-has="{ m: 'campusIntroduction', o: 'update' }"
              >编辑</el-button
            >
            <el-button
              type="text"
              @click="handleDelete(scope.row)"
              v-if="scope.row.status === 2"
              v-has="{ m: 'campusIntroduction', o: 'delete' }"
              >删除
            </el-button>
          </template>
        </el-table-column>
        <template slot="empty">
          <div style="margin-top: 15%">
            <TgLoading v-if="loading"></TgLoading>
            <div class="empty-container" v-else>暂无数据～</div>
          </div>
        </template>
      </el-table>
      <div class="tg-pagination">
        <span class="el-pagination__total">共 {{ total }} 条</span>
        <el-pagination
          background
          layout="sizes,prev,pager,next,jumper"
          :total="total"
          :page-size="params.page_size"
          :current-page="params.page"
          @current-change="currentChange"
          @size-change="sizeChange"
          :page-sizes="[10, 20, 50, 100]"
        ></el-pagination>
      </div>
    </div>

    <!-- 编辑弹窗 -->
    <edit-dialog
      :visible="editDialogVisible"
      :campus-data="currentCampus"
      @close="handleCloseDialog"
      @success="handleEditSuccess"
      :isEdit="isEdit"
      @getCampusInfo="getCampusInfo"
      :department_info="department_info"
      ref="editDialog"
    ></edit-dialog>

    <!-- 师资团队弹窗 -->
    <teacher-team-dialog
      :visible="teacherTeamDialogVisible"
      @close="handleCloseTeacherTeamDialog"
      :department_id="teacher_team_department_id"
    ></teacher-team-dialog>

    <!-- 学员评价图片弹窗 -->
    <student-evaluation-dialog
      :visible="studentEvaluationDialogVisible"
      :campus-data="currentCampusModule"
      :student_evaluation_type="student_evaluation_type"
      @close="handleCloseStudentEvaluationDialog"
      @getModuleList="getModuleList"
      @createModule="createModule"
      ref="studentEvaluationDialog"
      :currentCampusModuleDetail="currentCampusModuleDetail"
      @editModule="handleEditModule"
      @getModuleDetail="getModuleDetail"
      @deleteModule="handleDeleteModule"
    ></student-evaluation-dialog>
  </div>
</template>

<script>
import EditDialog from "./components/editDialog.vue";
import TeacherTeamDialog from "./components/teacherTeamDialog.vue";
import StudentEvaluationDialog from "./components/studentEvaluationDialog.vue";
import departmentIntroduceApi from "@/api/departmentIntroduce";
export default {
  name: "campusIntroductionManagement",
  components: {
    EditDialog,
    TeacherTeamDialog,
    StudentEvaluationDialog
  },
  data() {
    return {
      search_title: [
        {
          props: "department_name",
          label: "校区名称",
          show: true,
          width: 160,
          type: "input"
        },
        {
          props: "status",
          label: "状态",
          show: true,
          width: 160,
          type: "select",
          selectOptions: [
            { name: "不限", id: "" },
            { name: "已启用", id: 1 },
            { name: "已停用", id: 2 }
          ]
        }
      ],
      params: {
        status: "",
        department_name: "",
        page: 1,
        page_size: 10
      },
      list: [],
      total: 0,
      loading: false,
      editDialogVisible: false,
      teacherTeamDialogVisible: false,
      uploadDialogVisible: false,
      uploadEditable: true,
      studentEvaluationDialogVisible: false,
      studentEvaluationEditable: true,
      currentCampus: {},
      table_title: [
        {
          prop: "department_name",
          label: "校区",
          width: 160
        },
        {
          prop: "department_address",
          label: "校区地址",
          width: 160
        },
        {
          prop: "consult_phone",
          label: "咨询电话",
          width: 160
        },
        {
          prop: "department_intro",
          label: "校区简介",
          width: 160
        },
        {
          prop: "teacher_team_count",
          label: "师资团队",
          width: 160
        },
        {
          prop: "highlight_count",
          label: "校区亮点",
          width: 160
        },
        {
          prop: "student_review_count",
          label: "学员评价",
          width: 160
        },
        {
          prop: "status",
          label: "状态",
          width: 160
        }
      ],
      selectedTeachers: [],
      isEdit: false,
      student_evaluation_type: 2,
      department_info: {},
      teacher_team_department_id: "",
      currentCampusModule: {},
      currentCampusModuleDetail: {}
    };
  },
  computed: {
    schoolIds() {
      return this.$store.getters.doneGetSchoolId;
    }
  },
  watch: {
    schoolIds() {
      this.getList();
    }
  },
  methods: {
    reset() {
      this.params = {
        page: 1,
        page_size: 10,
        department_name: "",
        status: ""
      };
      this.getList();
    },
    searchVal() {
      this.getList();
    },
    getList() {
      this.loading = true;
      departmentIntroduceApi
        .getList({
          ...this.params,
          department_id: this.schoolIds
        })
        .then((res) => {
          if (res.code === 0) {
            this.list = res.data.results;
            this.total = res.data.count;
            this.loading = false;
          } else {
            this.$message.error(res.message);
            this.loading = false;
          }
        });
    },
    handleEdit(row) {
      // this.currentCampus = { ...row };
      departmentIntroduceApi
        .getDetail({
          id: row.id
        })
        .then((res) => {
          if (res.code === 0) {
            this.currentCampus = res.data;
            this.isEdit = true;
            this.editDialogVisible = true;
          } else {
            this.$message.error(res.message);
          }
        });
    },
    handleCloseDialog() {
      this.editDialogVisible = false;
      this.currentCampus = {};
    },
    async handleEditSuccess(data) {
      let res;
      data.media_list = JSON.stringify(data.media_list);
      // 更新列表数据
      if (this.isEdit) {
        res = await departmentIntroduceApi.editDepartmentIntroduce(data);
      } else {
        res = await departmentIntroduceApi.createDepartmentIntroduce(data);
      }
      if (res.code === 0) {
        this.$message.success(this.isEdit ? "编辑成功" : "新增成功");
        this.editDialogVisible = false;
        this.$refs.editDialog.loading = false;
        this.getList();
      } else {
        this.$refs.editDialog.loading = false;
        this.$message.error(res.message);
      }
    },
    currentChange(page) {
      this.params.page = page;
      this.getList();
    },
    sizeChange(size) {
      this.params.page_size = size;
      this.getList();
    },
    handleTeacherTeam(row) {
      this.teacher_team_department_id = row.department_id;
      this.teacherTeamDialogVisible = true;
    },
    handleCloseTeacherTeamDialog() {
      this.teacherTeamDialogVisible = false;
      this.teacher_team_department_id = "";
    },
    handleCampusImage(row) {
      // 预览模式打开上传弹窗
      this.student_evaluation_type = 2;
      this.handleStudentEvaluationUpload(row, false);
    },
    handleStudentEvaluationImage(row) {
      // 预览模式打开学员评价弹窗
      this.student_evaluation_type = 3;
      this.handleStudentEvaluationUpload(row, false);
    },
    // 启用/停用状态切换
    handleToggleStatus(row) {
      const action = row.status === 1 ? "停用" : "启用";
      this.$confirm(`确定要${action}该校区吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        // 模拟API调用
        // row.status = row.status === 1 ? 0 : 1;
        departmentIntroduceApi
          .enableOrDisable({
            ids: [row.id],
            status: row.status === 1 ? 2 : 1
          })
          .then((res) => {
            if (res.code === 0) {
              this.$message.success(`${action}成功`);
              this.getList();
            } else {
              this.$message.error(res.message);
            }
          });
      });
    },
    // 打开学员评价弹窗
    handleStudentEvaluationUpload(row, editable = true) {
      // this.currentCampusModule = { ...row };
      this.department_introduce_id = row.id;
      this.getModuleList();
      this.studentEvaluationEditable = editable;
      this.studentEvaluationDialogVisible = true;
    },
    getModuleList(
      params = {
        creator_ids: [],
        start_time: "",
        end_time: "",
        create_time: "",
        department_introduce_id: this.department_introduce_id,
        page: 1,
        page_size: 10
      }
    ) {
      if (params.create_time) {
        params.start_time = params.create_time[0];
        params.end_time = params.create_time[1];
        delete params.create_time;
      }
      departmentIntroduceApi
        .getModuleList({
          ...params,
          department_id: this.schoolIds,
          module_type: this.student_evaluation_type,
          department_introduce_id: this.department_introduce_id
        })
        .then((res) => {
          if (res.code === 0) {
            this.currentCampusModule = res.data;
          } else {
            this.$message.error(res.message);
          }
        });
    },
    // 选择变化
    handleSelectionChange(selection) {
      this.selectedTeachers = selection;
    },
    handleAddTeacher() {
      this.isEdit = false;
      this.editDialogVisible = true;
    },
    handleBatchDisable() {
      this.$confirm("确定要批量停用吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        departmentIntroduceApi
          .enableOrDisable({
            ids: this.selectedTeachers.map((item) => item.id),
            status: 2
          })
          .then((res) => {
            if (res.code === 0) {
              this.$message.success("批量停用成功");
              this.getList();
            } else {
              this.$message.error(res.message);
            }
          });
      });
    },
    handleBatchEnable() {
      this.$confirm("确定要批量启用吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        departmentIntroduceApi
          .enableOrDisable({
            ids: this.selectedTeachers.map((item) => item.id),
            status: 1
          })
          .then((res) => {
            if (res.code === 0) {
              this.$message.success("批量启用成功");
              this.getList();
            } else {
              this.$message.error(res.message);
            }
          });
      });
    },
    handleCloseStudentEvaluationDialog() {
      this.studentEvaluationDialogVisible = false;
      this.currentCampus = {};
      this.student_evaluation_type = 2;
    },
    getCampusInfo(department_id) {
      departmentIntroduceApi
        .getCampusInfo({
          department_id
        })
        .then((res) => {
          if (res.code === 0) {
            this.department_info = res.data;
          } else {
            this.$message.error(res.message);
          }
        });
    },
    handleDelete(row) {
      this.$confirm("确定要删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        departmentIntroduceApi
          .deleteDepartmentIntroduce({
            id: row.id
          })
          .then((res) => {
            if (res.code === 0) {
              this.$message.success("删除成功");
              this.getList();
            } else {
              this.$message.error(res.message);
            }
          });
      });
    },
    async createModule(form, params) {
      form.media_list = JSON.stringify(form.media_list);
      const res = await departmentIntroduceApi.createModule({
        ...form,
        ...params,
        department_introduce_id: this.department_introduce_id,
        module_type: this.student_evaluation_type
      });
      if (res.code === 0) {
        this.$message.success("新增成功");
        this.getModuleList(params);
        this.getList();
        this.$refs.studentEvaluationDialog.handleCloseStudentEvaluationDialog();
      } else {
        this.$message.error(res.message);
      }
    },
    async handleEditModule(form, params) {
      form.media_list = JSON.stringify(form.media_list);
      const res = await departmentIntroduceApi.updateModule({
        ...form,
        ...params
      });
      if (res.code === 0) {
        this.$message.success("编辑成功");
        this.getModuleList(params);
        this.getList();
        this.$refs.studentEvaluationDialog.handleCloseStudentEvaluationDialog();
      } else {
        this.$message.error(res.message);
      }
    },
    handleDeleteModule(id, params) {
      this.$confirm("确定要删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        departmentIntroduceApi
          .deleteModule({
            module_content_id: id
          })
          .then((res) => {
            if (res.code === 0) {
              this.$message.success("删除成功");
              this.getModuleList(params);
              this.getList();
            } else {
              this.$message.error(res.message);
            }
          });
      });
    },
    // 获取模块详情
    getModuleDetail(id) {
      departmentIntroduceApi
        .getModuleDetail({
          module_content_id: id
        })
        .then((res) => {
          if (res.code === 0) {
            this.currentCampusModuleDetail = res.data;
          } else {
            this.$message.error(res.message);
          }
        });
    }
  },
  mounted() {
    this.getList();
  }
};
</script>
<style lang="scss" scoped>
.campusIntroductionManagement {
  padding: 20px 0;
}
</style>
