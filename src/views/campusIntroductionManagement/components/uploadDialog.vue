<template>
  <div class="upload-dialog">
    <div class="upload-content">
      <!-- 多媒体上传区域 -->
      <div class="media-grid">
        <!-- 已上传的媒体项 -->
        <div v-for="(item, index) in mediaList" :key="index" class="media-item">
          <div class="media-wrapper">
            <img
              v-if="item.image_url"
              :src="item.image_url"
              class="media-thumbnail"
            />
            <div v-else-if="item.video_url" class="video-thumbnail">
              <img
                :src="
                  item.image_url ||
                  'https://tg-prod.oss-cn-beijing.aliyuncs.com/16d607c6-6cfd-43e4-b9f6-a7454c5d1b60.png'
                "
                class="media-thumbnail"
              />
              <div class="video-play-icon">
                <i class="el-icon-video-play"></i>
              </div>
            </div>

            <div class="media-actions">
              <el-button
                type="primary"
                icon="el-icon-view"
                circle
                size="mini"
                @click.stop="previewMedia(item)"
              ></el-button>
              <el-button
                v-if="editable"
                type="danger"
                icon="el-icon-delete"
                circle
                size="mini"
                @click.stop="deleteMedia(index)"
              ></el-button>
            </div>
          </div>
        </div>

        <!-- 上传按钮（当未达到最大数量时显示） -->
        <div
          v-if="mediaList.length < maxCount && editable"
          class="media-item upload-item"
        >
          <el-upload
            class="media-uploader"
            action="#"
            :show-file-list="false"
            ref="uploadImgs"
            :before-upload="beforeUpload"
            :http-request="uploadImg"
            :disabled="uploading"
          >
            <!-- 上传进度圆环 -->
            <el-progress
              v-if="uploading"
              type="circle"
              :percentage="uploadPercentage"
              :width="100"
              class="upload-progress"
            >
              <template #default>
                <div class="progress-content">
                  <span>{{ uploadPercentage }}%</span>
                </div>
              </template>
            </el-progress>

            <div v-else class="upload-placeholder">
              <i class="el-icon-plus upload-icon"></i>
              <div class="upload-text">上传文件</div>
            </div>
          </el-upload>
        </div>
      </div>

      <!-- 描述 -->
      <div class="upload-desc" v-if="maxWidth && maxHeight">
        <div class="upload-tip">
          建议上传：{{ maxWidth }} * {{ maxHeight }}，支持{{
            allowedImageFormats.join(",")
          }},{{ allowedVideoFormats.join(",") }}格式
        </div>
      </div>
    </div>

    <!-- 视频预览对话框 -->
    <el-dialog
      :visible.sync="videoVisible"
      title="视频预览"
      width="70%"
      center
      :append-to-body="true"
    >
      <div class="video-preview-container">
        <video
          v-if="currentVideoUrl"
          :src="currentVideoUrl"
          :poster="currentVideoPoster"
          controls
          style="width: 100%; max-height: 70vh"
        ></video>
      </div>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog
      :visible.sync="imageVisible"
      title="图片预览"
      width="70%"
      center
      :append-to-body="true"
    >
      <div class="image-preview-container">
        <img :src="currentImageUrl" class="preview-image" alt="预览图片" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "UploadDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    media_list: {
      type: Array,
      default: () => []
    },
    editable: {
      type: Boolean,
      default: true
    },
    maxCount: {
      type: Number,
      default: 1
    },
    allowedFormats: {
      type: Array,
      default: () => [
        "jpeg",
        "png",
        "webp",
        "jpg",
        "mp4",
        "mpeg",
        "webm",
        "mov"
      ]
    },
    allowImages: {
      type: Boolean,
      default: true
    },
    allowVideos: {
      type: Boolean,
      default: true
    },
    maxWidth: {
      type: Number
    },
    maxHeight: {
      type: Number
    }
  },
  data() {
    return {
      mediaList: [],
      videoVisible: false,
      currentVideoUrl: "",
      currentVideoPoster: "",
      imageVisible: false,
      currentImageUrl: "",
      loading: false,
      butLoading: false,
      uploading: false,
      uploadPercentage: 0,
      posterUploading: false,
      posterUploadPercentage: 0
    };
  },
  computed: {
    // 根据props计算允许的图片格式
    allowedImageFormats() {
      const imageFormats = ["jpeg", "png", "webp", "jpg"];
      return this.allowImages
        ? this.allowedFormats.filter((format) => imageFormats.includes(format))
        : [];
    },
    // 根据props计算允许的视频格式
    allowedVideoFormats() {
      const videoFormats = ["mp4", "mpeg", "webm", "mov"];
      return this.allowVideos
        ? this.allowedFormats.filter((format) => videoFormats.includes(format))
        : [];
    },
    // 获取所有允许的格式
    allAllowedFormats() {
      return [...this.allowedImageFormats, ...this.allowedVideoFormats];
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.initMediaInfo();
      }
    },
    media_list: {
      handler() {
        this.mediaList = [];
        console.log(this.media_list, "media_list");
        this.initMediaInfo();
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    this.Oss.getAliyun();
  },
  methods: {
    // 获取上传提示文本
    getUploadTipText() {
      const typeTexts = [];
      if (this.allowImages && this.allowedImageFormats.length > 0) {
        typeTexts.push("图片");
      }
      if (this.allowVideos && this.allowedVideoFormats.length > 0) {
        typeTexts.push("视频");
      }

      const typeText = typeTexts.length > 0 ? typeTexts.join("或") : "文件";
      const formatText = this.allAllowedFormats.join(",");

      return `请上传${typeText}（仅支持上传${formatText}）`;
    },

    // 初始化媒体信息
    initMediaInfo() {
      if (this.media_list) {
        this.mediaList = this.media_list;
      } else {
        this.resetMediaInfo();
      }
    },

    // 重置媒体信息
    resetMediaInfo() {
      this.mediaList = [];
    },

    handleClose() {
      this.$emit("close");
      this.videoVisible = false;
      this.imageVisible = false;
      this.currentVideoUrl = "";
      this.currentVideoPoster = "";
      this.currentImageUrl = "";
    },

    previewMedia(item) {
      if (item.video_url) {
        this.currentVideoUrl = item.video_url;
        this.currentVideoPoster = item.image_url || "";
        this.videoVisible = true;
      } else if (item.image_url) {
        this.previewImage(item.image_url);
      }
    },

    previewImage(imageUrl) {
      this.currentImageUrl = imageUrl;
      this.imageVisible = true;
    },

    deleteMedia(index) {
      this.mediaList.splice(index, 1);
      this.$emit("change", this.mediaList);
    },

    beforeUpload(file) {
      // 检查是否已达到最大上传数量
      if (this.mediaList.length >= this.maxCount) {
        this.$message.error(`最多只能上传 ${this.maxCount} 个文件`);
        return false;
      }

      const suffix = file.name.match(/[^.]+$/)[0].toLowerCase();
      const isImage = this.allowedImageFormats.includes(suffix);
      const isVideo = this.allowedVideoFormats.includes(suffix);

      if (!isImage && !isVideo) {
        this.$message.error(
          `文件类型不支持，请上传${this.allAllowedFormats.join(",")}`
        );
        return false;
      }

      // 如果不允许上传图片但文件是图片
      if (isImage && !this.allowImages) {
        this.$message.error("当前不允许上传图片");
        return false;
      }

      // 如果不允许上传视频但文件是视频
      if (isVideo && !this.allowVideos) {
        this.$message.error("当前不允许上传视频");
        return false;
      }

      const isLt2M = file.size / 1024 / 1024 < 20;
      if (!isLt2M) {
        this.$message.error("上传文件大小不能超过 20MB!");
        return false;
      }
      return true;
    },

    uploadImg(upload) {
      this.butLoading = true;
      const f = upload.file;
      const suffix = f.name.match(/[^.]+$/)[0].toLowerCase(); // 确保转换为小写
      const fileName = `campus_media_${Date.now()}.${suffix}`;
      const copyFile = new File([f], fileName, { type: f.type });

      // 设置上传状态和初始进度
      this.uploading = true;
      this.uploadPercentage = 0;

      // 创建进度回调函数
      const progressCallback = (percentage) => {
        this.uploadPercentage = Math.round(percentage);
      };

      // 使用项目中的OSS上传方法
      this.Oss.uploadFile(copyFile, progressCallback)
        .then((res) => {
          console.log(res);
          this.butLoading = false;

          if (res.code === 0) {
            // 创建新的媒体对象
            const newMedia = {
              type: this.allowedImageFormats.includes(suffix)
                ? "image"
                : "video",
              image_url: "",
              video_url: "",
              width: 0,
              height: 0,
              size: f.size
            };

            if (this.allowedImageFormats.includes(suffix)) {
              newMedia.image_url = res.url;
            } else if (this.allowedVideoFormats.includes(suffix)) {
              newMedia.video_url = res.url;
              // 为视频生成默认缩略图
              newMedia.image_url = "";
            }
            if (newMedia.image_url) {
              this.getImageDimensions(newMedia.image_url).then((res) => {
                newMedia.width = res.width;
                newMedia.height = res.height;
              });
            }
            // 添加到媒体列表
            this.mediaList.push(newMedia);
            this.$emit("change", this.mediaList);
            this.$message.success("上传成功");
          } else {
            this.$message.error("上传失败：" + (res.message || "未知错误"));
          }
        })
        .catch((error) => {
          console.error("上传失败:", error);
          this.$message.error("上传失败");
        })
        .finally(() => {
          this.butLoading = false;
          this.uploading = false;
          this.uploadPercentage = 0;
        });
    },
    getImageDimensions(url) {
      return new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = () => {
          resolve({
            width: img.naturalWidth,
            height: img.naturalHeight
          });
        };
        img.onerror = reject;
        img.src = url;
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.upload-content {
  padding: 20px 0;
}

.media-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.media-item {
  width: 120px;
  height: 120px;
  position: relative;
  border-radius: 6px;
  overflow: hidden;
}

.upload-item {
  border: 2px dashed #d9d9d9;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: border-color 0.3s;

  &:hover {
    border-color: #409eff;
  }
}

.media-uploader {
  width: 100%;
  height: 100%;

  ::v-deep .el-upload {
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #8c939d;
}

.upload-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.upload-text {
  font-size: 12px;
}

.media-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.video-thumbnail {
  position: relative;
  width: 100%;
  height: 100%;
}

.video-play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.upload-desc {
  //   text-align: center;
  color: #8c939d;
  font-size: 12px;
}

.upload-tip {
  margin-bottom: 5px;
}

.upload-count {
  font-weight: bold;
  color: #606266;
}

.media-wrapper {
  position: relative;
  width: 100%;
  height: 100%;

  &:hover .media-actions {
    opacity: 1;
  }
}

.media-actions {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s;
  gap: 10px;
  pointer-events: none;

  .el-button {
    pointer-events: auto;
  }
}

.image-preview-container,
.video-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;

  .preview-image {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
  }
}

/* 进度条相关样式 */
.upload-progress {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100px;
  height: 100px;
}

.progress-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  span {
    font-size: 14px;
    font-weight: bold;
  }
}

::v-deep .el-progress__text {
  display: none;
}

::v-deep .el-progress-circle {
  width: 100% !important;
  height: 100% !important;
}

::v-deep .el-dialog__body {
  padding: 20px;
}
</style>
