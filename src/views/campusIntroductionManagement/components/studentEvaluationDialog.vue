<template>
  <el-dialog
    :visible="visible"
    :title="title"
    width="1200px"
    :before-close="handleClose"
    :append-to-body="true"
    class="teacher-team-dialog"
    v-if="visible"
  >
    <div class="teacher-team-content">
      <tg-search
        :searchTitle.sync="search_title"
        :form.sync="searchParams"
        :showNum="5"
        @reset="reset"
        @search="searchVal"
      ></tg-search>
      <el-row class="tg-box--margin tg-shadow--margin tg-row--height">
        <el-button
          type="plain"
          class="tg-button--plain"
          @click="handleAdd"
          v-has="{ m: 'campusIntroduction', o: 'createModule' }"
        >
          新增</el-button
        >
      </el-row>
      <div class="tg-table__box">
        <div class="tg-box--border"></div>
        <el-table
          ref="table"
          :data="teacherList"
          tooltip-effect="dark"
          class="tg-table"
          :cell-style="{ borderRightColor: '#e0e6ed75' }"
          :header-cell-style="{ borderRightColor: '#e0e6ed75' }"
          border
        >
          <el-table-column
            v-for="item in table_title"
            :key="item.prop"
            :prop="item.prop"
            :label="item.label"
            :width="item.width"
            :show-overflow-tooltip="item.showOverflowTooltip"
            align="center"
          >
            <template slot-scope="scope">
              <span v-if="item.prop === 'media_list'">
                <el-image
                  :src="
                    scope.row.media_list
                      ? scope.row.media_list[0].image_url
                      : ''
                  "
                  fit="cover"
                  style="width: 50px; height: 50px"
                  :preview-src-list="
                    scope.row.media_list
                      ? scope.row.media_list.map((item) => item.image_url)
                      : []
                  "
                />
              </span>
              <span v-else>{{ scope.row[item.prop] }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="operation"
            label="操作"
            width="100"
            fixed="right"
            align="center"
          >
            <template slot-scope="scope">
              <el-button
                type="text"
                @click="handleEdit(scope.row)"
                v-has="{ m: 'campusIntroduction', o: 'updateModule' }"
                >编辑</el-button
              >
              <el-button
                type="text"
                @click="handleDelete(scope.row)"
                v-has="{ m: 'campusIntroduction', o: 'deleteModule' }"
                >删除</el-button
              >
            </template>
          </el-table-column>
          <template slot="empty">
            <div style="margin-top: 15%">
              <TgLoading v-if="loading"></TgLoading>
              <div class="empty-container" v-else>暂无数据～</div>
            </div>
          </template>
        </el-table>

        <div class="tg-pagination">
          <span class="el-pagination__total">共 {{ total }} 条</span>
          <el-pagination
            background
            layout="sizes,prev,pager,next,jumper"
            :total="total"
            :page-size="searchParams.page_size"
            :current-page="searchParams.page"
            @current-change="currentChange"
            @size-change="sizeChange"
            :page-sizes="[10, 20, 50, 100]"
          ></el-pagination>
        </div>
      </div>
      <add-student-height
        ref="addStudentHeight"
        v-if="addStudentHeightVisible"
        :visible.sync="addStudentHeightVisible"
        :campus-data="campusData"
        @submit="handleSubmit"
        :isEdit="isEdit"
        :currentCampusModuleDetail="currentCampusModuleDetail"
      />
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button class="tg-button--plain" type="plain" @click="handleClose">
        关闭
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import addStudentHeight from "./addStudentHeight.vue";
export default {
  name: "studentEvaluationDialog",
  components: {
    addStudentHeight
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    campusData: {
      type: Object,
      default: () => ({})
    },
    student_evaluation_type: {
      type: Number,
      default: 2
    },
    currentCampusModuleDetail: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    title() {
      if (this.student_evaluation_type === 2) {
        return "校区亮点";
      } else {
        return "学生评价";
      }
    }
  },
  data() {
    return {
      addStudentHeightVisible: false,
      loading: false,
      teacherList: [],
      total: 0,
      searchParams: {
        creator_ids: [],
        start_time: "",
        end_time: "",
        create_time: "",
        page: 1,
        page_size: 10
      },
      search_title: [
        {
          props: "creator_ids",
          label: "创建人",
          show: true,
          width: 160,
          type: "course_staff",
          is_leave: true
        },
        {
          props: "create_time",
          label: "创建时间",
          show: true,
          width: 160,
          type: "custom_date"
        }
      ],
      table_title: [
        {
          prop: "content",
          label: "内容",
          width: 600,
          showOverflowTooltip: true
        },
        {
          prop: "media_list",
          label: "图片",
          showOverflowTooltip: false
        },
        {
          prop: "creator_name",
          label: "创建人",
          showOverflowTooltip: false
        },
        {
          prop: "created_at",
          label: "创建时间",
          showOverflowTooltip: false
        }
      ]
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.searchParams.page = 1;
        this.searchParams.page_size = 10;
        this.searchParams.creator_ids = [];
        this.searchParams.start_time = "";
        this.searchParams.end_time = "";
        this.searchParams.create_time = "";
        // this.getTeacherList();
      }
    },
    campusData: {
      handler(val) {
        this.teacherList = val.results;
        this.total = val.count;
      },
      deep: true
    }
  },
  methods: {
    // 获取教师列表
    getTeacherList() {
      this.loading = true;
      this.$emit("getModuleList", this.searchParams);
      this.loading = false;
      // 实际使用时替换为真实的API调用
      // teacherApi.getTeacherList({
      //   ...this.searchParams,
      //   campus_id: this.campusData.id
      // }).then(res => {
      //   this.teacherList = res.data.list;
      //   this.total = res.data.total;
      //   this.loading = false;
      // });
    },

    // 重置搜索
    reset() {
      this.searchParams = {
        creator_ids: [],
        start_time: "",
        end_time: "",
        page: 1,
        page_size: 10
      };
      this.getTeacherList();
    },

    // 搜索
    searchVal() {
      this.searchParams.page = 1;
      this.getTeacherList();
    },

    // 分页改变
    currentChange(page) {
      this.searchParams.page = page;
      this.getTeacherList();
    },

    // 每页条数改变
    sizeChange(size) {
      this.searchParams.page_size = size;
      this.searchParams.page = 1;
      this.getTeacherList();
    },

    // 关闭弹窗
    handleClose() {
      this.$emit("close");
    },
    handleAdd() {
      this.isEdit = false;
      this.addStudentHeightVisible = true;
    },
    handleSubmit(form) {
      if (this.isEdit) {
        this.$emit("editModule", form, this.searchParams);
      } else {
        this.$emit("createModule", form, this.searchParams);
      }
    },
    handleEdit(row) {
      this.isEdit = true;
      this.$emit("getModuleDetail", row.module_content_id);
      this.addStudentHeightVisible = true;
    },
    handleCloseStudentEvaluationDialog() {
      this.addStudentHeightVisible = false;
      this.$refs.addStudentHeight.handleClose();
    },
    handleDelete(row) {
      this.$emit("deleteModule", row.module_content_id, this.searchParams);
    }
  }
};
</script>

<style lang="scss" scoped>
.teacher-team-dialog {
  ::v-deep .el-dialog__body {
    padding: 20px;
  }
}

.teacher-team-content {
  .tg-table__box {
    margin-top: 16px;
  }
}
::v-deep .el-dialog__body {
  overflow: auto;
}
</style>
