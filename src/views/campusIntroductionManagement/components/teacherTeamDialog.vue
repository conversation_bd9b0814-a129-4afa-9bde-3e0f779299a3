<template>
  <el-dialog
    :visible="visible"
    title="师资团队"
    width="1200px"
    :before-close="handleClose"
    :append-to-body="true"
    class="teacher-team-dialog"
  >
    <nedawTeacher
      :visible="false"
      :department_id="department_id"
      v-if="visible"
    />
    <!-- <div class="teacher-team-content">
      <tg-search
        :searchTitle.sync="search_title"
        :form.sync="searchParams"
        :showNum="5"
        @reset="reset"
        @search="searchVal"
      ></tg-search>

      <div class="tg-table__box">
        <div class="tg-box--border"></div>
        <el-table
          ref="table"
          :data="teacherList"
          tooltip-effect="dark"
          class="tg-table"
          :cell-style="{ borderRightColor: '#e0e6ed75' }"
          :header-cell-style="{ borderRightColor: '#e0e6ed75' }"
          border
        >
          <el-table-column
            v-for="item in table_title"
            :key="item.prop"
            :prop="item.prop"
            :label="item.label"
            :width="item.width"
          >
            <template slot-scope="scope">
              <span v-if="item.prop === 'status'">
                {{ scope.row.status === 1 ? "在职" : "离职" }}
              </span>
              <span v-else-if="item.prop === 'teacher_image'">
                <el-image
                  :src="scope.row.teacher_image"
                  fit="cover"
                  style="width: 50px; height: 50px"
                  :preview-src-list="[scope.row.teacher_image]"
                />
              </span>
              <span v-else>{{ scope.row[item.prop] }}</span>
            </template>
          </el-table-column>
          <template slot="empty">
            <div style="margin-top: 15%">
              <TgLoading v-if="loading"></TgLoading>
              <div class="empty-container" v-else>暂无数据～</div>
            </div>
          </template>
        </el-table>

        <div class="tg-pagination">
          <span class="el-pagination__total">共 {{ total }} 条</span>
          <el-pagination
            background
            layout="sizes,prev,pager,next,jumper"
            :total="total"
            :page-size="searchParams.pageSize"
            :current-page="searchParams.page"
            @current-change="currentChange"
            @size-change="sizeChange"
            :page-sizes="[10, 20, 50, 100]"
          ></el-pagination>
        </div>
      </div>
    </div> -->

    <span slot="footer" class="dialog-footer">
      <el-button class="tg-button--plain" type="plain" @click="handleClose">
        关闭
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import nedawTeacher from "../../nedawTeacherManagement";
export default {
  name: "TeacherTeamDialog",
  props: {
    department_id: {
      type: String,
      default: ""
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  components: {
    nedawTeacher
  },
  data() {
    return {};
  },
  methods: {
    handleClose() {
      this.$emit("close");
    }
  }
};
</script>

<style lang="scss" scoped>
.teacher-team-dialog {
  ::v-deep .el-dialog__body {
    padding: 20px;
  }
}

.teacher-team-content {
  .tg-table__box {
    margin-top: 16px;
  }
}
::v-deep .el-dialog__body {
  overflow: auto;
}
</style>
