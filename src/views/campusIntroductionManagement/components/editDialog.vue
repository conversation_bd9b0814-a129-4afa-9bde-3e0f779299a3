<template>
  <el-dialog
    :visible="visible"
    :title="isEdit ? '编辑校区' : '新增校区'"
    width="600px"
    :before-close="handleClose"
    :append-to-body="true"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="130px"
      class="campus-form"
      size="small"
    >
      <el-form-item label="校区" prop="campus_name">
        <el-input
          v-model="form.department_name"
          readonly
          placeholder="请选择校区"
          :disabled="isEdit"
          @click.native="handleSelectSchool"
          style="width: 368px"
        >
          <span
            slot="suffix"
            class="endText"
            :style="{
              background: '#2d80ed'
            }"
          >
            选择
          </span>
        </el-input>
      </el-form-item>

      <el-form-item label="校区地址" prop="department_address">
        <el-input
          v-model="form.department_address"
          placeholder="请输入校区地址"
          :maxlength="100"
          disabled
          show-word-limit
        ></el-input>
      </el-form-item>

      <el-form-item label="咨询电话" prop="consult_phone">
        <el-input
          v-model="form.consult_phone"
          placeholder="请输入咨询电话"
          maxlength="11"
        ></el-input>
      </el-form-item>

      <el-form-item label="校区简介" prop="department_intro">
        <el-input
          v-model="form.department_intro"
          type="textarea"
          placeholder="请输入校区简介"
          :rows="4"
          maxlength="500"
          show-word-limit
        ></el-input>
      </el-form-item>
      <el-form-item label="上传图片/视频" prop="media_list">
        <upload-dialog
          @change="handleUploadSuccess"
          :maxCount="5"
          :media_list="form.media_list"
          :maxWidth="750"
          :maxHeight="422"
          v-if="visible"
        ></upload-dialog>
      </el-form-item>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button class="tg-button--plain" type="plain" @click="handleClose">
        取消
      </el-button>
      <el-button
        class="tg-button--primary"
        type="primary"
        v-throttle="handleSubmit"
        :loading="loading"
      >
        保存
      </el-button>
    </span>
    <school-tree
      :flag.sync="school_tree_visible"
      v-if="visible"
      :id.sync="form.department_id"
      :name.sync="form.department_name"
      :controlSchool="true"
      :checkedControlSchool="true"
      :useData="usedCampusList"
      type="radio"
    >
    </school-tree>
  </el-dialog>
</template>

<script>
import schoolTree from "@/components/schoolTree/schoolTree";
import uploadDialog from "./uploadDialog.vue";
import departmentIntroduceApi from "@/api/departmentIntroduce";
export default {
  name: "EditDialog",
  components: {
    schoolTree,
    uploadDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    campusData: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    department_info: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      usedCampusList: [],
      school_tree_visible: false,
      loading: false,
      form: {
        campus_name: "",
        department_address: "",
        consult_phone: "",
        department_intro: "",
        department_id: "",
        department_name: "",
        media_list: []
      },
      rules: {
        department_name: [
          { required: true, message: "请输入校区名称", trigger: "blur" },
          {
            min: 1,
            max: 50,
            message: "校区名称长度在1到50个字符",
            trigger: "blur"
          }
        ],
        department_address: [
          { required: true, message: "请输入校区地址", trigger: "blur" }
        ],
        consult_phone: [
          { required: true, message: "请输入咨询电话", trigger: "blur" },
          {
            pattern: /^\d{11}$/,
            message: "请输入正确的电话号码格式",
            trigger: "blur"
          }
        ],
        department_intro: [
          { required: true, message: "请输入校区简介", trigger: "blur" },
          {
            min: 1,
            max: 500,
            message: "校区简介长度在1到500个字符",
            trigger: "blur"
          }
        ],
        media_list: [
          { required: true, message: "请上传图片/视频", trigger: "blur" }
        ]
      }
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.getFilledCampusList();
        this.initForm();
      } else {
        this.resetForm();
      }
    },
    campusData: {
      handler() {
        if (this.visible) {
          this.initForm();
        }
      },
      deep: true
    },
    department_info: {
      handler() {
        this.form.department_address = this.department_info.address;
      },
      deep: true
    },
    "form.department_id": {
      handler(newVal, oldVal) {
        console.log(newVal, oldVal, "newVal, oldVal");
        if (newVal && !this.isEdit) {
          this.$emit("getCampusInfo", newVal);
        }
      }
    }
  },
  methods: {
    // 初始化表单数据
    initForm() {
      if (this.isEdit) {
        this.form = {
          campus_name: this.campusData.campus_name || "",
          department_address: this.campusData.department_address || "",
          consult_phone: this.campusData.consult_phone || "",
          department_intro: this.campusData.department_intro || "",
          department_id: this.campusData.department_id || "",
          department_name: this.campusData.department_name || "",
          media_list: this.campusData.media_list || []
        };
      } else {
        this.resetForm();
      }
    },

    // 重置表单
    resetForm() {
      this.form = {
        campus_name: "",
        department_address: "",
        consult_phone: "",
        department_intro: "",
        department_id: "",
        department_name: "",
        media_list: []
      };
      if (this.$refs.form) {
        this.$refs.form.clearValidate();
      }
    },

    // 关闭弹窗
    handleClose() {
      this.$emit("close");
    },

    // 提交表单
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true;
          this.form.department_address = this.form.department_address.slice(
            0,
            100
          );
          this.$emit("success", {
            ...this.form,
            id: this.campusData.id
          });
        }
      });
    },
    handleUploadSuccess(data) {
      console.log(data);
      this.form.media_list = data;
    },
    confirmSchool(data) {
      console.log(data, "data");
      this.form.department_id = data.id;
      this.form.department_name = data.name;
    },
    handleSelectSchool() {
      if (this.isEdit) return;
      this.school_tree_visible = true;
    },
    getFilledCampusList() {
      departmentIntroduceApi.getFilledCampusList().then((res) => {
        if (res.code === 0) {
          this.usedCampusList = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.campus-form {
  .el-form-item {
    margin-bottom: 20px;
  }

  .el-input,
  .el-textarea {
    width: 100%;
  }
}

::v-deep .el-dialog__body {
  padding: 20px 20px 0 20px;
}

::v-deep .el-form-item__label {
  color: #1f2d3d;
  font-weight: 500;
}
.endText {
  width: 72px;
  height: 32px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  user-select: none;
  border-bottom-right-radius: 4px;
  border-top-right-radius: 4px;
  color: #fff;
}
</style>
