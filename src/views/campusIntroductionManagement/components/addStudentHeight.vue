<template>
  <el-dialog
    :visible="visible"
    :title="isEdit ? '编辑' : '新增'"
    width="600px"
    :before-close="handleClose"
    :append-to-body="true"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="130px"
      class="campus-form"
    >
      <el-form-item label="内容" prop="content">
        <el-input
          v-model="form.content"
          placeholder="请输入内容"
          type="textarea"
          :rows="2"
          show-word-limit
          maxlength="500"
          :autosize="{ minRows: 2 }"
        />
      </el-form-item>
      <el-form-item label="图片" prop="media_list">
        <upload-dialog
          @change="handleUploadSuccess"
          :max-count="9"
          :allow-images="true"
          :allow-videos="false"
          :allowed-formats="['jpeg', 'png', 'webp', 'jpg']"
          :media_list="currentCampusModuleDetail.media_list"
          :editable="true"
        />
        <div class="tips">
          请上传图片，图片数量不超过9张，图片格式为jpeg、png、webp、jpg，图片大小不超过20M。
        </div>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button class="tg-button--plain" type="plain" @click="handleClose">
        关闭
      </el-button>
      <el-button
        class="tg-button--primary"
        type="primary"
        v-throttle="handleSubmit"
        :loading="submitLoading"
      >
        确定
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import uploadDialog from "./uploadDialog.vue";
export default {
  name: "addStudentHeight",
  components: {
    uploadDialog
  },
  data() {
    return {
      submitLoading: false,
      submitTimeoutId: null,
      form: {
        content: "",
        media_list: [],
        module_content_id: null,
        module_type: null
      },
      rules: {
        media_list: [
          {
            required: true,
            message: "请上传图片",
            trigger: "change",
            validator: (rule, value, callback) => {
              if (!value || (Array.isArray(value) && value.length === 0)) {
                callback(new Error("请上传图片"));
              } else {
                callback();
              }
            }
          }
        ]
      }
    };
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    currentCampusModuleDetail: {
      type: Object,
      default: () => ({
        media_list: []
      })
    }
  },
  watch: {
    currentCampusModuleDetail: {
      handler(val) {
        if (this.isEdit && val) {
          this.form = {
            content: val.content || "",
            media_list: val.media_list || [],
            module_content_id: val.module_content_id,
            module_type: val.module_type
          };

          // 清除之前的验证状态
          this.$nextTick(() => {
            if (this.$refs.form) {
              this.$refs.form.clearValidate();
            }
          });
        }
      },
      deep: true
    }
  },
  methods: {
    handleClose() {
      this.$emit("update:visible", false);
      this.currentCampusModuleDetail.media_list = [];
      this.form = {
        content: "",
        media_list: [],
        module_content_id: null,
        module_type: null
      };

      // 清除表单验证状态
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.resetFields();
          this.$refs.form.clearValidate();
        }
      });
    },
    handleUploadSuccess(data) {
      console.log(data, "data");
      this.form.media_list = data;

      // 手动清除media_list字段的验证错误
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.validateField("media_list");
        }
      });
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.submitLoading = true;
          this.$emit("submit", this.form);

          // 监听提交成功事件，清除表单数据
          this.$once("submit-success", () => {
            clearTimeout(this.submitTimeoutId);
            this.resetFormData();
          });

          // 监听提交失败事件，关闭loading
          this.$once("submit-error", () => {
            clearTimeout(this.submitTimeoutId);
            this.submitLoading = false;
          });
        }
      });
    },

    // 重置表单数据的方法
    resetFormData() {
      this.form = {
        content: "",
        media_list: [],
        module_content_id: null,
        module_type: null
      };
      this.submitLoading = false;

      // 清除表单验证状态
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.resetFields();
          this.$refs.form.clearValidate();
        }
      });
    },

    // 供父组件调用的成功回调方法
    onSubmitSuccess() {
      this.submitLoading = false;
      this.resetFormData();
      this.$emit("update:visible", false);
    },

    // 供父组件调用的失败回调方法
    onSubmitError(errorMessage) {
      this.submitLoading = false;
      if (errorMessage) {
        this.$message.error(errorMessage);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .el-input__count {
  background-color: transparent;
}
.tips {
  font-size: 12px;
  color: #999;
  margin-top: -25px;
  // text-align: center;
  line-height: normal;
}
</style>
