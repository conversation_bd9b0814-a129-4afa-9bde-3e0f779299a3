<template>
  <!-- 新增 -->
  <el-dialog
    :title="type === 'create' ? '新增二维码' : '编辑二维码'"
    width="800px"
    :visible="true"
    :before-close="closeAdd"
    append-to-body
  >
    <el-form
      :model="form"
      :rules="rules"
      ref="ruleForm"
      label-width="80px"
      class="demo-ruleForm"
    >
      <el-form-item label="联系人" prop="contact_name">
        <el-input
          v-model.trim="form.contact_name"
          placeholder="请输入联系人"
          maxlength="10"
        ></el-input>
      </el-form-item>

      <el-form-item required label="负责校区">
        <el-button
          type="primary"
          size="small"
          @click="school_tree_visible = true"
          >选择校区</el-button
        >
        <school-tree
          :flag.sync="school_tree_visible"
          v-if="school_tree_visible"
          :id.sync="form.department_ids"
          @confirm="confirmDepartment"
          :type="'chooseSchool'"
          :use_store_options="true"
        >
        </school-tree>
      </el-form-item>
      <el-form-item class="form-item-cover" required label="上传图片">
        <el-upload
          action="#"
          list-type="picture-card"
          accept="image/*"
          :file-list="coverFileList"
          :limit="1"
          ref="uploadImgsBody"
          :http-request="(file) => uploadImg(file, 'coverFileList')"
        >
          <i slot="default" class="el-icon-plus"></i>
          <div class="el-upload-listitem" slot="file" slot-scope="{ file }">
            <el-image
              class="el-upload-list__item-thumbnail"
              :src="file.url"
              fit="cover"
              :preview-src-list="[file.url]"
            >
            </el-image>
            <span
              class="el-upload-list__item-delete"
              @click="handleRemove(file, 'coverFileList')"
            >
              <i class="el-icon-delete"></i>
            </span>
          </div>
        </el-upload>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button size="small" @click="closeAdd" class="tg-button--plain"
        >取消</el-button
      >
      <el-button
        type="primary"
        size="small"
        v-throttle="confirm"
        class="tg-button--primary"
        >确定</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import { v4 as uuidv4 } from "uuid";
import qrMiniCodeApi from "@/api/qrMiniCode";
import schoolTree from "@/components/schoolTree/schoolTree.vue";
export default {
  components: {
    schoolTree
  },
  props: {
    type: {
      type: String,
      default: "create"
    },
    infoData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      school_tree_visible: false,
      form: {
        status: 0,
        contact_name: "",
        department_ids: [],
        qr_code_url: ""
      },
      coverFileList: [],
      department_ids: "",
      rules: {
        contact_name: [
          { required: true, message: "请输入联系人", trigger: "blur" }
        ]
      }
    };
  },
  watch: {
    coverFileList(val) {
      if (val && val.length >= 1) {
        this.$nextTick(() => {
          $(".form-item-cover .el-upload--picture-card").hide();
        });
      } else {
        this.$nextTick(() => {
          $(".form-item-cover .el-upload--picture-card").show();
        });
      }
      if (this.coverFileList.length > 0) {
        this.form.qr_code_url = this.coverFileList[0].url;
      } else {
        this.form.qr_code_url = "";
      }
    }
  },
  created() {
    this.Oss.getAliyun();
    if (this.type === "edit") {
      this.form = {
        id: this.infoData.id,
        contact_name: this.infoData.contact_name,
        department_ids: this.infoData.department_ids,
        status: this.infoData.status,
        qr_code_url: this.infoData.qr_code_url
      };
      this.coverFileList = [
        {
          url: this.infoData.qr_code_url
        }
      ];
      this.department_ids = this.infoData.department_ids.join(",");
    }
  },
  methods: {
    confirmDepartment(data) {
      if (data) {
        console.log(data);
        this.form.department_ids = data.split(",");
        this.department_ids = data;
      } else {
        this.$message.error("请选择负责校区");
      }
    },
    uploadImg(upload, type) {
      const file = upload.file;
      const img = new Image();
      const reader = new FileReader();

      reader.onload = (e) => {
        img.src = e.target.result;
        img.onload = () => {
          let width = img.width;
          let height = img.height;

          // 如果图片宽度大于750px，进行等比例压缩
          if (width > 750) {
            const scale = width / 750;
            width = 750;
            height = Math.floor(height / scale);
          }

          // 使用canvas进行图片压缩
          const canvas = document.createElement("canvas");
          const ctx = canvas.getContext("2d");
          canvas.width = width;
          canvas.height = height;

          // 在canvas上绘制图片
          ctx.drawImage(img, 0, 0, width, height);

          // 获取压缩后的图片数据并转为blob
          canvas.toBlob(
            (blob) => {
              // 生成文件名
              const suffix = file.name.substring(
                file.name.lastIndexOf(".") + 1
              );
              const fileName = `${uuidv4()}.${suffix}`;

              // 创建新的File对象
              const compressedFile = new File([blob], fileName, {
                type: `image/${suffix === "jpg" ? "jpeg" : suffix}`,
                lastModified: Date.now()
              });

              // 上传到阿里云OSS
              this.Oss.uploadFile(compressedFile)
                .then((res) => {
                  if (res.code === 0) {
                    this[type].push({
                      name: res.objectKey,
                      url: res.url,
                      uid: file.uid
                    });
                  } else {
                    this.$message.error("上传图片失败");
                  }
                })
                .catch((err) => {
                  console.error("上传失败:", err);
                  this.$message.error("上传图片失败");
                });
            },
            file.type,
            0.9
          ); // 压缩质量0.9
        };
      };

      reader.readAsDataURL(file);
    },
    handleRemove(file, type) {
      this[type].map((item, index) => {
        if (file.uid === item.uid) {
          this[type].splice(index, 1);
        }
      });
    },
    closeAdd() {
      this.$emit("close");
    },
    confirm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.form.department_ids.length === 0) {
            this.$message.error("请选择负责校区");
            return false;
          }
          if (this.form.qr_code_url === "") {
            this.$message.error("请上传图片");
            return false;
          }
          qrMiniCodeApi
            .create(this.form)
            .then((res) => {
              if (res.data.code === 0) {
                this.$message.success("新增成功");
                this.$emit("confirm");
              } else {
                this.$message.error(res.data.message);
              }
            })
            .catch(() => {
              this.$message.error("新增失败");
            });
        } else {
          return false;
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .demo-ruleForm .el-form-item {
  margin-bottom: 20px;
}
/deep/ .el-upload-listitem {
  position: relative;
  height: 100%;
  .el-upload-list__item-delete {
    position: absolute;
    right: 10px;
    top: inherit;
    bottom: 10px;
    font-size: 12px;
    color: #f44336;
    width: 24px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    background: #ffffffb8;
    border-radius: 100%;
    display: block;
    cursor: pointer;
    &:hover {
      background: rgba(175, 174, 174, 0.69);
    }
  }
}
</style>
