<template>
  <div class="qr-code-management">
    <tgSearch
      :searchTitle.sync="searchTitle"
      @reset="resetForm"
      @search="query"
      :form.sync="form"
      class="tg-box--margin"
    ></tgSearch>
    <el-row style="margin-top: 16px" class="tg-row--margin">
      <el-col :span="8">
        <el-button
          type="plain"
          @click="createQRCode"
          class="tg-button--plain btn--left"
          v-has="{ m: 'qrCodeManagement', o: 'create' }"
        >
          新增</el-button
        >
      </el-col>
    </el-row>
    <div class="tg-table__box tg-box--margin" style="margin-left: 0px">
      <div class="tg-box--border"></div>
      <el-table
        :data="results"
        style="width: 100%"
        class="tg-table"
        :cell-style="{ borderRightColor: '#e0e6ed75' }"
        :header-cell-style="{ borderRightColor: '#e0e6ed75' }"
        border
      >
        <template v-for="(item, index) in table_title">
          <el-table-column
            v-if="item.show"
            :key="index"
            :prop="item.props"
            :label="item.label"
            :min-width="item.width"
            :fixed="item.fixed ? true : false"
            :sortable="item.sort"
            :show-overflow-tooltip="item.tooltip"
          >
            <template slot-scope="scope">
              <div v-if="item.props === 'contact_name'">
                {{ scope.row.contact_name }}
              </div>
              <div v-else-if="item.props === 'qr_code_url'">
                <div v-if="scope.row.qr_code_url" class="copy_name">
                  <el-image
                    style="width: 60px; height: 60px; margin-right: 10px"
                    :src="scope.row.qr_code_url"
                    fit="contain"
                    :preview-src-list="[scope.row.qr_code_url]"
                  ></el-image>
                  <el-button
                    class="tg-text--blue tg-table__name--ellipsis t-a-c"
                    type="text"
                    >{{ scope.row.qr_code_url }}</el-button
                  >
                  <div v-copy="scope.row.qr_code_url"></div>
                </div>
              </div>
              <div v-else-if="item.props === 'department_names'">
                <span
                  @click="goCourseAttribute(scope.row)"
                  style="color: #157df0; cursor: pointer"
                  >{{ scope.row?.department_names?.length || 0 }}校区</span
                >
              </div>
              <div v-else-if="item.props === 'status'">
                <el-switch
                  v-model="scope.row.status"
                  :active-value="1"
                  :inactive-value="2"
                  @change="changeStatus(scope.row)"
                ></el-switch>
              </div>
              <span v-else>{{ scope.row[scope.column.property] }}</span>
            </template>
          </el-table-column>
        </template>
        <el-table-column label="操作" width="140">
          <template slot-scope="scope">
            <el-button
              @click="edit(scope.row)"
              type="text"
              size="small"
              class="tg-text--blue tg-span__divide-line"
              v-has="{ m: 'qrCodeManagement', o: 'create' }"
              >编辑</el-button
            >
            <el-button
              @click="deletes(scope.row)"
              type="text"
              size="small"
              class="tg-text--blue tg-span__divide-line"
              v-has="{ m: 'qrCodeManagement', o: 'del' }"
              >删除</el-button
            >
          </template>
        </el-table-column>
        <template slot="empty">
          <div style="margin-top: 15%">
            <TgLoading v-if="loading"></TgLoading>
            <div class="empty-container" v-else>暂无数据～</div>
          </div>
        </template>
      </el-table>
      <div class="tg-pagination">
        <span class="el-pagination__total">共 {{ count }} 条</span>
        <el-pagination
          background
          layout="sizes,prev,pager,next,jumper"
          :total="count"
          :page-size="page_size"
          :current-page="page"
          @current-change="currentChange"
          @size-change="sizeChange"
          :page-sizes="[10, 20, 50, 100]"
        >
        </el-pagination>
      </div>
    </div>

    <create
      ref="create"
      @close="closeAdd"
      @confirm="confirm"
      v-if="dialogVisibleNewlyAdded"
      :infoData="currRow"
      :type="type"
    ></create>
    <schoolTree
      v-if="school_tree_visible"
      :flag.sync="school_tree_visible"
      @close="closeSchoolTree"
      :id="currRow.department_ids"
      type="isLook"
    ></schoolTree>
  </div>
</template>

<script>
import schoolTree from "@/components/schoolTree/saveSchoolTree.vue";
import qrMiniCodeApi from "@/api/qrMiniCode";
import create from "./create.vue";
export default {
  components: {
    schoolTree,
    create
  },
  data() {
    return {
      school_tree_visible: false,
      school_flag: false,
      searchTitle: [
        {
          props: "contact_name",
          label: "联系人",
          type: "input",
          clearable: true,
          show: true
        },
        {
          props: "status",
          label: "生效状态",
          type: "select",
          show: true,
          selectOptions: [
            { id: "", name: "不限" },
            { id: 1, name: "启用" },
            { id: 2, name: "禁用" }
          ]
        }
      ],
      form: {
        contact_name: "",
        status: ""
      },
      table_title: [
        {
          props: "contact_name",
          label: "联系人",
          show: true,
          tooltip: true
        },
        {
          props: "qr_code_url",
          label: "二维码",
          show: true,
          tooltip: true,
          width: 200
        },
        {
          props: "department_names",
          label: "负责校区",
          show: true
        },
        {
          props: "status",
          label: "生效状态",
          show: true,
          width: 160
        }
      ],
      results: [],
      loading: false,
      page_size: 10,
      page: 1,
      dialogVisibleNewlyAdded: false,
      count: 0,
      currRow: {},
      type: "create"
    };
  },
  computed: {
    departmentId() {
      return this.$store.getters.doneGetSchoolId;
    }
  },

  created() {
    this.Oss.getAliyun();
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      qrMiniCodeApi
        .list({
          page: this.page,
          page_size: this.page_size,
          department_id: this.departmentId,
          ...this.form
        })
        .then((res) => {
          if (res.data.code === 0) {
            const { results, count } = res.data.data;
            this.results = results ?? [];
            this.count = count ?? 0;
            this.loading = false;
          } else {
            this.$message.error(res.data.message);
            this.results = [];
            this.count = 0;
            this.loading = false;
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    goCourseAttribute(row) {
      this.currRow = row;
      this.school_tree_visible = true;
    },
    closeSchoolTree() {
      this.school_tree_visible = false;
    },
    changeStatus(row) {
      if (!this.$_has({ m: "qrCodeManagement", o: "updateStatus" })) {
        this.$message.warning("您没有权限修改状态,请联系管理员");
        return;
      }
      const status = row.status === 1 ? 2 : 1;
      this.$confirm("确定要修改状态吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          qrMiniCodeApi
            .updateStatus({
              id: row.id,
              status: row.status
            })
            .then((res) => {
              if (res.data.code === 0) {
                this.$message.success("操作成功");
              } else {
                row.status = status;
                this.$message.error(res.data.message);
              }
            })
            .catch((err) => {
              console.log(err);
              row.status = status;
              this.$message.error(err.message || "操作失败");
            });
        })
        .catch(() => {
          row.status = status;
        });
    },
    query() {
      this.page = 1;
      this.getList();
    },
    resetForm() {
      this.form = {
        contact_name: "",
        status: ""
      };
      this.page = 1;
      this.getList();
    },
    currentChange(page) {
      this.page = page;
      this.getList();
    },
    sizeChange(size) {
      this.page_size = size;
      this.getList();
    },
    closeAdd() {
      this.dialogVisibleNewlyAdded = false;
    },
    confirm() {
      this.dialogVisibleNewlyAdded = false;
      this.page = 1;
      this.getList();
    },

    createQRCode() {
      this.type = "create";
      this.dialogVisibleNewlyAdded = true;
    },
    edit(row) {
      this.type = "edit";
      this.dialogVisibleNewlyAdded = true;
      this.currRow = row;
    },
    deletes(row) {
      this.$confirm("确定删除该二维码吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        qrMiniCodeApi
          .del({
            id: row.id
          })
          .then((res) => {
            if (res.data.code === 0) {
              this.$message.success("删除成功");
              this.getList();
            } else {
              this.$message.error(res.data.message);
            }
          })
          .catch(() => {
            this.$message.error("删除失败");
          });
      });
    }
  }
};
</script>

<style lang="less" scoped>
.avatar-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  text-align: center;
  width: 150px;
  height: 150px;
  /deep/ .el-upload {
    width: 100%;
    height: 100%;
  }
  img {
    width: 100%;
    height: 100%;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100%;
    display: block;
    height: 100%;
    line-height: 150px;
    text-align: center;
  }
}
</style>
