<template>
  <div class="organization-list container">
    <div class="organization__content container tg-box--margin">
      <div class="organization-head">
        <tgSearch
          :searchTitle.sync="searchTitle"
          @reset="reset"
          @search="search"
          :form.sync="search_form"
        ></tgSearch>
      </div>
      <div class="tg-table__box">
        <div class="tg-box--border"></div>
        <el-table
          :data="organization_list"
          class="organization-table tg-table"
          style="width: 100%; margin-bottom: 20px"
          row-key="name"
          default-expand-all
          :tree-props="{ children: 'child' }"
          :row-class-name="tableRowClassName"
          :cell-style="{ borderRightColor: '#e0e6ed75' }"
          :header-cell-style="{ borderRightColor: '#e0e6ed75' }"
          border
        >
          <el-table-column
            prop="name"
            label="组织名称"
            width="460"
          ></el-table-column>
          <el-table-column label="类型">
            <template slot-scope="scope">
              <span v-if="scope.row.child_type === 'school'">校区</span>
              <span v-if="scope.row.child_type === 'center'">中心</span>
              <span v-if="scope.row.child_type === 'department'">部门</span>
              <span v-if="scope.row.child_type === 'project'">项目组</span>
              <span v-if="scope.row.child_type === 'other'">其他</span>
            </template>
          </el-table-column>
          <el-table-column
            label="人数"
            prop="child_employee_numb"
          ></el-table-column>
          <el-table-column
            label="负责人"
            prop="re_employee_name"
          ></el-table-column>
          <el-table-column
            label="联系方式"
            prop="re_employee_mobile"
          ></el-table-column>
          <el-table-column prop="area_name" label="区域名称"></el-table-column>
          <el-table-column label="操作" width="180">
            <template slot-scope="scope">
              <el-button
                size="small"
                class="tg-text--blue tg-span__divide-line"
                type="text"
                @click="add_popup(scope.row.id)"
                v-has="{ m: 'organization', o: 'create' }"
                >新增</el-button
              >
              <el-button
                size="small"
                class="tg-text--blue"
                type="text"
                @click="edit_popup(scope.row.id)"
                v-if="!scope.row.is_organization"
                v-has="{ m: 'organization', o: 'update' }"
                >编辑</el-button
              >
              <el-button
                style="display: none"
                size="small"
                @click="delete_dept(scope.row)"
                type="text"
                class="tg-text--blue tg-span__divide-line"
                v-model="visible"
                v-if="!scope.row.is_organization"
                v-has="{ m: 'organization', o: 'delete' }"
                >删除</el-button
              >
            </template>
          </el-table-column>
          <template slot="empty">
            <div style="margin-top: 15%">
              <TgLoading v-if="loading"></TgLoading>
              <div class="empty-container" v-else>暂无数据～</div>
            </div>
          </template>
        </el-table>
        <p v-if="!all_show" class="is-empty">暂无数据</p>
      </div>
    </div>
    <tgTip :tip="tip" v-if="tipFlag"></tgTip>
    <el-dialog
      title="新增组织"
      :visible.sync="dialogAddFormVisible"
      v-if="dialogAddFormVisible"
      :modal-append-to-body="false"
      width="800px"
    >
      <el-form
        ref="form"
        :model="form"
        class="organization-add"
        :label-width="form.department_type === 'company' ? '125px' : '86px'"
      >
        <el-form-item
          prop="name"
          label="名称"
          class="tg-box--margin tg-form-item"
          :rules="{
            required: true,
            message: '名称不能为空',
            trigger: 'blur'
          }"
        >
          <el-input
            v-model="form.name"
            autocomplete="off"
            placeholder="请输入名称"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="类型"
          class="tg-box--margin tg-form-item"
          prop="department_type"
          :rules="{
            required: true,
            message: '类型不能为空',
            trigger: 'blur'
          }"
        >
          <el-select
            v-model="form.department_type"
            placeholder="请选择类型"
            filterable
            clearable
            :popper-append-to-body="false"
            @change="department_type_change"
          >
            <el-option label="公司" value="company"></el-option>
            <el-option label="校区" value="school"></el-option>
            <el-option label="中心" value="center"></el-option>
            <el-option label="部门" value="department"></el-option>
            <!-- <el-option label="项目组" value="project"></el-option>
            <el-option label="其他" value="other"></el-option> -->
          </el-select>
        </el-form-item>
        <el-form-item
          label="校区属性"
          prop="school_type"
          v-if="form.department_type === 'school'"
          class="tg-box--margin tg-form-item"
        >
          <el-select
            :popper-append-to-body="false"
            v-model="form.school_type"
            placeholder="请选择区域"
            clearable
          >
            <el-option
              v-for="(item, index) in school_type_list"
              :label="item.name"
              :value="item.id"
              :key="index"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="区域"
          prop="area_id"
          :rules="{
            required: form.department_type == 'school' ? true : false,
            message: '区域不能为空',
            trigger: 'blur'
          }"
          class="tg-box--margin tg-form-item"
        >
          <el-select
            :popper-append-to-body="false"
            v-model="form.area_id"
            placeholder="请选择区域"
            filterable
            clearable
          >
            <el-option
              v-for="(item, index) in area_list"
              :label="item.name"
              :value="item.id"
              :key="index"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          prop="re_employee_id"
          label="负责人"
          class="tg-box--margin tg-form-item"
        >
          <course-staff
            :check_id.sync="form.re_employee_id"
            :check_name.sync="form.re_employee_name"
            staff_placeholder="请输入负责人"
          ></course-staff>
        </el-form-item>
        <el-form-item
          required
          v-if="form.department_type == 'school'"
          label="省市区"
          prop="position"
          class="tg-box--margin tg-form-item"
        >
          <choose-city
            :value.sync="form.position"
            :remoteIsLabel="true"
            @input="chooseCity"
          ></choose-city>
        </el-form-item>
        <el-form-item
          prop="address"
          required
          :label="form.department_type === 'school' ? '校区地址' : '地址'"
          class="tg-box--margin tg-form-item"
        >
          <el-input
            v-model="form.address"
            autocomplete="off"
            :placeholder="`请输入${
              form.department_type === 'school' ? '校区地址' : '地址'
            }`"
          ></el-input>
        </el-form-item>
        <el-form-item
          prop="address"
          label="统一社会信用代码"
          v-if="form.department_type === 'company'"
          class="tg-box--margin tg-form-item"
        >
          <el-input
            v-model="form.unified_credit_code"
            autocomplete="off"
            placeholder="请输入统一社会信用代码"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          @click="dialogAddFormVisible = false"
          class="tg-button--plain"
          >取消</el-button
        >
        <el-button type="primary" @click="add_submit" class="tg-button--primary"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      title="修改部门"
      :visible.sync="dialogEditFormVisible"
      v-if="dialogEditFormVisible"
      :modal-append-to-body="false"
      width="800px"
    >
      <el-form
        ref="form"
        :model="form"
        class="organization-add"
        :label-width="form.department_type === 'company' ? '125px' : '86px'"
      >
        <el-form-item
          :rules="{
            required: true,
            message: '名称不能为空',
            trigger: 'blur'
          }"
          label="名称"
          prop="name"
          class="tg-box--margin tg-form-item"
        >
          <el-input
            v-model="form.name"
            autocomplete="off"
            placeholder="请输入名称"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="类型"
          prop="department_type"
          :rules="{
            required: true,
            message: '类型不能为空',
            trigger: 'blur'
          }"
          class="tg-box--margin tg-form-item"
        >
          <el-select
            v-model="form.department_type"
            placeholder="请选择类型"
            :popper-append-to-body="false"
            @change="department_type_change"
          >
            <el-option label="公司" value="company"></el-option>
            <el-option label="校区" value="school"></el-option>
            <el-option label="中心" value="center"></el-option>
            <el-option label="部门" value="department"></el-option>
            <!-- <el-option label="项目组" value="project"></el-option>
            <el-option label="其他" value="other"></el-option> -->
          </el-select>
        </el-form-item>
        <el-form-item
          label="校区属性"
          prop="school_type"
          v-if="form.department_type === 'school'"
          class="tg-box--margin tg-form-item"
        >
          <el-select
            :popper-append-to-body="false"
            v-model="form.school_type"
            placeholder="请选择区域"
            clearable
          >
            <el-option
              v-for="(item, index) in school_type_list"
              :label="item.name"
              :value="item.id"
              :key="index"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          :rules="{
            required: form.department_type == 'school' ? true : false,
            message: '区域不能为空',
            trigger: 'blur'
          }"
          prop="area_id"
          label="区域"
          class="tg-box--margin tg-form-item"
        >
          <el-select
            v-model="form.area_id"
            placeholder="请选择区域"
            :popper-append-to-body="false"
          >
            <el-option
              v-for="(item, index) in area_list"
              :label="item.name"
              :value="item.id"
              :key="index"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          prop="re_employee_id"
          label="负责人"
          class="tg-box--margin tg-form-item"
        >
          <course-staff
            :check_id.sync="form.re_employee_id"
            :check_name.sync="form.re_employee_name"
            staff_placeholder="请输入负责人"
          ></course-staff>
        </el-form-item>
        <el-form-item
          v-if="form.department_type == 'school'"
          label="省市区"
          required
          prop="position"
          class="tg-box--margin tg-form-item"
        >
          <choose-city
            :value="form.position"
            :remoteIsLabel="true"
            @input="chooseCity"
          ></choose-city>
        </el-form-item>
        <el-form-item
          v-if="form.department_type == 'school'"
          label="经纬度"
          prop="latitude_longitude"
          class="tg-box--margin tg-form-item"
        >
          <el-input
            v-model="form.latitude_longitude"
            autocomplete="off"
            placeholder="请输入经纬度，例如：39.792041,116.512316"
          ></el-input>
          <el-button
            type="text"
            @click="exampleVisible = true"
            style="margin-left: 10px"
            >查看实例</el-button
          >
          <el-dialog
            title="预览"
            width="70%"
            append-to-body
            :visible.sync="exampleVisible"
          >
            <div style="display: flex; align-items: center">
              <span>获取经纬度网址：</span>
              <el-button type="text" @click="toPoint"
                >https://lbs.qq.com/getPoint/</el-button
              >
            </div>
            <img
              width="100%"
              src="https://tg-prod.oss-cn-beijing.aliyuncs.com/441743f3-e653-4ae3-b70e-811baf414993.jpg"
              alt=""
            />
          </el-dialog>
        </el-form-item>
        <el-form-item
          required
          prop="address"
          :label="form.department_type === 'school' ? '校区地址' : '地址'"
          class="tg-box--margin tg-form-item"
        >
          <el-input
            v-model="form.address"
            autocomplete="off"
            :placeholder="`请输入${
              form.department_type === 'school' ? '校区地址' : '地址'
            }`"
          ></el-input>
        </el-form-item>
        <el-form-item label="上级部门" class="tg-box--margin tg-form-item">
          <el-input
            placeholder="请选择上级部门"
            readonly
            @click.native="dialogVisible = true"
            v-model="form.father_department_name"
          >
            <i slot="suffix" class="el-input__icon el-icon-arrow-down"></i>
          </el-input>
          <organizationTree
            :flag.sync="dialogVisible"
            :can_choice_organization="true"
            :id.sync="form.father_department_id"
            :name.sync="form.father_department_name"
          ></organizationTree>
        </el-form-item>
        <el-form-item
          prop="address"
          label="统一社会信用代码"
          v-if="form.department_type === 'company'"
          class="tg-box--margin tg-form-item"
        >
          <el-input
            v-model="form.unified_credit_code"
            placeholder="请输入统一社会信用代码"
            autocomplete="off"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          @click="dialogEditFormVisible = false"
          class="tg-button--plain"
          >取消</el-button
        >
        <el-button
          type="primary"
          @click="edit_submit"
          class="tg-button--primary"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import tgSearch from "@/components/search/search";
import tgTip from "@/components/tip/tip";
import depetApi from "@/api/organization";
import areaApi from "@/api/regionalManagement";
import organizationTree from "@/components/organizationTree/organizationTree.vue";
import employeeApi from "@/api/staff";
import { rebuildData } from "@/public/utils";
import courseStaff from "@/components/staff/courseStaff";

export default {
  data() {
    return {
      exampleVisible: false,
      visible: false,
      dialogVisible: false,
      dialogAddFormVisible: false,
      dialogEditFormVisible: false,
      form: {
        name: "",
        department_type: "",
        father_department_id: "",
        area_id: "",
        school_type: "",
        father_department_name: "",
        re_employee_id: "",
        address: "",
        unified_credit_code: "",
        re_employee_name: "",
        position: []
      },

      searchTitle: [
        { props: "name", label: "组织名称", type: "input", show: true }
      ],
      search_form: {
        name: ""
      },
      tipFlag: false,
      tip: "有下属组织或人员存在，不能删除",
      organization_list: [],
      init_organization_list: [],
      area_list: [],
      school_type_list: [
        {
          id: 0,
          name: "不限"
        },
        {
          id: 1,
          name: "直营校"
        },
        {
          id: 2,
          name: "合作校"
        },
        {
          id: 3,
          name: "网校"
        }
      ],
      employee_list: [],
      all_show: true,
      height: window.innerHeight - 238,
      loading: false
    };
  },
  computed: {},
  methods: {
    toPoint() {
      window.open("https://lbs.qq.com/getPoint/");
    },
    department_type_change() {
      this.$refs.form.clearValidate();
    },
    chooseCity(data) {
      this.form.position = data;
    },
    add_submit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.form.school_type =
            this.form.school_type === "" ? 0 : this.form.school_type;
          depetApi.DepartmentCreate(this.form).then(() => {
            this.get_organization_overview_data();
            this.dialogAddFormVisible = false;
            this.getSchoolTree();
          });
        }
      });
    },
    edit_submit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.form.school_type =
            this.form.school_type === "" ? 0 : this.form.school_type;
          depetApi.DepartmentUpdate(this.form).then(() => {
            this.get_organization_overview_data();
            this.dialogEditFormVisible = false;
            this.getSchoolTree();
          });
        }
      });
    },
    add_popup(father_department_id) {
      this.get_employee_list();
      this.get_area_list();
      this.reset_form();
      this.form.father_department_id = father_department_id;
      this.dialogAddFormVisible = true;
    },
    edit_popup(department_id) {
      this.get_employee_list();
      this.get_area_list();
      this.reset_form();
      depetApi.DepartmentInfo({ department_id }).then((res) => {
        this.form = res.data;
        this.dialogEditFormVisible = true;
        this.form.department_id = department_id;
      });
    },
    delete_dept(dept_obj) {
      if (dept_obj.has_child) {
        this.$message.error("有子级部门，不能删除");
      } else {
        this.$confirm("此操作将永久删除, 是否继续?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          depetApi.DepartmentDelete({ id: dept_obj.id }).then(() => {
            this.get_organization_overview_data();
            this.getSchoolTree();
          });
        });
      }
    },
    getSchoolTree() {
      depetApi.getSchoolPart().then((res) => {
        const name = [];
        res.data.school_data.forEach((item) => {
          item.child.forEach((item1) => {
            name.push(item1.name);
          });
        });
        const choosename = sessionStorage.getItem("schoolName").split(",");
        choosename.map((item, index) => {
          if (!name.includes(item)) {
            choosename.splice(index, 1);
          }
        });
        this.$store.commit("setHeaderSchoolName", choosename.toString());
      });
    },
    reset_form() {
      this.form = {
        name: "",
        department_type: "",
        father_department_id: "",
        area_id: "",
        school_type: ""
      };
    },
    reset() {
      this.search_form = {
        name: ""
      };
      this.initList();
    },
    search() {
      const data = this.search_form;
      if (!data.name) return false;
      this.initList();
      this.$nextTick(() => {
        const arr = rebuildData(
          data.name,
          this.init_organization_list,
          { children: "child" },
          "name"
        );
        this.organization_list = arr;
        const new_arr = [];
        this.isAllShow(this.organization_list, new_arr);
        this.all_show = new_arr.indexOf(true) > -1;
      });
    },
    initList() {
      this.all_show = true;
      this.init_organization_list = this.initStatus(
        this.init_organization_list
      );
      this.organization_list = JSON.parse(
        JSON.stringify(this.init_organization_list)
      );
    },
    initStatus(data) {
      data.forEach((element) => {
        delete element.isShow;
        if (element.child) {
          this.initStatus(element.child);
        }
      });
      return data;
    },
    isAllShow(data, arr) {
      data.forEach((item) => {
        arr.push(item.isShow);
        if (item.child) {
          this.isAllShow(item.child, arr);
        }
      });
    },
    tableRowClassName({ row }) {
      if (!row.isShow && typeof row.isShow !== "undefined") {
        return "tg-row--hide";
      }
      return "";
    },
    get_organization_overview_data() {
      this.loading = true;
      depetApi.OrganizationOverView().then((res) => {
        this.loading = false;
        this.organization_list = [];
        this.organization_list.push(res.data);
        this.init_organization_list = JSON.parse(
          JSON.stringify(this.organization_list)
        );
      });
    },
    get_area_list() {
      areaApi.GetAreaList().then((res) => {
        this.area_list = [];
        this.area_list = res.data;
      });
    },
    get_employee_list() {
      employeeApi.GetEmployeeList().then((res) => {
        this.employee_list = [];
        this.employee_list = res.data;
      });
    }
  },
  created() {
    this.get_organization_overview_data();
  },
  components: {
    tgSearch,
    tgTip,
    organizationTree,
    courseStaff
  }
};
</script>
<style lang="less" scoped>
.organization-list {
  // overflow: hidden;
  ::v-deep .el-table td:last-child {
    border-right-color: transparent !important;
  }
  ::v-deep .el-table__header th:last-child {
    border-right-color: transparent !important;
  }
  .organization__content {
    // overflow: hidden;
    width: 100%;
    .organization-head {
      width: 100%;
      .tg-search {
        ::v-deep .el-form {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
        }
        align-items: flex-start;
      }
    }

    .organization-table {
      height: calc(100% - 20px);
      .tg-span__divide-line {
        cursor: pointer;
      }

      .tg-span__divide-line + .tg-span__divide-line {
        margin-left: 10px;
      }
    }
  }
  ::v-deep .el-table .tg-row--hide {
    display: none;
  }
  .is-empty {
    text-align: center;
    color: #ccc;
    font-weight: normal;
    font-size: 14px;
    position: absolute;
    top: 200px;
    left: 48%;
  }
  ::v-deep .el-dialog__body {
    padding: 0 16px;
    height: 373px;
  }
  .organization-add {
    ::v-deep .el-form-item__content,
    ::v-deep .el-form-item__label {
      line-height: 32px;
    }
  }
  .tg-form-item {
    ::v-deep .el-input,
    ::v-deep .el-select .el-input {
      width: 400px;
    }
  }
  .tg-table__box {
    box-shadow: none;
  }
  ::v-deep .el-table__body-wrapper {
    height: calc(100% - 46px);
    .empty-container {
      position: absolute;
      top: 50%;
      left: 50%;
    }
    .loading-container {
      position: absolute;
      top: 30%;
      left: 1%;
      background: transparent;
      .box {
        height: 100%;
      }
    }
  }
}
</style>
