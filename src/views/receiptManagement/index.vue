<template>
  <div class="container">
    <tg-search
      :searchTitle.sync="search_title"
      :form.sync="searchForm"
      :showNum="2"
      @educe="exportDataNew"
      :loadingState="exportLoading"
      :isExport="isExport"
      @reset="reset"
      @search="searchVal"
      class="tg-box--margin"
      :hasDefaultDate="true"
    >
      <!-- 物流单号筛选 -->
      <template #jd_track>
        <div class="form-item-jd_track">
          <el-select
            @change="jdTrackChange"
            v-model="searchForm.has_jd_track"
            placeholder="请选择"
            class="jd_track-select"
          >
            <el-option
              v-for="item in jd_track_options"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
          <el-input
            class="jd_track-input"
            v-model="searchForm.jd_track_no"
            placeholder="请输入物流单号"
            v-if="searchForm.has_jd_track === 'yes'"
          />
        </div>
      </template>
    </tg-search>
    <el-row class="tg-box--margin tg-shadow--margin tg-box--width">
      <el-button
        type="plain"
        class="tg-button--plain"
        @click="acceptMoney"
        v-has="{ m: 'receipt', o: 'proceedsAccount' }"
        >收款账户</el-button
      >
      <el-button
        type="plain"
        class="tg-button--plain"
        v-has="{ m: 'receipt', o: 'invoice_apply' }"
        @click="batchDate"
        :disabled="checked_order.length > 0 ? false : true"
        >批量修改有效期</el-button
      >
      <el-button
        type="plain"
        :disabled="!can_invoiced"
        class="tg-button--plain"
        :class="{ 'tg-button--disabled': !can_invoiced }"
        @click="batchApplyInvoice"
        v-has="{ m: 'receipt', o: 'invoice_apply' }"
        >开发票</el-button
      >
      <el-button
        type="plain"
        :disabled="!checked_order.length"
        class="tg-button--plain"
        :class="{ 'tg-button--disabled': !checked_order.length }"
        @click="batchFillJDExpressDelivery"
        v-has="{ m: 'receipt', o: 'editJdTrack' }"
        >填写快递信息</el-button
      >
      <el-button
        type="plain"
        :disabled="!is_Signed"
        class="tg-button--plain"
        :class="{
          'tg-button--disabled': !is_Signed
        }"
        @click="batchSign"
        v-if="is_electronic"
        v-has="{ m: 'electronicSign', o: 'push' }"
        >电子签</el-button
      >
      <el-button
        v-has="{ m: 'receipt', o: 'import_express' }"
        type="plain"
        class="tg-button--plain"
        @click="downloadExpressTemplate"
        >快递信息导入模版</el-button
      >
      <el-button
        v-has="{ m: 'receipt', o: 'import_express' }"
        type="plain"
        class="tg-button--plain"
        @click="importExpress"
        >导入快递信息</el-button
      >
      <el-divider direction="vertical"></el-divider>
      <el-checkbox v-model="discard">显示已作废收据</el-checkbox>
      <select-field
        :allFields.sync="table_title"
        :btnType="'button'"
      ></select-field>
    </el-row>
    <div class="tg-table__box">
      <div class="tg-box--border"></div>
      <el-table
        ref="table"
        border
        class="tg-table"
        tooltip-effect="dark"
        :cell-style="{ borderRightColor: '#e0e6ed75' }"
        :header-cell-style="{ borderRightColor: '#e0e6ed75' }"
        show-summary
        :data="tableInfo.list"
        :row-key="getRowKeys"
        :summary-method="getSummaries"
        @selection-change="handleSelectionChange"
        @sort-change="sortChange"
      >
        <el-table-column
          type="selection"
          width="50"
          :reserve-selection="true"
          fixed
        ></el-table-column>
        <template v-for="(item, index) in table_title">
          <el-table-column
            v-if="item.show"
            :key="index"
            :prop="item.props"
            :label="item.label"
            :min-width="item.width"
            :fixed="item.fixed"
            :sortable="item.sort"
            :show-overflow-tooltip="
              item.props === 'tw_period' || item.props === 'tw_source_id'
                ? false
                : true
            "
          >
            <template slot-scope="scope">
              <div v-if="item.props === 'receipt_no'" class="copy_name">
                <el-button
                  class="tg-text--blue tac"
                  type="text"
                  @click="toDetail(scope.row)"
                >
                  {{ scope.row.receipt_no }}
                  <span style="color: #f56c6c">{{
                    scope.row.receipt_status == 2 ? "【已作废】" : ""
                  }}</span>
                </el-button>
                <div v-copy="scope.row.receipt_no"></div>
              </div>
              <span v-else-if="item.props === 'couponsName'">
                {{
                  scope.row.coupon_info
                    ? scope.row.coupon_info.template_name
                    : ""
                }}
              </span>
              <span v-else-if="item.props === 'invoice_status'">
                {{
                  scope.row.receipt_type === "order_paid"
                    ? scope.row.invoice_status
                    : "不支持开票"
                }}
              </span>
              <span v-else-if="item.props === 'expressInformation'">
                <el-button
                  type="text"
                  @click="openExpressInformationDialog(scope.row)"
                  >查看</el-button
                >
              </span>
              <span v-else-if="item.props === 'fee_type'">{{
                scope.row.fee_type | feeTypeFilter(category_list)
              }}</span>
              <span v-else-if="item.props === 'coupon_plan_name'">{{
                couponPlanNameFilter(scope.row.coupon_plan_name)
              }}</span>
              <span v-else-if="item.props === 'couponsNum'">
                {{
                  scope.row.coupon_info ? scope.row.coupon_info.coupon_id : ""
                }}
              </span>
              <span v-else-if="item.props === 'couponsAmount'">
                {{
                  scope.row.coupon_info
                    ? scope.row.coupon_info.discount_price
                    : ""
                }}
              </span>
              <template v-else-if="item.type === 'select'">
                {{
                  scope.row[scope.column.property] == null
                    ? ""
                    : scope.row[scope.column.property].toString()
                }}
              </template>
              <span v-else-if="item.props === 'receipt_type'">
                {{ scope.row.receipt_type | receipTypeFilter(receipTypeDict)
                }}<span
                  v-if="
                    ['transfer_fee_come', 'transfer_fee_out'].includes(
                      scope.row.receipt_type
                    )
                  "
                  >({{ scope.row.origin_student_name }})</span
                >
              </span>
              <span v-else-if="item.type === 'price'">
                {{
                  scope.row[scope.column.property]
                    ? scope.row[scope.column.property].toFixed(2)
                    : "0.00"
                }}
              </span>
              <span v-else-if="item.props === 'is_print'">
                {{ scope.row[scope.column.property] == 2 ? "否" : "是" }}
              </span>
              <span v-else-if="item.props === 'coupons'">
                <img
                  src="../../assets/图片/message.png"
                  class="attribute"
                  @click="goCouponsDetail(scope.row.coupon_info)"
                />
              </span>
              <span v-else-if="item.props === 'refund_carryover_reason'">
                {{ scope.row.refund_carryover_reason_name }}
                <template v-if="scope.row.refund_carryover_sub_reason_name">
                  / {{ scope.row.refund_carryover_sub_reason_name }}
                </template>
              </span>
              <template v-else-if="item.props === 'mobile'">
                <mobileHyposensitization
                  :mobileTemInfo="{
                    row: scope.row,
                    has_eye_limit: scope.row?.has_eye_limit,
                    mobile: scope.row.mobile
                  }"
                ></mobileHyposensitization>
              </template>
              <span v-else-if="item.props === 'enrollment_source'">
                {{
                  scope.row.enrollment_source_first +
                  "," +
                  scope.row.enrollment_source_second
                }}
              </span>
              <span v-else-if="item.props === 'tw_remark'">
                {{ scope.row.tw_remark | tw_remark_filter }}
              </span>
              <div v-else-if="item.type == 'selectInput'">
                <el-select
                  v-model="scope.row.order_source_first_name"
                  v-if="
                    !(
                      scope.row[scope.column.property] ||
                      scope.row.receipt_type !== 'order_paid'
                    ) || can_edit_channel
                  "
                  @change="
                    (val) => {
                      channelChange(val, scope.row, scope.$index);
                    }
                  "
                  value-key="name"
                >
                  <el-option
                    v-for="(item, index) in channe_list"
                    :key="index"
                    :label="item.name"
                    :value="item"
                  ></el-option>
                </el-select>
                <span v-else>{{ scope.row[scope.column.property] }}</span>
              </div>
              <div v-else-if="item.type == 'selectInputSub'">
                <el-select
                  v-model="scope.row.order_source_second_name"
                  v-if="
                    !(
                      scope.row[scope.column.property] ||
                      scope.row.receipt_type !== 'order_paid'
                    ) || can_edit_channel
                  "
                  value-key="name"
                  @change="
                    (val) => {
                      channelSubChange(val, scope.row, scope.$index);
                    }
                  "
                  @click.native="selectClick(scope.row, scope.$index)"
                  @focus="selectInput(scope.row, scope.$index)"
                >
                  <el-option
                    v-for="(item, index) in scope.row.sub_channe_list"
                    :key="index"
                    :label="item.name"
                    :value="item"
                  ></el-option>
                </el-select>
                <span v-else>{{ scope.row[scope.column.property] }}</span>
              </div>
              <div v-else-if="item.props === 'order_channel_type'">
                <span>{{
                  scope.row.order_channel_type | channel_type_filter
                }}</span>
              </div>
              <div v-else-if="item.props === 'tw_source_id'">
                <el-input
                  v-if="can_edit_channel"
                  v-model="scope.row.tw_source_id"
                  placeholder="请输入渠道Id"
                  @blur="
                    (val) => {
                      changeSourceIdAdmin(val, scope.row, scope.$index);
                    }
                  "
                ></el-input>
                <el-input
                  v-else-if="
                    !(
                      scope.row.tw_source_id.length > 0 ||
                      scope.row.receipt_type !== 'order_paid'
                    ) || can_edit_channel
                  "
                  v-model="scope.row.new_tw_source_id"
                  placeholder="请输入渠道Id"
                  @blur="
                    (val) => {
                      changeSourceId(val, scope.row);
                    }
                  "
                ></el-input>
                <span v-else>
                  {{ scope.row.tw_source_id }}
                </span>
              </div>
              <div
                v-else-if="
                  item.props === 'tw_period' &&
                  (scope.row.department_name === '聂卫平围棋网校-新' ||
                    scope.row.department_name === '网校测试校区C')
                "
              >
                <el-select
                  v-if="can_edit_channel"
                  v-model="scope.row.tw_period"
                  clearable
                  placeholder="请选择报名期次"
                  @change="
                    (val) => changePeriodAdmin(val, scope.row, scope.$index)
                  "
                  @focus="getRowTwPeriodList(scope.row)"
                >
                  <el-option
                    v-for="item in scope.row.tw_period_options"
                    :key="item"
                    :label="item"
                    :value="item"
                  >
                  </el-option>
                </el-select>

                <el-select
                  v-else-if="
                    !(
                      scope.row.tw_period.length > 0 ||
                      scope.row.receipt_type !== 'order_paid'
                    ) || can_edit_channel
                  "
                  v-model="scope.row.new_tw_period"
                  placeholder="请选择报名期次"
                  clearable
                  @change="(val) => changePeriod(val, scope.row)"
                  @focus="getRowTwPeriodList(scope.row)"
                >
                  <el-option
                    v-for="item in scope.row.tw_period_options"
                    :key="item"
                    :label="item"
                    :value="item"
                  >
                  </el-option>
                </el-select>
                <span v-else>
                  {{ scope.row.tw_period }}
                </span>
              </div>
              <div v-else-if="item.props === 'is_contract_status'">
                {{ scope.row.contract_status != "" ? "已推送" : "未推送" }}
              </div>
              <div v-else-if="item.props === 'sin_sign'">
                <el-button
                  type="text"
                  icon="el-icon-view icon-tool"
                  :disabled="scope.row.contract_url === ''"
                  @click="viewSinSign(scope.row)"
                  :loading="scope.row.viewLoading"
                ></el-button>
                <el-button
                  type="text"
                  icon="el-icon-download icon-tool"
                  :disabled="
                    scope.row.contract_status !== '已签署' &&
                    scope.row.contract_status !== '已解除'
                  "
                  @click="downloadSinSign(scope.row)"
                  :loading="scope.row.downloadLoading"
                ></el-button>
              </div>
              <div v-else-if="item.props === 'is_ylb'">
                <el-tag type="success" v-if="scope.row.is_ylb === 'yes'">
                  是
                </el-tag>
                <el-tag type="info" v-else>否</el-tag>
              </div>
              <div v-else-if="item.props === 'logistics_receipt_date'">
                {{ scope.row.logistics_receipt_date | getTime }}
              </div>
              <div v-else-if="item.props === 'seven_days_receipt_date'">
                {{ scope.row.seven_days_receipt_date | getTime }}
              </div>
              <div v-else-if="item.props === 'delivery_method'">
                {{
                  scope.row.delivery_method === "arrive"
                    ? "到店领取"
                    : scope.row.delivery_method === "jd"
                    ? "快递"
                    : ""
                }}
              </div>
              <div v-else-if="item.props === 'third_order_id'">
                <!-- disabled：没有权限或者是易宝订单 -->
                <el-input
                  :disabled="
                    !$_has({ m: 'receipt', o: 'edit_third_order_id' }) ||
                    !scope.row.third_is_modify
                  "
                  v-model="scope.row.third_order_id"
                  placeholder="请输入第三方支付单号"
                  @change="
                    () => {
                      editThirdOrderId(scope.row);
                    }
                  "
                ></el-input>
              </div>
              <div
                v-else-if="
                  item.props === 'volume_period' &&
                  (scope.row.department_name === '聂卫平围棋网校-新' ||
                    scope.row.department_name === '网校测试校区C')
                "
              >
                <!-- <el-select
                  v-if="can_edit_channel"
                  v-model="scope.row.volume_period"
                  clearable
                  filterable
                  allow-create
                  placeholder="请选择或输入放量期次"
                  @change="
                    (val) =>
                      changeVolumePeriodAdmin(val, scope.row, scope.$index)
                  "
                >
                  <el-option
                    v-for="item in volume_period_options"
                    :key="item.name"
                    :label="item.name"
                    :value="item.name"
                  >
                  </el-option>
                </el-select> -->

                <el-select
                  v-if="
                    scope.row.receipt_type !== 'refund_order' && is_edit_period
                  "
                  v-model="scope.row.volume_period_display"
                  placeholder="请选择或输入放量期次"
                  clearable
                  filterable
                  allow-create
                  @change="(val) => changeVolumePeriod(val, scope.row)"
                >
                  <el-option
                    v-for="item in volume_period_options"
                    :key="item.name"
                    :label="item.name"
                    :value="item.name"
                  >
                  </el-option>
                </el-select>
                <span v-else>
                  {{ scope.row.volume_period?.toString() || "" }}
                </span>
              </div>
              <div
                v-else-if="
                  item.props === 'transfer_period' &&
                  (scope.row.department_name === '聂卫平围棋网校-新' ||
                    scope.row.department_name === '网校测试校区C')
                "
              >
                <!-- <el-select
                  v-if="can_edit_channel"
                  v-model="scope.row.transfer_period"
                  clearable
                  filterable
                  allow-create
                  placeholder="请选择或输入转化期次"
                  @change="
                    (val) =>
                      changeTransferPeriodAdmin(val, scope.row, scope.$index)
                  "
                >
                  <el-option
                    v-for="item in transfer_period_options"
                    :key="item.name"
                    :label="item.name"
                    :value="item.name"
                  >
                  </el-option>
                </el-select> -->

                <el-select
                  v-if="
                    scope.row.receipt_type !== 'refund_order' && is_edit_period
                  "
                  v-model="scope.row.transfer_period_display"
                  placeholder="请选择或输入转化期次"
                  clearable
                  filterable
                  allow-create
                  @change="(val) => changeTransferPeriod(val, scope.row)"
                >
                  <el-option
                    v-for="item in transfer_period_options"
                    :key="item.name"
                    :label="item.name"
                    :value="item.name"
                  >
                  </el-option>
                </el-select>
                <span v-else>
                  {{ scope.row.transfer_period?.toString() || "" }}
                </span>
              </div>
              <span v-else-if="item.props === 'volume_period'">
                {{ scope.row.volume_period?.toString() || "" }}
              </span>
              <span v-else-if="item.props === 'transfer_period'">
                {{ scope.row.transfer_period?.toString() || "" }}
              </span>
              <span v-else>{{ scope.row[scope.column.property] }}</span>
            </template>
          </el-table-column>
        </template>
        <template slot="empty">
          <div style="margin-top: 15%">
            <loading v-if="loading"></loading>
            <div class="empty-container" v-else>暂无数据～</div>
          </div>
        </template>
      </el-table>
      <div class="tg-pagination">
        <span class="el-pagination__total">共 {{ tableInfo.total }} 条</span>
        <el-pagination
          background
          layout="sizes,prev,pager,next,jumper"
          :total="tableInfo.total"
          :page-size="tableInfo.page_size"
          :current-page="tableInfo.page"
          @current-change="currentChange"
          @size-change="sizeChange"
          :page-sizes="[10, 20, 50, 100]"
        ></el-pagination>
      </div>
    </div>
    <receipt-detail
      v-if="detail_visible"
      :receiptNo="receipt_no"
      :receiptType="receipt_type"
      :payTime="pay_time"
      :status="receipt_status"
      @close="detail_visible = false"
    ></receipt-detail>
    <acceptMoneyDialog ref="acceptMoney"></acceptMoneyDialog>
    <batchFix ref="batchFix" :list="checked_order"></batchFix>
    <invoiced
      v-if="invoiced_visible"
      @close="invoiced_visible = false"
      :list="invoiced_list"
    ></invoiced>
    <carry-over-detail
      :orderId="order_id"
      :receipt_no="receipt_no"
      @close="corry_detail_visible = false"
      v-if="corry_detail_visible"
    ></carry-over-detail>
    <refund-detail
      :orderId="order_id"
      @close="refund_detail_visible = false"
      v-if="refund_detail_visible"
    ></refund-detail>

    <couponsDetailDialog
      :list="couponsList"
      v-if="coupons_detail_visible"
      @close="coupons_detail_visible = false"
    ></couponsDetailDialog>
    <transferFeeDialog
      :receipt_no="receipt_no"
      v-if="transferFeeDialogVisiable"
      @close="transferFeeDialogVisiable = false"
    />
    <transferFeeDetail
      :receipt_no="receipt_no"
      v-if="transferFeeDetailVisiable"
      @close="transferFeeDetailVisiable = false"
    />
    <InvoiceEdit
      v-if="invoiceEditVisiable"
      @close="invoiceEditVisiable = false"
      pageType="edit"
      :receipt_infos="receipt_infos"
      :price="invoice_total_price"
    ></InvoiceEdit>
    <compTool ref="compTool" />
    <comPushSign
      v-if="checked_order.length != 0"
      ref="comPushSign"
      :student_id="this.checked_order[0].student_id"
      @pushSuccess="searchVal"
    />
    <fillJDExpressDeliveryDialog
      v-if="isShowFillJDInfoDialog"
      :dialogVisible="isShowFillJDInfoDialog"
      @close="handleFillJDExpressDeliveryClose"
      :tableData="checked_order"
    ></fillJDExpressDeliveryDialog>
    <flowInfo
      v-if="isShowexprEssInformationDialog"
      :visible="isShowexprEssInformationDialog"
      :jd_track_no="curJdTrackNo"
      @close="isShowexprEssInformationDialog = false"
    ></flowInfo>
    <importExpress
      v-if="importExpressVisible"
      @close="importExpressVisible = false"
    />
  </div>
</template>

<script>
import { mapState } from "vuex";
import receiptDetail from "./components/detail.vue"; // 收费详情
import CarryOverDetail from "./components/carryOverDetail.vue"; // 结转详情
import RefundDetail from "./components/refundDetail.vue"; // 结转详情
import acceptMoneyDialog from "./components/acceptMoneyDialog.vue";
import couponsDetailDialog from "./components/couponsDetailDialog";
import importExpress from "./components/importExpress.vue"; // 导入快递信息
import invoiced from "./components/invoiced.vue";
import batchFix from "./components/batchFix.vue";
import chargeApi from "@/api/charge";
import receiptApi from "@/api/receipt";
import channelApi from "@/api/channel";
import periodApi from "@/api/period";
import { tw_remark_list, tw_channel_list } from "@/public/dict";
// import invoiceApi from "@/api/invoice";

// import { downLoadFile } from "@/public/downLoadFile";
import transferFeeDialog from "@/components/schoolTransferFee/transferFeeDialog.vue";
import transferFeeDetail from "@/components/schoolTransferFee/transferFeeDetail.vue";
import { export_excel_sync_new } from "@/public/asyncExport";
import loading from "../loading";
import Vue from "vue";
import quickTime from "@/public/quickTime";
import systemSettingApi from "@/api/systemSetting";
import { tableSummaries } from "@/mixins/tableSummaries";
import InvoiceEdit from "@/views/invoice/edit.vue";
import compTool from "@/components/comp-tool";
import comPushSign from "@/components/com-push-sign";
import electronicApi from "@/api/electronic"; // 电子签
import FileSaver from "file-saver";
import fillJDExpressDeliveryDialog from "./components/fillJDExpressDeliveryDialog";
import flowInfo from "./components/flowInfo";
// import { downLoadFileByHref } from "@/public/downLoadFile";
tw_remark_list.unshift({
  id: "",
  name: "不限"
});
export default {
  name: "ReceiptManagement",
  mixins: [tableSummaries],
  data() {
    return {
      isExport: false,
      importExpressVisible: false,
      refund_reason: [],
      exportLoading: false,
      loading: true,
      detail_visible: false,
      corry_detail_visible: false,
      refund_detail_visible: false,
      invoiced_visible: false,
      coupons_detail_visible: false,
      transferFeeDialogVisiable: false,
      transferFeeDetailVisiable: false,
      invoiceEditVisiable: false,
      can_invoiced: true,
      invoiced_list: [], // 已经开票的弹出层
      order_id: "",
      receipt_no: "",
      receipt_infos: [],
      invoice_total_price: 0,
      pay_time: "",
      receipt_type: "",
      receipt_status: "",
      couponsList: [],
      jd_track_options: [
        {
          id: "",
          name: "不限"
        },
        {
          id: "yes",
          name: "有"
        },
        {
          id: "no",
          name: "无"
        }
      ],
      search_title: [
        {
          props: "stu",
          label: "学员信息",
          type: "input",
          show: true,
          placeholder: "请输入学员姓名/学号/手机号"
        },
        {
          props: "receiptType",
          lable: "",
          type: "checkboxGroup",
          show: true,
          option: []
        },
        // {
        //   props: "billState",
        //   label: "发票状态",
        //   type: "select",
        //   show: false,
        //   selectOptions: [
        //     { id: "1", name: "不限" },
        //     { id: "3", name: "已开票" },
        //     { id: "4", name: "未开票" },
        //   ],
        // },
        {
          props: "receiptState",
          label: "收据状态",
          type: "select",
          show: false,
          selectOptions: [
            { id: "1", name: "创建" },
            { id: "2", name: "作废" }
          ]
        },
        {
          props: "chargeType",
          label: "收费类型",
          type: "mutipleSelect",
          show: false,
          selectOptions: []
        },
        {
          props: "pay_time",
          label: "交费日期",
          type: "date",
          show: false,
          selectOptions: [],
          has_options: true
        },
        {
          props: "course_id",
          label: "课程名称",
          show: false,
          type: "choose_course"
        },
        {
          props: "order_id",
          label: "订单信息",
          type: "input",
          show: true,
          style: {
            width: "220px"
          },
          placeholder: "请输入订单编号"
        },
        {
          props: "receiptNum",
          label: "收据号",
          type: "input",
          show: false,
          placeholder: "请输入收据单号"
        },
        {
          props: "handlers",
          label: "经手人",
          type: "course_staff",
          is_leave: true,
          show: false
        },
        {
          props: "belongPeople",
          label: "业绩归属人",
          type: "mark_staff",
          is_leave: true,
          show: false
        },
        {
          props: "outer_remark",
          label: "外部备注",
          type: "input",
          show: false,
          placeholder: "请输入外部备注"
        },
        {
          props: "inner_remark",
          label: "内部备注",
          type: "input",
          show: false,
          placeholder: "请输入内部备注"
        },
        {
          props: "billSource",
          label: "收据来源",
          type: "select",
          show: false,
          selectOptions: [
            { name: "不限", id: "" },
            { id: "front_end", name: "前台订单" },
            { id: "beginning", name: "期初导入" },
            { id: "tw_import", name: "TW导入" },
            { id: "tw_h5", name: "TW支付" },
            { id: "wechat_mini", name: "微信小程序" }
          ]
        },
        {
          props: "payAccount",
          label: "收款方式",
          type: "mutipleSelect",
          show: false,
          selectOptions: []
        },
        {
          props: "isPrint",
          label: "是否打印",
          type: "select",
          show: false,
          selectOptions: [
            { name: "不限", id: "" },
            { id: "1", name: "已打印" },
            { id: "0", name: "未打印" }
          ]
        },

        {
          props: ["channel_id", "sub_channel_id", "channel_ids"],
          label: "市场渠道",
          type: "choose_channel",
          show: false
        },

        {
          props: [
            "order_source_first",
            "order_source_second",
            "order_channel_ids"
          ],
          label: "订单来源",
          type: "choose_channel",
          show: false
        },
        {
          props: "tw_source_id",
          label: "渠道Id",
          type: "input",
          show: false,
          placeholder: "请输入渠道Id"
        },
        {
          props: "tw_period",
          label: "报名期次",
          type: "input",
          show: false,
          placeholder: "请输入报名期次"
        },
        {
          props: "tw_remark",
          label: "退费来源",
          type: "select",
          show: false,
          selectOptions: tw_remark_list
        },
        {
          props: "contract_status",
          label: "电子签状态",
          type: "select",
          show: true,
          selectOptions: [
            { name: "不限", id: undefined },
            { name: "已推送", id: "signing" },
            { name: "已签署", id: "done" },
            { name: "签署失败", id: "failed" },
            { name: "解除中", id: "releasing" },
            { name: "已解除", id: "release" }
          ]
        },
        {
          props: "contract_is_sign",
          label: "是否推送电子签",
          type: "select",
          show: true,
          selectOptions: [
            { name: "不限", id: "" },
            { name: "已推送", id: "yes" },
            { name: "未推送", id: "no" }
          ]
        },
        {
          props: "is_ylb",
          label: "元萝卜订单",
          type: "select",
          show: true,
          selectOptions: [
            { name: "不限", id: "" },
            { name: "是", id: "yes" },
            { name: "否", id: "no" }
          ]
        },
        {
          props: "jd_track_no",
          label: "是否有京东物流",
          type: "custom-slot",
          slot: "jd_track",
          show: true
        },
        // SN码
        {
          props: "ylb_sn_no",
          label: "SN码",
          type: "input",
          show: true
        },
        // 发货方式
        {
          props: "delivery_method",
          label: "发货方式",
          type: "select",
          show: true,
          selectOptions: [
            { name: "不限", id: "" },
            { name: "到店领取", id: "arrive" },
            { name: "快递", id: "jd" }
          ]
        },

        // 第三方订单号
        {
          props: "third_order_id",
          label: "第三方订单号",
          type: "input",
          show: true,
          placeholder: "请输入第三方订单号"
        }
        // {
        //   props: "invoice_status",
        //   label: "开票状态",
        //   show: true,
        //   width: 140,
        //   type: "select",
        //   // 开票状态 0-未申请 1-审批中 2-已开票 3-拒绝开票 4-审批完成待开票 5-开票作废
        //   selectOptions: [
        //     { name: "未申请", id: 0 },
        //     { name: "审批中", id: 1 },
        //     { name: "已开票", id: 2 },
        //     { name: "拒绝开票", id: 3 },
        //     { name: "审批完成待开票", id: 4 },
        //     { name: "开票作废", id: 5 }
        //   ]
        // }
      ],
      searchForm: {
        jd_track_no: "",
        has_jd_track: "",
        course_id: "",
        stu: "",
        chargeType: "",
        receiptType: [
          "order_paid",
          "refund_order",
          "transfer_order",
          "transfer_fee_come",
          "transfer_fee_out"
        ],
        // billState: "",
        receiptState: "1",
        pay_time: "",
        receiptNum: "",
        order_id: "",
        // billNum: "",
        handlers: "",
        tw_remark: "",
        belongPeople: "",
        outer_remark: "",
        inner_remark: "",
        billSource: "",
        payAccount: [],
        isPrint: "",
        // admissionsSource: "",
        channel_id: "",
        sub_channel_id: "",
        tw_source_id: "",
        tw_period: "",
        order_source_first: "",
        order_source_second: "",
        sort: "",
        contract_status: undefined,
        contract_is_sign: "",
        is_ylb: "",
        delivery_method: "",
        ylb_sn_no: "",
        third_order_id: "",
        invoice_status: ""
      },
      table_title: [
        {
          props: "pay_time",
          label: "收据日期",
          show: true,
          width: 160,
          fixed: true,
          sort: "custom"
        },
        {
          props: "receipt_no",
          label: "收据号",
          show: true,
          width: 130,
          fixed: true
        },
        {
          props: "invoice_status",
          label: "开票状态",
          show: true,
          width: 120,
          sort: "custom"
        },
        {
          props: "expressInformation",
          label: "快递信息",
          show: true,
          width: 120,
          sort: "custom"
        },
        {
          props: "order_id",
          label: "订单编号",
          show: true,
          width: 160
        },
        {
          props: "bill_no",
          label: "发票号",
          show: true,
          width: 140
        },
        {
          props: "fee_type",
          label: "收费类型",
          show: true,
          width: 140
        },
        {
          props: "company_tax_no",
          label: "公司税号",
          show: true,
          width: 140
        },
        {
          props: "student_number",
          label: "学号",
          show: true,
          width: 140,
          sort: "custom"
        },
        {
          props: "student_name",
          label: "姓名",
          show: true,
          width: 140,
          sort: "custom"
        },
        {
          props: "mobile",
          label: "手机号码",
          show: true,
          width: 140,
          sort: "custom"
        },
        {
          props: "student_category",
          label: "学员类别",
          show: true,
          width: 140,
          type: "select"
        },
        {
          props: "enrollment_source",
          label: "市场渠道",
          show: true,
          width: 140
        },
        {
          props: "course_name",
          label: "课程名称",
          show: true,
          width: 140,
          type: "select"
        },
        {
          props: "course_price",
          label: "课程实交金额",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "course_able_price",
          label: "课程应交金额",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "article_price",
          label: "物品金额",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "article_name",
          label: "物品名称",
          show: true,
          width: 140,
          type: "select"
        },
        {
          props: "match_name",
          label: "赛事名称",
          show: true,
          width: 140,
          type: "select"
        },
        {
          props: "package_name",
          label: "教辅包名称",
          show: true,
          width: 140,
          type: "select"
        },
        {
          props: "discount_price",
          label: "折扣金额",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "direct_offset",
          label: "直减金额",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "wallet_offset",
          label: "电子钱包抵扣金额",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "coupons",
          label: "优惠券",
          show: true,
          width: 140
        },
        {
          props: "coupon_offset",
          label: "优惠券金额",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "coupon_plan_name",
          label: "优惠方案名称",
          show: true,
          width: 140
        },
        {
          props: "coupon_discount_money",
          label: "优惠方案金额",
          show: true,
          width: 140,
          type: "price"
        },

        {
          props: "original_price",
          label: "应交金额",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "actual_price",
          label: "实交金额",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "payable_refund_price",
          label: "应退金额",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "refundable_course_price",
          label: "应退课程金额",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "refundable_article_price",
          label: "应退物品金额",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "actual_refund_price",
          label: "实退金额",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "refund_course_price",
          label: "实退课程金额",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "refund_article_price",
          label: "实退物品金额",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "carryover_price",
          label: "结转金额",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "actual_carryover_price",
          label: "实际结转金额",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "cash_price",
          label: "现金收款",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "cash_replace_price",
          label: "虚拟收款",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "other_price",
          label: "第三方延迟收款",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "qr_code_price",
          label: "二维码收款",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "qr_code_replace_price",
          label: "易宝-在线支付（二维码）收款",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "pos_price",
          label: "POS机收款",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "transfer_price",
          label: "转账汇款",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "yb_static_qr_code_price",
          label: "易宝-静态二维码&pos机线下",
          show: true,
          width: 200,
          type: "price"
        },
        {
          props: "transfer_cross_campus_price",
          label: "转校转费-跨校区",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "hui_fu_static_qr_code_price",
          label: "汇付静态二维码",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "cft_price",
          label: "财付通收款",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "xhs_shop_price",
          label: "小红书店铺收款",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "tiktok_shop_price",
          label: "抖音店铺收款",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "view_account_shop_price",
          label: "视频号店铺收款",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "kuaishou_shop_price",
          label: "快手店铺收款",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "taobao_shop_price",
          label: "淘宝店铺收款",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "wwxx_shop_price",
          label: "万物心选店铺收款",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "mami_shop_price",
          label: "妈觅店铺收款",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "xhs_sxt_price",
          label: "小红书私信通收款",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "inner_platform_price",
          label: "内部平台收款",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "star_platform_price",
          label: "其他达人平台收款",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "dianping_price",
          label: "大众点评-美团收款",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: " tiktok_local_price",
          label: "抖音本地生活收款",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "baidu_price",
          label: "百度收款",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "xhs_zyx_price",
          label: "直营校-小红书收款",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "gaode_price",
          label: "高德收款",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "union_price",
          label: "银联收款",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "cash_refund_replace_price",
          label: "虚拟退款",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "third_delay_replace_price",
          label: "第三方退款",
          show: true,
          width: 140,
          type: "price"
        },
        {
          props: "origin_back_price",
          label: "原路退回金额",
          show: true,
          width: 140,
          type: "price"
        },
        // {
        //   props: "other_price",
        //   label: "其他账户",
        //   show: true,
        //   width: 140,
        // },
        {
          props: "third_order_id",
          label: "第三方支付单号",
          show: true,
          width: 190
        },
        {
          props: "area_name",
          label: "区域",
          show: true,
          width: 140
        },
        {
          props: "department_name",
          label: "校区",
          show: true,
          width: 140
        },
        {
          props: "order_source_first_name",
          label: "订单一级渠道",
          show: true,
          width: 180,
          type: "selectInput"
        },
        {
          props: "order_source_second_name",
          label: "订单二级渠道",
          show: true,
          width: 180,
          type: "selectInputSub"
        },
        {
          props: "order_channel_type",
          label: "渠道类型",
          show: true,
          width: 180
        },
        {
          props: "tw_source_id",
          label: "渠道Id",
          show: true,
          width: 180
        },
        {
          props: "tw_period",
          label: "报名期次",
          show: true,
          width: 180
        },
        {
          props: "volume_period",
          label: "放量期次",
          show: true,
          width: 180
        },
        {
          props: "transfer_period",
          label: "转化期次",
          show: true,
          width: 180
        },
        {
          props: "receipt_type",
          label: "收据类型",
          show: true,
          width: 140
        },
        {
          props: "pay_types",
          label: "收款方式",
          show: true,
          width: 140,
          type: "select"
        },

        {
          props: "payCardNum",
          label: "付款卡号",
          show: true,
          width: 140
        },
        {
          props: "is_print",
          label: "打印状态",
          show: true,
          width: 140
        },
        {
          props: "performances",
          label: "业绩归属人",
          show: true,
          width: 140,
          type: "select"
        },
        {
          props: "class_num",
          label: "上课次数",
          show: true,
          width: 100,
          type: "select"
        },
        {
          props: "renovator",
          label: "收费人",
          show: true,
          width: 140
        },
        {
          props: "creator",
          label: "制单人",
          show: true,
          width: 140
        },
        {
          props: "outer_remark",
          label: "外部备注",
          show: true,
          width: 140
        },
        {
          props: "inner_remark",
          label: "内部备注",
          show: true,
          width: 140
        },
        {
          props: "refund_carryover_reason",
          label: "退费结转原因",
          show: true,
          width: 140
        },
        {
          props: "tw_remark",
          label: "退费来源",
          show: true,
          width: 140
        },
        {
          props: "merchant_name",
          label: "公司主体",
          show: true,
          width: 140
        },
        {
          props: "is_contract_status",
          label: "是否推送电子签",
          show: true,
          width: 140
        },
        {
          props: "contract_status",
          label: "电子签状态",
          show: true,
          width: 140
        },
        {
          props: "sin_sign",
          label: "查看电子签",
          show: true,
          width: 140
        },
        {
          props: "is_ylb",
          label: "元萝卜订单",
          show: true,
          width: 140
        },
        {
          props: "logistics_receipt_date",
          label: "物流签收日期",
          show: true,
          width: 140
        },
        {
          props: "seven_days_receipt_date",
          label: "7天确收日期",
          show: true,
          width: 140
        },
        {
          props: "delivery_method",
          label: "发货方式",
          show: true,
          width: 140
        },
        {
          props: "ylb_sn_no",
          label: "SN码",
          show: true,
          width: 140
        }
      ],
      tableInfo: {
        list: [],
        loading: false,
        total: 0,
        page_size: 10,
        page: 1
      },
      checked_order: [],
      payWayOptions: [],
      receipTypeDict: null,
      receipStatusDict: null,
      category_list: [],
      idList: [],
      discard: false,
      receipt_total: {},
      channe_list: [],
      table_index: null,
      table_list: [],
      refund_carryover_reason_name: "",
      can_edit_channel: false,
      is_electronic: false,
      isShowFillJDInfoDialog: false,
      isShowexprEssInformationDialog: false,
      curJdTrackNo: "",
      volume_period_options: [],
      transfer_period_options: [],
      is_edit_period: false
    };
  },
  components: {
    receiptDetail,
    acceptMoneyDialog,
    couponsDetailDialog,
    batchFix,
    flowInfo,
    fillJDExpressDeliveryDialog,
    CarryOverDetail,
    RefundDetail,
    invoiced,
    loading,
    transferFeeDialog,
    transferFeeDetail,
    InvoiceEdit,
    compTool,
    comPushSign,
    importExpress
  },
  watch: {
    table_title: {
      handler() {
        this.clearSelection();
      },
      immediate: true,
      deep: true
    },
    ChargeCategory() {
      this.getChargeTypeList();
    },
    discard(val) {
      if (val) {
        this.searchForm.receiptState = "";
      } else {
        this.searchForm.receiptState = "1";
      }
      this.search();
    },
    ids(val) {
      this.idList = val.split(",");
      this.searchForm.channel_id = "";
      this.searchForm.sub_channel_id = "";
      this.searchForm.channel_ids = [];
      this.searchForm.channel_ids_name = [];
      this.searchForm.order_channel_ids = [];
      this.searchForm.order_channel_ids_name = [];
      this.search();
    },
    departmentId(val) {
      this.filterDepartmentId(val);
    }
  },
  filters: {
    channel_type_filter(val) {
      const obj = tw_channel_list.find((item) => item.value === val);
      return obj ? obj.label : "";
    },
    tw_remark_filter(val) {
      if (val) {
        return tw_remark_list.find((item) => item.id === val)?.name;
      }
      return "";
    },
    channelNameFilter: function (value) {
      if (value) {
        const arr = [];
        const { channelName, subChannelName } = value;
        if (channelName) {
          arr.push(channelName);
        }
        if (subChannelName) {
          arr.push(subChannelName);
        }
        return arr.join("—");
      }
      return "";
    },
    couponsNameFilter: function (value) {
      if (value) {
        return value.map((item) => item.templateName).join("、");
      }
      return "";
    },
    couponsAmountFilter: function (value) {
      if (value) {
        let sum = 0;
        value.map((item) => {
          sum += item.discountAmount;
        });
        return sum.toFixed(2);
      }
      return "";
    },
    performanceUserFilter(value) {
      if (value) {
        return value.map((item) => item.userName).join("、");
      }
      return "";
    },
    payCardNumFilter(value) {
      if (value) {
        const arr = value.filter((item) => item.paymentMethod === "transfer");
        return arr[0] ? arr[0].paymentAccount : "";
      }
      return "";
    },
    getFilterPaymentMethodCh(val, list) {
      if (val) {
        const payCh = [];
        val.map((item) => {
          const arr = list.filter((item2) => {
            return item2.id === item.paymentMethod;
          });
          if (arr.length) {
            payCh.push(arr[0].name);
          }
        });
        return payCh.join("、");
      }
      return "";
    },
    itemsNameFilter(value, type) {
      if (value) {
        const arr = value.filter((item) => item.businessType === type);
        return arr.map((item) => item.productName).join("、");
      }
      return "";
    },
    discountPlanNameFilter(value) {
      const strs = [];
      value.map((item) => {
        if (item.itemFee.discountPlanObject) {
          strs.push(item.itemFee.discountPlanObject.discountPlanName);
        }
      });
      return strs.join("、");
    },
    zkjeFilter(value) {
      if (value) {
        let num = 0;
        const { orderOriginPrice, orderAfterDiscountPrice } = value;
        num = orderOriginPrice - orderAfterDiscountPrice;
        return num.toFixed(2);
      }
      return "";
    },
    payWayAmountFilter(value, type) {
      if (value && value.length) {
        const arr = value.filter((item) => item.paymentMethod === type);
        return arr[0] ? arr[0].paymentAmount.toFixed(2) : "";
      }
      return "";
    },
    receipTypeFilter(value, obj) {
      if (value && obj) {
        return obj[value];
      }
      return "";
    },
    // reasonFilter(val) {
    //   if (val) {
    //     const arr = refund_reason.filter((item) => item.id === val);
    //     return arr[0] && arr[0].label;
    //   }
    //   return "";
    // },
    feeTypeFilter(val, list) {
      if (val) {
        const payCh = [];
        val.map((item) => {
          const arr = list.filter((item2) => {
            return item2.id === item;
          });
          if (arr.length) {
            payCh.push(arr[0].name);
          }
          // if (item === "article") {
          //   payCh.push("物品");
          // }
          // if (item === "match") {
          //   payCh.push("赛事");
          // }
        });
        return payCh.join("、");
      }
      return "";
    }
  },
  methods: {
    editThirdOrderId(row) {
      this.$confirm("确定要修改第三方订单号吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        receiptApi
          .editThirdOrderId({
            receipt_no: row.receipt_no,
            third_order_id: row.third_order_id
          })
          .then((res) => {
            if (res.data.code === 0) {
              this.$message.success("修改成功");
            } else {
              // 如果编辑失败，则恢复原来的第三方订单号
              row.third_order_id = row.third_order_id_old;
              this.$message.error(res.data.message);
            }
          })
          .catch((err) => {
            this.$message.error(err.message);
          });
      });
    },
    importExpress() {
      this.importExpressVisible = true;
    },
    // 下载模版
    downloadExpressTemplate() {
      const downloadElement = document.createElement("a");
      downloadElement.href =
        "./express_template.xlsx?v=" + window.__APP_VERSION__;
      downloadElement.download = `快递信息导入模版.xlsx`; // 下载后文件名
      document.body.appendChild(downloadElement);
      downloadElement.click(); // 点击下载
      document.body.removeChild(downloadElement);
    },
    jdTrackChange(val) {
      if (val === "no" || val === "") {
        this.searchForm.jd_track_no = "";
      }
    },
    getRowTwPeriodList(row) {
      // 获取网校期次列表
      const course_name = row.course_name.join(",");

      periodApi
        .getChargeList({
          search_start: row.pay_time,
          search_end: row.pay_time,
          course_name
        })
        .then((res) => {
          if (res.data.code === 0) {
            const arr = res.data.data || [];
            if (arr.length) {
              row.tw_period_options = arr;
            }
          }
        });
    },
    async getRefundTransferReason() {
      const { data } = await systemSettingApi.refundTransferReason({
        department_id: this.departmentId,
        name: ["退费原因设置", "结转原因设置"]
      });
      if (data.message === "success") {
        this.refund_reason = data.data ?? [];
      }
    },
    sortChange(val) {
      const { prop, order } = val;
      let _oreder = "";
      if (order === "ascending") {
        _oreder = "asc";
      } else if (order === "descending") {
        _oreder = "desc";
      }
      this.searchForm.sort = `${prop} ${_oreder}`;
      this.search();
    },
    refundReasonFilter(val) {
      if (val) {
        const arr = this.refund_reason?.filter((item) => item.id === val);
        return arr[0]?.name;
      }
      return "";
    },
    couponPlanNameFilter(val) {
      if (val) {
        return val.join(",");
      }
    },
    selectInput(val, index) {
      if (!val.order_source_second_name) {
        this.table_index = index;
        this.getSubChannel(val.order_source_first);
      }
    },
    selectClick(row, index) {
      if (!row.sub_channe_list) {
        this.table_index = index;
        this.getSubChannel(row.order_source_first);
      }
    },
    async getPaymentMethod() {
      const res = await chargeApi.paymentMethod();
      const { data } = res.data;
      const obj = this.search_title.find((item) => item.props === "payAccount");
      if (data) {
        const arr = [];
        for (const key in data) {
          arr.push({ name: data[key].value, id: data[key].key });
        }
        obj.selectOptions = arr;
        // obj.selectOptions.unshift({ name: "不限", id: "" });

        this.payWayOptions = arr;
      }
    },
    goCouponsDetail(list) {
      this.couponsList = list;
      this.coupons_detail_visible = true;
    },
    getReceipType() {
      receiptApi.getReceipType().then((res) => {
        const { data } = res;
        const arr = [];
        const item = data.data;
        for (const key in item) {
          if (Object.hasOwnProperty.call(item, key)) {
            arr.push({
              name: item[key],
              value: key
            });
          }
        }
        this.receipTypeDict = data.data;
        this.search_title[1].option = arr;
      });
    },
    // 重置数据
    reset() {
      this.tableInfo.page = 1;
      this.searchForm = {
        course_id: "",
        stu: "",
        chargeType: "",
        receiptType: [
          "order_paid",
          "refund_order",
          "transfer_order",
          "transfer_fee_come",
          "transfer_fee_out"
        ],
        // billState: "",
        receiptState: "1",
        pay_time: "",
        order_id: "",
        receiptNum: "",
        billNum: "",
        handlers: "",
        tw_remark: "",
        belongPeople: "",
        outer_remark: "",
        inner_remark: "",
        billSource: "",
        payAccount: [],
        isPrint: "",
        channel_id: "",
        sub_channel_id: "",
        tw_source_id: "",
        tw_period: "",
        sort: "",
        contract_status: undefined,
        contract_is_sign: "",
        is_ylb: "",
        has_jd_track: "",
        jd_track_no: "",
        delivery_method: "",
        ylb_sn_no: "",
        third_order_id: "",
        invoice_status: ""
      };
      this.clearSelection();
      this.tableInfo.page_size = 10;
      this.tableInfo.page = 1;
      this.$refs.table.clearSort();
      this.search();
    },
    searchVal() {
      this.clearSelection();
      this.tableInfo.page = 1;
      this.search();
    },
    clearSelection() {
      this.$nextTick(() => {
        this.$refs.table.clearSelection();
      });
    },
    //
    searchProtoAdapter() {
      const searchForm = this.searchForm;
      const completedDate = searchForm.pay_time;
      let pay_start_time = null;
      let pay_end_time = null;
      if (completedDate && completedDate.length) {
        // completedDateRange = {
        //   first: new Date(completedDate[0]).getTime(),
        //   second: new Date(completedDate[1]).getTime()
        // };
        pay_start_time = completedDate[0];
        pay_end_time = completedDate[1];
      }
      console.log(searchForm);
      const params = {
        student_info: searchForm.stu,
        receipt_type: searchForm.receiptType,
        receipt_status: searchForm.receiptState,
        // fee_type: searchForm.chargeType,
        pay_start_time,
        pay_end_time,
        course_name: searchForm.course_id_name,
        receipt_no: searchForm.receiptNum,
        order_id: searchForm.order_id,
        handled_by: searchForm.handlers,
        performance: searchForm.belongPeople,
        source: searchForm.billSource,
        pay_method: searchForm.payAccount,
        is_print: searchForm.isPrint,
        tw_remark: searchForm.tw_remark,
        student_source_first: searchForm.channel_id,
        student_source_two: searchForm.sub_channel_id,
        department_ids: this.idList,
        order_source_first: searchForm.order_source_first,
        order_source_second: searchForm.order_source_second,
        tw_source_id: searchForm.tw_source_id,
        tw_period: searchForm.tw_period,
        inner_remark: searchForm.inner_remark,
        outer_remark: searchForm.outer_remark,
        sort: searchForm.sort,
        contract_status: searchForm.contract_status,
        contract_is_sign: searchForm.contract_is_sign,
        is_ylb: searchForm.is_ylb,
        has_jd_track: searchForm.has_jd_track,
        jd_track_no: searchForm.jd_track_no,
        delivery_method: searchForm.delivery_method,
        ylb_sn_no: searchForm.ylb_sn_no,
        third_order_id: searchForm.third_order_id,
        invoice_status: searchForm.invoice_status
      };
      if (!searchForm.belongPeople) {
        Vue.delete(params, "performance");
      }
      if (searchForm.chargeType) {
        params.fee_type = searchForm.chargeType;
      }
      if (searchForm.course_id && searchForm.course_id.length > 0) {
        params.course_id = searchForm.course_id.split(",");
      }
      return params;
    },
    // 查询
    search() {
      this.loading = true;
      this.tableInfo.list = [];
      const params = this.searchProtoAdapter();
      const { page, page_size } = this.tableInfo;
      const query = {
        page,
        page_size,
        ...params
      };
      this.tableInfo.loading = true;
      receiptApi.getReceiptListNew(query).then((res) => {
        const { data } = res.data;
        if (res.data.code === 0) {
          data.results.map((item) => {
            item.tw_period_options = [];
            item.third_order_id_old = item.third_order_id;

            // 处理数组类型的期次字段，转换为显示用的字符串
            item.volume_period_display = Array.isArray(item.volume_period)
              ? item.volume_period.length > 0
                ? item.volume_period[0]
                : ""
              : item.volume_period || "";
            item.transfer_period_display = Array.isArray(item.transfer_period)
              ? item.transfer_period.length > 0
                ? item.transfer_period[0]
                : ""
              : item.transfer_period || "";
          });
          this.tableInfo.list = JSON.parse(JSON.stringify(data.results));

          this.table_list = JSON.parse(JSON.stringify(data.results));
          this.tableInfo.total = data.count;
          this.loading = false;
          this.totalLoaded = false;
        } else {
          this.$message.error(res.data.message);
          this.loading = false;
        }
      });
      // receiptApi.receiptTotal(params).then((res) => {
      //   this.receipt_total = res.data.data;
      // });
    },
    // 获取合计数据
    obtainSumData() {
      const params = this.searchProtoAdapter();
      this.totalLoading = true;
      receiptApi
        .receiptTotal(params)
        .then((res) => {
          this.totalLoading = false;

          const { data, code } = res.data;
          if (code === 0) {
            this.receipt_total = data;
            this.totalLoaded = true;
          } else {
            this.$message.error("获取合计数据失败！");
          }
        })
        .catch(() => {
          this.totalLoading = false;
        });
    },
    // 接受款账户汇总
    acceptMoney() {
      const date = this.searchForm.pay_time || [];
      this.$refs.acceptMoney.openDialog({ date });
    },
    // 批量修改有效期
    batchDate() {
      if (this.checked_order.length <= 0) return;
      this.$refs.batchFix.openDialog();
    },

    exportDataNew() {
      const opt = {
        vm: this, // vue组件实例，
        api_url: "/api/report-center-service/admin/receipt/export", // 导出接口地址
        file_name: "收据列表", // 文件名
        success_msg: "收据列表导出成功！", // 导出成功的提示语
        error_msg: "收据列表导出失败！", // 导出失败的提示语,
        query: this.searchProtoAdapter() // 列表的筛选值
      };
      export_excel_sync_new(opt);
    },
    // 申请开票
    // applyInvoice() {
    //   const params = [];
    //   this.checked_order.map((item) => {
    //     params.push({
    //       receipt_id: item.receipt_no,
    //       receipt_price: item.actual_price * 100, // 金额以分为单位
    //       receipt_type: item.receipt_type,
    //       order_id: item.order_id,
    //       department_id: item.department_id
    //     });
    //   });
    //   invoiceApi
    //     .apply({
    //       receipt_infos: params
    //     })
    //     .then((res) => {
    //       const { code, data, message } = res.data;
    //       if (code === 0) {
    //         this.$message.success("提交成功！");
    //         // setTimeout(() => {
    //         this.clearSelection();
    //         this.search();
    //         // }, 3000);
    //       } else if (code === 2) {
    //         const receipt_list = data.filter((item) => !item.is_apply);
    //         this.invoiced_visible = true;
    //         this.invoiced_list = receipt_list;
    //       } else {
    //         this.$message.error(message);
    //       }
    //     });
    // },

    // 批量开票
    batchApplyInvoice() {
      const checkSameValue = this.checked_order.every(
        (item) => item.student_name === this.checked_order[0].student_name
      );
      if (!this.checked_order.length) {
        this.$message.warning("请选择收据！");
        return;
      }
      if (!checkSameValue) {
        this.$message.warning("请选择同一学员的收据！");
        return;
      }

      this.$confirm("确认要开票吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          const receipt_infos = [];
          this.checked_order.map((item) => {
            console.log(item);
            receipt_infos.push({
              receipt_id: item.receipt_no,
              receipt_price: Number((item.actual_price * 100).toFixed(2)), // 金额以分为单位
              receipt_type: item.receipt_type,
              order_id: item.order_id,
              department_id: item.department_id,
              pay_time: item.pay_time,
              receipt_status: item.receipt_status
            });
          });
          const invoice_total_price = this.checked_order.reduce(
            (total, item) => {
              return total + item.actual_price * 100; // 金额以分为单位
            },
            0
          );
          this.invoice_total_price = Number(invoice_total_price.toFixed(2));
          this.receipt_infos = receipt_infos;
          this.invoiceEditVisiable = true;
        })
        .catch(() => {});
    },
    batchFillJDExpressDelivery() {
      if (this.checked_order.length) {
        this.isShowFillJDInfoDialog = true;
      }
    },
    handleFillJDExpressDeliveryClose() {
      this.isShowFillJDInfoDialog = false;
    },
    openExpressInformationDialog(row) {
      this.curJdTrackNo = row.jd_track_no;
      this.isShowexprEssInformationDialog = true;
    },
    handleEssInformationDialogClose() {
      this.isShowexprEssInformationDialog = false;
    },
    // 获取选中项
    handleSelectionChange(val) {
      this.checked_order = val;
      // if (this.checked_order.length <= 0) {
      //   this.can_invoiced = false;
      //   return;
      // }
      // const has_fund_order = this.checked_order.some(
      //   (item) => item.receipt_type === "refund_order" // 退费收据
      // );
      const every_order_paid = this.checked_order.every(
        (item) => item.receipt_type === "order_paid" // 收费收据
      );
      const invoiced = this.checked_order.some(
        (item) => item.invoice_status === "已开票"
      );
      // const has_transfer_order = this.checked_order.some(
      //   (item) => item.receipt_type === "transfer_order"
      // );
      // const has_transfer_order = this.checked_order.some(
      //   (item) => item.receipt_type === "transfer_order"
      // );

      // 不能开发票的情况
      if (!every_order_paid) {
        this.can_invoiced = false;
        return;
      }
      if (invoiced) {
        this.can_invoiced = false;
        return;
      }
      if (val.length > 1) {
        this.can_invoiced = false;
        return;
      }
      this.can_invoiced = true;
    },
    getRowKeys(row) {
      return row.id;
    },
    // 改变页数
    currentChange(page) {
      this.tableInfo.page = page;
      this.clearSelection();
      this.search();
    },
    sizeChange(val) {
      this.tableInfo.page = 1;
      this.tableInfo.page_size = val;
      this.search();
    },
    // 查看收据
    toDetail(row) {
      this.order_id = row.order_id;
      this.receipt_no = row.receipt_no;
      this.receipt_type = row.receipt_type;
      this.receipt_status = row.receipt_status;
      this.pay_time = row.createdAt;
      this.refund_carryover_reason_name = row?.refund_carryover_reason_name;
      if (row.receipt_type === "transfer_order") {
        // 结转
        this.corry_detail_visible = true;
      } else if (row.receipt_type === "order_paid") {
        // 收费
        this.detail_visible = true;
      } else if (row.receipt_type === "refund_order") {
        // 退费
        this.refund_detail_visible = true;
      } else if (row.receipt_type === "transfer_fee_come") {
        // 转费转入
        this.transferFeeDialogVisiable = true;
      } else if (row.receipt_type === "transfer_fee_out") {
        // 转费转出
        this.transferFeeDetailVisiable = true;
      }

      // if (row.receipt_type == 3) {
      //   this.corry_detail_visible = true;
      // } else if (row.receipt_type == 1) {
      //   this.detail_visible = true;
      // } else if (row.receipt_type == 2) {
      //   this.refund_detail_visible = true;
      // }
    },
    // 收费类型
    getChargeTypeList() {
      const arr = [
        { name: "赛事", id: "match" },
        { name: "物品", id: "article" }
      ];
      const { ChargeCategory } = this;
      for (const key in ChargeCategory) {
        arr.push({
          name: ChargeCategory[key].category_name,
          id: ChargeCategory[key].category_id
        });
      }
      this.category_list = arr;
      this.search_title[3].selectOptions = arr;
    },
    channelChange(val, row, index) {
      if (!val.id && row.receipt_type !== "order_paid") return;
      row.order_source_first_name = val.name;
      this.table_index = index;
      this.getSubChannel(val.id);
      if (val.name === this.table_list[index].order_source_first_name) return;
      const obj = {
        order_source_first: val.id,
        order_source_first_name: val.name,
        order_source_second: "",
        order_source_second_name: "",
        tw_period: row.tw_period,
        tw_source_id: "",
        receipt_no: row.receipt_no
      };

      receiptApi.orderEditChannel(obj).then((res) => {
        if (res.data.code === 0) {
          this.$message.success("订单一级渠道修改成功");
          setTimeout(() => {
            this.search();
          }, 500);
        }
      });
    },
    channelSubChange(val, row, index) {
      if (!val.id && row.receipt_type !== "order_paid") return;
      if (val.name === this.table_list[index].order_source_second_name) return;
      row.order_source_second_name = val.name;
      const obj = {
        order_source_first: row.order_source_first,
        order_source_first_name: row.order_source_first_name,
        order_source_second: val.id,
        order_source_second_name: val.name,
        tw_period: row.tw_period,
        tw_source_id: "",
        receipt_no: row.receipt_no
      };

      receiptApi.orderEditChannel(obj).then((res) => {
        if (res.data.code === 0) {
          this.$message.success("订单二级渠道修改成功");
          setTimeout(() => {
            this.search();
          }, 500);
        }
      });
    },
    getChannelList() {
      const query = {
        is_enabled: true,
        channel_filter: "YES",
        department_id: this.departmentId
      };
      channelApi.getChannelList(query).then((res) => {
        this.channe_list = res.data.results;
      });
    },
    getSubChannel(id) {
      const params = {
        parentid: id,
        page: 1,
        pageSize: 1000,
        _t: new Date().getTime(),
        order: "desc",
        is_enabled: true
      };
      channelApi.getSubChannelByChannelId({ params }).then((res) => {
        if (this.table_index !== null) {
          Vue.set(
            this.tableInfo.list[this.table_index],
            "sub_channe_list",
            res.data.results
          );
        }
      });
    },
    getSummaries(param) {
      const sums = [];
      const { columns } = param;
      const {
        actual_carryover_price,
        actual_price,
        actual_refund_price,
        carryover_price,
        direct_offset,
        discount_price,
        original_price,
        payable_refund_price,
        wallet_offset,
        coupon_offset
      } = this.receipt_total;
      columns.forEach((val, index) => {
        if (index === 1) {
          sums[index] = this.getSummariesJsx();
          return;
        }
        // 实际结转金额
        if (val.property === "actual_carryover_price") {
          sums[index] = this.getSummariesCellJsx(actual_carryover_price);
          // sums[index] = this.receipt_total.actual_carryover_price?.toFixed(2);
          return;
        }
        // 实交金额
        if (val.property === "actual_price") {
          sums[index] = this.getSummariesCellJsx(actual_price);
          // sums[index] = this.receipt_total.actual_price?.toFixed(2);
          return;
        }
        // 实退金额
        if (val.property === "actual_refund_price") {
          sums[index] = this.getSummariesCellJsx(actual_refund_price);
          // sums[index] = this.receipt_total.actual_refund_price?.toFixed(2);
          return;
        }
        // 结转金额
        if (val.property === "carryover_price") {
          sums[index] = this.getSummariesCellJsx(carryover_price);
          // sums[index] = this.receipt_total.carryover_price?.toFixed(2);
          return;
        }
        // 直减金额
        if (val.property === "direct_offset") {
          sums[index] = this.getSummariesCellJsx(direct_offset);
          // sums[index] = this.receipt_total.direct_offset?.toFixed(2);
          return;
        }
        // 折扣金额
        if (val.property === "discount_price") {
          sums[index] = this.getSummariesCellJsx(discount_price);
          // sums[index] = this.receipt_total.discount_price?.toFixed(2);
          return;
        }
        // 应交金额
        if (val.property === "original_price") {
          sums[index] = this.getSummariesCellJsx(original_price);
          // sums[index] = this.receipt_total.original_price?.toFixed(2);
          return;
        }
        // 应退金额
        if (val.property === "payable_refund_price") {
          // sums[index] = this.receipt_total.payable_refund_price?.toFixed(2);
          sums[index] = this.getSummariesCellJsx(payable_refund_price);
          return;
        }
        // 电子钱包抵扣金额
        if (val.property === "wallet_offset") {
          // sums[index] = this.receipt_total.wallet_offset?.toFixed(2);
          sums[index] = this.getSummariesCellJsx(wallet_offset);
        }
        // 优惠券金额
        if (val.property === "coupon_offset") {
          // sums[index] = this.receipt_total.coupon_offset?.toFixed(2);
          sums[index] = this.getSummariesCellJsx(coupon_offset);
        }
      });
      return sums;
    },
    changeSourceIdAdmin(value, row, index) {
      if (row.tw_source_id === "") {
        this.$message.error("渠道id不能为空!");
        row.tw_source_id = this.table_list[index].tw_source_id;
        return;
      } else if (row.tw_source_id === this.table_list[index].tw_source_id) {
        return;
      }
      const obj = {
        receipt_no: row.receipt_no,
        tw_source_id: row.tw_source_id,
        order_source_first: "",
        order_source_first_name: "",
        order_source_second: "",
        order_source_second_name: "",
        tw_period: row.tw_period
      };

      receiptApi.orderEditChannel(obj).then((res) => {
        if (res.data.code === 0) {
          this.$message.success("渠道Id修改成功!");
          setTimeout(() => {
            this.search();
          }, 500);
        }
      });
    },
    changeSourceId(value, row) {
      if (!row.new_tw_source_id || row.receipt_type !== "order_paid") return;
      const obj = {
        receipt_no: row.receipt_no,
        tw_source_id: row.new_tw_source_id,
        order_source_first: "",
        order_source_first_name: "",
        order_source_second: "",
        order_source_second_name: "",
        tw_period: row.tw_period
      };

      receiptApi.orderEditChannel(obj).then((res) => {
        if (res.data.code === 0) {
          this.$message.success("渠道Id修改成功!");
          setTimeout(() => {
            this.search();
          }, 500);
        }
      });
    },
    changePeriodAdmin(value, row, index) {
      if (row.tw_period === "") {
        this.$message.error("报名期次不能为空!");
        row.tw_period = this.table_list[index].tw_period;
        return;
      } else if (row.tw_period === this.table_list[index].tw_period) {
        return;
      }
      const obj = {
        receipt_no: row.receipt_no,
        tw_period: row.tw_period,
        tw_source_id: row.tw_source_id,
        order_source_first: row.order_source_first,
        order_source_first_name: row.order_source_first_name,
        order_source_second: row.order_source_second,
        order_source_second_name: row.order_source_second_name
      };

      receiptApi.orderEditChannel(obj).then((res) => {
        if (res.data.code === 0) {
          this.$message.success("报名期次修改成功!");
          setTimeout(() => {
            this.search();
          }, 500);
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    changePeriod(value, row) {
      if (!row.new_tw_period || row.receipt_type !== "order_paid") return;
      const obj = {
        receipt_no: row.receipt_no,
        tw_period: row.new_tw_period,
        tw_source_id: row.tw_source_id,
        order_source_first: row.order_source_first,
        order_source_first_name: row.order_source_first_name,
        order_source_second: row.order_source_second,
        order_source_second_name: row.order_source_second_name
      };

      receiptApi.orderEditChannel(obj).then((res) => {
        if (res.data.code === 0) {
          this.$message.success("报名期次修改成功!");
          setTimeout(() => {
            this.search();
          }, 500);
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 管理员修改放量期次
    // changeVolumePeriodAdmin(value, row, index) {
    //   if (row.volume_period === "") {
    //     this.$message.error("放量期次不能为空!");
    //     row.volume_period = this.table_list[index].volume_period;
    //     return;
    //   } else if (row.volume_period === this.table_list[index].volume_period) {
    //     return;
    //   }
    //   const obj = {
    //     receipt_no: row.receipt_no,
    //     volume_period: row.volume_period
    //   };

    //   receiptApi.orderEditChannel(obj).then((res) => {
    //     if (res.data.code === 0) {
    //       this.$message.success("放量期次修改成功!");
    //       setTimeout(() => {
    //         this.search();
    //       }, 500);
    //     } else {
    //       this.$message.error(res.data.message);
    //     }
    //   });
    // },
    // 普通用户修改放量期次
    changeVolumePeriod(value, row) {
      // if (!value || row.receipt_type !== "order_paid") return;
      const obj = {
        receipt_no: row.receipt_no,
        volume_period: [value],
        type: 2
      };

      receiptApi.editVolumePeriod(obj).then((res) => {
        if (res.data.code === 0) {
          this.$message.success("放量期次修改成功!");
          // 更新本地数据
          row.volume_period = [value];
          row.volume_period_display = value;
          setTimeout(() => {
            this.search();
          }, 500);
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 管理员修改转化期次
    // changeTransferPeriodAdmin(value, row, index) {
    //   if (row.transfer_period === "") {
    //     this.$message.error("转化期次不能为空!");
    //     row.transfer_period = this.table_list[index].transfer_period;
    //     return;
    //   } else if (
    //     row.transfer_period === this.table_list[index].transfer_period
    //   ) {
    //     return;
    //   }
    //   const obj = {
    //     receipt_no: row.receipt_no,
    //     transfer_period: row.transfer_period
    //   };

    //   receiptApi.orderEditChannel(obj).then((res) => {
    //     if (res.data.code === 0) {
    //       this.$message.success("转化期次修改成功!");
    //       setTimeout(() => {
    //         this.search();
    //       }, 500);
    //     } else {
    //       this.$message.error(res.data.message);
    //     }
    //   });
    // },
    // 普通用户修改转化期次
    changeTransferPeriod(value, row) {
      // if (!value || row.receipt_type !== "order_paid") return;
      const obj = {
        receipt_no: row.receipt_no,
        transfer_period: [value],
        type: 1
      };

      receiptApi.editVolumePeriod(obj).then((res) => {
        if (res.data.code === 0) {
          this.$message.success("转化期次修改成功!");
          // 更新本地数据
          row.transfer_period = [value];
          row.transfer_period_display = value;
          setTimeout(() => {
            this.search();
          }, 500);
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    setSearchDefault() {
      const pastThirty = quickTime.GetDate("pastThirty");
      this.$set(this.searchForm, "pay_time", pastThirty);
    },
    getReceiptRolStudent() {
      receiptApi.receiptRoleStudent().then((res) => {
        const user_info = JSON.parse(localStorage.getItem("user_info"));
        const index = res.data.data.id.findIndex(
          (item) => item === user_info.employee_id
        );
        if (index > -1) {
          this.can_edit_channel = true;
        } else {
          this.can_edit_channel = false;
        }
      });
    },
    batchSign() {
      this.$refs.comPushSign.onShow(
        this.checked_order[0].receipt_no,
        false,
        this.checked_order[0].mobile
      );
    },
    async downloadSinSign(row) {
      if (row.contract_url) {
        const PDFFile = row.contract_url;
        const PDFFileName = row.contract_no + ".pdf";
        FileSaver.saveAs(PDFFile, PDFFileName);
      } else {
        row.downloadLoading = true;
        const res = await electronicApi.ContractDownload({
          contract_no: row.contract_no
        });
        if (res.data.code === 0) {
          // const PDFFile = res.data.data;
          const PDFFile = res.data.data;
          const PDFFileName = res.data.data + ".pdf";
          FileSaver.saveAs(PDFFile, PDFFileName);
          // const PDFFileName = row.contract_no + ".pdf";
          // downLoadFileByHref(PDFFile);
        } else {
          this.$message.error(res.data.message);
        }
        row.downloadLoading = false;
      }
    },
    viewSinSign(row) {
      console.log(row);
      if (!row.contract_url) {
        this.$message.error("暂无合同");
      } else {
        this.$refs.compTool.onShow(row.contract_url);
      }
    },
    // 筛选电子签模版校区
    filterDepartmentId(departmentIds) {
      console.log(departmentIds);
      electronicApi.ContractDepartments().then((res) => {
        if (res.data.code === 0) {
          const exists = departmentIds.some((id) => res.data.data.includes(id));
          // 电子签按钮是否显示
          this.is_electronic = exists;
        }
      });
    },
    // 分别获取网校期次列表
    getPeriodedList(period_type, name) {
      periodApi
        .getList({
          search_start: moment().format("YYYY-MM-DD"),
          search_end: moment().format("YYYY-MM-DD"),
          period_type
        })
        .then((res) => {
          if (res.data.code === 0) {
            this[name] = res.data.data.results || [];
          }
        });
    },
    // 放量期次查询
    getReleasePeriodList() {
      this.getPeriodedList("放量期次", "volume_period_options");
    },
    // 转换期次查询
    getConvertPeriodList() {
      this.getPeriodedList("转化期次", "transfer_period_options");
    }
  },
  created() {
    this.getReceipType();
    this.$store.dispatch("getChargeCategory", { channel: "all" });
    if (this.$_has({ m: "receipt", o: "export" })) {
      this.isExport = true;
    }
    this.filterDepartmentId(this.departmentId);
  },
  mounted() {
    this.getRefundTransferReason();
    this.idList = this.ids.split(",");
    this.getChannelList();
    this.getPaymentMethod();
    this.setSearchDefault();
    this.search();
    this.getReceiptRolStudent();
    this.getReleasePeriodList();
    this.getConvertPeriodList();
    if (this.$_has({ m: "receipt", o: "edit_period" })) {
      this.is_edit_period = true;
    }
  },
  computed: {
    ...mapState({
      ChargeCategory: (state) => state.dictionary.ChargeCategory
    }),
    ids() {
      return this.$store.getters.doneGetSchoolId.toString();
    },
    departmentId() {
      return this.$store.getters.doneGetSchoolId;
    },
    is_Signed() {
      return (
        // 当选择一个收据 && 这个收据为收费类型 && 这个收据的合同状态为已签署 才可以点击
        this.checked_order.length === 1 &&
        this.checked_order[0].receipt_type === "order_paid" &&
        this.checked_order[0].contract_status !== "已签署" &&
        this.checked_order[0].receipt_status !== 2
      );
    }
  }
};
</script>

<style lang="less" scoped>
::v-deep .el-table td:last-child {
  border-right-color: transparent !important;
}
::v-deep .el-table__header th:last-child {
  border-right-color: transparent !important;
}
.tac {
  text-align: center !important;
}
.container {
  .el-table {
    height: auto;
  }
  ::v-deep .el-table--scrollable-x .el-table__body-wrapper {
    z-index: 2;
  }
}
.attribute {
  width: 29px;
  height: 23px;
  cursor: pointer;
  vertical-align: middle;
}
::v-deep .el-table__body-wrapper {
  // height: calc(100% - 46px);
  height: auto;
  .empty-container {
    position: absolute;
    top: 50%;
    left: 50%;
  }
  .loading-container {
    position: absolute;
    top: 30%;
    left: 1%;
    background: transparent;
    .box {
      height: 100%;
    }
  }
}
/deep/ .tg-search {
  div.tg-search__box:first-child .el-input {
    width: 280px;
  }
  .el-checkbox {
    margin-right: 16px;
  }
  .el-checkbox__label {
    padding-left: 8px;
  }
  label:last-child {
    margin-right: 0;
  }
}
.icon-tool {
  color: #2d80ed;
  cursor: pointer;
  font-size: 16px;
  margin-left: 12px;
}
/deep/ .form-item-jd_track {
  .jd_track-select {
    .el-input {
      width: 100px;
    }
  }
  .el-input.is-focus::after {
    border: 0;
  }
}
/deep/ .jd_track-input {
  margin-left: -3px;
  .el-input .el-input__inner {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
}
</style>
