<template>
  <div class="right-content container">
    <el-row class="tg-box--margin tg-shadow--margin tg-box--width">
      <el-button
        @click="showDialog('add')"
        type="plain"
        class="tg-button--plain"
        v-has="{ m: 'course_charge', o: 'create' }"
        >新增</el-button
      >
      <el-button
        type="plain"
        :disabled="stop_use"
        class="tg-button--plain"
        :class="{ disabled: stop_use }"
        @click="batch('disable')"
        v-has="{ m: 'course_charge', o: 'charge_status' }"
        >批量停用</el-button
      >
      <el-button
        type="plain"
        :disabled="start_use"
        :class="{ disabled: start_use }"
        class="tg-button--plain"
        @click="batch('enable')"
        v-has="{ m: 'course_charge', o: 'charge_status' }"
        >批量启用</el-button
      >
    </el-row>
    <div class="tg-table__box">
      <div style="height: 90px" class="tg-box--border"></div>
      <el-table
        ref="table"
        :data="list"
        tooltip-effect="dark"
        class="tg-table"
        :row-key="getRowKeys"
        v-loading="is_loading"
        @selection-change="handleSelectionChange"
        style="width: 100%"
        :cell-style="{ borderRightColor: '#e0e6ed75' }"
        :header-cell-style="{ borderRightColor: '#e0e6ed75' }"
        border
      >
        <el-table-column
          type="selection"
          width="50"
          fixed="left"
          :reserve-selection="true"
        ></el-table-column>
        <el-table-column show-overflow-tooltip label="学员状态" width="200">
          <template slot-scope="scope">
            <span>
              <!-- {{
                scope.row.student_states.indexOf("allow_intention") > -1
                  ? scope.row.student_states.length > 1
                    ? "意向客户,"
                    : "意向客户"
                  : ""
              }} -->
              {{
                scope.row.student_states | studentStateFilter(StudentState)
              }}</span
            >
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip label="产品规格" width="150">
          <template slot-scope="scope">
            <span>{{
              scope.row.business_specs_list | getSpecificationNames
            }}</span>
          </template>
        </el-table-column>

        <!-- ----------价格设置---------------- -->
        <el-table-column label="价格设置">
          <el-table-column width="230" show-overflow-tooltip label="实交金额">
            <template slot-scope="scope">
              <span v-if="scope.row.fee_attr_category == 'due_collect'">{{
                (+scope.row.price_min).toFixed(2) +
                "~" +
                (+scope.row.price_max).toFixed(2)
              }}</span>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip label="应交金额" width="150">
            <template slot-scope="scope">
              <span v-if="scope.row.fee_attr_category == 'original'">{{
                (+scope.row.price_min).toFixed(2) +
                "~" +
                (+scope.row.price_max).toFixed(2)
              }}</span>
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip width="200" label="折后金额">
            <template slot-scope="scope">
              <span v-if="scope.row.fee_attr_category == 'after_discount'">{{
                (+scope.row.price_min).toFixed(2) +
                "~" +
                (+scope.row.price_max).toFixed(2)
              }}</span>
            </template>
          </el-table-column>

          <el-table-column show-overflow-tooltip label="是否无门槛" width="100">
            <template slot-scope="scope">
              <span>{{
                scope.row.fee_attr_category == "none" ? "是" : "否"
              }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <!-- ----------价格设置结束---------------- -->
        <el-table-column show-overflow-tooltip label="收费类型" width="180">
          <template slot-scope="scope">
            <span>{{ scope.row.ChargeCategory }}</span>
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip label="限制触发次数" width="160">
          <template slot-scope="scope">
            <span>{{ scope.row.limit_number || "不限" }}</span>
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip label="规则有效期" width="200">
          <template slot-scope="scope">
            <span v-if="scope.row.start_time">{{
              moment(scope.row.start_time).format("YYYY-MM-DD") +
              " ~ " +
              moment(scope.row.end_time).format("YYYY-MM-DD")
            }}</span>
            <span v-else>不限</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" width="200" label="操作">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="showDialog('edit', scope.row)"
              v-has="{ m: 'course_charge', o: 'update' }"
              class="tg-text--blue tg-span__divide-line"
              >编辑</el-button
            >
            <el-button
              v-if="scope.row.is_enable === true"
              type="text"
              size="small"
              @click="closeOrOpen('disable', scope.row)"
              v-has="{ m: 'course_charge', o: 'charge_status' }"
              class="tg-text--blue tg-span__divide-line"
              >停用</el-button
            >
            <el-button
              v-if="scope.row.is_enable === false"
              type="text"
              size="small"
              @click="closeOrOpen('enable', scope.row)"
              v-has="{ m: 'course_charge', o: 'charge_status' }"
              class="tg-text--blue tg-span__divide-line"
              >启用</el-button
            >
            <el-button
              @click="showSchAuth(scope.row)"
              type="text"
              size="small"
              v-has="{ m: 'organization', o: 'school' }"
              class="tg-text--blue tg-span__divide-line"
              >授权校区</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div style="justify-content: flex-end; padding: 0" class="tg-pagination">
        <el-pagination
          background
          layout="prev, pager, next"
          :total="total"
          :page-size="10"
          :current-page.sync="page"
          @current-change="currentChange"
        >
        </el-pagination>
      </div>
    </div>
    <create-charge-seting
      v-if="create_visible"
      :type="pageType"
      :id="currCourseRow.id"
      :charge_id="charge_id"
      :name="currCourseRow.course_name"
      :row_data="charge_type_row"
      @close="back"
      :visible="create_visible"
    ></create-charge-seting>
    <schools-authorize
      v-if="schools_authorize_show"
      :depart_ids="auth_department_id"
      type="edit"
      @close="schools_authorize_show = false"
      @save="saveCampus"
    ></schools-authorize>
  </div>
</template>

<script>
import { courseChargeOpt } from "@/api/courseManagement.js";
import CreateChargeSeting from "@/views/charge/createChargeSeting.vue";
import SchoolsAuthorize from "@/views/charge/schoolsAuthorize.vue";
import { mapState } from "vuex";
import { charge_business_type } from "@/public/dict.js";
const chargeApi = require("@/api/charge.js");

export default {
  name: "chargeTypeList",
  data() {
    return {
      id: "",
      pageType: "add",
      create_visible: false,
      list: [],
      page: 1,
      total: 0,
      is_loading: false,
      height: window.innerHeight - 380,
      product_id: "",
      selectionsIds: [],
      start_use: false,
      stop_use: false,
      currCourseRow: null,
      schools_authorize_show: false,
      auth_department_id: [],
      charge_id: "",
      charge_type_row: {}
    };
  },
  props: {
    category: {
      type: String,
      default: charge_business_type.course
    }
  },
  components: {
    CreateChargeSeting,
    SchoolsAuthorize
  },

  created() {
    // this.getList();
    this.$store.dispatch("getFeeAttr", {});
    this.$store.dispatch("getChargeCategory", {
      channel: "all"
    });
    this.$store.dispatch("getStudentStateList");
    this.$store.dispatch("getStudentCateList");
  },
  computed: {
    ...mapState({
      FeeAttr: (state) => state.dictionary.FeeAttr,
      ChargeCategory: (state) => state.dictionary.ChargeCategory,
      StudentState: (state) => state.dictionary.StudentState,
      StudentCate: (state) => state.dictionary.StudentCate
    })
  },
  filters: {
    getSpecificationNames: (arr) => {
      const values = [];
      if (arr) {
        arr.map((item) => {
          values.push(item.specification_name);
        });
      }
      return values.join();
    },
    studentStateFilter: (arr, studentState) => {
      const values = [];
      const obj = JSON.parse(JSON.stringify(studentState));
      obj.allow_intention = "意向客户";
      obj.visitor = "游客";
      if (arr) {
        arr.map((key) => {
          if (obj[key]) {
            values.push(obj[key]);
          }
        });
      }
      return values.join();
    },
    StudentCateFilter: (arr, studentCate) => {
      const values = [];
      if (arr) {
        arr.map((key) => {
          if (studentCate[key]) {
            values.push(studentCate[key]);
          }
        });
      }
      return values.join();
    }
  },
  methods: {
    getList(product_id) {
      this.product_id = product_id;
      this.is_loading = true;
      chargeApi
        .getChargeList({
          course_id: product_id,
          page: this.page,
          page_size: 10
        })
        .then((res) => {
          if (+res.data.code === 0 && +res.status === 200) {
            this.list = res.data.data.results || [];
            this.list.forEach((val) => {
              val.ChargeCategory = this.ChargeCategory.filter(
                (item) => item.category_id === val.charge_category
              )[0].category_name;
            });
            this.total = res.data.data.count;
          }
          this.is_loading = false;
        });
    },
    // 分页
    currentChange() {
      this.getList(this.product_id);
    },
    getRowKeys(row) {
      return row.id;
    },
    back() {
      this.create_visible = false;
    },
    closeSchAuth() {
      this.schools_authorize_show = false;
    },
    showSchAuth(row) {
      this.schools_authorize_show = true;
      this.auth_department_id = row.department_ids || [];
      this.charge_type_row = row;
    },
    showDialog(type, row) {
      if (type === "edit") {
        this.charge_id = row.id;
        this.charge_type_row = row;
      } else {
        this.charge_id = "";
        this.charge_type_row = null;
      }
      this.pageType = type;
      this.create_visible = true;
    },
    handleSelectionChange(list) {
      this.selectionsIds = list.map((item) => item.id);
      this.start_use = list.some((item) => item.is_enable === true);
      this.stop_use = list.some((item) => item.is_enable === false);
    },
    batch(type) {
      const str = type === "enable" ? "启用" : "停用";
      if (this.selectionsIds.length === 0) return false;
      this.$confirm(`是否确认${str}选中的收费类型?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        courseChargeOpt({
          is_enable: type === "enable",
          ids: this.selectionsIds
        }).then((res) => {
          if (res.data.message === "success") {
            this.$refs.table.clearSelection();
            this.$message.success(`批量${str}成功`);
            this.getList(this.product_id);
          } else {
            this.$message.error(`批量${str}失败`);
          }
        });
      });
    },
    closeOrOpen(type, row) {
      const { end_time, id } = row;
      if (end_time && type === "enable") {
        const today = this.moment(new Date()).format("YYYY-MM-DD");
        const ruleEndDate = this.moment(end_time).format("YYYY-MM-DD");

        if (ruleEndDate !== "0001-01-01" && today > ruleEndDate) {
          this.$message.info("规则有效期已过期，不能启用！");
          return false;
        }
      }
      const str = type === "enable" ? "启用" : "停用";
      this.$confirm(`是否确认${str}该收费类型?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        courseChargeOpt({
          is_enable: type === "enable",
          ids: [id]
        }).then((res) => {
          if (res.data.message === "success") {
            this.$message.success(`${str}成功`);
            this.getList(this.product_id);
          } else {
            this.$message.error(`${str}失败`);
          }
        });
      });
    },
    setRow(row) {
      // 当前配置的课程数据
      this.currCourseRow = row;
    },
    saveCampus(arr) {
      chargeApi
        .saveDepartment({
          id: this.charge_type_row.id,
          department_ids: arr
        })
        .then(() => {
          this.getList(this.product_id);
        });
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .right-content {
  width: calc(100% - 350px);
  // .el-table--border td,
  // .el-table--border th {
  //   border-right: 0px;
  // }
  ::v-deep .el-table td:last-child {
    border-right-color: transparent !important;
  }
  ::v-deep .el-table__header th:last-child {
    border-right-color: transparent !important;
  }
  .el-table--border td,
  .el-table--border th {
    border-right: 0px;
  }
}
::v-deep .el-table--border th {
  // border-right-color: #2d80ed;
  border-bottom-color: #2d80ed;
  border-right: 0px;
}
::v-deep tr:first-child {
  th:nth-child(3) {
    border-right: 1px solid #2d80ed;
  }
  th:nth-child(5) {
    border-left: 1px solid #2d80ed;
  }
}
// ::v-deep .el-table--border th {
//   // border-right-color: #2d80ed;
//   border-bottom-color: #2d80ed;
//   border-right: 0px;
// }
// ::v-deep tr:first-child {
//   th:nth-child(3) {
//     border-right: 1px solid #2d80ed;
//   }
//   th:nth-child(5) {
//     border-left: 1px solid #2d80ed;
//   }
// }
/deep/ .el-table thead.is-group th {
  background-color: #fff;
}
.tg-border {
  border-left: 1px solid #2d80ed;
  border-right: 1px solid #2d80ed;
}
::v-deep .el-table__body .el-table__row.hover-row td {
  background-color: #ebf4ff !important;
}
</style>
