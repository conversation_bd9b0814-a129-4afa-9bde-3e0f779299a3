<template>
  <div>
    <el-dialog
      :title="type == 'add' ? '新增收费类型' : '修改收费类型'"
      :visible.sync="visible"
      width="1016px"
      class="tg-dialogs--custom"
      :before-close="back"
      top="100px"
      custom-class="tg-dialogs--custom"
    >
      <div class="tg-dialogs__content--custom">
        <el-form
          ref="form_refs"
          label-width="97px"
          :model="form"
          :rules="rules"
          class="tg-form"
        >
          <div class="course-info__title--left">
            <img alt src="../../assets/图片/course_infor.png" />
            <span class="course-info__label">
              {{ name }}
            </span>
          </div>
          <div class="info tg-box--margin">
            <div class="info__content">
              <div class="info-box__border">
                <span class="special-span--top">年份</span>
                <span>{{ info.course_year }}</span>
              </div>
              <div class="info-box__border">
                <span>课程种类</span>
                <span>
                  {{ info.course_level_string }}
                </span>
              </div>
              <div class="info-box__border">
                <span>课程属性</span>
                <span>
                  {{ info.course_attribute | getLabel(course_attribute) }}
                </span>
              </div>
              <div class="info-box__border">
                <span>班型</span>
                <span>{{ info.lesson_type }}</span>
              </div>
              <div class="info-box__border">
                <span>课程类型</span>
                <span>
                  {{ info.course_genre | getLabel(course_genre) }}
                </span>
              </div>
              <div class="info-box__border">
                <span>售卖对象</span>
                <span>
                  {{ info.sales_to | getStuState(student_state_list) }}
                </span>
              </div>
              <div class="info-box__border info-box__border--special">
                <span>计费形式</span>
                <span>按课时</span>
              </div>
              <div
                class="info-box__border info-box--special info-box__border--special"
              >
                <span>动态课消</span>
                <span>
                  {{
                    info.dynamic_discount == ""
                      ? ""
                      : `每排一次课，计${info.dynamic_discount}课时`
                  }}--
                </span>
              </div>
              <div class="info-box__border--special">
                <span>创建时间</span>
                <span>
                  {{ info.created_at | getTime }}
                </span>
              </div>
            </div>
          </div>
          <el-row style="margin-bottom: 16px">
            <el-button
              class="tg-button--primary"
              type="primary"
              @click="dialogSpecShow = true"
              >课程规格/属性/授权校区</el-button
            >
          </el-row>
          <el-form-item label="学员状态" required prop="student_type">
            <el-select
              :popper-append-to-body="false"
              v-model="form.student_type"
              placeholder="请选择"
              multiple
            >
              <el-option
                v-for="item in student_state_list"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
              <!-- <el-option label="意向客户" value="allow_intention"></el-option> -->
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="学员类别：" required prop="student_cate">
            <el-select
              :popper-append-to-body="false"
              v-model="form.student_cate"
              placeholder="请选择"
            >
              <el-option
                v-for="item in student_cate_list"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>-->
          <el-form-item
            v-show="price_info.purchase_type != 1"
            label="产品购买数量"
            prop
          >
            <div class="tg-table__box">
              <div class="tg-box--border"></div>
              <el-table
                ref="table"
                :data="
                  price_info.purchase_type == 2
                    ? price_info.course_count_price || []
                    : price_info.course_stair_price || []
                "
                tooltip-effect="dark"
                class="tg-table"
                @selection-change="handleSelectionChange"
                :header-cell-style="{ 'background-color': '#F5F8FC' }"
                :row-key="getRowKeys"
              >
                <el-table-column
                  type="selection"
                  width="80"
                  :reserve-selection="true"
                ></el-table-column>
                <el-table-column label="规格名称" prop="name" width="180">
                  <template slot-scope="scope">
                    {{
                      getSpecificationName(
                        scope.row.specification_id,
                        spec_list
                      )
                    }}
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="price_info.purchase_type == 2"
                  label="课时"
                  prop="price_count"
                  width="150"
                ></el-table-column>
                <el-table-column
                  v-if="price_info.purchase_type == 3"
                  label="购买数量区间（课时）"
                  prop="article_category_name"
                  width="150"
                >
                  <template slot-scope="scope">
                    {{ scope.row.min_price_count }} -
                    {{ scope.row.max_price_count }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-form-item>
          <el-form-item label="价格设置" required prop="price_type">
            <div style="display: flex; height: 32px">
              <el-select
                :popper-append-to-body="false"
                v-model="form.price_type"
                placeholder="请选择"
                @change="changePrice"
                clearable
              >
                <el-option
                  v-for="item in FeeAttr"
                  :key="item.category_id"
                  :label="item.category_name"
                  :value="item.category_id"
                ></el-option>
              </el-select>
              <span
                v-if="form.price_type !== 'none'"
                style="margin-left: 16px; display: inline-flex"
              >
                <el-form-item label required prop="min_price">
                  <el-input
                    style="width: 100px"
                    v-model.number="form.min_price"
                    placeholder="最小值"
                  ></el-input>
                </el-form-item>
                <span style="margin: 0 5px">至</span>
                <el-form-item label required prop="max_price">
                  <el-input
                    style="width: 100px"
                    v-model.number="form.max_price"
                    placeholder="最大值"
                  ></el-input>
                </el-form-item>
              </span>
            </div>
          </el-form-item>
          <el-form-item label="收费类型" required prop="charge_type">
            <el-select
              clearable
              :popper-append-to-body="false"
              v-model="form.charge_type"
              placeholder="请选择"
            >
              <el-option
                v-for="item in ChargeCategory"
                :key="item.category_id"
                :label="item.category_name"
                :value="item.category_id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="限制触发次数" prop="limit_num">
            <el-input
              style="width: 400px"
              v-model.number="form.limit_num"
              placeholder="请输入"
              type="number"
              :min="1"
            ></el-input>
            <span style="margin: 0 16px 0 10px">次</span>
            <span>
              <el-checkbox
                :true-label="1"
                :false-label="0"
                v-model="form.is_limit_num"
                >不限</el-checkbox
              >
            </span>
          </el-form-item>
          <el-form-item label="规则有效期" prop="rule_date">
            <el-date-picker
              v-model="form.rule_date"
              type="daterange"
              value-format="yyyy-MM-dd HH:mm:ss"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              prefix-icon
              popper-class="tg-date-picker tg-date--range"
              :picker-options="pickerOptions"
              style="width: 400px"
            ></el-date-picker>
            <span style="margin-left: 10px">
              <el-checkbox
                :true-label="2"
                :false-label="1"
                v-model="form.has_date_rule"
                >不限</el-checkbox
              >
            </span>
          </el-form-item>
          <el-form-item label="授权校区" required prop="department_id">
            <el-button
              class="tg-button--primary"
              type="primary"
              @click="showAuthSchool"
              >去授权给校区</el-button
            >
          </el-form-item>
          <el-form-item label="启用状态" required prop="is_enabled">
            <el-radio v-model="form.is_enabled" :label="true">启用</el-radio>
            <el-radio v-model="form.is_enabled" :label="false">禁用</el-radio>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="tg-button--plain" type="plain" @click="back"
          >返回</el-button
        >
        <el-button
          v-has="{ m: 'course_charge', o: 'save' }"
          class="tg-button--primary"
          type="primary"
          @click="determine('form')"
          >保存</el-button
        >
      </span>
    </el-dialog>
    <schools-authorize
      v-if="schools_authorize_show"
      :depart_ids="this.form.department_id"
      type="edit"
      @close="closeSchoolsAuthorize"
      @save="setDepartIds"
    ></schools-authorize>
    <dialog-spec
      v-if="dialogSpecShow"
      @close="dialogSpecShow = false"
      :course_attribute="course_attribute_list"
      :attribute="dialogSpecAttr"
      :com_course_id="form.product_id"
    ></dialog-spec>
  </div>
</template>
<script>
import {
  getCourseInfoPriceSpec,
  getAuthorizedSpec,
  getCourseInfoBasic,
  getCourseConfigAll,
  getCourseConfigByType
} from "@/api/courseManagement.js";
import chargeApi from "@/api/charge.js";
import { charge_business_type } from "@/public/dict.js";
import SchoolsAuthorize from "@/views/charge/schoolsAuthorize.vue";
import { mapState } from "vuex";
import DialogSpec from "@/components/courseManagement/dialogSpec.vue";
export default {
  components: {
    SchoolsAuthorize,
    DialogSpec
  },
  data() {
    return {
      form: {
        student_type: [],
        specification_id: [],
        price_type: "",
        charge_type: "",
        category: charge_business_type.course,
        department_id: [],
        has_date_rule: 2,
        is_enabled: true,
        limit_num: "",
        is_limit_num: 1,
        max_price: "",
        min_price: "",
        price: "",
        product_id: "",
        rule_end_date: "",
        rule_start_date: "",
        rule_date: []
      },
      rules: {},
      student_state_list: [],
      student_cate_list: [],
      spec_list: [],
      pickerOptions: {},
      price_info: {
        course_count_price: []
      },
      schools_authorize_show: false,
      info: {},
      course_genre: [],
      course_level: [],
      course_class_type: [],
      course_attribute: [],
      course_type: [],
      course_attribute_list: {},
      dialogSpecShow: false,
      dialogSpecAttr: ""
    };
  },
  props: {
    id: {
      type: String
    },
    charge_id: {
      type: [Number, String]
    },
    name: {
      type: String
    },
    visible: {
      type: Boolean
    },
    type: {
      type: String
    },
    row_data: {
      type: Object
    }
  },
  computed: {
    ...mapState({
      // 价格设置
      FeeAttr: (state) => state.dictionary.FeeAttr,
      // 收费类型
      ChargeCategory: (state) => state.dictionary.ChargeCategory,
      StudentState: (state) => state.dictionary.StudentState,
      StudentCate: (state) => state.dictionary.StudentCate
    })
  },
  watch: {
    "form.is_limit_num": {
      handler(newVal) {
        if (newVal === 1) {
          this.form.limit_num = "";
        }
      },
      immediate: false
    },
    "form.limit_num": {
      handler(newVal) {
        if (newVal <= 0) {
          this.form.limit_num = "";
          this.form.is_limit_num = 1;
        } else {
          this.form.is_limit_num = 0;
        }
      },
      immediate: false
    },
    "form.rule_date": {
      handler(newVal) {
        if (newVal && newVal.length) {
          this.form.has_date_rule = 1;
          this.form.rule_start_date = newVal[0];
          this.form.rule_end_date = newVal[1];
        } else {
          this.form.has_date_rule = 2;
          this.form.rule_start_date = "";
          this.form.rule_end_date = "";
        }
      },
      immediate: false
    },
    "form.has_date_rule": {
      handler(newVal) {
        if (newVal === 2) {
          this.form.rule_date = null;
        }
      },
      deep: true,
      immediate: false
    }
  },
  filters: {
    getLabel(val, data) {
      const item = data.find((item1) => item1.config_value === val);
      return typeof item === "undefined" ? "" : item.config_name;
    },
    getStuState(val, data) {
      const label = [];
      if (typeof val === "undefined") return "";
      for (let i = 0; i < val.length; i++) {
        const item = data.find((item1) => item1.value === val[i]);
        label.push(typeof item === "undefined" ? "" : item.label);
      }
      return label.join("，");
    }
  },
  created() {
    this.form.product_id = this.id;
    this.getCourseConfig();
    this.getStudentStateList();
    this.getStudentCateList();
    this.getDetails();
    this.getSelect();
  },
  mounted() {
    this.getPickerOptions();
    this.getSpecTableData();
    if (this.type === "edit") {
      this.setCourseChargeInfo();
    }
  },
  methods: {
    async getDetails() {
      try {
        const { data } = await getCourseInfoBasic({ id: this.form.product_id });
        const sales_to =
          typeof data.multi_props.sales_to === "undefined"
            ? []
            : data.multi_props.sales_to;
        if (data.allow_intention_sale === 1) {
          sales_to.push("allow_intention");
        }
        this.info = { ...data, sales_to };
        this.dialogSpecAttr = data.course_attribute;
      } catch (e) {
        console.error(e);
      }
    },
    async getSelect() {
      const { data } = await getCourseConfigByType({}, "course_attribute");
      this.course_attribute_list = data;
    },
    getSpecificationName(val, data) {
      const item = data.find((item) => item.specification_id === val);
      return typeof item === "undefined" ? "" : item.specification_name;
    },
    getDiscountId(val, data) {
      const item = data.find((item) => item.specification_id === val);
      return typeof item === "undefined" ? "" : item.discount_id;
    },
    percent_rule(value, callback) {
      if (value) {
        const reg = /^(?:0|[1-9][0-9]?|100)(.[0-9]{0,2})?$/;
        if (!reg.test(value)) {
          this.$message.error("请输入0-100的两位小数");
          callback(new Error());
        } else {
          callback();
        }
      } else {
        callback();
      }
    },
    // 学员状态
    getStudentStateList() {
      this.student_state_list = [];
      const { StudentState } = this;
      for (const key in this.StudentState) {
        this.student_state_list.push({ label: StudentState[key], value: key });
      }
      this.student_state_list.push({ label: "游客", value: "visitor" });
      this.student_state_list.push({
        label: "意向客户",
        value: "allow_intention"
      });
    },
    // 学员类别
    getStudentCateList() {
      this.student_cate_list = [];
      const { StudentCate } = this;
      for (const key in StudentCate) {
        this.student_cate_list.push({ label: StudentCate[key], value: key });
      }
    },
    getChargeListCampus() {
      chargeApi.chargeListCampus({
        businessType: charge_business_type.course,
        chargeItemId: this.charge_id
      });
    },
    getRowKeys(row) {
      return row.id;
    },
    getSpecList() {
      return getAuthorizedSpec({
        course_id: this.id
      });
    },
    getPriceSpec() {
      return getCourseInfoPriceSpec({ id: this.id });
    },
    async getSpecTableData() {
      const [res, res1] = await Promise.all([
        this.getSpecList(),
        this.getPriceSpec()
      ]);
      this.$nextTick(() => {
        this.spec_list = res.data || [];
        this.price_info = res1.data || [];
        this.initSelection();
      });
    },
    handleSelectionChange(val) {
      if (val && val.length > 0) {
        this.form.specification_id = val.map((item) => item.specification_id);
      }
    },
    changePrice() {
      this.form.max_price = "";
      this.form.min_price = "";
    },
    getMaxNum(id, arr) {
      if (arr.purchase_type === 3) {
        // 根据id获得阶梯的对应最大值最小值，进行获取
        const currentNum = arr.course_stair_price.filter(
          (item) => item.specification_id === id
        )[0];
        return currentNum ? currentNum.max_price_count : 0;
      } else {
        return 0;
      }
    },
    getMinNum(id, arr) {
      if (arr.purchase_type === 3) {
        // 根据id获得阶梯的对应最大值最小值，进行获取
        const currentNum = arr.course_stair_price.filter(
          (item) => item.specification_id === id
        )[0];
        return currentNum ? currentNum.min_price_count : 0;
      } else {
        return 0;
      }
    },
    // 提交
    determine() {
      this.$refs.form_refs.validate((valid) => {
        if (valid) {
          const { form } = this;
          if (form.min_price > form.max_price) {
            this.$message.error("价格设置最小值不能大于最大值");
            return false;
          }
          const params = {
            business_specs: [],
            fee_type_request: {
              charge_category: form.charge_type,
              course_id: form.product_id,
              department_ids: form.department_id,
              end_time: form.rule_end_date,
              fee_attr_category: form.price_type,
              is_enable: form.is_enabled,
              limit_number: form.limit_num ? form.limit_num : 0,
              price_max: form.max_price ? form.max_price : 0,
              price_min: form.min_price ? form.min_price : 0,
              start_time: form.rule_start_date,
              student_states: form.student_type
            }
          };
          // 处理规格参数
          if (form.specification_id && form.specification_id.length > 0) {
            form.specification_id.forEach((id) => {
              params.business_specs.push({
                discount_id: this.getDiscountId(id, this.spec_list),
                purchase_type: this.price_info.purchase_type,
                max_num: this.getMaxNum(id, this.price_info),
                min_num: this.getMinNum(id, this.price_info),
                specification_id: id,
                specification_name: this.getSpecificationName(
                  id,
                  this.spec_list
                )
              });
            });
          }

          if (this.type === "add") {
            chargeApi.courseChargeCreate(params).then((res) => {
              if (+res.status === 200 && +res.data.code === 0) {
                this.$parent.getList(this.id);
                this.back();
              }
            });
          } else {
            params.fee_type_id = this.row_data.id;
            chargeApi.courseChargeUpdate(params).then((res) => {
              if (+res.status === 200 && +res.data.code === 0) {
                this.$parent.getList(this.id);
                this.back();
              }
            });
          }
        } else {
          this.$message.error("您有必填项未填或未选择授权校区");
          return false;
        }
      });
    },
    // 是编辑的，获取收费配置信息
    setCourseChargeInfo() {
      chargeApi.getFeeTypeInfo({ id: this.row_data.id }).then((res) => {
        if (+res.status === 200 && +res.data.code === 0) {
          const data = res.data.data;
          if (data.start_time) {
            data.rule_date = [data.start_time, data.end_time];
          }
          let specification_id = "";
          if (data.business_specs_list && data.business_specs_list.length > 0) {
            specification_id = data.business_specs_list.map(
              (item) => item.specification_id
            );
          }

          this.form = {
            specification_id,
            student_type: data.student_states,
            price_type: data.fee_attr_category,
            charge_type: data.charge_category,
            rule_date: data.rule_date || [],
            rule_end_date: data.end_time || "",
            rule_start_date: data.start_time || "",
            department_id: data.department_ids || [],
            has_date_rule: data.start_time ? 1 : 2,
            is_enabled: data.is_enable,
            limit_num: data.limit_number,
            is_limit_num: data.limit_number ? 0 : 1,
            max_price: data.price_max,
            min_price: data.price_min,
            product_id: data.course_id
          };
          this.initSelection();
        }
      });
    },
    // 编辑时初始选中规格
    initSelection() {
      const rows =
        this.price_info.purchase_type === 2
          ? this.price_info.course_count_price
          : this.price_info.course_stair_price;
      const { specification_id } = this.form;
      if (rows && rows.length > 0) {
        rows.forEach((row) => {
          if (specification_id.indexOf(row.specification_id) >= 0) {
            this.$refs.table.toggleRowSelection(row);
          }
        });
      }
    },
    back() {
      this.$emit("close");
    },
    setDepartIds(ids) {
      this.form.department_id = ids;
    },
    closeSchoolsAuthorize() {
      this.schools_authorize_show = false;
    },
    showAuthSchool() {
      this.schools_authorize_show = true;
    },
    getPickerOptions() {
      this.pickerOptions = {
        disabledDate: (date) => {
          return date.getTime() < new Date().getTime() - 24 * 3600 * 1000;
        }
      };
    },
    minNumZero(props) {
      if (+this.form[props] < 0) {
        this.$message.error("输入数值不能小于0");
        this.form[props] = "";
      }
    },
    getConfigListByType(data, config_type) {
      data.map((item) => {
        if (config_type === item.config_type) {
          this[config_type] = item.config_list;
        }
      });
    },
    getCourseConfig() {
      getCourseConfigAll().then((res) => {
        if (res.data) {
          const config_name_list = [
            "course_genre",
            "course_level",
            "course_class_type",
            "course_attribute",
            "course_type"
          ];
          config_name_list.map((item) => {
            this.getConfigListByType(res.data, item);
          });
        }
      });
    }
  }
};
</script>
<style lang="less" scoped>
.tg-dialogs--custom {
  ::v-deep & > .el-dialog__body {
    padding: 16px;
  }
}

.tg-button--pointer {
  cursor: pointer;
}
.tg-dialogs__content--custom {
  height: 600px;
  overflow-y: scroll;
  overflow-x: hidden;

  .tg-button__wrap {
    padding-bottom: 30px;
    display: inline-flex;
    flex-direction: row;
    align-items: center;
    img {
      height: 14px;
      width: 14px;
      margin-right: 8px;
    }
    span {
      color: @base-color;
    }
  }

  .more {
    width: 16px;
    height: 4px;
    margin-right: 5px;
    vertical-align: middle;
  }

  .tg-select {
    ::v-deep .el-select-dropdown.el-popper {
      width: 220px;
    }
  }
}
::v-deep .el-dialog {
  .el-form-item__content {
    margin-bottom: 16px;
  }
  .el-form-item__content,
  .el-form-item__label {
    line-height: 32px;
  }
  .tg-table__box {
    margin: 0;
    box-shadow: none;
    width: 400px;
  }
  .el-table {
    padding: 0;
  }
  .el-table__empty-block {
    width: 100% !important;
  }
  .el-select .el-input {
    width: 400px;
  }
  .el-table__header,
  .el-table__body {
    width: 400px !important;
  }
  .el-radio {
    margin-right: 16px;
  }
}
.course-name-label {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100px;
  line-height: 44px;
}
.course-name-image {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}
.course-name {
  color: #2d80ed;
  font-size: 14px;
  line-height: 44px;
  margin-left: 8px;
}
.course-info__title--left {
  display: flex;
  flex-direction: row;
  align-items: center;
  img {
    width: 32px;
    margin-right: 10px;
  }
}
.info {
  border: 1px solid @base-color;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 16px;
}

.info__content {
  // line-height: 46px;
  // height: 46px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;

  & > div {
    width: 33.33%;
    display: flex;
    align-items: center;
  }

  span {
    font-size: @text-size_normal;
    color: @text-color_second;
    font-family: @text-famliy_medium;
    line-height: 46px;
  }

  span:nth-child(1) {
    background-color: #f5f8fc;
    min-width: 100px;
    display: inline-block;
    text-align: right;
    padding-right: 16px;
  }

  span:nth-child(2) {
    width: calc(100% - 132px);
    padding-left: 16px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  span.tg-text--blue {
    color: #157df0;
  }

  .special-span--top {
    border-top-left-radius: 4px;
  }
  .info-box__border {
    box-sizing: border-box;
    border-bottom: 1px solid #e0e6ed;
    border-right: 1px solid #e0e6ed;

    &:nth-child(3n) {
      border-right: 0;
    }
  }
}
</style>
