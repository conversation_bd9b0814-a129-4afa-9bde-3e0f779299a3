<template>
  <div>
    <!-- <tg-search
      :searchTitle.sync="search_title"
      :form.sync="listParams"
      @reset="reset"
      @search="onSubmit"
    ></tg-search> -->
    <el-row class="tg-box--margin tg-shadow--margin tg-row--height">
      <!-- v-if="$_has({ m: 'survey', o: 'create' })" -->
      <el-button
        type="plain"
        v-if="$_has({ m: 'restCourse', o: 'create' })"
        class="tg-button--plain"
        @click="onCreate"
        >添加权限
      </el-button>
    </el-row>
    <div class="tg-table__box" v-if="$_has({ m: 'restCourse', o: 'create' })">
      <div class="tg-box--border" style="height: 100%"></div>
      <el-table
        ref="table"
        :data="dataList"
        tooltip-effect="dark"
        class="tg-table"
        v-loading="false"
      >
        <template v-for="(field, index) in table_title">
          <el-table-column
            :key="index"
            :prop="field.prop"
            :label="field.label"
            :width="field.width"
            :min-width="field.width || field.minWidth"
          >
            <template slot-scope="scope">
              <template
                v-if="
                  [
                    'course_num_is_enabled',
                    'surplus_amount_is_enabled',
                    'course_cancel_push_is_enabled',
                    'mini_buy_course',
                    'mini_course_time_table',
                    'mini_is_billable',
                    'mini_is_attendance',
                    'mini_deduct_course_num',
                    'mini_deduct_price'
                  ].includes(field.prop)
                "
              >
                <el-switch
                  :inactive-value="2"
                  :active-value="1"
                  :disabled="!$_has({ m: 'restCourse', o: 'edit' })"
                  @change="(val) => handleSwitch(val, scope.row, field.prop)"
                  v-model="scope.row[field.prop]"
                ></el-switch>
              </template>
              <template v-else>{{ scope.row[field.prop] }}</template>
            </template>
          </el-table-column>
        </template>
        <el-table-column label="操作" :width="250" class-name="table-options">
          <template slot-scope="scope">
            <el-button
              @click="onEdit(scope.row)"
              type="text"
              v-if="$_has({ m: 'restCourse', o: 'edit' })"
              class="tg-text--blue tg-span__divide-line"
              >编辑</el-button
            >
            <el-button
              type="text"
              @click="onDelete(scope.row)"
              v-if="$_has({ m: 'restCourse', o: 'delete' })"
              class="tg-text--blue tg-span__divide-line"
              >删除</el-button
            >
          </template>
        </el-table-column>
        <template slot="empty">
          <div style="margin-top: 2%">
            <loading v-if="loading"></loading>
            <div class="empty-container" v-else>暂无数据～</div>
          </div>
        </template>
      </el-table>
      <!-- 分页 -->
      <div class="tg-pagination">
        <span class="el-pagination__total">共 {{ total }} 条</span>
        <el-pagination
          background
          layout="sizes, prev, pager, next, jumper"
          :total="total"
          :current-page.sync="listParams.page"
          @current-change="handleCurrentChange"
          @size-change="sizeChange"
          :page-sizes="[10, 20, 50, 100]"
        >
        </el-pagination>
      </div>
      <el-dialog
        :title="`${dialogType === 'add' ? '新增' : '编辑'}权限`"
        :visible.sync="dialogFormVisible"
        :before-close="handleClose"
      >
        <el-form :model="form" label-position="top" ref="form">
          <el-form-item label="校区">
            <el-input
              style="width: 300px"
              :disabled="dialogType === 'edit'"
              v-model="departmentNames"
              @click.native="school_tree_visible = true"
              placeholder="请选择校区"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="是否显示剩余课程数量"
            prop="course_num_is_enabled"
          >
            <el-switch
              :inactive-value="2"
              :active-value="1"
              active-text="显示"
              inactive-text="不显示"
              v-model="form.course_num_is_enabled"
            ></el-switch>
          </el-form-item>
          <el-form-item
            label="是否显示剩余金额"
            prop="surplus_amount_is_enabled"
          >
            <el-switch
              :inactive-value="2"
              :active-value="1"
              active-text="显示"
              inactive-text="不显示"
              v-model="form.surplus_amount_is_enabled"
            ></el-switch>
          </el-form-item>
          <el-form-item label="是否显示报读课程菜单" prop="mini_buy_course">
            <el-switch
              :inactive-value="2"
              :active-value="1"
              active-text="显示"
              inactive-text="不显示"
              v-model="form.mini_buy_course"
            ></el-switch>
          </el-form-item>
          <el-form-item label="是否显示课表" prop="mini_course_time_table">
            <el-switch
              :inactive-value="2"
              :active-value="1"
              active-text="显示"
              inactive-text="不显示"
              v-model="form.mini_course_time_table"
            ></el-switch>
          </el-form-item>
          <el-form-item
            label="课消是否微信推送"
            prop="course_cancel_push_is_enabled"
          >
            <el-switch
              :inactive-value="2"
              :active-value="1"
              active-text="是"
              inactive-text="否"
              v-model="form.course_cancel_push_is_enabled"
            ></el-switch>
          </el-form-item>
          <el-form-item
            label="课消详情扣课数量是否显示"
            prop="mini_deduct_course_num"
          >
            <el-switch
              :inactive-value="2"
              :active-value="1"
              active-text="显示"
              inactive-text="不显示"
              v-model="form.mini_deduct_course_num"
            ></el-switch>
          </el-form-item>
          <el-form-item
            label="课消详情课消金额是否显示"
            prop="mini_deduct_price"
          >
            <el-switch
              :inactive-value="2"
              :active-value="1"
              active-text="显示"
              inactive-text="不显示"
              v-model="form.mini_deduct_price"
            ></el-switch>
          </el-form-item>
          <!-- <el-form-item label="课表-出勤状态显示" prop="mini_is_attendance">
            <el-switch
              :inactive-value="2"
              :active-value="1"
              active-text="显示"
              inactive-text="不显示"
              v-model="form.mini_is_attendance"
            ></el-switch>
          </el-form-item> -->
        </el-form>
        <school-tree
          :flag.sync="school_tree_visible"
          v-if="school_tree_visible"
          :id.sync="form.department_id"
          :name.sync="form.department_name"
          type="chooseSchool"
          :useData="checkedDepartmentIds"
          :controlSchool="true"
          :checkedControlSchool="true"
        >
        </school-tree>
        <div slot="footer" class="dialog-footer">
          <el-button @click="cancle">取 消</el-button>
          <el-button type="primary" @click="save">确 定</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import loading from "@/views/loading";
import schoolTree from "@/components/schoolTree/schoolTree";
import {
  restCourseWebShowList,
  restCourseWebShowEdit,
  restCourseWebShowDelete,
  restCourseWebShowAdd
} from "@/api/restCourse";
export default {
  components: { loading, schoolTree },
  data() {
    return {
      dialogType: "add",
      search_title: [
        {
          props: "department_id",
          label: "所属校区",
          type: "school",
          show: true,
          selectOptions: [],
          school_choose_type: "radio",
          use_store_options: true
        }
      ],
      loading: false,
      departmentNames: "",
      checkedDepartmentIds: [],
      checkedDepartmentNames: [],
      dataList: [],
      table_title: [
        {
          prop: "area_name",
          label: "区域"
        },
        {
          prop: "department_name",
          label: "校区名称"
        },
        {
          prop: "operator_chn",
          label: "操作人"
        },
        {
          prop: "course_num_is_enabled",
          label: "剩余课程数量"
        },
        {
          prop: "surplus_amount_is_enabled",
          label: "剩余金额"
        },
        {
          prop: "mini_buy_course",
          label: "报读课程菜单显示"
        },
        {
          prop: "mini_course_time_table",
          label: "课表显示"
        },
        {
          prop: "course_cancel_push_is_enabled",
          label: "课消是否微信推送"
        },
        {
          prop: "mini_deduct_course_num",
          label: "课消详情扣课数量是否显示"
        },
        {
          prop: "mini_deduct_price",
          label: "课消详情课消金额是否显示"
        },
        {
          prop: "mini_is_billable",
          label: "课表-计费状态显示"
        }
        // {
        //   prop: "mini_is_attendance",
        //   label: "课表-出勤状态显示"
        // }
      ],
      total: 0,
      listParams: {
        department_id: "",
        page: 1,
        page_size: 10,
        sort: ""
      },
      form: {
        department_id: [],
        department_name: "",
        course_num_is_enabled: 1,
        surplus_amount_is_enabled: 1,
        course_cancel_push_is_enabled: 1,
        mini_buy_course: 1,
        mini_course_time_table: 1,
        mini_is_billable: 1,
        mini_is_attendance: 1,
        mini_deduct_course_num: 1,
        mini_deduct_price: 1
      },
      dialogFormVisible: false,
      school_tree_visible: false
    };
  },
  computed: {
    school_id() {
      return this.$store.getters.doneGetSchoolId;
    }
  },
  watch: {
    "form.department_name": {
      handler(newVal) {
        const newValArr = newVal.split(",");
        const oldValArr = this.checkedDepartmentNames;
        newValArr.forEach((i, k) => {
          if (!oldValArr.includes(i)) {
            console.log(i);
            this.departmentNames += i + ", ";
          }
        });
      }
    },
    school_id: {
      handler(val) {
        this.listParams.department_id = val;
        this.getList();
      },
      immediate: true
    }
  },
  methods: {
    onSubmit() {
      this.getList();
    },
    reset() {
      this.listParams = {
        page: 1,
        page_size: 10,
        sort: ""
      };
      this.getList();
    },
    onCreate() {
      this.dialogType = "add";
      this.departmentNames = "";
      this.getList({ type: "all", department_id: "" });
      this.form.course_num_is_enabled = 1;
      this.form.surplus_amount_is_enabled = 1;
      this.form.course_cancel_push_is_enabled = 1;
      // this.form = {
      //   department_id: "",
      //   course_num_is_enabled: 1,
      //   surplus_amount_is_enabled: 1
      // };
      this.dialogFormVisible = true;
    },
    onEdit(row) {
      this.dialogType = "edit";
      this.form = { ...row };
      this.departmentNames = row.department_name;
      this.dialogFormVisible = true;
    },
    onDelete(row) {
      this.$confirm(
        "是否要删除这个校区下的剩余课程数量和剩余金额显示权限?",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      ).then(async () => {
        const { code } = await restCourseWebShowDelete({ id: row.id });
        if (code === 0) {
          this.$message.success("删除成功!");
          this.getList();
        }
      });
    },
    async getList(params = this.listParams) {
      const { code, data } = await restCourseWebShowList(params);
      if (code === 0) {
        if (params.type === "all") {
          this.checkedDepartmentIds = data.results.map((i) => i.department_id);
          this.checkedDepartmentNames = data.results.map(
            (i) => i.department_name
          );
        } else {
          this.total = data.count;
          this.dataList = data.results;
        }
      }
    },
    async handleSwitch(val, row, field) {
      const params = {
        id: row.id
      };
      params[field] = val;
      const { code, message } = await restCourseWebShowEdit(params);
      if (code === 0) {
        this.$message.success("更新成功！");
      } else {
        this.$message.error(message);
      }
      this.getList();
    },
    handleCurrentChange(current) {
      this.listParams.page = current;
      this.getList();
    },
    sizeChange(val) {
      this.listParams.page = 1;
      this.listParams.page_size = val;
      this.getList();
    },
    async save() {
      if (this.departmentNames) {
        this.form.department_id = this.form.department_id.split(",");
      } else {
        this.$message.error("请选择校区");
        return;
      }
      if (this.dialogType === "add") {
        const res = await restCourseWebShowAdd(this.form);
        if (res.code === 0) {
          this.$message.success("添加成功");
          this.dialogFormVisible = false;
          this.getList();
        } else {
          this.$message.error(res.message);
        }
      } else {
        console.log(this.form);
        const { id } = this.form;
        const { code, message } = await restCourseWebShowEdit({
          id,
          ...this.form
        });
        if (code === 0) {
          this.$message.success("编辑成功");
          this.dialogFormVisible = false;
          this.getList();
        } else {
          this.$message.error(message);
        }
      }
    },
    handleClose() {
      this.cancle();
      console.log(this.$refs);
    },
    cancle() {
      this.$refs.form.resetFields();
      this.dialogFormVisible = false;
    }
  }
};
</script>

<style></style>
