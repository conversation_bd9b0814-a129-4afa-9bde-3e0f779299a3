<template>
  <div>
    <el-dialog
      title="选择排课"
      :visible="true"
      width="1000px"
      :before-close="back"
    >
      <!-- @select="select"
        @row-click="rowClick" -->
      <el-form
        class="attendant-audition__content"
        :model="form"
        ref="form"
        :inline="true"
        v-if="classroom_schedule.length === 0"
      >
        <el-form-item label="任课老师">
          <course-staff
            :has_modal="true"
            :check_id.sync="form.teacher_id"
            :check_name.sync="form.teacher_name"
            :staff_placeholder="'请选择任课老师'"
          ></course-staff>
        </el-form-item>
        <el-form-item label="上课日期">
          <el-date-picker
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            class="record-label"
            v-model="form.timeChange"
            value-format="yyyy-MM-dd"
            popper-class="tg-date-picker tg-date--range"
            :pickerOptions="pickerOptions"
          ></el-date-picker>
        </el-form-item>
        <el-button
          type="primary"
          class="tg-button--primary tg-button__icon"
          @click="handleSearch"
        >
          <img
            src="@/assets/图片/icon_search.png"
            alt=""
            class="tg-button__icon--normal"
          />查询</el-button
        >
        <el-button
          type="primary"
          class="tg-button--primary tg-button__icon"
          @click="remake"
        >
          <img
            src="@/assets/图片/icon_reset.png"
            alt=""
            class="tg-button__icon--normal"
          />重置</el-button
        >
      </el-form>
      <!-- 单选 -->
      <el-table
        :data="list.results || []"
        ref="multipleTable"
        :row-key="getRowKeys"
        :cell-style="{ borderRightColor: '#e0e6ed75' }"
        :header-cell-style="{ borderRightColor: '#e0e6ed75' }"
        height="500px"
        highlight-current-row
        @current-change="rowClick"
        border
      >
        <el-table-column prop="course_name" label="课程名称" width="120">
        </el-table-column>
        <el-table-column
          prop="classroom_name"
          label="班级名称"
          width="160"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <div class="copy_name">
              <div>{{ scope.row.classroom_name }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="classroom_alias_name"
          label="班级别名"
          width="160"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <div class="copy_name">
              <div>{{ scope.row.classroom_alias_name }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="teacher_name" label="任课老师" width="120">
        </el-table-column>
        <el-table-column label="在班人数" width="120">
          <template slot-scope="scope">
            {{ scope.row.total_student_numb }}/{{
              scope.row.pre_enrolment_numb
            }}
          </template>
        </el-table-column>
        <el-table-column label="上课时间">
          <template slot-scope="scope">
            {{ getTime(scope.row.start_time) }} {{ scope.row.week_day_chn }}
          </template>
        </el-table-column>

        <template slot="empty">
          <div style="margin-top: 15%">
            <TgLoading v-if="loading"></TgLoading>
            <div class="empty-container" v-else>暂无数据～</div>
          </div>
        </template>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button class="tg-button--plain" type="plain" @click="back"
          >取 消</el-button
        >
        <el-button class="tg-button--primary" type="primary" @click="shellce"
          >确 定</el-button
        >
      </span>
      <!-- 分页 -->
      <pagination
        v-show="list.count > 0"
        :total="Number(list.count)"
        :page.sync="tableInfo.page"
        :limit.sync="tableInfo.page_size"
        @pagination="getList"
      />
    </el-dialog>
  </div>
</template>

<script>
import pagination from "@/components/Pagination";
import timeFormat from "@/public/timeFormat";
import { picker_options } from "@/public/datePickerOptions";
import schoolServiceSchedulingApi from "@/api/schoolServiceScheduling";
// 选择排课
export default {
  name: "courseScheduling",
  props: {
    classroom_id: {
      type: String,
      default: ""
    },
    match_index: {
      type: Number,
      default: 0
    },
    department_ids: {
      type: [Array, String],
      default: () => []
    }
  },
  components: {
    pagination
  },
  data() {
    return {
      dialogVisible: false,
      customer_data: [],
      list: [],
      loading: true,
      totalLoaded: false,
      tableInfo: {
        page: 1,
        page_size: 10,
        sort: ""
      },
      form: {
        status: "not_start,is_started",
        classroom_id: "",
        match_index: 0,
        teacher_id: "",
        teacher_name: "",
        timeChange: []
      },
      pickerOptions: picker_options,
      classroom_schedule: []
    };
  },
  // 计算属性
  computed: {},

  methods: {
    back() {
      this.$emit("close");
    },
    getList() {
      const search = this.changeSearch();
      this.loading = true;
      schoolServiceSchedulingApi
        .GetSchoolServicesChedulingList({
          ...this.tableInfo,
          ...search
        })
        .then((res) => {
          console.log("res :>> ", res);
          this.list = res.data;
          this.loading = false;
        });
    },
    rowClick(row) {
      console.log("row :>> ", row);
      this.customer_data = row;
    },
    handleSearch() {
      this.tableInfo.page = 1;
      this.getList();
    },
    shellce() {
      this.$emit("confirm", this.customer_data);
    },
    getRowKeys(row) {
      return row.id;
    },

    getTime(time) {
      return timeFormat.GetTime(time);
    },

    remake() {
      this.form = {
        status: "not_start,is_started",
        teacher_id: "",
        teacher_name: "",
        timeChange: []
      };
      this.getList();
    },
    changeSearch() {
      const search = JSON.parse(JSON.stringify(this.form));
      if (search.timeChange.length > 0) {
        search.class_time_begin = search.timeChange[0];
        search.class_time_over = search.timeChange[1];
      }
      search.status = search.status.split(",");
      if (this.department_ids && this.department_ids.length > 0) {
        search.department_id = this.department_ids;
      }
      return search;
    }
  },
  created() {
    this.form.classroom_id = this.classroom_id;
    this.form.match_index = Number(this.match_index);
    this.getList();
  },
  mounted() {}
};
</script>

<style lang="less" scoped>
::v-deep .el-dialog__body {
  padding: 20px;
  .record-label .el-input__icon.el-icon-date::before {
    top: 8px;
  }
}
::v-deep .loading-container {
  width: 100% !important;
}
::v-deep .el-form-item__label,
::v-deep .el-form-item__content {
  line-height: 32px;
}
::v-deep .attendant-audition__content {
  margin-bottom: 12px;
  .custom--select {
    ::v-deep .el-input .el-input__inner {
      cursor: pointer;
    }
    ::v-deep .el-input .el-input__suffix-inner {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      height: inherit;
      margin-right: 10px;
      cursor: pointer;
    }
    img {
      height: 4px;
      width: 16px;
    }
  }
}
</style>
