<template>
  <div>
    <el-dialog
      title="编辑内容"
      :visible="true"
      width="1016px"
      class="custom_edit_dialogs"
      append-to-body
      :before-close="back"
    >
      <div class="local-tool">
        <el-button
          class="tg-button--primary"
          type="primary"
          @click="open_resource_hub"
          >资料库</el-button
        >
      </div>
      <div v-loading="uplaodLoding" class="editor_container">
        <Toolbar
          ref="toolbar"
          style="border-bottom: 1px solid #ccc"
          :editor="editor"
          :defaultConfig="toolbarConfig"
          :mode="mode"
        />
        <Editor
          style="height: 500px; overflow-y: hidden"
          v-model="html"
          :defaultConfig="editorConfig"
          :mode="mode"
          @onCreated="onCreated"
        />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="tg-button--plain" type="plain" @click="back"
          >关闭</el-button
        >
        <el-button class="tg-button--primary" type="primary" @click="confirm"
          >确定</el-button
        >
      </span>
    </el-dialog>
    <resourceHub
      v-if="resource_hub_visible"
      @choose="resourceHubChoose"
      @close="resource_hub_visible = false"
      :department_id="department_id"
    ></resourceHub>
  </div>
</template>

<script>
// import appletResource from "@/api/appletResource";
import resourceHub from "./resourceHub";
import { DomEditor } from "@wangeditor/editor";
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
export default {
  name: "editorDialog",
  props: {
    editContent: {
      type: String,
      default: ""
    },
    department_id: {
      type: String,
      default: ""
    }
  },
  components: { Editor, Toolbar, resourceHub },
  data() {
    return {
      uplaodLoding: false,
      resource_hub_visible: false,
      editor: null,
      html: "<p></p>",
      toolbarConfig: {
        excludeKeys: [
          "headerSelect",
          "fullScreen",
          "codeBlock",
          "todo",
          "insertTable",
          "fontFamily",
          "lineHeight",
          "underline",
          "italic",
          "through"
        ]
        // insertKeys: {}
      },
      editorConfig: {
        placeholder: "请输入内容...",
        MENU_CONF: {
          uploadImage: {
            maxNumberOfFiles: 1,
            // 自定义上传图片 方法
            customUpload: this.uploadImg,
            // 上传接口设置文件名
            // fieldName: "image_file",
            allowedFileTypes: [
              "image/png",
              "image/jpg",
              "image/jpeg",
              "image/gif"
            ]
            // meta: {
            //   token: localStorage.getItem("token")
            // }
          },
          uploadVideo: {
            allowedFileTypes: [
              "video/mp4",
              "video/mpeg",
              "video/webm",
              "video/ogg"
            ],
            maxNumberOfFiles: 1,
            customUpload: this.uploadImg
            // fieldName: "image_file",
            // meta: {
            //   token: localStorage.getItem("token")
            // }
          }
        }
      },
      mode: "default" // or 'simple'
    };
  },
  mounted() {
    this.Oss.getAliyun();
  },
  methods: {
    onCreated(editor) {
      this.editor = Object.seal(editor); // 一定要用 Object.seal() ，否则会报错
      console.log("onCreated", editor);
      // const toolbar = editor.getConfig();
      console.log(editor.getAllMenuKeys()); // 当前菜单排序和分组
      this.$nextTick(() => {
        const toolbar = DomEditor.getToolbar(this.editor);
        const curToolbarConfig = toolbar.getConfig();
        this.$nextTick(() => {
          if (this.editContent) {
            this.html = this.editContent;
          }
        });
        console.log("curToolbarConfig :>> ", curToolbarConfig);
      });
    },
    back() {
      this.$emit("close");
    },
    confirm() {
      const data = this.editor.getHtml();
      this.$emit("confirm", data);
      // this.back();
    },
    resourceHubChoose(content, type, poster = "") {
      console.log("content :>> ", content, type);
      this.insertHtml(content, type, poster);
    },
    open_resource_hub() {
      this.resource_hub_visible = true;
    },
    insertHtml(content, type, poster = "") {
      const editor = this.editor; // 获取 editor 实例
      console.log(editor, "editor");
      console.log(content, type, "content");
      if (editor == null) return;
      editor.restoreSelection(); // 恢复选区，会自动聚焦到上次失焦位置
      if (type === 1) {
        editor.dangerouslyInsertHtml(`<img src="${content}" />`);
      } else if (type === 2) {
        const node = {
          type: "video",
          src: content,
          poster,
          children: [{ text: "" }]
        };
        editor.insertNode(node);
      } else {
        editor.dangerouslyInsertHtml(content);
      }
    },
    uploadImg(file, insertFn) {
      console.log("file :>> ", file);
      const isLt50M = file.size / 1024 / 1024 < 50;
      const isLt500M = file.size / 1024 / 1024 < 500;
      if (!isLt50M && file.type.indexOf("image") !== -1) {
        this.$message.warning("上传图片大小不能超过10MB!");
        return;
      } else if (!isLt500M && file.type.indexOf("video") !== -1) {
        this.$message.warning("上传视频大小不能超过500MB!");
        return;
      }
      this.uplaodLoding = true;
      const f = file;
      const orginName = f.name.substring(0, f.name.lastIndexOf("."));
      const suffix = f.name.match(/[^.]+$/)[0];
      const name = `${orginName}_${this.$uuid.v1()}.${suffix}`;
      const copyFile = new File([f], name);
      this.Oss.uploadFile(copyFile)
        .then((res) => {
          console.log(res);
          if (res.code === 0) {
            this.uplaodLoding = false;
            insertFn(res?.url || "");
          } else {
            this.uplaodLoding = false;
            this.$message.error("上传失败！");
          }
        })
        .catch(() => {
          this.uplaodLoding = false;
          this.$message.error("上传失败！");
        });
    }
  },
  beforeDestroy() {
    const editor = this.editor;
    if (editor == null) return;
    editor.destroy(); // 组件销毁时，及时销毁编辑器
  }
};
</script>

<style lang="less" scoped>
.local-tool {
  margin-bottom: 10px;
  text-align: right;
}
/deep/ .editor_container {
  border: 1px solid #ccc;

  img,
  video {
    max-width: 100%;
  }
}
</style>
