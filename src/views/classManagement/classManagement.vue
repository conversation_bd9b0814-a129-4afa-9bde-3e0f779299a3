<template>
  <div id="classManagement" class="container">
    <!-- 头部 -->

    <!-- <div > -->
    <tg-search
      :searchTitle.sync="searchTitle"
      :form.sync="search"
      :showNum="3"
      @reset="reset"
      @change="getSubChannel"
      @search="searchList"
      class="tg-box--margin"
    ></tg-search>
    <!-- </div> -->
    <el-row style="margin: 16px 0 0 6px; width: 100%">
      <el-col :span="24">
        <el-button
          type="plain"
          class="tg-button--plain"
          @click="treeSchShow"
          v-has="{ m: 'class', o: 'create' }"
          >新增</el-button
        >
        <el-button
          type="plain"
          class="tg-button--plain"
          @click="del"
          v-has="{ m: 'class', o: 'delete' }"
          >批量删除</el-button
        >
        <el-button
          type="plain"
          class="tg-button--plain"
          :loading="exportLoading"
          @click="exportExcel"
          v-has="{ m: 'class', o: 'export' }"
          >导出</el-button
        >
        <el-button
          type="plain"
          class="tg-button--plain"
          @click="downloadTemplate"
          v-has="{ m: 'class', o: 'importTemplate' }"
          >班级导入下载模板</el-button
        >
        <el-button
          type="plain"
          class="tg-button--plain"
          @click="importTemplate"
          v-has="{ m: 'class', o: 'importTemplate' }"
          >班级导入</el-button
        >
        <el-button
          type="plain"
          class="tg-button--plain"
          v-has="{ m: 'class', o: 'importStuTemplate' }"
          @click="downloadStuTemplate"
        >
          班级学员导入下载模板
        </el-button>
        <el-button
          type="plain"
          class="tg-button--plain"
          v-has="{ m: 'class', o: 'importStuTemplate' }"
          @click="importStuTemplate"
        >
          班级学员导入
        </el-button>
        <select-field
          :allFields.sync="table_title"
          :btnType="'button'"
        ></select-field>
      </el-col>
    </el-row>
    <div class="tg-table__box tg-box--margin">
      <div class="tg-box--border"></div>
      <el-table
        @selection-change="handleSelectionChange"
        :data="schoolServiceClassroomList?.results || []"
        class="tg-table"
        :row-key="getRowKeys"
        ref="table"
        :summary-method="getSummaries"
        show-summary
        :cell-style="{ borderRightColor: '#e0e6ed75' }"
        :header-cell-style="{ borderRightColor: '#e0e6ed75' }"
        border
        @sort-change="sortChange"
      >
        <el-table-column type="selection" width="50" :reserve-selection="true">
        </el-table-column>
        <template v-for="(item, index) in table_title">
          <el-table-column
            v-if="item.show"
            :key="index"
            :prop="item.props"
            :label="item.label"
            :min-width="item.width"
            :fixed="item.fixed ? true : false"
            :sortable="item.sort"
            :show-overflow-tooltip="item.tooltip"
          >
            <template slot-scope="scope">
              <div v-if="item.props === 'name'">
                <div class="copy_name">
                  <span v-if="scope.row.match_index > 0" class="tg__match-icon"
                    >赛</span
                  >
                  <el-button
                    v-has="{ m: 'class', o: 'info' }"
                    class="tg-text--blue tg-table__name--ellipsis"
                    type="text"
                    @click="openEdit(scope.row)"
                    >{{ scope.row.name }}</el-button
                  >
                  <div v-copy="scope.row.name"></div>
                </div>
              </div>
              <template v-else-if="item.type === 'date'">
                {{
                  scope.row[scope.column.property]
                    ? scope.row[scope.column.property].indexOf("0001-01-01") >
                      -1
                      ? ""
                      : getDate(scope.row[scope.column.property])
                    : ""
                }}
              </template>
              <template v-else-if="item.props === 'assistant_teacher_name'">
                {{
                  scope.row.assistant_teacher_name == null
                    ? ""
                    : scope.row.assistant_teacher_name.toString()
                }}
              </template>
              <template v-else-if="item.props === 'student_numb'">
                {{ scope.row.student_numb }} /
                {{ scope.row.pre_enrolment_numb }}
              </template>
              <template v-else-if="item.props === 'in_student_percent'">
                {{ scope.row.in_student_percent }}%
              </template>
              <template v-else-if="item.props === 'finish_lesson_numb'">
                {{ scope.row.finish_lesson_numb }}/{{
                  scope.row.reality_lesson_numb
                }}
              </template>
              <span v-else>{{ scope.row[scope.column.property] }}</span>
            </template>
          </el-table-column>
        </template>
        <el-table-column fixed="right" label="操作">
          <template slot-scope="scope">
            <el-dropdown v-if="scope.row.cancel_status === true">
              <operate-more :index="scope.$index"></operate-more>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  @click.native="openCancelClass(scope.row)"
                  v-if="scope.row.cancel_status === true"
                  type="text"
                  size="small"
                  ><span>撤销结业</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <template v-else>
              <span v-if="scope.row.status === 'is_end'">已结业</span>
              <el-dropdown v-else>
                <operate-more :index="scope.$index"></operate-more>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    @click.native="openDividing(scope.row)"
                    type="text"
                    size="small"
                    v-has="{ m: 'class', o: 'divid' }"
                    ><span>入班</span>
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-has="{ m: 'class', o: 'offDuty' }"
                    @click.native="openOffDuty(scope.row)"
                    type="text"
                    size="small"
                    ><span>出班</span>
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-if="scope.row.match_index === 0"
                    v-has="{ m: 'class', o: 'transfer' }"
                    @click.native="openTransferDialog(scope.row)"
                    type="text"
                    size="small"
                    ><span>转班</span>
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-has="{ m: 'class', o: 'finished' }"
                    type="text"
                    size="small"
                    @click.native="openDialogFinished(scope.row)"
                    ><span>结业</span>
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-has="{ m: 'class', o: 'modifyTeacher' }"
                    @click.native="modifyTeacher(scope.row)"
                    type="text"
                    size="small"
                    ><span>修改班主任</span>
                  </el-dropdown-item>
                  <el-dropdown-item
                    v-has="{ m: 'class', o: 'class_notice' }"
                    @click.native="sendContent(scope.row, 'class_notice')"
                    type="text"
                    size="small"
                    ><span>发送班级通知</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </template>
        </el-table-column>
        <template slot="empty">
          <div style="margin-top: 15%">
            <TgLoading v-if="loading"></TgLoading>
            <div class="empty-container" v-else>暂无数据～</div>
          </div>
        </template>
      </el-table>
      <div class="tg-pagination">
        <span class="el-pagination__total"
          >共 {{ schoolServiceClassroomList.count }} 条</span
        >
        <el-pagination
          background
          layout="sizes,prev,pager,next,jumper"
          :total="schoolServiceClassroomList.count"
          :page-size.sync="page_size"
          :current-page.sync="page"
          @current-change="currentChange"
          @size-change="sizeChange"
          :page-sizes="[10, 20, 50, 100]"
        >
        </el-pagination>
      </div>
    </div>

    <!-- 转班 -->
    <transfer-classes
      @closeTransferDialog="closeTransferDialog"
      v-if="transferDialog"
      :rowData="rowData"
    ></transfer-classes>

    <!-- 结业 -->
    <el-dialog
      title="提示"
      v-if="dialogFinished"
      width="800px"
      :visible="true"
      class="dialog-finished"
    >
      <div class="tip-box">
        <div class="text">
          <img
            src="@/assets/图片/icon_info.png"
            alt=""
          />结业之后不能再修改班级信息，也不能做入班、出班、排课、升班等操作。
        </div>
        <div class="text txt">确定将"{{ rowData.name }}"结业？</div>
      </div>

      <div class="dialog-finished-form">
        <el-form
          ref="finishedform"
          :model="finishedform"
          :rules="finishedformRules"
          label-width="120px;"
          style="margin-top: 20px; padding-bottom: 213px"
        >
          <el-form-item label="实际结业日期" prop="date">
            <el-date-picker
              v-model="finishedform.date"
              :picker-options="pickerReleaseDateOptions"
              type="date"
              :editable="false"
              clearable
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              placeholder="请选择实际结业日期"
              style="width: 322px"
              popper-class="tg-date-picker"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogFinished = false" class="tg-button--plain"
          >取消</el-button
        >
        <el-button
          type="primary"
          @click="classroomFinishedConfirm"
          class="tg-button--primary"
          :disabled="finishDisabled"
          >确定</el-button
        >
      </span>
    </el-dialog>

    <!-- 新增班级 -->
    <classNew
      @close="closeClassNewWindow"
      v-if="dialogAddVisible"
      :type="listOperateType"
      :addDepartmentId="add_department_id"
      :addDepartmentName="add_department_name"
    ></classNew>

    <!-- 修改班主任 -->
    <el-dialog
      title="修改班主任"
      :visible.sync="dialogModifyTeacher"
      width="800px"
      style="height: 80%"
      class="modify-teacher-dialog"
      custom-class="modify-teacher-dialog"
    >
      <!-- <div style="width: 798px; margin-top: -50px; margin-left: -20px">
        <el-divider></el-divider>
      </div> -->
      <el-form
        style="margin-top: -10px; margin-left: -15px"
        :model="modifyTeacherForm"
        :rules="teacherFormRules"
        ref="modifyTeacherForm"
        label-width="100px"
        class="modify-teacher-form"
      >
        <el-form-item label="修改班主任" prop="header_teacher_name">
          <course-staff
            class="modify-teacher"
            :check_id.sync="modifyTeacherForm.header_teacher_id"
            :check_name.sync="modifyTeacherForm.header_teacher_name"
          ></course-staff>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogModifyTeacher = false" class="tg-button--plain"
          >取消</el-button
        >
        <el-button
          type="primary"
          @click="determineTeacher"
          class="tg-button--primary"
          >确定</el-button
        >
      </span>
    </el-dialog>
    <school-tree
      :flag.sync="school_tree_visible"
      :id.sync="add_department_id"
      :name.sync="add_department_name"
      :required="true"
      :has_modal="true"
      type="radio"
      @confirm="add"
      :use_store_options="true"
    >
    </school-tree>
    <!-- 发送问卷调查 -->
    <PushActionStudents
      v-if="pushActionVisible"
      :data="pushActionData.data"
      survey_type="graduation"
      :campus_id="pushActionData.department_id"
      @cancel="pushActionCancel"
      @confirm="pushActionConfirm"
    ></PushActionStudents>
    <classImport v-if="classImport_visible" @close="closeWindow"></classImport>
    <importStu v-if="importStu_visible" @close="closeStuWindow"></importStu>
    <editorDialog
      v-if="editorDialogVisible"
      :department_id="editorDialogDepartmentId"
      @close="editorDialogVisible = false"
      @confirm="editorDialogConfirm"
    ></editorDialog>
  </div>
</template>

<script>
import SelectField from "@/components/selectField/selectField.vue";
import timeFormat from "@/public/timeFormat"; // 日期转化
import classManagementApi from "@/api/classManagement"; // 班级导出
import classNew from "../../components/classManagement/classNew.vue"; // 新增班级
// import tgSearch from "@/components/search/search.vue";
import operateMore from "@/components/operateMore/index.vue";
import moment from "moment";
import transferClasses from "./transferClasses";
import courseStaff from "@/components/staff/courseStaff.vue"; // 选择员工
import schoolTree from "@/components/schoolTree/schoolTree"; // 校区弹框
import classImport from "./classImport.vue";
import importStu from "../classDetail/components/importStu.vue";
import editorDialog from "@/views/classManagement/editorDialog.vue";
import { tableSummaries } from "@/mixins/tableSummaries";

import { getCourseConfigAll, classroomCancel } from "@/api/courseManagement.js";
import appletResource from "@/api/appletResource"; // 资料库
export default {
  components: {
    SelectField,
    // tgSearch,
    classNew, // 新增班级
    courseStaff,
    operateMore,
    transferClasses,
    schoolTree,
    classImport,
    importStu,
    editorDialog
  },
  mixins: [tableSummaries],
  data() {
    return {
      table_title: [
        {
          props: "name",
          label: "班级名称",
          show: true,
          width: 160,
          fixed: true,
          tooltip: true,
          sort: "custom"
        },
        {
          props: "alias_name",
          label: "班级别名",
          show: true,
          width: 140,
          fixed: true,
          tooltip: true,
          sort: "custom"
        },
        {
          props: "department_name",
          label: "所属校区",
          show: true,
          width: 140,
          tooltip: true,
          sort: "custom"
        },
        {
          props: "course_name",
          label: "课程名称",
          show: true,
          width: 160,
          tooltip: true,
          sort: "custom"
        },
        {
          props: "course_level",
          label: "课程种类",
          show: true,
          width: 160,
          tooltip: true,
          sort: "custom"
        },
        {
          props: "lesson_type",
          label: "班型",
          show: true,
          width: 160,
          tooltip: true,
          sort: "custom"
        },
        {
          props: "teacher_name",
          label: "任课老师",
          show: true,
          width: 120,
          tooltip: true
        },
        {
          props: "assistant_teacher_name",
          label: "助教",
          show: true,
          width: 110,
          tooltip: true
        },
        {
          props: "header_teacher_name",
          label: "班主任",
          show: true,
          width: 110,
          tooltip: true
        },
        {
          props: "school_room_name",
          label: "默认教室",
          show: true,
          width: 140,
          tooltip: true
        },
        {
          props: "class_time",
          label: "上课时间",
          show: true,
          width: 170,
          tooltip: true
        },
        {
          props: "student_numb",
          label: "人数",
          show: true,
          width: 130,
          tooltip: true
        },
        {
          props: "in_student_percent",
          label: "入班率",
          show: true,
          width: 130,
          tooltip: true
        },
        {
          props: "pre_lesson_numb",
          label: "计划课次",
          show: true,
          width: 130,
          tooltip: true,
          sort: "custom"
        },
        {
          props: "finish_lesson_numb",
          label: "已上/已排课次",
          show: true,
          width: 150,
          tooltip: true
        },
        {
          props: "pre_start_time",
          label: "计划开班日期",
          show: true,
          width: 170,
          type: "date",
          tooltip: true,
          sort: "custom"
        },
        {
          props: "scheduling_start_time",
          label: "实际开班日期",
          show: true,
          width: 170,
          type: "date",
          tooltip: true
        },
        {
          props: "pre_end_time",
          label: "计划结业日期",
          show: true,
          width: 170,
          type: "date",
          tooltip: true,
          sort: "custom"
        },
        {
          props: "reality_end_time",
          label: "实际结业日期",
          show: true,
          width: 170,
          type: "date",
          tooltip: true,
          sort: "custom"
        },
        {
          props: "classroom_type_chn",
          label: "班级类型",
          show: true,
          width: 130,
          tooltip: true,
          sort: "custom"
        },
        {
          props: "course_category_chn",
          label: "班级类别",
          show: true,
          width: 130,
          tooltip: true,
          sort: "custom"
        },
        {
          props: "status_chn",
          label: "结业状态",
          show: true,
          width: 115,
          tooltip: true,
          sort: "custom"
        },
        {
          props: "memo",
          label: "备注",
          show: true,
          width: 130,
          tooltip: true
        },
        {
          props: "created_at",
          label: "班级创建日期",
          show: true,
          width: 130,
          type: "date",
          tooltip: true,
          sort: "custom"
        }
      ],
      pushActionVisible: false,
      editorDialogVisible: false,
      send_type: "",
      pushActionData: {
        data: [],
        department_id: ""
      },
      finishDisabled: false,
      exportLoading: false,
      importStu_visible: false,
      dialogModifyTeacher: false,
      dialogAddVisible: false,
      transferDialog: false,
      dialogFinished: false,
      listOperateType: "",
      openStaff: false,
      tab: 1,
      type: "",
      name: "", // 名字
      memo: "", // 备注
      is_enabled: "", // 是否开启
      id: "",
      page: 1,
      page_size: 10,
      rowIds: "",
      modifyTeacherForm: {
        header_teacher_name: "",
        header_teacher_id: ""
      },
      finishedform: {
        date: ""
      },
      finishedformRules: {
        date: [
          { required: true, message: "请输入实际结业日期", trigger: "blur" }
        ]
      },
      teacherFormRules: {
        header_teacher_name: [
          { required: true, message: "请选择班主任", trigger: "blur" }
        ]
      },
      // 筛选字段
      searchTitle: [
        { props: "name", label: "班级名称", type: "input", show: true },
        { props: "alias_name", label: "班级别名", type: "input", show: true },
        // {
        //   props: "classroom_category",
        //   label: "班级类别",
        //   type: "select",
        //   show: false,
        //   selectOptions: [{ id: "", name: "不限" }],
        // },
        {
          props: "classroom_type",
          label: "班级类型",
          type: "select",
          show: true,
          selectOptions: [{ id: "", name: "不限" }]
        },
        {
          props: "status",
          label: "结业状态",
          type: "select",
          show: false,
          selectOptions: []
        },
        {
          props: "course_id",
          label: "课程名称",
          type: "choose_course",
          show: false
        },
        {
          props: "teacher_id",
          label: "任课老师",
          type: "course_staff",
          show: false,
          is_leave: true,
          selectOptions: []
        },
        {
          props: "header_teacher_id",
          label: "班主任",
          type: "course_staff",
          show: false,
          is_leave: true,
          selectOptions: []
        },

        {
          props: "department_id",
          label: "所属校区",
          type: "school",
          show: false,
          selectOptions: [],
          school_choose_type: "radio",
          use_store_options: true
        },
        {
          props: "student_id",
          label: "学员",
          type: "student",
          show: true,
          placeholder: "请输入学员姓名/学号/电话"
        },
        {
          props: "scheduling_status",
          label: "排课状态",
          type: "select",
          show: false,
          selectOptions: [{ id: "", name: "不限" }]
        },

        {
          props: ["class_time", "class_start_time"],
          label: "上课时间",
          subLabel: ["上课时间", ""],
          type: "cascader",
          show: false,
          selectOptionsOne: [
            { id: "", name: "不限" },
            { id: "1", name: "星期一" },
            { id: "2", name: "星期二" },
            { id: "3", name: "星期三" },
            { id: "4", name: "星期四" },
            { id: "5", name: "星期五" },
            { id: "6", name: "星期六" },
            { id: "0", name: "星期日" }
          ],
          selectOptionsTwo: [
            { id: "09:00-12:00", name: "09:00-12:00上午" },
            { id: "13:00-17:00", name: "13:00-17:00下午" },
            { id: "17:00-23:59", name: "17:00-23:59晚上" }
          ]
        },
        {
          props: "sign_up_time",
          label: "计划结业日期",
          type: "date",
          show: false,
          width: 200,
          has_options: true
        },
        {
          props: "pre_time",
          label: "计划开班日期",
          type: "date",
          show: false,
          width: 200,
          has_options: true
        },
        {
          props: "create_time",
          label: "班级创建日期",
          type: "custom_date",
          show: false,
          width: 200,
          has_options: true
        },
        {
          props: "reality_date",
          label: "实际结业日期",
          type: "date",
          show: false,
          width: 200,
          has_options: true
        },
        {
          props: "course_level",
          label: "课程种类",
          type: "select",
          show: false,
          selectOptions: [{ id: "", name: "不限" }]
        },
        {
          props: "lesson_type",
          label: "班型",
          type: "select",
          show: false,
          selectOptions: [{ id: "", name: "不限" }]
        },
        // 只看赛事
        {
          props: "is_competition",
          label: "只查看赛事班级",
          type: "bool",
          show: false
        }
      ],
      course_level: [],
      lesson_type: [],
      // 筛选
      search: {
        name: "",
        alias_name: "",
        teacher_id: "",
        header_teacher_id: "",
        course_id: "",
        class_time: "",
        classroom_type: "",
        classroom_category: "",
        status: "not_start",
        student_id: "",
        scheduling_status: "",
        pre_time: "",
        create_time: "",
        reality_date: "",
        department_id: "",
        sort: "",
        course_level: "",
        lesson_type: "",
        is_competition: ""
      },
      height: window.innerHeight - 370,
      status_list: [],
      rowData: {
        name: ""
      },
      select_time: "",
      pickerReleaseDateOptions: {
        disabledDate: (time) => {
          // var selectTime = moment(this.select_time).valueOf();
          // var now = time.valueOf();
          return time.getTime() > Date.now();
        }
      },
      add_department_id: "",
      add_department_name: "",
      school_tree_visible: false,
      classImport_visible: false,
      stuTotal: {
        student_numb: "",
        pre_enrolment_numb: ""
      },
      loading: false,
      list: {},
      editorDialogClassroomId: "",
      editorDialogDepartmentId: "",
      send_types: {
        course_summary: "课程总结",
        class_notice: "班级通知",
        parent_class: "家长课堂"
      }
    };
  },
  beforeRouteEnter(to, from, next) {
    // 需要重新获取数据列表的上级页面
    const route_names = [
      "classManagementDivid",
      "offDutyClasses",
      "classDetail"
    ];
    if (route_names.includes(from.name)) {
      next((vm) => {
        vm.searchStaff();
        vm.$refs.table.clearSelection();
      });
    } else {
      next();
    }
  },
  computed: {
    // 班级列表
    schoolServiceClassroomList() {
      const list =
        this.$store.getters.doneGetSchoolServiceClassroomListNew || {};
      return {
        ...list,
        results: list.results || [] // 确保 results 始终是数组
      };
    },
    schoolIds() {
      return this.$store.getters.doneGetSchoolId;
    },
    school_name() {
      return this.$store.getters.doneGetSchoolName;
    }
  },
  watch: {
    schoolServiceClassroomList(new_obj) {
      this.list = new_obj || [];
      this.loading = false;
      this.totalLoaded = false;
    },
    // 转
    success(new_bool) {
      if (new_bool) {
        this.$store.dispatch("getInvitationList", {
          page: 1
        });
      }
    },
    schoolIds() {
      this.clearSelection();
      this.searchStaff();
    }
  },
  methods: {
    // 发送学员报告
    editorDialogConfirm(content) {
      console.log("content :>> ", content);
      const { send_type, send_types } = this;
      // 询问框提示
      this.$confirm(`确定发送${send_types[send_type]}吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        const { id, name } = this.rowData;
        const params = {
          classroom: {
            classroom_id: id,
            classroom_name: name
          },
          content,
          type: send_type,
          students: []
        };
        appletResource
          .feedbackSend(params)
          .then((res) => {
            if (res.data.code === 0) {
              this.editorDialogVisible = false;
              this.$message.success("发送成功！");
            } else {
              this.$message.error(res.data.message);
            }
          })
          .catch((err) => {
            console.error(err);
            this.$message.error("发送失败！");
          });
      });
    },
    showPushDialog() {
      this.pushActionVisible = false;
      if (this.pushActionData.data.length > 0) {
        this.pushActionVisible = true;
      }
    },
    pushActionCancel() {
      this.pushActionVisible = false;
    },
    pushActionConfirm() {
      this.pushActionVisible = false;
    },
    getCourseConfig() {
      getCourseConfigAll()
        .then((res) => {
          if (res.data) {
            // 从API响应中提取课程类型筛选下拉菜单选项
            const extractOptions = (config_type, config_list) => {
              return config_list
                .find((item) => item.config_type === config_type)
                .config_list.map((item) => {
                  return {
                    id: item.config_name,
                    name: item.config_name
                  };
                });
            };

            // 更新课程类型筛选下拉菜单选项
            const course_levelOptions = extractOptions(
              "course_level",
              res.data
            );
            this.searchTitle
              .find((item) => item.props === "course_level")
              .selectOptions.push(...course_levelOptions);

            // 更新班型筛选下拉菜单选项
            const lesson_typeOptions = extractOptions(
              "course_class_type",
              res.data
            );
            this.searchTitle
              .find((item) => item.props === "lesson_type")
              .selectOptions.push(...lesson_typeOptions);
          } else {
            // 处理API响应数据为空的情况
            console.error("API response does not contain data.");
          }
        })
        .catch((error) => {
          // 处理API请求错误
          console.error("Error fetching course configuration:", error);
        });
    },
    // 下载模版
    downloadStuTemplate() {
      const downloadElement = document.createElement("a");
      downloadElement.href =
        "./classroom_excel.xlsx?v=" + window.__APP_VERSION__;
      downloadElement.download = `班级学员信息模版.xlsx`; // 下载后文件名
      document.body.appendChild(downloadElement);
      downloadElement.click(); // 点击下载
      document.body.removeChild(downloadElement);
    },
    // 导入模板
    importStuTemplate() {
      this.importStu_visible = true;
    },
    closeStuWindow() {
      this.importStu_visible = false;
    },
    clearSelection() {
      this.$nextTick(() => {
        this.$refs.table.clearSelection();
      });
    },
    getSummariesCellJsx(val) {
      let jsx = null;
      const { totalLoading, totalLoaded } = this;
      const { student_numb, pre_enrolment_numb } = this.stuTotal;
      jsx = (
        <span>
          {totalLoading ? (
            <i class="el-icon-loading"></i>
          ) : (
            <span>
              {totalLoaded ? `${student_numb}/${pre_enrolment_numb}` : "*"}
            </span>
          )}
        </span>
      );
      return jsx;
    },

    getSummaries(param) {
      const { columns } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 1) {
          sums[index] = this.getSummariesJsx();
        } else {
          if (column.property === "student_numb") {
            sums[index] = this.getSummariesCellJsx();
          } else {
            sums[index] = "";
          }
        }
      });
      return sums; // 最后返回合计行的数据
    },
    downloadTemplate() {
      // classManagementApi.downloadTemplate().then((res) => {
      //   var blob = new Blob([res.data], {
      //     type: "application/vnd.ms-excel",
      //   }); // type这里表示xlsx类型
      //   var downloadElement = document.createElement("a");
      //   var href = window.URL.createObjectURL(blob); // 创建下载的链接
      //   downloadElement.href = href;
      //   downloadElement.download = `班级信息模版.xlsx`; // 下载后文件名
      //   document.body.appendChild(downloadElement);
      //   downloadElement.click(); // 点击下载
      //   document.body.removeChild(downloadElement);
      // });
      const downloadElement = document.createElement("a");

      downloadElement.href =
        "./classInfo_excel.xlsx?v=" + window.__APP_VERSION__;
      downloadElement.download = `班级管理模版.xlsx`; // 下载后文件名
      document.body.appendChild(downloadElement);
      downloadElement.click(); // 点击下载
      document.body.removeChild(downloadElement);
    },
    importTemplate() {
      this.classImport_visible = true;
    },
    closeWindow() {
      this.classImport_visible = false;
      this.clearSelection();
      this.searchStaff();
    },
    closeClassNewWindow() {
      this.dialogAddVisible = false;
      this.clearSelection();
      this.reset();
    },
    // 打开入班
    openDividing(row) {
      const {
        course_id,
        id,
        name,
        department_id,
        department_name,
        match_index
      } = row;
      const is_competition = match_index > 0; // 是否是赛事班级
      this.$router.push({
        name: is_competition
          ? "classManagementDividMatch"
          : "classManagementDivid",
        query: {
          course_id,
          classroom_id: id,
          classroom_name: name,
          department_id,
          department_name,
          page: this.page,
          match_index
        }
      });
    },
    // 打开出班
    openOffDuty(row) {
      const { course_id, id, name, department_id, department_name } = row;
      this.$router.push({
        name: "offDutyClasses",
        query: {
          course_id,
          classroom_id: id,
          classroom_name: name,
          department_id,
          department_name,
          page: this.page
        }
      });
    },
    openTransferDialog(row) {
      this.rowData = row;
      this.transferDialog = true;
    },
    closeTransferDialog() {
      this.transferDialog = false;
    },
    // 修改班主任
    modifyTeacher(row) {
      this.rowData = row;
      const { header_teacher_id, header_teacher_name } = row;
      this.modifyTeacherForm = {
        header_teacher_name,
        header_teacher_id
      };
      this.dialogModifyTeacher = true;
    },

    sendContent(row, type) {
      if (row.student_numb <= 0) {
        this.$message.warning("班级学员数量为0，无法发送！");
        return;
      }
      this.send_type = type;
      this.editorDialogDepartmentId = row.department_id;
      this.editorDialogClassroomId = row.id;
      this.rowData = row;
      this.editorDialogVisible = true;
    },

    // 撤销结业
    openCancelClass(row) {
      this.$confirm("确认要取消该班级结业, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        const classroom_id = row.id;
        // this.clearSelection();
        classroomCancel({ classroom_id }).then((res) => {
          if (res.data.code === 0) {
            this.$message.success("操作成功!");
            this.initList();
          } else {
            this.$message.error(res.data.message);
          }
        });
      });
    },
    // 修改班主任确认
    determineTeacher() {
      this.$refs.modifyTeacherForm.validate((valid) => {
        if (valid) {
          this.dialogModifyTeacher = false;
          classManagementApi
            .SchoolServiceClassroomUpdateHeaderTeacher({
              id: this.rowData.id,
              ...this.modifyTeacherForm
            })
            .then((res) => {
              if (!res.err) {
                this.$message.success("修改成功!");
                this.clearSelection();
                this.searchStaff();
              }
            });
        }
      });
    },
    // 校区弹窗
    treeSchShow() {
      if (this.schoolIds && this.schoolIds.length === 1) {
        this.add_department_id = this.schoolIds[0];
        this.add_department_name = this.school_name[0];
        this.dialogAddVisible = true;
        this.listOperateType = "add";
      } else {
        this.school_tree_visible = true;
        this.add_department_id = "";
        this.add_department_name = "";
      }
    },

    // 新增弹窗
    add() {
      if (this.add_department_id) {
        this.school_tree_visible = false;
        this.dialogAddVisible = true;
        this.listOperateType = "add";
      } else {
        this.$message.info("请勾选校区");
      }
    },

    // //姓名弹窗
    openEdit(row) {
      // this.dialogAddVisible = true;
      // this.listOperateType = "edit";
      // this.$store.dispatch("getSchoolServiceClassroomInfo", {
      //   id: row.id,
      // });
      const id = row.id;
      const course_id = row.course_id;
      this.$router.push({
        path: `/classDetail`,
        query: { id, course_id }
      });
    },
    // 批量删除
    del() {
      if (this.customer_data.length === 0) return;
      this.$confirm("此操作将删除班级, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        const id = this.customer_data.map((item) => item.id);
        classManagementApi
          .schoolServiceClassroomDelete({ classroom_id: id })
          .then(() => {
            this.clearSelection();
            this.initList();
          });
      });
    },

    // 多选框
    handleSelectionChange(val) {
      this.customer_data = val;
    },
    // 时间格式
    getDate(time) {
      return timeFormat.GetDate(time);
    },
    // 导出
    exportExcel() {
      // opid
      this.exportLoading = true;
      const search = this.changeSearch();
      if (!search.department_id) {
        search.department_id = this.schoolIds;
      }
      classManagementApi
        .SchoolServiceClassroomExcel({ ...search, status_type: "intention" })
        .then((res) => {
          const blob = new Blob([res.data], {
            type: "application/vnd.ms-excel"
          }); // type这里表示xlsx类型
          const downloadElement = document.createElement("a");
          const href = window.URL.createObjectURL(blob); // 创建下载的链接
          downloadElement.href = href;

          downloadElement.download = `${moment(new Date()).format(
            "班级管理YYYY年MM月DD日HH时mm分"
          )}.xlsx`; // 下载后文件名
          document.body.appendChild(downloadElement);
          downloadElement.click(); // 点击下载
          document.body.removeChild(downloadElement);
          this.exportLoading = false;
        });
    },
    sortChange(val) {
      const { prop, order } = val;
      let _oreder = "";
      if (order === "ascending") {
        _oreder = "asc";
      } else if (order === "descending") {
        _oreder = "desc";
      }
      this.search.sort = `${prop} ${_oreder}`;
      this.searchList();
    },
    getSubChannel(id) {
      const params = {
        parentid: id,
        page: 1,
        pageSize: 1000,
        _t: new Date().getDate(),
        order: "desc",
        is_enabled: true
      };
      this.$store.dispatch("getSubChannelByChannelId", { params });
    },
    searchList() {
      this.page = 1;
      this.searchStaff();
    },
    // 查询接口
    searchStaff() {
      this.clearSelection();
      // const search = this.changeSearch(this.search);
      // this.getStudentTotal({ department_id: this.schoolIds, ...search });
      // const search = this.changeSearch(this.search);
      // this.getStudentTotal({ ...search });
      this.initList();
    },
    changeSearch() {
      const search = JSON.parse(JSON.stringify(this.search));
      if (
        search.pre_time &&
        search.pre_time !== "" &&
        typeof search.pre_time !== "undefined"
      ) {
        search.pre_begin_start_time = search.pre_time[0];
        search.pre_over_start_time = search.pre_time[1];
      }
      if (
        search.create_time &&
        search.create_time !== "" &&
        typeof search.create_time !== "undefined"
      ) {
        search.create_start_time = search.create_time[0];
        search.create_end_time = search.create_time[1];
      }
      if (
        search.reality_date &&
        search.reality_date !== "" &&
        typeof search.reality_date !== "undefined"
      ) {
        search.reality_begin_end_time = search.reality_date[0];
        search.reality_over_end_time = search.reality_date[1];
      }

      if (
        search.sign_up_time &&
        search.sign_up_time !== "" &&
        typeof search.sign_up_time !== "undefined"
      ) {
        search.pre_begin_end_time = search.sign_up_time[0];
        search.pre_over_end_time = search.sign_up_time[1];
      }
      if (search.course_id && search.course_id !== undefined) {
        const ids = search.course_id.split(",");
        search.course_id = ids;
      }
      if (search.is_competition && search.is_competition !== undefined) {
        search.match_exists = search.is_competition ? 1 : 0;
      }
      search.department_id = search.department_id
        ? search.department_id
        : this.schoolIds;
      delete search.time;
      delete search.sign_up_time;
      delete search.pre_time;
      delete search.create_time;
      delete search.reality_date;
      delete search.is_competition;
      return search;
    },

    reset() {
      this.search = {
        name: "",
        teacher_id: "",
        course_id: "",
        header_teacher_id: "",
        alias_name: "",
        class_time: "",
        student_id: "",
        classroom_type: "",
        classroom_category: "",
        status: "not_start",
        scheduling_status: "",
        pre_time: "",
        create_time: "",
        reality_date: "",
        sort: "",
        course_level: "",
        lesson_type: "",
        is_competition: ""
      };
      this.page = 1;
      this.page_size = 10;
      this.clearSelection();
      this.initList();
      // const search = this.changeSearch(this.search);
      // this.getStudentTotal({ department_id: this.schoolIds, ...search });
    },
    // 分页
    currentChange(val) {
      this.page = val;
      this.initList();
    },
    sizeChange(val) {
      this.page_size = val;
      this.page = 1;
      this.initList();
    },
    getRowKeys(row) {
      return row.id;
    },
    // 获取合计数据
    obtainSumData() {
      const search = this.changeSearch(this.search);
      this.totalLoading = true;
      classManagementApi
        .getStudentTotal({
          department_id: this.schoolIds,
          ...search
        })
        .then((res) => {
          this.totalLoading = false;
          if (+res.status === 200 && +res.data.code === 0) {
            this.stuTotal = res.data.data;
            this.totalLoaded = true;
          } else {
            this.$message.error("获取合计数据失败！");
          }
        })
        .catch(() => {
          this.totalLoading = false;
        });
    },
    // getStudentTotal(data) {
    //   classManagementApi.getStudentTotal(data).then((res) => {
    //     if (+res.status === 200 && +res.data.code === 0) {
    //       this.stuTotal = res.data.data;
    //     }
    //   });
    // },
    // 结业
    openDialogFinished(row) {
      this.select_time = row.pre_start_time;
      this.dialogFinished = true;
      this.rowData = row;
      console.log(this.rowData);
      this.getClassStudent(row);
    },
    getClassStudent(row) {
      const { department_id, id } = row;
      this.finishDisabled = true;
      classManagementApi
        .GetSchoolServiceClassroomStudentList({
          classroom_id: id,
          show_leave_student: true,
          status: "in_classroom"
        })
        .then((res) => {
          this.finishDisabled = false;
          const { data } = res;
          const arr = [];
          if (data) {
            data.forEach((item) => {
              arr.push({
                student_id: item.student_id,
                student_name: item.student_name,
                student_mobile: item.student_mobile,
                student_type: item.status,
                student_type_chn: "已结业"
              });
            });
          }
          this.pushActionData.data = arr;
          this.pushActionData.department_id = department_id;
        })
        .catch(() => {
          this.finishDisabled = false;
        });
    },
    classroomFinishedConfirm() {
      this.$refs.finishedform.validate((valid) => {
        if (valid) {
          // 调接口 如果有未完的课
          this.finishDisabled = true;
          classManagementApi
            .checkClassroomSchedulingStatus({
              classroom_id: this.rowData.id
            })
            .then((res) => {
              console.log(res);
              if (res.data.data.num > 0) {
                this.$confirm(
                  "该班级下还有未上课的排课，是否结业并取消",
                  "提示",
                  {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                  }
                )
                  .then(() => {
                    this.classroomFinished();
                  })
                  .catch(() => {
                    //
                  });
              } else {
                const classroom_ids = this.rowData.id;
                const { date } = this.finishedform;
                classManagementApi
                  .GetSchoolServiceClassroomFinished({
                    classroom_ids: [classroom_ids],
                    finished_time: date
                  })
                  .then((res) => {
                    this.finishDisabled = false;
                    if (!res.err) {
                      this.$message.success("提交成功!");
                      this.dialogFinished = false;
                      this.clearSelection();
                      this.searchStaff();
                      this.showPushDialog();
                    }
                  })
                  .catch(() => {
                    this.finishDisabled = false;
                  });
              }
            });
        }
      });
    },

    classroomFinished() {
      this.$refs.finishedform.validate((valid) => {
        if (valid) {
          const classroom_ids = this.rowData.id;
          const { date } = this.finishedform;
          classManagementApi
            .GetSchoolServiceClassroomFinished({
              classroom_ids: [classroom_ids],
              finished_time: date
            })
            .then((res) => {
              if (!res.err) {
                this.$message.success("提交成功!");
                this.dialogFinished = false;
                this.clearSelection();
                this.searchStaff();
                this.showPushDialog();
              }
            });
        }
      });
    },

    // getSchoolServiceClassroomMapCategory() {
    //   classManagementApi.GetSchoolServiceClassroomMapCategory().then((res) => {
    //     for (let key in res.data) {
    //       this.searchTitle[2].selectOptions.push({
    //         id: key,
    //         name: res.data[key],
    //       });
    //     }
    //   });
    // },
    getSchoolServiceClassroomMapType() {
      classManagementApi.GetSchoolServiceClassroomMapType().then((res) => {
        for (const key in res.data) {
          this.searchTitle[2].selectOptions.push({
            id: key,
            name: res.data[key]
          });
        }
      });
    },
    getSchoolServiceClassroomMapStatus() {
      classManagementApi.GetSchoolServiceClassroomMapStatus().then((res) => {
        // console.log(res.data);
        for (const key in res.data) {
          this.searchTitle[3].selectOptions.push({
            id: key,
            name: res.data[key]
          });
        }
        this.searchTitle[3].selectOptions.unshift({ name: "不限", id: "" });
      });
    },
    getSchoolServiceClassroomMapScheduling() {
      classManagementApi
        .GetSchoolServiceClassroomMapScheduling()
        .then((res) => {
          for (const key in res.data) {
            this.searchTitle[9].selectOptions.push({
              id: key,
              name: res.data[key]
            });
          }
        });
    },
    initList() {
      console.log(this.search);
      this.list.results = [];
      this.loading = true;
      const search = this.changeSearch(this.search);
      this.$store.dispatch("getSchoolServiceClassroomListNew", {
        ...search,
        page: this.page,
        page_size: this.page_size
      });
    }
  },
  mounted() {
    // this.getSchoolServiceClassroomMapCategory();
    this.getSchoolServiceClassroomMapType();
    this.getSchoolServiceClassroomMapStatus();
    this.getSchoolServiceClassroomMapScheduling();
  },
  created() {
    this.getCourseConfig();
    this.initList();

    // this.getStudentTotal({ department_id: this.schoolIds });
    this.$bus.on("closeTransferDialog", this.closeTransferDialog);
    this.$bus.on("reloadClassManagementTable", this.searchStaff);
  },
  beforeDestroy() {
    this.$bus.off("closeTransferDialog", this.closeTransferDialog);
    this.$bus.off("reloadClassManagementTable", this.searchStaff);
  }
};
</script>
<style lang="less" scoped>
#classManagement {
  height: 100vh;
  ::v-deep .el-table td:last-child {
    border-right-color: transparent !important;
  }
  ::v-deep .el-table__header th:last-child {
    border-right-color: transparent !important;
  }
  .search {
    background: #ffffff;
    box-shadow: 0 2px 6px 0 #ccd0d9;
    border-radius: 4px;
    height: 112px;
    margin: 0 6px;
  }
  .demo-table-expand {
    font-size: 0;
  }
  .demo-table-expand label {
    width: 90px;
    color: #99a9bf;
  }
  .demo-table-expand .el-form-item {
    margin-right: 0;
    margin-bottom: 0;
    width: 50%;
  }
  .outbound_two {
    height: 630px;
  }
  .tg-button__icon--normal {
    width: 10px;
    height: 11px;
    margin-right: 9px;
  }
  .el-tabs__nav-wrap::after {
    background-color: #ffffff !important;
  }
  .el-tabs__nav-scroll {
    background: #f5f8fc !important;
  }
  .el-table__expanded-cell[class*="cell"] {
    border: 1px solid #2d80ed;
  }
  .search .el-form-item__content {
    line-height: 40px;
  }
  .el-dialog__footer {
    margin-top: -45px;
    /* padding: 11px 24px; */
  }
  .el-textarea__inner {
    height: 195px !important;
  }
}
::v-deep .el-table__body-wrapper {
  height: auto;
}
::v-deep .el-table {
  height: auto;
}
::v-deep .modify-teacher-dialog {
  .el-form-item__content,
  .el-form-item__label {
    line-height: 32px;
  }
}
/deep/ .modify-teacher {
  .tg_select {
    width: 300px;
  }
}
.tip-box {
  height: 68px;
  padding: 10px;
  background-color: #f5f8fc;
  border: 1px solid @base-color;
  border-radius: 4px;
  .text {
    display: flex;
    align-items: center;
    img {
      width: 16px;
      margin-right: 10px;
    }
  }
  .txt {
    margin-left: 26px;
    color: #fd6865;
    margin-top: 10px;
  }
}
.dialog-finished {
  /deep/ .el-dialog__body {
    padding: 16px;
  }
}
.dialog-finished-form {
  /deep/ .el-form-item__content,
  /deep/ .el-form-item__label {
    line-height: 32px;
  }
  ::v-deep .el-date-editor.el-date-editor--date:focus-within:after {
    border: 0px !important;
  }
}
::v-deep .el-table__body-wrapper {
  height: calc(100% - 46px);
  .el-table__empty-block {
    display: flex;
    align-items: normal;
    justify-content: normal;
  }
  .empty-container {
    position: absolute;
    top: 50%;
    left: 50%;
  }
  .loading-container {
    position: absolute;
    top: 30%;
    left: 1%;
    background: transparent;
    .box {
      height: 100%;
    }
  }
}
</style>
