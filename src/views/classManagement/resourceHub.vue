<template>
  <el-dialog
    title="资料库"
    :visible="true"
    width="1016px"
    class="custom_edit_dialogs"
    append-to-body
    :before-close="back"
  >
    <div style="position: relative">
      <div class="student-tab">
        <div>
          <span
            v-for="(item, i) in tabList"
            :key="i"
            :class="{ 'student-tab--active': curr_tab === item.value }"
            @click="changeTab(item.value)"
            >{{ item.name }}</span
          >
        </div>
        <div>
          <!-- 搜索 -->
          <el-input
            v-model="search_value"
            placeholder="请输入名称关键字"
            style="width: 200px; margin-left: 20px"
            clearable
            size="small"
            @clear="search_value = ''"
            @change="search"
          ></el-input>
          <el-button
            type="primary"
            size="small"
            icon="el-icon-search"
            style="margin-left: 20px"
            @click="search"
            >搜索</el-button
          >
        </div>
      </div>
      <div v-loading="is_loading" class="student-tab-content">
        <div v-for="(item, i) in list" :key="i" class="card-box">
          <el-card :body-style="{ padding: '0px' }">
            <div v-if="curr_tab === 1" class="image">
              <el-image
                :src="item.content"
                fit="contain"
                :lazy="true"
                :preview-src-list="[item.content]"
              >
              </el-image>
            </div>
            <div v-else-if="curr_tab === 2" class="video">
              <video
                :src="item.content"
                :poster="item.poster"
                controls
                controlsList="nodownload"
                style="width: 100%; height: 200px"
              ></video>
            </div>
            <div v-else-if="curr_tab === 3" class="words">
              {{ item.content }}
            </div>
            <div style="padding: 14px 14px 0 14px">
              <el-tooltip
                :content="item.name"
                placement="bottom"
                effect="light"
              >
                <div class="tg_ellipsis title">{{ item.name }}</div>
              </el-tooltip>

              <div @click="choose(item)" class="ctrl-bottom">
                <!-- <el-button class="tg-text--blue" type="text">查看</el-button> -->
                <el-button class="tg-text--blue" type="text">选择</el-button>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </div>
    <!-- 分页 -->
    <div class="tg-pagination">
      <span class="el-pagination__total">共 {{ total }} 条</span>
      <el-pagination
        background
        layout="sizes,prev,pager,next,jumper"
        :total="total"
        :page-size="pageSize"
        :current-page="page"
        @current-change="currentChange"
        @size-change="sizeChange"
        :page-sizes="[10, 20, 50, 100]"
      >
      </el-pagination>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button class="tg-button--plain" type="plain" @click="back"
        >关闭</el-button
      >
      <!-- <el-button class="tg-button--primary" type="primary" @click="confirm"
        >确定</el-button
      > -->
    </span>
  </el-dialog>
</template>

<script>
import appletResource from "@/api/appletResource";
export default {
  name: "resourceHub",
  props: {
    department_id: {
      type: String,
      default: "",
      required: true
    }
  },
  data() {
    return {
      currentDate: new Date(),
      curr_tab: 1,
      search_value: "",
      pageSize: 10,
      page: 1,
      total: 0,
      is_loading: false,
      list: [],
      tabList: [
        {
          name: "图片库",
          value: 1
        },
        {
          name: "视频库",
          value: 2
        },
        {
          name: "话术库",
          value: 3
        }
      ]
    };
  },
  mounted() {
    this.getList();
  },
  computed: {},
  methods: {
    confirm() {
      this.$emit("confirm");
    },
    back() {
      this.$emit("close");
    },
    choose(item) {
      const { content, poster } = item;
      this.$emit("choose", content, this.curr_tab, poster);
      this.back();
    },
    changeTab(val) {
      this.curr_tab = val;
      this.page = 1;
      this.search_value = "";
      this.getList();
    },
    getList() {
      this.is_loading = true;
      this.list = [];
      appletResource
        .getFileList({
          page: this.page,
          page_size: this.pageSize,
          category_id: this.curr_tab,
          department_id: this.department_id,
          name: this.search_value
        })
        .then((res) => {
          console.log("res :>> ", res);
          this.is_loading = false;
          if (res.data.code === 0) {
            this.list = res.data?.data?.results || [];
            this.total = res.data?.data?.count || 0;
          } else {
            this.$message.error(res.data.message);
          }
        })
        .catch((err) => {
          this.is_loading = false;
          console.error(err);
          this.$message.error("获取列表失败!");
        });
    },
    search() {
      this.page = 1;
      this.getList();
    },

    currentChange(val) {
      this.page = val;
      this.is_loading = true;
      this.getList();
    },
    sizeChange(val) {
      this.pageSize = val;
      this.is_loading = true;
      this.getList();
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .el-dialog__body {
  padding: 16px;
  background-color: #fafafa;
}
.student-tab {
  position: sticky;
  top: 0;
  left: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 46px;
  background: #fff;
  border-radius: 4px;
  padding: 0 16px;
  box-shadow: 0 2px 6px 0 rgba(204, 208, 217, 0.35);
  width: 100%;
  box-sizing: border-box;
  z-index: 2;
  span {
    font-family: "PingFangSC-Semibold, sans-serif,Arial";
    color: #475669;
    font-size: 14px;
    display: inline-block;
    height: 44px;
    border-bottom: 2px solid transparent;
    line-height: 44px;
    cursor: pointer;
    font-weight: bold;
  }
  span + span {
    margin-left: 32px;
  }
  .student-tab--active {
    color: #2d80ed;
    border-color: #2d80ed;
  }
}
.student-tab-content {
  padding: 16px 0;
  display: grid;
  gap: 20px 20px;
  grid-template-columns: repeat(4, 230.5px);
  height: 600px;
  overflow-y: scroll;
  position: relative;
  z-index: 1;
  .card-box {
    // width: 25%;
    cursor: pointer;
    transition: all 0.3s;
    &:hover {
      //放大
      transform: scale(1.05);
    }
    .ctrl-bottom {
      margin-top: 13px;
      line-height: 12px;
      display: flex;
      justify-content: flex-end;
    }

    .image {
      width: 100%;
      height: 200px;
      background-color: #f1f1f1;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
    }
    .video {
      width: 100%;
      height: 200px;
      background-color: #000;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
    }
    .words {
      width: 100%;
      height: 200px;
      border-bottom: 1px solid #f1f1f1;
      padding: 10px;
      overflow: scroll;
      ::-webkit-scrollbar {
        display: none;
      }
    }
  }
}
</style>
