<!--选择学员-->
<template>
  <div>
    <el-dialog
      :visible="true"
      width="1016px"
      :before-close="handleClose"
      class="choose-student"
    >
      <div slot="title">
        <span>一键转班：</span>
        <span class="class-title">{{ rowData.alias_name }}</span>
      </div>
      <div class="tg-dialog__content">
        <div class="class-list">
          <div class="search tg-box--margin">
            <el-form :inline="true" @submit.native.prevent :model="form">
              <el-form-item>
                <el-input
                  placeholder="请输入学生姓名/学号/电话"
                  class="search__input"
                  v-model="form.student_name"
                >
                  <img
                    src="../../assets/图片/icon_search_grey.png"
                    slot="prefix"
                  />
                </el-input>
              </el-form-item>
              <el-form-item class="tg-btn--margin">
                <el-button
                  type="primary"
                  class="tg-button--primary tg-button__icon"
                  @click="searchVal"
                >
                  <img
                    src="../../assets/图片/icon_search.png"
                    alt=""
                    class="tg-button__icon--normal"
                  />查询
                </el-button>
                <el-button
                  type="primary"
                  class="tg-button--primary tg-button__icon"
                  @click="reset"
                >
                  <img
                    src="../../assets/图片/icon_reset.png"
                    alt=""
                    class="tg-button__icon--normal"
                  />重置
                </el-button>
              </el-form-item>
            </el-form>
          </div>
          <div class="tg-table__box">
            <div class="tg-box--border"></div>
            <el-table
              ref="table"
              :data="list"
              tooltip-effect="dark"
              class="tg-table"
              :height="flag ? 353 : 449"
              style="width: 100%"
              @selection-change="handleSelectionChange"
              :row-key="getRowKeys"
            >
              <el-table-column
                type="selection"
                width="50"
                :reserve-selection="true"
              ></el-table-column>
              <el-table-column
                label="学员姓名"
                width="150"
                prop="student_name"
              ></el-table-column>
              <el-table-column
                label="学号"
                prop="student_number"
                width="130"
              ></el-table-column>
              <el-table-column label="购买数量" width="110" prop="course_hours">
              </el-table-column>
              <el-table-column label="课消数量" width="110" prop="course_hours">
              </el-table-column>
              <el-table-column
                label="剩余课(课时)"
                width="120"
                prop="student_cash"
              >
              </el-table-column>
            </el-table>
            <div class="tg-pagination">
              <span class="el-pagination__total">共 {{ total }} 条</span>
              <el-pagination
                background
                layout="prev, pager, next,jumper"
                :total="total"
                :page-size="page_size"
                :current-page="page"
                @current-change="currentChange"
              >
              </el-pagination>
            </div>
          </div>
        </div>
        <div class="class-list--right">
          <div class="organization__title">
            <span
              >已选 学员<em>{{ right_stu_list.length }}</em
              >个</span
            >
            <span class="all-clear" @click="clear">
              <img src="../../assets/图片/icon_clear.png" alt="" />
              清空
            </span>
          </div>
          <div
            class="organization__info"
            v-for="(item, index) in right_stu_list"
            :key="index"
          >
            <span>{{ item.student_name }}-{{ item.student_number }}</span>
            <img
              src="../../assets/图片/icon_close_green.png"
              alt=""
              @click="delOne(index, item)"
            />
          </div>
          <span v-if="right_stu_list.length === 0" class="is-empty"
            >暂无数据</span
          >
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <template v-if="isNetworkSchool">
          <el-button
            v-has="{ m: 'userPool', o: 'shiftAdjustStudent' }"
            class="tg-button--primary"
            type="primary"
            @click="applyChangeShift"
            >进入调班池</el-button
          >
        </template>
        <el-button class="tg-button--plain" type="plain" @click="back"
          >取消</el-button
        >
        <el-button class="tg-button--primary" type="primary" @click="really"
          >确定</el-button
        >
      </span>
    </el-dialog>
    <choose-class
      check_id=""
      check_name=""
      type="转班"
      :choose_class_visible="showChooseClass"
      @close="closeChooseClassDio"
      v-if="showChooseClass"
    ></choose-class>
  </div>
</template>
<script>
import changeShiftPool from "@/api/changeShiftPool";
import timeFormat from "@/public/timeFormat";
import classManagementApi from "@/api/classManagement";
import chooseClass from "./chooseClass";
// import schoolTree from "@/components/schoolTree/schoolTree";
export default {
  name: "transferClasses",
  data() {
    return {
      right_stu_list: [],
      flag: false,
      page: 1,
      page_size: 10,
      total: 0,
      list: [],
      form: {
        student_name: "",
        classroom_id: ""
      },
      showChooseClass: false
    };
  },
  props: {
    rowData: Object,
    testData: [String, Array],
    notFind: {
      type: Boolean,
      default: false
    }
  },
  components: {
    // schoolTree
    chooseClass
  },
  mounted() {
    if (!this.notFind) {
      this.form.classroom_id = this.rowData.id;
      this.getList({
        page: 1,
        page_size: 10,
        ...this.form
      });
    } else {
      this.getTemplateData();
    }
  },
  computed: {
    isNetworkSchool() {
      return ["网校测试校区C", "聂卫平围棋网校-新"].includes(
        this.list[0]?.department_name
      );
    }
  },
  methods: {
    getTime(time) {
      return timeFormat.GetTime(time);
    },
    handleClose() {
      this.clear();
      this.back();
    },
    delOne(index, item) {
      console.log(index, item);
      this.$nextTick(() => {
        this.$refs.table.toggleRowSelection(item);
      });
      this.right_stu_list.splice(index, 1);
    },
    clear() {
      this.right_stu_list = [];
      this.$refs.table.clearSelection();
    },
    async applyChangeShift() {
      if (!this.right_stu_list.length) {
        this.$message.error("请选择学员！");
        return;
      }
      const params = {
        student_ids: this.right_stu_list.map((i) => i.student_id),
        remove_classroom_id: this.right_stu_list[0].classroom_id,
        shift_type: "shift",
        operate_id: this.operate_id
      };
      const { code, message } = await changeShiftPool.shiftAdjustStudent(
        params
      );
      if (code === 0) {
        this.back();
        this.$message.success("成功进入调班池！");
      } else {
        this.$message.error(message);
      }
    },
    back() {
      this.$emit("closeTransferDialog");
    },
    really() {
      if (!this.right_stu_list.length) {
        this.$message.info("请先添加学员!");
        return;
      }
      this.showChooseClassDio();
    },

    currentChange(val) {
      this.page = val;
      if (!this.notFind) {
        this.getList({
          page: val,
          page_size: this.page_size,
          ...this.form
        });
      }
    },
    getRowKeys(row) {
      return row.id;
    },
    handleSelectionChange(val) {
      this.right_stu_list = val;
    },
    reset() {
      this.page = 1;
      if (!this.notFind) {
        this.form = {
          student_name: "",
          classroom_id: this.rowData.id
        };
        this.searchVal();
      } else {
        this.getTemplateData();
      }
    },
    searchVal() {
      if (!this.notFind) {
        this.page = 1;
        this.getList({ page: 1, ...this.form });
      } else {
        if (this.form.student_name) {
          this.list = this.list.filter((item) => {
            return item.student_name.indexOf(this.form.student_name) !== -1;
          });
        } else {
          this.getTemplateData();
        }
      }
    },
    getTemplateData() {
      this.list = JSON.parse(JSON.stringify(this.testData));
      this.total = this.list.length;
    },
    getList(d) {
      d.status = ["in_classroom", "wait_in_classroom"];
      d.match_exists = 2;
      classManagementApi
        .GetSchoolServiceClassroomStudentList(d)
        .then((res) => {
          this.list = res.data.results == null ? [] : res.data.results;
          this.is_loading = false;
          this.total = res.data.count;
        })
        .catch(() => {
          this.is_loading = false;
        });
    },
    showChooseClassDio() {
      this.showChooseClass = true;
    },
    closeChooseClassDio() {
      this.showChooseClass = false;
      this.clear();
      this.back();
    }
  }
};
</script>
<style lang="less" scoped>
.choose-student {
  .search {
    width: 100%;
    img {
      width: 14px;
      height: 14px;
      margin-right: 12px;
      margin-left: 11px;
      margin-top: -4px;
      vertical-align: middle;
    }
    img.search__img {
      width: 8px;
      height: 12px;
      margin-right: 10px;
      cursor: pointer;
    }
    ::v-deep .el-form-item__content,
    ::v-deep .el-form-item__label {
      line-height: 32px;
    }
    .search__input {
      width: 278px;
      ::v-deep .el-input__inner {
        padding-left: 40px;
      }
    }
    .el-button {
      width: 72px;
      img {
        margin-left: 0;
        margin-right: 7px;
      }
    }
    // .search_teacher {
    //   ::v-deep .el-input {
    //     width: 170px;
    //     .el-input__inner {
    //       padding-left: 16px;
    //     }
    //   }
    // }
    ::v-deep .el-form-item {
      margin-right: 10px;
    }
    ::v-deep .el-form-item:nth-child(2) {
      margin-right: 0;
    }
    ::v-deep .el-form-item.tg-btn--margin {
      margin-right: 10px;
    }
    ::v-deep .bottom_select {
      margin-right: 20px;
      .el-input {
        width: 296px;
      }
    }
  }
  ::v-deep .el-dialog__header {
    .class-title {
      color: @base-color;
    }
  }

  ::v-deep .el-dialog__body {
    padding: 0 16px 0 16px;
  }
  .tg-dialog__content {
    display: flex;
    flex-direction: row;
    height: 589px;
  }
  .class-list {
    width: calc(100% - 257px);
    border-right: 1px solid #e0e6ed;
    .tg-table__box {
      margin-left: 0;
      margin-right: 16px;
    }
    ::v-deep .el-table {
      padding: 0;
      th {
        background: #f5f8fc;
      }
      .el-table__header {
        padding: 0 16px;
        background: #f5f8fc;
      }
      .el-table__body {
        padding: 0 16px;
        box-sizing: border-box;
      }
    }
  }
  .class-list--right {
    width: 257px;
    margin-left: 16px;
    margin-top: 16px;
    overflow-y: scroll;
    padding-bottom: 10px;
    .organization__title,
    .organization__info {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }
    .all-clear {
      // color: #157df0;
      font-size: @text-size_small;
      font-family: @text-famliy_medium;
      cursor: pointer;
      img {
        width: 14px;
        height: 14px;
        margin-right: 8px;
        vertical-align: middle;
        margin-top: -3px;
      }
    }
    .organization__title {
      em {
        font-style: normal;
        color: @base-color;
      }
    }
    .organization__info {
      border: 1px solid @base-color;
      border-radius: 4px;
      height: 40px;
      align-items: center;
      padding: 0 16px;
      margin-top: 16px;
      ::v-deep .el-input,
      ::v-deep .el-input__inner {
        height: 40px;
        line-height: 40px;
      }
      img {
        width: 16px;
        height: 16px;
        cursor: pointer;
      }
      span:nth-child(1) {
        overflow-x: scroll;
        width: calc(100% - 36px);
        white-space: nowrap;
      }
    }
    .required {
      &::before {
        content: "*";
        margin-right: 5px;
        color: #ff0317;
      }
    }
  }
  .is-empty {
    color: @text-color_third;
    width: 100%;
    display: inline-block;
    text-align: center;
    line-height: 350px;
  }
  ::v-deep .stu-school-tree {
    color: #000;
    .el-dialog {
      .el-form {
        padding-top: 20px;
        .el-input {
          width: 210px;
        }
        button {
          margin-left: 30px;
        }
      }
    }
  }
}
</style>
