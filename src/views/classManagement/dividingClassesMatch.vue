<template>
  <div class="p-6 container">
    <div class="title tg-box--width" @click="$router.go(-1)">
      <img src="../../assets/图片/icon_menu_down_ac.png" alt="" />
      <span>返回</span>
    </div>
    <div class="classTip tg-box--width">
      入班-<span>{{ classroom_name }}</span>
    </div>
    <div class="content">
      <div class="pannel-box border-bottom">
        <div class="left-box">
          <div class="pannel-add-box">
            <div class="addStudentTip">
              <h5>添加的学员</h5>
              <div>
                <el-button
                  size="small"
                  type="primary"
                  @click="dialogStudent = true"
                  >更多学员</el-button
                >
                <el-button size="small" type="primary" @click="batchRemove"
                  >批量移除</el-button
                >
              </div>
            </div>
            <div class="tg-table__box tg-box--margin">
              <div class="tg-box--border"></div>
              <el-table
                ref="tempTable"
                :data="tempTable.currPageData"
                style="width: 100%"
              >
                <el-table-column type="selection" width="55"></el-table-column>
                <el-table-column
                  prop="student_base.student_name"
                  label="姓名"
                  width="width"
                >
                </el-table-column>
                <el-table-column
                  prop="ylb_status"
                  label="元萝卜用户"
                  width="120"
                >
                  <template slot-scope="scope">
                    <ylb-tag :ylb_status="scope.row.ylb_status"></ylb-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="student_base.student_mobile"
                  label="手机号"
                  width="130"
                >
                  <template slot-scope="scope">
                    <mobileHyposensitization
                      :mobileTemInfo="{
                        row: scope.row,
                        has_eye_limit: scope.row?.has_eye_limit,
                        mobile: scope.row?.student_base.student_mobile
                      }"
                    ></mobileHyposensitization>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="student_base.student_type"
                  label="学员类别"
                  width="100px"
                >
                  <template slot-scope="scope">
                    {{ scope.row?.student_category_name?.join("、") }}
                  </template>
                </el-table-column>
                <el-table-column
                  prop="nie_dao_level"
                  label="聂道棋力"
                  width="100px"
                >
                </el-table-column>
                <el-table-column
                  prop="school_manager_name"
                  label="学管师"
                  width="100px"
                >
                  <template slot-scope="scope">
                    {{ scope.row?.school_manager_name?.join("、") }}
                  </template>
                </el-table-column>
                <el-table-column prop="" label="教务" width="100px">
                  <template slot-scope="scope">
                    {{ scope.row?.student_base?.education_name?.join("、") }}
                  </template>
                </el-table-column>
                <el-table-column
                  prop="student_base.app_bind_type"
                  label="APP绑定"
                  width="100px"
                  sortable
                >
                </el-table-column>
                <el-table-column
                  prop="student_base.student_number"
                  label="学号"
                  width="width"
                  sortable
                >
                </el-table-column>
                <el-table-column
                  prop="student_gender"
                  label="性别"
                  width="width"
                >
                  <template slot-scope="scope">
                    <div>
                      {{ scope.row.student_base.gender }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="student_cash"
                  label="剩余课(课时)"
                  width="120px"
                >
                </el-table-column>
                <el-table-column label="入班日期" width="120px">
                  <template slot-scope="scope">
                    <div>
                      {{ moment(scope.row.enter_time).format("YYYY-MM-DD") }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="出班日期" width="120px">
                  <template slot-scope="scope">
                    <div>
                      {{
                        moment(scope.row.out_time).format("YYYY-MM-DD")
                          | formatInitDate
                      }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column fixed="right" label="操作" width="75px">
                  <template slot-scope="scope">
                    <el-button
                      @click="removeTempData(scope.row)"
                      class="tg-text--blue"
                      type="text"
                      >移除</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
              <!-- 分页 -->
              <div class="tg-pagination">
                <span class="el-pagination__total"
                  >共 {{ tempTable.total }} 条</span
                >
                <el-pagination
                  background
                  layout="prev, pager, next,jumper"
                  :total="tempTable.total"
                  :page-size="tempTable.page_size"
                  :current-page="tempTable.page"
                  @current-change="temp_currentChange"
                >
                </el-pagination>
              </div>
            </div>
          </div>
          <div class="really-box p-top-16">
            <h5 class="p-bottom-16">在该班级就读的学员</h5>
            <el-form ref="form" :model="form1" label-width="80px">
              <div class="form-left">
                <el-form-item
                  class="inline-form-item"
                  label-width="0px"
                  label=""
                >
                  <el-input
                    style="margin-right: 26px"
                    prefix-icon="el-icon-search"
                    size="small"
                    placeholder="请输入学员姓名"
                    v-model="form1.student_name"
                    @keyup.native.enter="getStuList(form1)"
                  ></el-input>
                </el-form-item>
                <el-form-item class="inline-form-item" label="入班日期">
                  <el-date-picker
                    style="width: 250px"
                    v-model="form1.date"
                    type="daterange"
                    range-separator="至"
                    format="yyyy-MM-dd"
                    size="small"
                    value-format="yyyy-MM-dd"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    @change="getStuList(form1)"
                    :pickerOptions="pickerOptions"
                    popper-class="tg-date-picker tg-date--range"
                  >
                  </el-date-picker>
                </el-form-item>
                <el-form-item
                  class="inline-form-item"
                  label-width="20px"
                  label=""
                >
                  <el-button
                    type="primary"
                    class="tg-button--primary tg-button__icon"
                    @click="getStuList(form1)"
                  >
                    <img
                      src="../../assets/图片/icon_search.png"
                      alt
                      class="tg-button__icon--large"
                    />查询
                  </el-button>
                </el-form-item>
              </div>
            </el-form>
            <div class="tg-table__box tg-box--margin">
              <div class="tg-box--border"></div>
              <el-table
                v-loading="form1.loading"
                :data="stu_data"
                style="width: 100%"
              >
                <el-table-column fixed="left" prop="student_name" label="姓名">
                  <template slot-scope="scope">
                    <span
                      @click="getStuDetail(scope.row)"
                      style="color: #2d80ed; cursor: pointer"
                    >
                      {{ scope.row.student_name }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="ylb_status"
                  label="元萝卜用户"
                  width="120"
                >
                  <template slot-scope="scope">
                    <ylb-tag :ylb_status="scope.row.ylb_status"></ylb-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="student_mobile"
                  label="手机号"
                  width="130"
                >
                  <template slot-scope="scope">
                    <mobileHyposensitization
                      :mobileTemInfo="{
                        row: scope.row,
                        has_eye_limit: scope.row?.has_eye_limit,
                        mobile: scope.row.student_mobile
                      }"
                    ></mobileHyposensitization>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="app_bind_type"
                  label="APP绑定"
                  width="100px"
                  sortable
                >
                </el-table-column>
                <el-table-column
                  prop="student_number"
                  label="学号"
                  sortable
                  width="width"
                >
                </el-table-column>
                <el-table-column
                  prop="student_gender"
                  label="性别"
                  width="width"
                >
                  <template slot-scope="scope">
                    <div>
                      {{ scope.row.student_gender | formatGender }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="student_cash"
                  label="剩余课(课时)"
                  width="120px"
                >
                </el-table-column>
                <el-table-column label="入班日期" width="120px">
                  <template slot-scope="scope">
                    <div>
                      {{ moment(scope.row.enter_time).format("YYYY-MM-DD") }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="出班日期" width="120px">
                  <template slot-scope="scope">
                    <div>
                      {{
                        moment(scope.row.out_time).format("YYYY-MM-DD")
                          | formatInitDate
                      }}
                    </div>
                  </template>
                </el-table-column>
                <!-- <el-table-column fixed="right" label="操作" width="60px">
                <template>
                  <el-button class="tg-text--blue" type="text">移除</el-button>
                </template>
              </el-table-column> -->
              </el-table>
              <!-- 分页 -->
              <div class="tg-pagination">
                <span class="el-pagination__total"
                  >共 {{ form1.total }} 条</span
                >
                <el-pagination
                  background
                  layout="prev, pager, next,jumper"
                  :total="form1.total"
                  :page-size="form1.page_size"
                  :current-page="form1.page"
                  @current-change="f1_currentChange"
                >
                </el-pagination>
              </div>
            </div>
          </div>
        </div>
        <div class="right-box">
          <div class="p-16 container">
            <div class="r-title tg-box--width">
              <h5>报读该课程的学员</h5>
              <div class="checked-group">
                <el-checkbox
                  v-model="state"
                  true-label="true"
                  false-label="false"
                  @change="checkBoxChange('true')"
                  >待分班</el-checkbox
                >
                <el-checkbox
                  v-model="state"
                  true-label="false"
                  false-label="true"
                  @change="checkBoxChange('false')"
                  >已分班</el-checkbox
                >
              </div>
            </div>
            <el-form
              ref="form2"
              :model="form2"
              @submit.native.prevent
              inline
              class="m-bottom-16 tg-box--width"
              label-width="100px"
            >
              <el-form-item class="inline-form-item" label="学员姓名">
                <el-input
                  style="width: 168px"
                  prefix-icon="el-icon-search"
                  placeholder="请输入学员姓名"
                  v-model="form2.name"
                ></el-input>
              </el-form-item>
              <el-form-item class="inline-form-item" label="学员类别">
                <el-select
                  v-model="form2.student_type"
                  placeholder="请选择学员类别"
                >
                  <el-option
                    v-for="item in student_type_list"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item class="inline-form-item" label="聂道棋力">
                <el-select
                  v-model="form2.nie_dao_level"
                  placeholder="请选择聂道棋力"
                >
                  <el-option
                    v-for="item in nie_dao_level_list"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>

              <el-form-item class="inline-form-item" label="学管师">
                <course-staff
                  :has_modal="false"
                  :check_id.sync="form2.school_manager_id"
                  :check_name.sync="form2.school_manager_name"
                  :staff_placeholder="'请选择学管师'"
                  :obj="form2.school_manager_map"
                ></course-staff>
              </el-form-item>
              <el-form-item class="inline-form-item" label="教务">
                <course-staff
                  :has_modal="false"
                  :check_id.sync="form2.education_ids"
                  :check_name.sync="form2.education_name"
                  :staff_placeholder="'请选择教务'"
                ></course-staff>
              </el-form-item>

              <el-form-item class="inline-form-item">
                <el-button
                  type="primary"
                  class="tg-button--primary tg-button__icon m-left-20"
                  @click="getCourseStuList(form2)"
                >
                  <img
                    src="@/assets/图片/icon_search.png"
                    alt
                    class="tg-button__icon--large"
                  />查询
                </el-button>
                <el-button
                  type="primary"
                  class="tg-button--primary tg-button__icon m-left-20"
                  @click="resetForm2"
                >
                  <img
                    src="@/assets/图片/icon_reset.png"
                    alt
                    class="tg-button__icon--large"
                  />重置
                </el-button>
              </el-form-item>
            </el-form>
            <div class="m-bottom-16 tg-box--width">
              <el-button
                type="primary"
                size="small"
                class="tg-button--primary"
                @click="batchAdd"
              >
                批量添加
              </el-button>
            </div>
            <div class="tg-table__box tg-box--margin">
              <div class="tg-box--border"></div>
              <el-table
                ref="courseTable"
                style="width: 100%"
                v-loading="form2.loading"
                :data="stu_course_data"
                class="course-table tg-table"
              >
                <el-table-column
                  :selectable="courseTableSelectable"
                  type="selection"
                  width="55"
                ></el-table-column>
                <el-table-column
                  prop="student_base.student_name"
                  label="学员姓名"
                  width="100px"
                >
                </el-table-column>

                <el-table-column
                  prop="student_base.student_number"
                  label="学号"
                  width="100px"
                >
                </el-table-column>
                <el-table-column
                  prop="student_base.student_mobile"
                  label="手机号"
                  width="130"
                >
                  <template slot-scope="scope">
                    <mobileHyposensitization
                      :mobileTemInfo="{
                        row: scope.row,
                        has_eye_limit: scope.row?.has_eye_limit,
                        mobile: scope.row.student_base?.student_mobile
                      }"
                    ></mobileHyposensitization>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="student_base.student_type"
                  label="学员类别"
                  width="100px"
                >
                  <template slot-scope="scope">
                    {{ scope.row?.student_category_name?.join("、") }}
                  </template>
                </el-table-column>
                <el-table-column
                  prop="nie_dao_level"
                  label="聂道棋力"
                  width="100px"
                >
                </el-table-column>
                <el-table-column
                  prop="school_manager_name"
                  label="学管师"
                  width="100px"
                >
                  <template slot-scope="scope">
                    {{ scope.row?.school_manager_name?.join("、") }}
                  </template>
                </el-table-column>
                <el-table-column prop="" label="教务" width="100px">
                  <template slot-scope="scope">
                    {{ scope.row?.student_base?.education_name?.join("、") }}
                  </template>
                </el-table-column>
                <el-table-column fixed="right" label="操作" width="90">
                  <template slot-scope="scope">
                    <el-button
                      v-if="scope.row.added"
                      class="tg_text_gray"
                      type="text"
                      >已添加</el-button
                    >
                    <el-button
                      v-else
                      @click="addToTemp([scope.row])"
                      class="tg-text--blue"
                      type="text"
                      >添加</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
              <!-- 分页 -->
              <div class="tg-pagination">
                <span class="el-pagination__total"
                  >共 {{ form2.total }} 条</span
                >
                <el-pagination
                  background
                  layout="prev, pager, next,jumper"
                  :total="form2.total"
                  :page-size="form2.page_size"
                  :current-page="form2.page"
                  @current-change="f2_currentChange"
                >
                </el-pagination>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="footer">
        <el-button class="tg-button--plain" type="plain" @click="back"
          >取消</el-button
        >
        <!-- <el-button
          class="tg-button--primary"
          :loading="loading"
          type="primary"
          @click="submitData"
          >{{ loading ? "提交中" : "确定" }}</el-button
        > -->
        <el-button
          class="tg-button--primary"
          type="primary"
          @click="chooseClass"
          >选择排课</el-button
        >
        <el-button
          class="tg-button--primary"
          type="primary"
          @click="createClass"
          >创建排课</el-button
        >
      </div>
      <choose-student
        @close="closeStudent"
        :department_id="department_id"
        :department_name="department_name"
        v-if="dialogStudent"
        :dividStu="true"
        :student_type="['in_school']"
      ></choose-student>
      <course-scheduling
        v-if="dialogScheduling"
        @close="dialogScheduling = false"
        @confirm="confirmScheduling"
        :classroom_id="form1.classroom_id"
        :match_index="match_index"
        :department_ids="department_id"
      ></course-scheduling>
      <course-scheduling-create
        v-if="dialogSchedulingCreate"
        @closeCreateDialog="dialogSchedulingCreate = false"
        @confirmScheduling="confirmSchedulingCreate"
        :classroom_id="form1.classroom_id"
        :classroom_name_match="classroom_name"
        :match_index="match_index"
      ></course-scheduling-create>
    </div>
  </div>
</template>
<script>
import classManagement from "@/api/classManagement";
import studentInforApi from "@/api/studentInfor";
import ChooseStudent from "./chooseStudent";
import { picker_options } from "@/public/datePickerOptions";
import categoryApi from "@/api/studentCategory";
import CourseScheduling from "@/views/classManagement/course_scheduling";
import CourseSchedulingCreate from "@/views/schoolServiceScheduling/create.vue";
import { nie_dao_level_list } from "@/public/dict";
export default {
  components: {
    ChooseStudent,
    CourseScheduling,
    CourseSchedulingCreate
  },
  name: "classManagementDividMatch",
  props: {
    // show:{
    //     required: true,
    //     type: Boolean,
    //     default: false,
    // },
    // rowData: {
    //   required: true,
    //   type: Object,
    //   default: () => {}
    // }
  },
  data() {
    return {
      loading: false,
      tempTable: {
        page: 1,
        page_size: 10,
        total: 0,
        data: [],
        currPageData: []
      },
      form1: {
        student_name: "",
        date: "",
        enter_begin_time: "",
        enter_over_time: "",
        page: 1,
        classroom_id: "",
        page_size: 10,
        total: 0,
        is_loading: false
      },
      form2: {
        name: "",
        page: 1,
        page_size: 10,
        total: 0,
        course_id: "",
        // already_checked: false,
        // waiting_checked: true,
        is_loading: false,
        student_type: "",
        nie_dao_level: "",
        school_manager_id: "",
        education_ids: ""
      },
      state: "true",
      stu_data: [],
      stu_course_data: [],
      // currShow:this.show
      dialogStudent: false,
      classroom_name: "",
      department_id: "",
      department_name: "",
      pickerReleaseDateOptions: {
        disabledDate: (time) => {
          const curDate = new Date().getTime();
          return time.getTime() > curDate;
        }
      },
      pickerOptions: picker_options,
      student_type_list: [],
      match_index: 0,
      dialogScheduling: false,
      dialogSchedulingCreate: false,
      scheduling_id: "",
      nie_dao_level_list,
      class_time: ""
    };
  },
  computed: {
    already() {
      return "";
    },
    waiting() {
      return "";
    }
  },
  created() {
    const {
      classroom_id,
      course_id,
      classroom_name,
      department_id,
      department_name,
      match_index
    } = this.$route.query;
    this.form1.classroom_id = classroom_id;
    this.form2.course_id = course_id;
    this.form2.match_index = +match_index;

    this.classroom_name = classroom_name;
    this.department_id = department_id;
    this.department_name = department_name;
    this.match_index = +match_index;
    this.getStuList(this.form1);
    this.getCourseStuList(this.form2);
    this.initCategoryList();
  },
  methods: {
    confirmSchedulingCreate() {
      this.dialogSchedulingCreate = false;
    },
    confirmScheduling(row) {
      console.log("row :>> ", row);
      this.scheduling_id = row.id;
      this.submitData();
    },
    chooseClass() {
      if (!this.class_time) {
        this.$message.info("该班级暂无上课时间，无法继续操作！");
        return;
      }
      this.dialogScheduling = true;
    },
    createClass() {
      this.dialogSchedulingCreate = true;
    },
    courseTableSelectable(row) {
      return !row.added;
    },
    initCategoryList() {
      categoryApi
        .getStudentCrtegoryList({
          status: 1
        })
        .then((res) => {
          if (res.data.code === 0) {
            this.student_type_list = res.data.data;
          }
        });
    },
    handleClose() {
      this.back();
    },
    back() {
      // this.currShow = false;
      // this.form = {};
      this.$router.go(-1);
      // this.$emit("closeDialogDividing", false);
    },
    getStuDetail(row) {
      const id = row.student_id;
      const customer_id = row?.student_base?.customer_id ?? "";
      const department_id = row.department_id;
      this.$router.push({
        name: `studentInforDetails`,
        query: { id, customer_id, department_id }
      });
    },
    resetTempTable() {
      this.tempTable = {
        page: 1,
        page_size: 5,
        total: 0,
        data: [],
        currPageData: []
      };
    },
    resetStuList() {
      const classroom_id = this.form1.classroom_id;
      this.form1 = {
        student_name: "",
        date: "",
        enter_begin_time: "",
        enter_over_time: "",
        page: 1,
        classroom_id,
        page_size: 10,
        total: 0,
        is_loading: false
      };
      this.getStuList(this.form1);
    },
    resetForm2() {
      this.form2.page = 1;
      this.form2.page_size = 10;
      this.form2.total = 0;
      this.form2.name = "";
      this.form2.student_type = "";
      this.form2.nie_dao_level = "";
      this.form2.school_manager_id = "";
      this.form2.school_manager_name = "";
      this.form2.education_ids = "";
      this.form2.education_name = "";

      this.getCourseStuList(this.form2);
    },
    getStuList(data) {
      this.form1.is_loading = true;
      if (!data.date) {
        data.enter_begin_time = "";
        data.enter_over_time = "";
      }
      data.status = ["in_classroom", "wait_in_classroom"];
      classManagement
        .GetSchoolServiceClassroomStudentList(data)
        .then((res) => {
          this.stu_data = res.data.results == null ? [] : res.data.results;
          this.form1.is_loading = false;
          this.form1.total = res.data.count;
        })
        .catch(() => {
          this.form1.is_loading = false;
        });
    },
    f1_currentChange(val) {
      this.form1.page = val;
      this.getStuList(this.form1);
    },
    f2_currentChange(val) {
      this.form2.page = val;
      this.getCourseStuList(this.form2);
    },
    temp_currentChange(val) {
      this.tempTable.page = val;
      this.getTempData();
    },
    closeStudent() {
      this.dialogStudent = false;
    },
    // 获取当前班级信息
    getClassInfo() {
      const { classroom_id } = this.form1;
      return classManagement.GetSchoolServiceClassroomInfo({
        id: classroom_id
      });
    },
    // 获取报读该课程的学员
    async getCourseStuList(d) {
      d.class_room_in = this.state !== "true";
      const promise = await this.getClassInfo();
      const { data, status } = promise;
      if (status === 200) {
        d.exclude_ids = data.student_ids; // 排除在班学员
        d.department_id = this.department_id;
        this.class_time = data.class_time;
        if (this.state === "true") {
          d.drop_school = true;
          d.out_school = true;
        }
        const params = {
          ...d,
          nie_dao_level: d.nie_dao_level ? d.nie_dao_level : undefined
        };
        this.form2.is_loading = true;
        studentInforApi
          .getNewStudentInforList(params)
          .then((res) => {
            console.log(res);
            this.stu_course_data =
              res.data.data.results == null ? [] : res.data.data.results;
            this.stu_course_data.map((item) => {
              this.$set(item, "added", false);
            });
            this.form2.is_loading = false;
            this.form2.total = res.data.data.count;
            this.addChecked();
          })
          .catch(() => {
            this.form2.is_loading = false;
          });
      }
    },
    checkBoxChange(val) {
      this.state = val;
      this.form2.page = 1;
      this.form2.name = "";
      this.getCourseStuList(this.form2);
    },
    // 临时区域每页的数据
    getTempData(_type) {
      let { page, page_size } = this.tempTable;
      // 移除时需要判断当前页面是不是最后一条数据，需要做页数减项
      if (_type === "remove" && this.tempTable.currPageData.length === 1) {
        page = page > 1 ? page - 1 : 1;
        this.tempTable.page = page;
      }
      const start = (page - 1) * page_size;
      const end = page * page_size;
      this.tempTable.currPageData = this.tempTable.data.slice(start, end);
    },
    // 添加数据到临时区域
    addToTemp(arr) {
      const { total } = this.tempTable;
      arr.forEach((el) => {
        this.tempTable.data.unshift(el);
      });
      this.tempTable.total = total + arr.length;
      this.getTempData("add");
    },
    // 批量添加
    batchAdd() {
      const selectedRows = this.$refs.courseTable.selection;
      // 空提示
      if (!selectedRows.length) {
        this.$message.info("请选择要添加的学员");
        return;
      }
      this.addToTemp(selectedRows);
      this.$refs.courseTable.clearSelection();
    },
    // 批量移除
    batchRemove() {
      const selectedRows = this.$refs.tempTable.selection;
      // 空提示
      if (!selectedRows.length) {
        this.$message.info("请选择要移除的学员");
        return;
      }
      this.tempTable.currPageData = this.tempTable.currPageData.filter(
        (item) => !selectedRows.includes(item)
      );
      selectedRows.forEach((item) => {
        this.removeTempData(item);
      });
    },
    // 移除某条临时数据
    removeTempData(row) {
      const { total } = this.tempTable;
      this.tempTable.data.map((item, index) => {
        if (item.student_id === row.student_id) {
          this.tempTable.data.splice(index, 1);
        }
      });
      this.tempTable.total = total - 1;
      this.getTempData("remove");
    },
    // 对比临时表的数据和课程学生表的数据，判断显示已添加、添加两种状态
    addChecked() {
      this.stu_course_data.map((sd) => {
        sd.added = false;
        this.tempTable.data.map((td) => {
          if (sd.student_id === td.student_id) {
            sd.added = true;
          }
        });
      });
    },
    submitData() {
      const ids = [];
      this.tempTable.data.map((item) => {
        ids.push(item.student_id);
      });
      if (!ids.length) {
        this.$message.info("添加学员不能为空!");
        return;
      }
      const { classroom_id } = this.form1;
      const enter_time = this.moment(new Date()).format("YYYY-MM-DD");
      const params = {
        classroom_id,
        enter_time,
        handler_type: "add",
        memo: "",
        student_ids: ids,
        scheduling_id: this.scheduling_id
      };
      this.loading = true;
      const loadingView = this.$loading({
        lock: true,
        text: "提交中",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)"
      });
      classManagement
        .GetSchoolServiceClassroomAddstudent(params)
        .then((res) => {
          if (!res.err) {
            if (res.data) {
              this.$confirm(`${res.data}已在班`, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
              })
                .then(() => {
                  this.dialogSchedulingCreate = false;
                  this.dialogScheduling = false;
                  loadingView.close();
                  this.backPage(false);
                })
                .catch(() => {
                  loadingView.close();
                  this.backPage(false);
                });
            } else {
              loadingView.close();
              this.backPage(true);
            }
          } else {
            this.loading = false;
            loadingView.close();
          }
        })
        .catch(() => {
          this.loading = false;
          loadingView.close();
        });
    },
    backPage(type) {
      if (type) {
        this.$message.success("提交成功!");
      }
      this.resetTempTable();
      this.resetStuList();
      this.$bus.emit("reloadClassManagementTable");
      this.back();
    }
  },
  watch: {
    "form1.date": {
      handler(newObj) {
        if (newObj && newObj.length > 1) {
          this.form1.enter_begin_time = this.moment(newObj[0]).format(
            "YYYY-MM-DD"
          );
          this.form1.enter_over_time = this.moment(newObj[1]).format(
            "YYYY-MM-DD"
          );
        } else {
          this.form1.enter_begin_time = "";
          this.form1.enter_over_time = "";
        }
      },
      immediate: false
    },
    "tempTable.data": {
      handler() {
        // this.getTempData();
        this.addChecked();
      },
      immediate: false
    }
  }
};
</script>
<style lang="less" scoped>
.container {
  height: auto;
  min-height: 100%;
}
.p-6 {
  padding-top: 16px;
  padding-bottom: 6px;
  box-sizing: border-box;
}
.p-16 {
  padding: 16px;
  box-sizing: border-box;
}
.p-top-16 {
  padding-top: 16px !important;
}
.p-bottom-16 {
  padding-bottom: 16px !important;
}
.m-left-20 {
  margin-left: 20px;
}
.m-bottom-16 {
  margin-bottom: 16px;
}
.border-bottom {
  border-bottom: 1px solid #e9f0f7;
}
.title {
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 48px;
  line-height: 48px;
  border: 1px solid @base-color;
  border-radius: 4px;
  padding-left: 16px;
  cursor: pointer;
  background-color: #fff;
  box-sizing: border-box;
  img {
    width: 10px;
    height: 6px;
    margin-right: 10px;
    transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    -o-transform: rotate(90deg);
    -webkit-transform: rotate(90deg);
  }
  span {
    color: @text-color_second;
    font-family: @text-famliy_medium;
    font-size: @text-size_normal;
  }
}
::v-deep.tg-button__icon > span {
  display: flex;
}
.classTip {
  margin: 16px 0 16px 0;
  color: @text-color_second;
  font-family: @text-famliy_medium;
  font-size: @text-size_normal;
  span {
    color: @base-color;
  }
}
.content {
  width: 100%;
  height: calc(100% - 20px);
  background: rgb(255, 255, 255);
  box-shadow: 0 2px 6px 0 rgb(204 208 217);
  // padding: 20px 0px 0px 20px;
  overflow-y: scroll;
  box-sizing: border-box;
  flex: 1;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  .inline-form-item {
    display: inline-block;
  }
  /deep/ .tg-table__box {
    margin: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  /deep/ .el-range-editor--small .el-range-separator {
    line-height: 32px;
  }
  .pannel-box {
    border-top: 1px solid #e9f0f7;
    padding: 0;
    display: flex;
    height: calc(100% - 64px);
    box-sizing: border-box;
    flex: auto;
    .left-box {
      width: 60%;
      // border-right: 1px solid #f1f1f1;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      .pannel-add-box {
        padding: 0px 16px 16px;
        box-sizing: border-box;
        border-bottom: 1px solid #e9f0f7;
        border-right: 1px solid #e9f0f7;
        flex: 50%;
        display: flex;
        flex-direction: column;
        .addStudentTip {
          width: 100%;
          height: 32px;
          display: inline-flex;
          align-items: center;
          justify-content: space-between;
          margin-top: 16px;
        }
      }
      .really-box {
        padding: 0px 16px 16px;
        box-sizing: border-box;
        flex: 50%;
        border-right: 1px solid #e9f0f7;
        display: flex;
        flex-direction: column;
        .el-form {
          display: flex;
          justify-content: space-between;
        }
        /deep/ .el-form-item__content {
          line-height: 32px;
        }
        /deep/ .el-form-item__label {
          line-height: 32px;
        }
      }
      h5 {
        font-size: @text-size_normal;
        color: @base-color;
        margin: 0;
        font-weight: 400;
      }
      .tg-table__box {
        margin-top: 16px;
      }
    }
    .right-box {
      width: 40%;
      margin: 0 auto;
      box-sizing: border-box;
      .r-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;
        h5 {
          font-size: @text-size_normal;
          color: @base-color;
          font-weight: 400;
          margin: 0;
        }
      }
      .tg_text_gray {
        color: @base-gray;
        cursor: auto;
      }
    }
  }
  .footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 16px;
  }
  .tg-button__icon--large {
    width: 14px;
    height: 14px;
    margin-right: 8px;
  }
  /deep/ .course-table {
    padding: 0 10px;
    .el-table__fixed-right {
      z-index: 9;
    }
  }
  /deep/ .el-input__icon.el-icon-date::before {
    top: 13px;
  }
}
</style>
