<!--选择学员-->
<template>
  <div>
    <el-dialog
      :visible="true"
      title="选择学员"
      width="1000px"
      :before-close="handleClose"
      class="choose-students"
      :modal="has_modal"
      :append-to-body="true"
    >
      <div class="tg-dialog__content">
        <div class="class-list">
          <div class="search tg-box--margin">
            <el-form @submit.native.prevent :inline="true" :model="form">
              <el-form-item>
                <el-input
                  placeholder="请输入学员姓名/学号/手机号"
                  class="search__input"
                  v-model="form.name"
                >
                  <img
                    src="../../../assets/图片/icon_search_grey.png"
                    alt=""
                    slot="prefix"
                  />
                  <span slot="suffix" class="searchBtn" @click="flag = !flag">
                    <img
                      :src="
                        !flag
                          ? require('../../../assets/图片/icon_double_down.png')
                          : require('../../../assets/图片/icon_double_up_ac.png')
                      "
                      alt=""
                      class="search__img"
                    />
                  </span>
                </el-input>
              </el-form-item>
              <el-form-item class="tg-form--special">
                <el-button
                  type="primary"
                  class="tg-button--primary tg-button__icon"
                  @click="searchVal"
                >
                  <img
                    src="../../../assets/图片/icon_search.png"
                    alt=""
                    class="tg-button__icon--normal"
                  />查询
                </el-button>
                <el-button
                  type="primary"
                  class="tg-button--primary tg-button__icon"
                  @click="reset"
                >
                  <img
                    src="../../../assets/图片/icon_reset.png"
                    alt=""
                    class="tg-button__icon--normal"
                  />重置
                </el-button>
              </el-form-item>
              <div style="display: flex; align-items: center">
                <el-form-item label="学员状态" class="search-teacher">
                  <el-select
                    v-model="form.student_type"
                    :popper-append-to-body="false"
                    placeholder="请选择学员状态"
                    multiple
                  >
                    <template v-for="(item, index) in student_state_list">
                      <el-option
                        v-if="item.show"
                        :key="index"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </template>
                  </el-select>
                </el-form-item>
                <!-- <el-form-item
                  label="校区"
                  class="tg-form-item tg-box--margin"
                  v-if="flag && showSchoolSearch"
                >
                  <el-input
                    placeholder="请选择所属校区"
                    readonly
                    v-model="form.department_name"
                    @click.native="school_tree_visible = true"
                    class="tg-input--special tg-select--dialog"
                    @mouseenter.native="school_flag = true"
                    @mouseleave.native="school_flag = false"
                  >
                    <img
                      slot="suffix"
                      :src="
                        !school_flag
                          ? require('../../../assets/图片/icon_more.png')
                          : require('../../../assets/图片/icon_more_ac.png')
                      "
                      alt=""
                      class="more"
                    />
                  </el-input>
                  <school-tree
                    :flag.sync="school_tree_visible"
                    :id.sync="form.department_id"
                    :name.sync="form.department_name"
                    type="chooseSchool"
                    class="stu-school-tree"
                    :use_store_options="true"
                  >
                  </school-tree>
                </el-form-item> -->
                <el-form-item
                  label="课程"
                  class="tg-form-item tg-box--margin custom--select"
                  v-if="flag"
                >
                  <el-input
                    v-model="form.course_name"
                    readonly
                    placeholder="请选择课程"
                    @click.native="choose_course_visible = true"
                    @mouseenter.native="course_flag = true"
                    @mouseleave.native="course_flag = false"
                    :class="{ 'border--active': course_flag }"
                    class="tg-select--dialog tg-input--special"
                  >
                    <img
                      :src="
                        !course_flag
                          ? require('../../../assets/图片/icon_more.png')
                          : require('../../../assets/图片/icon_more_ac.png')
                      "
                      slot="suffix"
                      alt=""
                      class="more"
                    />
                  </el-input>
                </el-form-item>
              </div>
              <div style="display: flex; align-items: center">
                <el-form-item
                  label="班级"
                  class="tg-form-item tg-box--margin custom--select"
                  v-if="flag"
                >
                  <el-input
                    v-model="form.classroom_name"
                    readonly
                    placeholder="请选择班级"
                    @click.native="choose_class_visible = true"
                    @mouseenter.native="class_flag = true"
                    @mouseleave.native="class_flag = false"
                    :class="{ 'border--active': class_flag }"
                    class="tg-select--dialog tg-input--special"
                  >
                    <img
                      :src="
                        !class_flag
                          ? require('../../../assets/图片/icon_more.png')
                          : require('../../../assets/图片/icon_more_ac.png')
                      "
                      slot="suffix"
                      alt=""
                      class="more"
                    />
                  </el-input>
                </el-form-item>
                <el-form-item
                  label="报名"
                  class="tg-form-item tg-box--margin enrollment-date"
                  v-if="flag"
                >
                  <el-date-picker
                    class="tg-input--special"
                    v-model="form.date"
                    type="daterange"
                    range-separator="至"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :pickerOptions="pickerOptions"
                    popper-class="tg-date-picker tg-date--range"
                  >
                  </el-date-picker>
                </el-form-item>
              </div>
            </el-form>
          </div>
          <div class="tg-table__box">
            <div class="tg-box--border"></div>
            <el-table
              ref="table"
              :data="list"
              tooltip-effect="dark"
              class="tg-table"
              @selection-change="handleSelectionChange"
              @current-change="handleCurrentChange"
              :row-key="getRowKeys"
              :highlight-current-row="type == 'radio' ? true : false"
              @row-click="rowClick"
            >
              <el-table-column
                type="selection"
                width="50"
                :reserve-selection="true"
                v-if="type != 'radio'"
              ></el-table-column>
              <el-table-column
                width="20"
                v-if="type == 'radio'"
              ></el-table-column>
              <el-table-column
                label="学号"
                prop="student_base.student_number"
                width="100"
                sortable
              ></el-table-column>
              <el-table-column
                label="学员姓名"
                prop="student_base.student_name"
                width="120"
                show-overflow-tooltip
                sortable
              ></el-table-column>
              <el-table-column
                label="学员状态"
                prop="student_type_chn"
                width="90"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column label="性别" width="75">
                <template slot-scope="scope">
                  <div>
                    {{ scope.row.student_base.gender }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="手机号" width="130" sortable>
                <template slot-scope="scope">
                  <span>{{
                    scope.row.student_base.student_mobile | formatPhoneNumber
                  }}</span>
                </template>
              </el-table-column>
              <!-- <el-table-column
                  label="剩余课(课时)"
                  width="100"
                  prop="left_course_hour"
                >
                </el-table-column> -->

              <el-table-column
                label="聂道棋力"
                prop="nie_dao_level"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column
                width="140"
                label="报名校区"
                prop="department_name"
                sortable
                show-overflow-tooltip
              ></el-table-column>
            </el-table>
            <div class="tg-pagination">
              <span class="el-pagination__total">共 {{ total }} 条</span>
              <el-pagination
                background
                layout="prev, pager, next,jumper"
                :total="total"
                :page-size="page_size"
                :current-page="page"
                @current-change="currentChange"
              >
              </el-pagination>
            </div>
          </div>
        </div>
        <div class="class-list--right">
          <div class="organization__title">
            <span
              >已选 学员<em>{{ right_students_list.length }}</em
              >个</span
            >
            <span class="all-clear" @click="clear">
              <img src="../../../assets/图片/icon_clear.png" alt="" />
              清空
            </span>
          </div>
          <div class="right-list-container">
            <div
              class="organization__info"
              v-for="(item, index) in right_students_list"
              :key="index"
            >
              <span
                >{{ item.student_base.student_name }}-{{
                  item.student_base.student_number
                }}</span
              >
              <img
                src="../../../assets/图片/icon_close_green.png"
                alt=""
                @click="delOne(index, item.id)"
              />
            </div>
            <span v-if="right_students_list.length === 0" class="is-empty"
              >暂无数据</span
            >
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="tg-button--plain" type="plain" @click="back"
          >取消</el-button
        >
        <el-button
          class="tg-button--primary"
          type="primary"
          @click="really"
          :loading="loading"
          >确定</el-button
        >
      </span>
    </el-dialog>
    <choose-course
      :check_id.sync="form.course_id"
      :check_name.sync="form.course_name"
      :check_arr.sync="course_check_arr"
      :choose_course_visible="choose_course_visible"
      v-if="choose_course_visible"
      :status="true"
      @close="choose_course_visible = false"
    ></choose-course>
    <choose-class
      :check_id.sync="form.classroom_id"
      :check_name.sync="form.classroom_name"
      :check_arr.sync="class_check_arr"
      :choose_class_visible="choose_class_visible"
      v-if="choose_class_visible"
      @close="choose_class_visible = false"
      :has_modal="false"
    ></choose-class>
  </div>
</template>
<script>
import studentsApi from "@/api/studentInfor";
import { picker_options } from "@/public/datePickerOptions";
import moment from "moment";

export default {
  data() {
    return {
      showSchoolSearch: true,
      right_students_list: [],
      flag: false,
      page: 1,
      page_size: 10,
      total: 0,
      list: [],
      initData: [], // 存储table获取到的所有数据
      form: {
        name: "",
        date: "",
        course_name: "",
        sign_up_begin_time: "",
        sign_up_over_time: "",
        classroom_name: "",
        student_type: [],
        department_id: [],
        department_name: [],
        classroom_id: "",
        course_id: "",
        send_card: this.send_card
      },
      search_flag: false,
      rowSelectFlag: false,
      categroy_list: [],
      student_state_list: [],
      choose_class_visible: false,
      choose_course_visible: false,
      class_flag: false,
      course_flag: false,
      course_check_arr: [],
      class_check_arr: [],
      school_tree_visible: false,
      school_flag: false,
      pickerOptions: picker_options
    };
  },
  props: {
    send_card: {
      type: Number,
      default: 1
    },
    check_id: String,
    check_name: String,
    sourceType: {
      type: String,
      default: ""
    },
    check_arr: Array,
    has_modal: {
      type: Boolean,
      default: true
    },
    department_id: Array,
    department_name: Array,
    type: {
      type: String,
      default: "select"
    },
    status: {
      type: Boolean,
      default: false // true只显示启用的列表
    },
    exclude_id: {
      type: Array,
      default: () => [] // exclude
    },
    rowInfo: {
      type: Object,
      default: () => {}
    },
    search_val: String,
    mapType: String,
    student_type: {},
    allChecked: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    "form.date": {
      handler(val) {
        const { form } = this;
        if (val) {
          this.$set(form, "sign_up_begin_time", moment(val[0]).format());
          this.$set(form, "sign_up_over_time", moment(val[1]).format());
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    if (this.$route.name === "schoolTransferFee") {
      this.showSchoolSearch = false;
    } else {
      this.showSchoolSearch = true;
    }
    if (this.student_type && this.student_type.length > 0)
      this.$set(this.form, "student_type", this.student_type);
    this.form = {
      ...this.form,
      department_id: this.department_id ? this.department_id.toString() : "",
      department_name: this.department_name
        ? this.department_name.toString()
        : ""
    };
    if (this.search_val) {
      this.form.name = this.search_val;
    }
    console.log(this.rowInfo, "rowInfo");
    if (
      typeof this.rowInfo === "object" &&
      JSON.stringify(this.rowInfo) !== "{}"
    )
      this.right_students_list =
        JSON.parse(JSON.stringify(this.check_arr)) || [];
    this.getStateList();
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    delOne(index) {
      if (this.type === "radio") {
        this.$nextTick(() => {
          this.$refs.table.setCurrentRow();
        });
        this.right_students_list.splice(index, 1);
      } else {
        setTimeout(() => {
          this.$nextTick(() => {
            // let shouldRemove = true; // 标记是否要执行 this.right_students_list.splice(index, 1)
            this.rowSelectFlag = true;
            const id = this.right_students_list[index].student_id;
            this.list.forEach((item) => {
              if (item.student_id === id) {
                // shouldRemove = false; // 当执行到这里时，取消执行 splice 操作
                this.$refs.table.toggleRowSelection(item, false);
              }
            });
            this.right_students_list.splice(index, 1);

            this.rowSelectFlag = false;
          });
        }, 0);
      }
    },
    clear() {
      if (this.type === "radio") {
        this.$nextTick(() => {
          this.$refs.table.setCurrentRow();
        });
      } else {
        this.$nextTick(() => {
          this.$refs.table.clearSelection();
          this.right_students_list = [];
        });
      }
    },
    back() {
      this.$emit("close");
    },
    really() {
      const ids = [];
      const names = [];
      this.right_students_list.forEach((item) => {
        ids.push(item.student_id);
        names.push(item.student_base.student_name);
      });
      this.$emit(
        "update:check_id",
        this.right_students_list.length > 0 ? ids.toString() : ""
      );
      this.$emit(
        "update:check_name",
        this.right_students_list.length > 0 ? names.toString() : ""
      );
      this.$emit(
        "update:check_arr",
        this.right_students_list.length > 0 ? this.right_students_list : []
      );
      this.$emit("confirm", { p_type: 1 });
    },
    openDialog() {},
    currentChange(val) {
      this.page = val;
      this.getSearchStudents();
    },
    getRowKeys(row) {
      // this.$refs.form.clearSelection();完成后需要手动清空
      return row.student_id;
    },
    handleSelectionChange(val) {
      if (this.type !== "radio") {
        if (this.rowSelectFlag) return;
        const list_ids = this.list.map((item) => item.student_id);
        const arr1 = val.filter((item, index) => {
          return list_ids.indexOf(item.student_id) !== -1;
        });
        this.right_students_list = this.right_students_list
          .filter((itemA) => {
            return !this.list.some((itemC) => {
              return itemA.student_id === itemC.student_id;
            });
          })
          .concat(arr1);
      }
    },
    handleCurrentChange(val) {
      if (this.type === "radio") {
        this.right_students_list = val == null ? [] : [val];
      }
    },
    reset() {
      this.page = 1;
      this.form = {
        name: "",
        date: "",
        course_name: "",
        sign_up_begin_time: "",
        sign_up_over_time: "",
        classroom_name: "",
        student_type: [],
        send_card: this.send_card,
        department_id:
          typeof this.form.department_id === "string"
            ? this.form.department_id.split(",")
            : this.form.department_id,
        classroom_id: "",
        course_id:
          typeof this.form.course_id === "string"
            ? this.form.course_id.split(",")
            : this.form.course_id
      };
      if (this.mapType === "transfer") {
        this.form.student_type = this.student_state_list.map(
          (item) => item.value
        );
      }
      this.getSearchStudents();
      // this.$refs.table.clearSelection();
    },
    searchVal() {
      this.page = 1;
      this.getSearchStudents();
    },
    rowClick(row) {
      if (this.type === "radio") return false;
      const ids = this.right_students_list.map((item) => item.student_id);
      const index = ids.indexOf(row.student_id);
      this.$refs.table.toggleRowSelection(row, index === -1);
    },
    getSearchStudents() {
      this.getStudents({
        page: this.page,
        page_size: this.page_size,
        ...this.form,
        course_id:
          typeof this.form.course_id === "string"
            ? this.form.course_id.split(",")
            : this.form.course_id,
        department_id:
          typeof this.form.department_id === "string"
            ? this.form.department_id.split(",")
            : this.form.department_id
      });
    },
    getStudents(data) {
      if (this.mapType === "transfer" && data.student_type.length <= 0) {
        this.$message.error("请选择要查询的学员状态!");
        return;
      }
      if (this.mapType === "transfer") {
        data.student_category = "is_student";
      }
      data.is_no_token =
        this.sourceType === "transferAccounts" ? true : undefined;
      studentsApi.getNewStudentInforList(data).then((res) => {
        this.list = res.data.data.results || [];
        // const newItems = this.list.filter((item) => {
        //   return !this.initData.some((val) => {
        //     return val.student_id === item.student_id;
        //   });
        // });

        // this.initData = this.initData.concat(newItems);
        this.total = res.data.data.count;
        this.search_flag = false;
        this.$nextTick(() => {
          if (this.type === "radio") {
            this.right_students_list.forEach((row) => {
              const find_row = this.list.find(
                (item) => item.student_id === row.student_id
              );
              this.$refs.table.setCurrentRow(find_row);
            });
          } else {
            setTimeout(() => {
              this.rowSelectFlag = true;
              this.list.forEach((row) => {
                const find_index = this.right_students_list.findIndex(
                  (item) => item.student_id === row.student_id
                );
                this.$refs.table.toggleRowSelection(row, find_index !== -1);
              });
              this.rowSelectFlag = false;
            }, 0);
          }
        });
      });
    },
    getStateList(data) {
      this.student_state_list = [];
      studentsApi.getStudentState(data).then((res) => {
        for (const key in res.data) {
          // if(this.student_type.length > 0){
          //   this.student_state_list.push({ label: res.data[key], value: key ,show: this.student_type.indexOf(key) > -1 ? true : false});
          // }else{
          this.student_state_list.push({
            label: res.data[key],
            value: key,
            show: true
          });
          // }
        }
        if (this.mapType === "transfer") {
          const filterData = this.student_state_list.filter(
            (item) => item.value !== "audition" && item.value !== "temp"
          );
          this.student_state_list = filterData;
          this.form.student_type = this.student_state_list.map(
            (item) => item.value
          );
        } else {
          if (this.allChecked) {
            this.form.student_type = this.student_state_list.map(
              (item) => item.value
            );
          }
        }
        console.log(this.student_state_list, this.mapType);
        this.getStudents({
          page: this.page,
          page_size: this.page_size,
          ...this.form,
          department_id:
            typeof this.form.department_id === "string"
              ? this.form.department_id.split(",")
              : this.form.department_id
        });
      });
    }
  },
  created() {
    this.right_students_list = JSON.parse(JSON.stringify(this.check_arr)) || [];
  }
};
</script>
<style lang="less" scoped>
/deep/ .el-dialog {
  max-width: 100%;
  max-height: 100%;
}
.choose-students {
  .search {
    width: 100%;

    .search__input img {
      width: 14px;
      height: 14px;
      margin-right: 12px;
      margin-left: 11px;
      margin-top: -4px;
      vertical-align: middle;
    }

    .searchBtn {
      display: inline-block;
      width: 100%;
      height: 100%;
      cursor: pointer;
      user-select: none;
    }
    img.search__img {
      width: 8px;
      height: 12px;
      margin-right: 10px;
      cursor: pointer;
    }
    ::v-deep .el-form-item__content,
    ::v-deep .el-form-item__label {
      line-height: 32px;
    }
    .search__input {
      width: 278px;
      // margin-right: 10px;
      ::v-deep .el-input__inner {
        padding-left: 40px;
      }
      ::v-deep .el-input__suffix {
        right: 1px;
        background: #ebf4ff;
        height: 30px;
        top: 1px;
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
      }
    }
    .el-button {
      width: 72px;
      img {
        margin-left: 0;
        margin-right: 7px;
      }
    }
    .search-teacher {
      margin-top: 16px;
      ::v-deep .el-input {
        width: 270px;
        .el-input__inner {
          padding-left: 16px;
        }
      }
    }
    ::v-deep .el-form-item {
      margin-right: 10px;
    }
    :v-deep .custom--select {
      width: 100%;
    }
    ::v-deep .el-form-item:nth-child(2) {
      margin-right: 0;
    }
    ::v-deep .el-form-item.tg-form--special {
      margin-right: 10px;
    }
    .enrollment-date {
      .el-input__inner {
        width: 300px;
      }
    }
  }
  ::v-deep .el-dialog__body {
    padding: 0 16px 0 16px;
    // overflow: hidden;
  }
  .tg-dialog__content {
    display: flex;
    flex-direction: row;
    height: 622px;
  }
  .class-list {
    width: calc(100% - 274px);
    border-right: 1px solid #e0e6ed;
    display: flex;
    flex-direction: column;
    .tg-table__box {
      width: 690px;
      margin-left: 0;
      margin-right: 16px;
    }
    ::v-deep .el-table {
      padding: 0;
      th {
        background: #f5f8fc;
      }
      .el-table__header {
        padding: 0 16px;
        background: #f5f8fc;
      }
      .el-table__body {
        padding: 0 16px;
      }
    }
  }
  .class-list--right {
    width: 257px;
    padding-top: 16px;
    padding-left: 16px;
    // overflow-y: scroll;
    box-sizing: border-box;
    height: 100%;
    padding-bottom: 10px;
    display: flex;
    flex-direction: column;
    overflow: auto;
    .organization__title,
    .organization__info {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }
    .all-clear {
      // color: #157df0;
      font-size: @text-size_small;
      font-family: @text-famliy_medium;
      cursor: pointer;
      img {
        width: 14px;
        height: 14px;
        margin-right: 8px;
        vertical-align: middle;
        margin-top: -3px;
      }
    }
    .right-list-container {
      width: 100%;
      flex: auto;
      overflow: auto;
    }
    .organization__title {
      em {
        font-style: normal;
        color: @base-color;
      }
    }
    .organization__info {
      border: 1px solid @base-color;
      border-radius: 4px;
      height: 40px;
      align-items: center;
      padding: 0 16px;
      margin-top: 16px;
      ::v-deep .el-input,
      ::v-deep .el-input__inner {
        height: 40px;
        line-height: 40px;
      }
      img {
        width: 16px;
        height: 16px;
        cursor: pointer;
      }
      span:nth-child(1) {
        overflow-x: scroll;
        width: calc(100% - 36px);
        white-space: nowrap;
      }
    }
    .required {
      &::before {
        content: "*";
        margin-right: 5px;
        color: #ff0317;
      }
    }
  }
  .is-empty {
    color: @text-color_third;
    width: 100%;
    display: inline-block;
    text-align: center;
    line-height: 350px;
  }
  .tg-input--special {
    width: 300px;
  }
  .more {
    width: 16px;
    margin-right: 10px;
    height: 4px;
    cursor: pointer;
  }
}
</style>
