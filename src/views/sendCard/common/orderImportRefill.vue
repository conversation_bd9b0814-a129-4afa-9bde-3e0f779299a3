<template>
  <el-dialog
    :visible="true"
    width="1200px"
    title="导入"
    :show-close="true"
    class="batch-import"
    :modal-append-to-body="true"
    :close-on-press-escape="false"
    append-to-body
    top="7vh"
    :before-close="handleClose"
  >
    <div class="content">
      <el-upload
        drag
        :multiple="false"
        accept=".xls,.xlsx"
        action="#"
        :show-file-list="false"
        :on-change="toJson"
        :auto-upload="false"
        class="tg-upload"
        ref="upload"
      >
        <img src="../../../assets/图片/icon_import.png" alt="" />
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      </el-upload>
      <!-- 下载模版 -->
      <!-- <el-button type="plain" class="tg-button--plain" @click="downloadTemplate"
        >下载模版</el-button
      > -->
    </div>
    <div class="batch--top tg-table--custom">
      <div class="tg-table__box">
        <div class="tg-box--border"></div>
        <el-table
          width="100%"
          height="183"
          :data="importData"
          class="tg-table"
          ref="tgTable"
        >
          <template v-for="(item, index) in importTitle">
            <el-table-column
              :key="index"
              :prop="item.prop"
              :label="item.label"
              :width="item.width"
            >
              <template slot-scope="scope">
                <div class="channel-table--inline">
                  <span>{{ scope.row[scope.column.property] }}</span>
                </div>
              </template>
            </el-table-column>
          </template>
        </el-table>
      </div>
      <div class="tg-upload__button">
        <div class="tg-upload__tips">
          <img src="../../../assets/图片/icon_info.png" alt="" />
          <span
            >只能上传<em>excel文件</em>，注意数据标题为首行，上方为导入数据预览</span
          >
        </div>
        <el-button
          type="primary"
          :disabled="importData.length == 0"
          @click="dataUpload"
          class="tg-button--primary"
          >上传</el-button
        >
      </div>
      <div class="tg-upload__loading" v-if="isImporting">
        <el-progress type="circle" :percentage="percentage"></el-progress>
      </div>
    </div>
    <div class="error-table">
      <div class="errTitle">
        <span class="error-table__title">错误数据</span>
        <span>
          <!-- @click="downloadExcel" -->
          <el-button
            type="primary"
            :disabled="errorData.length > 0 ? false : true"
            @click="exportExcel('会员卡管理错误数据', 'mainTable')"
            class="tg-button--primary"
            :loading="exportLoading"
            >导出</el-button
          >
          <!-- <el-button
            type="primary"
            :disabled="errorData.length > 0 ? false : true"
            @click="reUpload"
            class="tg-button--primary"
            >重新上传</el-button
          > -->
        </span>
      </div>
      <div class="tg-table__box" style="position: relative">
        <div class="tg-box--border"></div>
        <el-table
          width="100%"
          height="183"
          :data="errorData"
          class="tg-table"
          id="mainTable"
        >
          <template v-for="(item, index) in errorTitle">
            <el-table-column
              :prop="item.prop"
              :label="item.label"
              :key="index"
              :width="item.width"
            >
              <template slot-scope="scope">
                <div class="channel-table--inline">
                  <span>{{ scope.row[scope.column.property] }}</span>
                </div>
              </template>
            </el-table-column>
          </template>
        </el-table>
        <div class="tg-uploaderr__loading" v-if="isReUploading">
          <el-progress
            type="circle"
            :percentage="reUploadPercentage"
          ></el-progress>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button
        @click="handleClose"
        class="tg-button--plain"
        :disabled="isImporting ? true : false"
        >关闭</el-button
      >
    </span>
  </el-dialog>
</template>
<script>
import { BigNumber } from "bignumber.js";

// import { exportTableToExcel } from "@/public/downloadExcel";
import timeFormat from "@/public/timeFormat";
// import { downLoadFile } from "@/public/downLoadFile";
import FileSaver from "file-saver";
import XLSX from "xlsx";
import yikeCard from "@/api/yikeCard";
import { readExcel } from "@/public/importExcel";
// import { exportTableToExcel } from "@/public/downloadExcel";

export default {
  props: {
    type: {
      type: String
    }
  },
  // 需要选择渠道
  data() {
    return {
      exportLoading: false,
      fullscreenLoading: false,
      isImporting: false,
      importTitle: [
        {
          prop: "department_name",
          label: "机构名称",
          type: "String"
        },
        {
          prop: "department_name",
          label: "学校名称",
          type: "String"
        },
        {
          prop: "card_number",
          label: "卡号",
          type: "String"
        },
        {
          prop: "card_password",
          label: "密码",
          type: "String"
        },
        // {
        //   prop: "status",
        //   label: "状态",
        //   width: 110,
        //   type: "String"
        // },
        // {
        //   prop: "type",
        //   label: "类型",
        //   width: 110,
        //   type: "String"
        // },
        {
          prop: "validity_type",
          label: "充值卡类型",
          type: "String"
        }
        // {
        //   prop: "person",
        //   label: "推荐人",
        //   width: 120,
        //   type: "String"
        // },

        // {
        //   prop: "time",
        //   label: "领卡时间",
        //   width: 110
        // },

        // {
        //   prop: "operator",
        //   label: "开卡人",
        //   width: 140,
        //   type: "String"
        // },
        // {
        //   prop: "oper_time",
        //   label: "开卡时间",
        //   width: 110
        // }
      ],
      tableTitle: [],
      errorTitle: [],
      importData: [],
      errorData: [],
      percentage: 0,
      excelTitle: [],
      erroReUpload: [],
      isReUploading: false,
      reUploadPercentage: 0
    };
  },
  mounted() {
    this.errorTitle = [
      ...this.importTitle,
      { prop: "msg", label: "错误原因", width: 120 }
    ];
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
    // 前端实现导出
    exportExcel(filename, tableId) {
      this.exportLoading = true;
      /* generate workbook object from table */
      const xlsxParam = { raw: true }; // 导出的内容只做解析，不进行格式转换
      const table = document.querySelector("#" + tableId).cloneNode(true);
      // table.removeChild(table.querySelector(".el-table__fixed")); // 这里是双下划线
      const wb = XLSX.utils.table_to_book(table, xlsxParam);
      /* get binary string as output */
      const wbout = XLSX.write(wb, {
        bookType: "xlsx",
        bookSST: true,
        type: "array"
      });
      try {
        FileSaver.saveAs(
          new Blob([wbout], { type: "application/octet-stream" }),
          filename + ".xlsx"
        );
        this.exportLoading = false;
      } catch (e) {
        if (typeof console !== "undefined") {
          console.log(e, wbout);
        }
      }
      return wbout;
    },
    flattenLabels(data, parentLabel = "") {
      return data?.reduce((acc, item) => {
        // 拼接当前标签（如果有父标签）
        const currentLabel = parentLabel
          ? `${parentLabel}-${item.label}`
          : item.label;

        // 如果有子节点，递归处理并使用当前标签作为父标签
        if (item.children && item.children.length > 0) {
          return [...acc, ...this.flattenLabels(item.children, currentLabel)];
        }

        // 否则直接添加当前标签
        return [...acc, currentLabel];
      }, []);
    },
    async toJson(file) {
      console.log("file", file);
      this.fullscreenLoading = this.$loading({
        lock: true,
        text: "Loading",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)"
      });
      try {
        const fileData = await readExcel(file);
        console.log("fileData", fileData);
        const importData = fileData[0].sheet;
        console.log(importData);
        this.excelTitle = [];
        for (const key in importData[0]) {
          this.excelTitle.push({ label: key, prop: key });
        }
        // 原始数据
        const initObj = {};
        this.importTitle.forEach((item) => {
          initObj[item.prop] = "";
        });
        const newData = importData.map((item) => {
          const obj = JSON.parse(JSON.stringify(initObj));
          console.log("item", item);
          for (const i in item) {
            const titleObj = this.importTitle.find(
              (it) => it.label.replace(/\*+$/, "") === i
            );
            if (titleObj) {
              obj[titleObj.prop] =
                item[i] === "null"
                  ? ""
                  : titleObj.type === "String"
                  ? item[i].toString()
                  : item[i];
            }
          }
          if (obj.age === "null") obj.age = null;
          return obj;
        });
        this.$nextTick(() => {
          // 以服务的方式调用的 Loading 需要异步关闭
          this.fullscreenLoading.close();
        });
        this.importData = newData;
        // this.relativeFlag = true;
        // this.excelData = importData;
      } catch (err) {
        this.$nextTick(() => {
          // 以服务的方式调用的 Loading 需要异步关闭
          this.fullscreenLoading.close();
        });
      }
    },
    async dataUpload() {
      this.errorData = [];
      this.isImporting = true;
      this.percentage = 0;
      console.log(this.importData);
      for (let index = 0; index < this.importData.length; index++) {
        console.log("????");
        const value = this.importData[index];
        const save_obj = Object.assign({}, value);
        save_obj.time = save_obj.time ? this.getDate(save_obj.time) : "";
        save_obj.oper_time = save_obj.oper_time
          ? this.getDate(save_obj.oper_time)
          : "";
        await yikeCard
          .createPrepaid(save_obj)
          .then((res) => {
            console.log("上传返回", res);
            const length = this.importData.length;
            const b = new BigNumber(this.percentage);
            const a = new BigNumber(Math.round((1 / length) * 10000) / 100.0);
            const c = Number(b.plus(a));
            this.percentage = c;
            if (res.err) {
              this.errorData.push({ ...value, msg: res.err });
            }
            if (+res?.data?.code === 1) {
              this.errorData.push({ ...value, msg: res.data.message });
            }
          })
          .catch(() => {
            const length = this.importData.length;
            const b = new BigNumber(this.percentage);
            const a = new BigNumber(Math.round((1 / length) * 10000) / 100.0);
            const c = Number(b.plus(a));
            this.percentage = c;
            this.errorData.push({ ...value, msg: "未知错误" });
          });
      }
      setTimeout(() => {
        this.isImporting = false;
      }, 2000);
      if (this.errorData.length === 0) {
        this.$message.success("上传成功");
        // this.exportData();
      } else {
        this.$message.warning("上传结束，请检查错误数据");
        this.erroReUpload = this.errorData;
        // this.exportData();
      }
    },
    getDate(time) {
      return timeFormat.GetDate(time);
    }

    // 导出
    // async exportData() {
    //   const that = this;
    //   setTimeout(async () => {
    //     this.$message.info("正在导出数据,请稍等...");
    //     await yikeCard
    //       .prepaidExport()
    //       .then(function (res) {
    //         // 导出流
    //         console.log(res, "导出");
    //         const blob = new Blob([res.data]); // response.data为后端传的流文件
    //         const downloadFilename =
    //           "北京聂圣体育发展有限责任公司_" +
    //           that.moment(new Date()).format("YYYY-MM-DD HH:mm:ss") +
    //           ".zip"; // 设置导出的文件名  用moment时间插件对文件命名防止每次都是一样的文件名
    //         if (window.navigator && window.navigator.msSaveOrOpenBlob) {
    //           // 兼容ie浏览器
    //           window.navigator.msSaveOrOpenBlob(blob, downloadFilename);
    //         } else {
    //           // 谷歌,火狐等浏览器
    //           const url = window.URL.createObjectURL(blob);
    //           const downloadElement = document.createElement("a");
    //           downloadElement.style.display = "none";
    //           downloadElement.href = url;
    //           downloadElement.download = downloadFilename;
    //           document.body.appendChild(downloadElement);
    //           downloadElement.click();
    //           document.body.removeChild(downloadElement);
    //           window.URL.revokeObjectURL(url);
    //         }
    //         that.$message.success("导出成功");
    //       })
    //       .catch(function (err) {
    //         console.log(err);
    //       })
    //       .finally(function () {});
    //     // downLoadFile(res.data, "人力拆表数据");
    //   }, 500);
    // }
    // async reUpload() {
    //   this.errorData = [];
    //   this.isReUploading = true;
    //   this.reUploadPercentage = 0;
    //   for (let index = 0; index < this.erroReUpload.length; index++) {
    //     const value = this.erroReUpload[index];
    //     const save_obj = Object.assign({}, value);
    //     save_obj.is_first = false; // 只有第一条数据（索引为0）的is_first为true
    //     save_obj.amortization_price = Number(save_obj.amortization_price || 0);
    //     save_obj.amortization_ratio = Number(save_obj.amortization_ratio || 0);
    //     await compensationApi
    //       .importData(save_obj)
    //       .then((res) => {
    //         const length = this.erroReUpload.length;
    //         const b = new BigNumber(this.reUploadPercentage);
    //         const a = new BigNumber(Math.round((1 / length) * 10000) / 100.0);
    //         const c = Number(b.plus(a));
    //         this.reUploadPercentage = c;
    //         if (res.err) {
    //           this.errorData.push({ ...value, msg: res.err });
    //         }
    //         if (+res?.data?.code === 1) {
    //           this.errorData.push({ ...value, msg: res.data.message });
    //         }
    //       })
    //       .catch(() => {
    //         const length = this.erroReUpload.length;
    //         const b = new BigNumber(this.reUploadPercentage);
    //         const a = new BigNumber(Math.round((1 / length) * 10000) / 100.0);
    //         const c = Number(b.plus(a));
    //         this.reUploadPercentage = c;
    //         this.errorData.push({ ...value, msg: "未知错误" });
    //       });
    //   }
    //   setTimeout(() => {
    //     this.isReUploading = false;
    //   }, 2000);
    //   if (this.errorData.length === 0) {
    //     this.$message.success("上传成功");
    //     this.exportData();
    //   } else {
    //     this.$message.warning("上传结束，请检查错误数据");
    //     this.erroReUpload = this.errorData;
    //     // this.exportData();
    //   }
    // },
    // downloadTemplate() {
    //   const downloadElement = document.createElement("a");
    //   downloadElement.href =
    //     "./compensationImport.xlsx?v=" + window.__APP_VERSION__;
    //   downloadElement.download = `充值卡导入模板.xlsx`; // 下载后文件名
    //   document.body.appendChild(downloadElement);
    //   downloadElement.click(); // 点击下载
    //   document.body.removeChild(downloadElement);
    // }
  }
};
</script>
<style lang="less" scoped>
::v-deep .el-table__header {
  line-height: 0 !important;
}
.batch-import {
  .batch--top {
    // display: flex;
    // flex-direction: row;
    padding-bottom: 16px;
    position: relative;
    .tg-upload__loading {
      position: absolute;
      top: 0;
      left: 0;
      background: rgba(0, 0, 0, 0.5);
      margin: auto;
      right: 0;
      bottom: 0;
      width: 100%;
      height: inherit;
      z-index: 10;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      .el-progress {
        z-index: 11;
        ::v-deep .el-progress__text {
          color: white;
        }
      }
    }
  }
  .tg-upload__button {
    // text-align: right;
    margin-top: 16px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }
  /deep/.el-table {
    padding: 0;
    th {
      //   height: 30px;
    }
  }
  .tg-table--custom {
    // width: calc(100% - 400px);
    width: 100%;
    ::v-deep .el-table {
      padding: 0;
      th {
        // height: 30px;
      }
      tr td .cell {
        // padding-left: 26px;
      }
    }
    ::v-deep .el-table__header tr {
      background-color: #f5f8fc;
      & > th {
        background-color: transparent;
      }
    }
    ::v-deep .el-table__header {
      background-color: #f5f8fc;
      // padding: 0 16px;
    }
  }
  .error-table {
    width: 100%;
    .error-table__title {
      font-size: 14px;
      font-weight: bold;
      color: @text-color_second;
      font-family: @text-famliy_medium;
      padding-left: 12px;
      position: relative;
      &::after {
        content: "";
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background-color: @base-color;
        position: absolute;
        top: 7px;
        left: 0;
      }
    }
  }
  .content {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    height: 32px;
    align-items: center;
    .el-select + .el-select {
      margin-left: 16px;
    }
  }
  ::v-deep .el-upload-dragger {
    border: 1px dashed #d0dce7;
    border-radius: 4px;
    height: 32px;
    background-color: #f7f8fa;
    width: 220px;
    img {
      width: 20px;
      height: 16px;
      margin-right: 12px;
      vertical-align: middle;
      margin-top: -3px;
    }
    .el-upload__text {
      display: inline-block;
      font-size: 10px;
      font-family: @text-famliy_medium;
      line-height: 30px;
    }
  }
  .tg-upload__tips {
    img {
      width: 12px;
      height: 12px;
      margin-right: 8px;
      vertical-align: middle;
      margin-top: -1px;
    }
    span {
      font-size: 12px;
      font-family: @text-famliy_medium;
      color: #b3b7c6;
    }
    em {
      color: #ff0317;
      font-style: normal;
    }
  }
  .tg-table__box {
    margin-left: 0;
    margin-right: 0;
    box-shadow: none;
    .tg-box--border {
      //   height: 61px;
      width: calc(100% - 1px);
    }
  }
  ::v-deep .el-dialog__body {
    padding: 16px;
  }
  ::v-deep .el-dialog__footer {
    padding: 11px 16px 7px 16px;
  }
  ::v-deep .el-dialog__body {
    height: 531px;
  }
  .tg-text--disabled {
    color: #8492a6;
    em {
      color: #455569;
    }
  }
  .tg-upload--disabled {
    ::v-deep .el-upload-dragger {
      cursor: not-allowed;
    }
  }
  .tg-row {
    display: inline-block;
  }
  .tg-row + .tg-row {
    margin-left: 40px;
  }
  .errTitle {
    display: inline-flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
}
.tg-upload {
  line-height: 0;
}
.tg-uploaderr__loading {
  position: absolute;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.5);
  margin: auto;
  right: 0;
  bottom: 0;
  width: 100%;
  height: inherit;
  z-index: 10;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  .el-progress {
    z-index: 11;
    ::v-deep .el-progress__text {
      color: white;
    }
  }
}
</style>
