<template>
  <div class="entityCardRecord">
    <tg-search
      :searchTitle.sync="search_title"
      :form.sync="searchForm"
      :showNum="3"
      @reset="reset"
      @search="searchVal"
      :searchLoadingState="searchLoading"
      class="search"
      :isExport="isExport"
      @educe="exportExcel"
      :loadingState="exportLoading"
    ></tg-search>
    <div style="margin-left: 0" class="tg-table__box tg-table-margin">
      <div class="tg-box--border"></div>
      <el-table
        ref="table"
        :data="list"
        tooltip-effect="dark"
        class="tg-table"
        border
        :cell-style="{ borderRightColor: '#e0e6ed75' }"
        :header-cell-style="{ borderRightColor: '#e0e6ed75' }"
      >
        <template v-for="(item, index) in tableTitle">
          <el-table-column
            v-if="item.show"
            :key="index"
            :prop="item.props"
            :label="item.label"
            :min-width="item.width"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <template v-if="item.props === 'card_no'">
                <span style="justify-content: flex-start" class="copy_name">
                  {{ scope.row.card_no }}
                  <div
                    style="margin-left: 10px"
                    v-copy="scope.row.card_no"
                  ></div>
                </span>
              </template>
              <span v-else-if="item.props === 'validity_type'">
                {{
                  scope.row.validity_type === 1
                    ? "月卡"
                    : scope.row.validity_type === 2
                    ? "半年卡"
                    : scope.row.validity_type === 3
                    ? "年卡"
                    : scope.row.validity_type === 4
                    ? "双周卡"
                    : scope.row.validity_type === 5
                    ? "季卡"
                    : scope.row.validity_type === 6
                    ? "双月卡"
                    : "周卡"
                }}
              </span>
              <div
                v-else-if="item.props === 'card_password'"
                :style="{
                  display: 'flex',
                  'justify-content': !$_has({ m: 'all_phone', o: 'has_limit' })
                    ? 'left'
                    : 'flex-start',
                  'align-items': 'center'
                }"
              >
                <mobileHyposensitization
                  :mobileTemInfo="{
                    row: scope.row,
                    has_eye_limit: scope.row?.has_eye_limit,
                    mobile: scope.row.card_password
                  }"
                ></mobileHyposensitization>
                <div v-copy="scope.row.card_password"></div>
              </div>
              <span v-else>{{ scope.row[scope.column.property] }}</span>
            </template>
          </el-table-column>
        </template>
        <template slot="empty">
          <div style="margin-top: 15%">
            <TgLoading v-if="loading"></TgLoading>
            <div class="empty-container" v-else>暂无数据～</div>
          </div>
        </template>
      </el-table>
      <!-- 分页 -->
      <div class="tg-pagination">
        <span class="el-pagination__total">共 {{ total }} 条</span>
        <el-pagination
          background
          layout="sizes,prev,pager,next,jumper"
          :total="total"
          :page-size="pageSize"
          :current-page="page"
          @current-change="currentChange"
          @size-change="sizeChange"
          :page-sizes="[10, 20, 50, 100]"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import quickTime from "@/public/quickTime";
import yikeCard from "@/api/yikeCard";
import {
  card_type,
  send_card_customer_status,
  card_validity_type
} from "@/public/dict";
export default {
  data() {
    return {
      searchLoading: false,
      isExport: true,
      exportLoading: false,
      searchForm: {
        card_no: "",
        card_type: undefined,
        validity_type: undefined,
        student_info: "",
        current_status: undefined,
        use_department_ids: [],
        send_employee_ids: []
      },
      search_title: [
        {
          label: "卡号",
          props: "card_no",
          type: "input",
          show: true
        },
        {
          props: "card_type",
          label: "卡类型",
          type: "select",
          show: true,
          selectOptions: card_type
        },
        {
          props: "validity_type",
          label: "有效期类型",
          type: "select",
          show: true,
          selectOptions: card_validity_type
        },
        {
          label: "使用人",
          props: "student_info",
          type: "input",
          placeholder: "请输入学员姓名/学号/手机号",
          width: 200,
          show: true
        },
        {
          props: "current_status",
          label: "客户当前状态",
          type: "select",
          show: false,
          selectOptions: send_card_customer_status
        },
        {
          props: "use_department_ids",
          label: "使用校区",
          type: "school",
          show: false,
          selectOptions: [],
          school_choose_type: "radio",
          use_store_options: true
        },
        {
          props: "send_employee_ids",
          label: "发卡人",
          type: "course_staff",
          is_leave: true,
          show: false
        },
        {
          props: "send_time",
          label: "发卡时间",
          type: "date",
          show: false,
          selectOptions: [],
          has_options: true
        },
        {
          props: "activation_time",
          label: "开卡时间",
          type: "date",
          show: false,
          selectOptions: [],
          has_options: true
        }
      ],
      page: 1,
      pageSize: 10,
      total: 0,
      tableTitle: [
        {
          props: "card_no",
          label: "卡号",
          show: true,
          width: 160
        },
        {
          props: "card_password",
          label: "卡密码",
          show: true,
          width: 100
        },
        { props: "department_name", label: "校区", show: true, width: 100 },
        {
          props: "card_type_name",
          label: "卡类型",
          show: true,
          width: 100
        },
        {
          props: "validity_period_name",
          label: "有效期类型",
          show: true,
          width: 100
        },
        { props: "student_name", label: "学员姓名", show: true, width: 100 },
        {
          props: "customer_status",
          label: "客户开卡状态",
          show: true,
          width: 100
        },
        {
          props: "current_status",
          label: "客户当前状态",
          show: true,
          width: 100
        },
        {
          props: "use_department_name",
          label: "使用校区",
          show: true,
          width: 100
        },
        {
          props: "send_employee_name",
          label: "发卡人",
          show: true,
          width: 100
        },
        { props: "send_time", label: "发卡时间", show: true, width: 150 },
        { props: "activation_time", label: "开卡时间", show: true, width: 150 }
      ],
      list: []
    };
  },
  components: {},
  computed: {
    departmentId() {
      return this.$store.getters.doneGetSchoolId;
    }
  },
  created() {
    if (this.$_has({ m: "yiKeOpenCard", o: "entityCardRecordExport" })) {
      this.isExport = true;
    } else {
      this.isExport = false;
    }
    // this.setSearchDefault();
    this.searchVal();
  },
  methods: {
    exportExcel() {
      if (this.list.length === 0) {
        this.$message.info("数据为空，无法导出");
        return;
      }

      this.$confirm("是否导出实体卡管理列表？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          const searchForm = this.getSearchForm();
          this.exportLoading = true;
          yikeCard.entityCardRecordExport(searchForm).then((res) => {
            console.log(res);
            const blob = new Blob([res.data], {
              type: "application/vnd.ms-excel"
            }); // type这里表示xlsx类型
            const downloadElement = document.createElement("a");
            const href = window.URL.createObjectURL(blob); // 创建下载的链接
            downloadElement.href = href;
            downloadElement.download = `实体卡管理列表.xlsx`; // 下载后文件名
            document.body.appendChild(downloadElement);
            downloadElement.click(); // 点击下载
            document.body.removeChild(downloadElement);
          });
          this.$message.success("导出成功!");
        })
        .finally(() => {
          this.exportLoading = false;
        });
    },

    reset() {
      this.page = 1;
      this.pageSize = 10;
      this.searchForm = {
        card_no: "",
        card_type: undefined,
        validity_type: undefined,
        student_info: "",
        current_status: undefined,
        use_department_ids: [],
        send_employee_ids: [],
        send_time: [],
        activation_time: []
      };
      // this.setSearchDefault();
      this.searchVal();
    },
    getSearchForm() {
      const { send_time, activation_time } = this.searchForm;
      if (send_time && send_time.length) {
        this.searchForm.send_time_start = send_time[0];
        this.searchForm.send_time_end = send_time[1];
      } else {
        delete this.searchForm.send_time_start;
        delete this.searchForm.send_time_end;
      }
      if (activation_time && activation_time.length) {
        this.searchForm.activation_time_start = activation_time[0];
        this.searchForm.activation_time_end = activation_time[1];
      } else {
        delete this.searchForm.activation_time_start;
        delete this.searchForm.activation_time_end;
      }
      return this.searchForm;
    },
    searchVal() {
      this.loading = true;
      const searchForm = this.getSearchForm();
      yikeCard
        .entityCardRecord({
          ...searchForm,
          page: this.page,
          page_size: this.pageSize,
          department_id: this.departmentId
        })
        .then((res) => {
          if (res.data.code === 0) {
            this.list = res.data.data.results ?? [];
            this.total = res.data.data.count ?? 0;
            this.loading = false;
          } else {
            this.loading = false;
            this.$message.error(res.data.message);
          }
        });
    },
    currentChange(val) {
      this.page = val;
      this.searchVal();
    },
    sizeChange(val) {
      this.page = 1;
      this.pageSize = val;
      this.searchVal();
    },
    setSearchDefault() {
      const pastThirty = quickTime.GetDate("pastThirty");
      this.$set(this.searchForm, "send_time", pastThirty);
      this.$set(this.searchForm, "activation_time", pastThirty);
    }
  }
};
</script>

<style lang="less" scoped></style>
