<template>
  <!-- <div>
    <test />
  </div> -->
  <div class="nedaw-circle-management">
    <div class="statistics" v-has="{ m: 'nedawCircle', o: 'listStatistics' }">
      <!-- <div class="statistics-title">
      </div> -->
      <div class="statistics-content">
        <el-card
          class="box-card"
          v-for="(item, index) in statisticsList"
          :key="index"
        >
          <div class="statistics-title-item" v-if="index === 0">统计</div>
          <div class="statistics-title-item" v-else>
            <span style="font-size: 16px">日期范围：</span>
            <!-- <div class="btn--left">
              <span
                v-for="(item, index) in selectOptions"
                :key="index"
                @click="changeTime(item.value, index)"
                :class="{ choose: hover_index === index }"
                >{{ item.name }}</span
              >
            </div> -->
            <el-date-picker
              v-model="statisticsParams.date"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              @change="changeDate($event)"
              end-placeholder="结束日期"
              popper-class="tg-date-picker tg-date--range"
              :pickerOptions="pickerOptions"
              value-format="yyyy-MM-dd"
              :clearable="true"
            ></el-date-picker>
          </div>
          <div class="statistics-item">
            <div
              class="statistics-item-block"
              v-for="(item1, index1) in item"
              :key="index1"
            >
              <div class="statistics-item-value">{{ item1.value }}</div>
              <div class="statistics-item-label">{{ item1.label }}</div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
    <tg-search
      :searchTitle.sync="searchTitle"
      :form.sync="params"
      @reset="reset"
      @search="searchList"
      class="tg-box--margin"
    ></tg-search>
    <el-row style="margin: 16px 0 0 6px; width: 100%">
      <el-col :span="10">
        <el-button
          type="plain"
          @click="add"
          class="tg-button--plain"
          v-has="{ m: 'nedawCircle', o: 'create' }"
          >新增</el-button
        >
        <el-button
          type="plain"
          @click="batchAudit(4)"
          class="tg-button--plain"
          :class="{
            'tg-button--disabled': !isDisabled
          }"
          :disabled="!isDisabled"
          v-has="{ m: 'nedawCircle', o: 'verify' }"
          >审核通过</el-button
        >
        <el-button
          type="plain"
          :class="{
            'tg-button--disabled': !isDisabled
          }"
          :disabled="!isDisabled"
          @click="batchAudit(5)"
          class="tg-button--plain"
          v-has="{ m: 'nedawCircle', o: 'verify' }"
          >审核驳回</el-button
        >
      </el-col>
    </el-row>
    <div class="tg-table__box">
      <div class="tg-box--border"></div>
      <el-table
        ref="table"
        class="tg-table"
        tooltip-effect="dark"
        :data="list"
        :row-key="getRowKeys"
        @selection-change="handleSelectionChange"
        v-loading="loading"
        :cell-style="{ borderRightColor: '#e0e6ed75' }"
        :header-cell-style="{ borderRightColor: '#e0e6ed75' }"
        border
      >
        <el-table-column
          type="selection"
          fixed="left"
          width="50"
          :reserve-selection="true"
        ></el-table-column>
        <template v-for="(item, index) in table_title">
          <el-table-column
            v-if="item.show"
            :key="index"
            :fixed="item.fixed"
            :prop="item.props"
            :label="item.label"
            :min-width="item.width"
            :show-overflow-tooltip="item.show_overflow_tooltip"
          >
            <template slot-scope="scope">
              <div v-if="item.props === 'is_visitor'">
                <span v-if="scope.row.is_visitor === 3">是</span>
                <span v-else>否</span>
              </div>
              <div v-else-if="item.props === 'index'">
                <div class="copy_name">
                  <el-button
                    type="text"
                    :disabled="!$_has({ m: 'nedawCircle', o: 'detail' })"
                    @click="openInfo(scope.row)"
                    >{{ scope.row.index }}</el-button
                  >
                  <div v-copy="scope.row.index.toString()"></div>
                </div>
              </div>
              <div v-else-if="item.props === 'content'" style="width: 100%">
                <el-button
                  type="text"
                  :disabled="!$_has({ m: 'nedawCircle', o: 'detail' })"
                  @click="openContent(scope.row)"
                  style="width: 100%; overflow: hidden; text-overflow: ellipsis"
                  >{{ scope.row.content }}</el-button
                >
              </div>
              <div v-else-if="item.props === 'image_url'">
                <template
                  v-if="
                    getMediaType(scope.row?.image_url) === 'image' &&
                    scope.row?.image_url?.length
                  "
                >
                  <el-image
                    :src="scope.row['image_url'][0].url"
                    :preview-src-list="
                      scope.row['image_url'].map((item) => item.url)
                    "
                    style="width: 100px; height: 100px"
                    fit="cover"
                  />
                </template>
                <template
                  v-else-if="
                    getMediaType(scope.row?.image_url) === 'video' &&
                    scope.row?.image_url?.length
                  "
                >
                  <video
                    @click="openPreview(scope.row)"
                    :poster="
                      scope.row['cover_url'] ||
                      `${scope.row['image_url'][0].url}?x-oss-process=video/snapshot,t_1000,f_jpg,w_0,h_0,m_fast`
                    "
                    style="width: 100%; height: 100px; cursor: pointer"
                    :src="scope.row['image_url'][0].url"
                  />
                </template>
              </div>
              <div v-else-if="item.props === 'likes_count'">
                <el-button
                  type="text"
                  :disabled="
                    !$_has({ m: 'nedawCircle', o: 'updateCustomCount' })
                  "
                  @click="openCustomizeNumberLikes(scope.row)"
                >
                  {{ scope.row.likes_count }}(+{{
                    scope.row.custom_likes_count
                  }})
                </el-button>
              </div>
              <div v-else-if="item.props === 'publish_status'">
                {{
                  publish_status_options.find(
                    (item) => item.id === scope.row.publish_status
                  )?.name
                }}
              </div>
              <div v-else-if="item.props === 'department_name'">
                {{ scope.row.department_name.join(",") }}
              </div>
              <div v-else-if="item.props === 'top_status'">
                <el-switch
                  v-model="scope.row.top_status"
                  :disabled="
                    !$_has({ m: 'nedawCircle', o: 'updateTopStatus' }) ||
                    scope.row.publish_status !== 4
                  "
                  :active-value="1"
                  :inactive-value="2"
                  @change="changeTopStatus(scope.row)"
                >
                </el-switch>
              </div>
              <div v-else-if="item.props === 'visibility_scope'">
                {{
                  scope.row.visibility_scope
                    ?.map(
                      (item) =>
                        visible_range_options.find((item1) => item1.id === item)
                          ?.name
                    )
                    ?.join(",")
                }}
              </div>
              <span v-else>{{ scope.row[scope.column.property] }}</span>
            </template>
          </el-table-column>
        </template>
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <template
              v-if="
                [1, 3].includes(scope.row.publish_status) &&
                (scope.row.operator_name === user_info.name ||
                  user_info.id === '1')
              "
            >
              <el-button
                v-has="{ m: 'nedawCircle', o: 'publish' }"
                @click="updateStatus(scope.row, 2)"
                type="text"
                >发布</el-button
              >
            </template>
            <template
              v-if="
                ![4].includes(scope.row.publish_status) &&
                (scope.row.operator_name === user_info.name ||
                  user_info.id === '1')
              "
            >
              <el-button
                v-has="{ m: 'nedawCircle', o: 'update' }"
                @click="updateRow(scope.row)"
                type="text"
                >修改</el-button
              >
            </template>
            <template
              v-if="
                scope.row.operator_name === user_info.name ||
                user_info.id === '1'
              "
            >
              <el-button
                v-has="{ m: 'nedawCircle', o: 'delete' }"
                @click="deleteRow(scope.row)"
                type="text"
                >删除</el-button
              >
            </template>
            <template v-if="scope.row.publish_time">
              <el-button
                v-has="{ m: 'nedawCircle', o: 'statistics' }"
                @click="statistics(scope.row)"
                type="text"
                >统计</el-button
              >
            </template>
            <template
              v-if="
                [4].includes(scope.row.publish_status) &&
                (scope.row.operator_name === user_info.name ||
                  user_info.id === '1')
              "
            >
              <el-button
                v-has="{ m: 'nedawCircle', o: 'delist' }"
                @click="updateStatus(scope.row, 3)"
                type="text"
                >下架</el-button
              >
            </template>
          </template>
        </el-table-column>
      </el-table>
      <div class="tg-pagination">
        <span class="el-pagination__total">共 {{ total }} 条</span>
        <el-pagination
          background
          layout="sizes,prev,pager,next,jumper"
          :current-page="params.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="params.page_size"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="currentChange"
        />
      </div>
    </div>
    <el-dialog
      title="预览"
      width="430px"
      v-if="previewVisible"
      :visible.sync="previewVisible"
    >
      <preview :form="rowInfo" :mediaType="mediaType"></preview>
    </el-dialog>
    <controlsList
      v-if="controlsListVisible"
      :visible.sync="controlsListVisible"
      @refresh="init"
      :row="rowInfo"
      :type.sync="controlsType"
    />
    <customizeNumberLikes
      v-if="customizeNumberLikesVisible"
      @refresh="init"
      :visible.sync="customizeNumberLikesVisible"
      :row="rowInfo"
    />
    <resourcePreview
      class="resource-preview"
      v-if="preview_visible"
      :url="preview_url"
      :poster="poster"
      @close="preview_visible = false"
    />
  </div>
</template>

<script>
import { visible_range_options, publish_status_options } from "@/public/dict";
import { picker_options } from "@/public/datePickerOptions";
import nedawCircleManagement from "@/api/nedawCircleManagement";
import controlsList from "./dialog/controlsList.vue";
import customizeNumberLikes from "./dialog/customizeNumberLikes.vue";
import preview from "./components/preview.vue";
import moment from "moment";
import resourcePreview from "@/views/appletResource/diag/resourcePreview.vue";
export default {
  components: {
    controlsList,
    customizeNumberLikes,
    preview,
    resourcePreview
  },
  computed: {
    user_info() {
      return JSON.parse(localStorage.getItem("user_info"));
    },
    isDisabled() {
      return (
        this.selectedRows.length !== 0 &&
        this.selectedRows.every((item) => item.publish_status === 2)
      );
    },
    school_id() {
      return this.$store.getters.doneGetSchoolId;
    }
  },
  watch: {
    school_id: {
      handler(val) {
        this.statisticsParams.department_ids = val;
        this.params.department_ids = val;
        this.init();
        this.getIndexStatistics();
      }
    }
  },
  data() {
    return {
      previewVisible: false,
      preview_visible: false,
      preview_url: "",
      poster: "",
      mediaType: "",
      hover_index: 0,
      choose: "week",
      pickerOptions: picker_options,
      selectOptions: [
        { name: "近七日", value: "week" },
        { name: "近30日", value: "month" },
        { name: "自定义", value: "custom" }
      ],
      controlsType: "create",
      controlsListVisible: false,
      customizeNumberLikesVisible: false,
      visible_range_options,
      rowInfo: {},
      statisticsParams: {
        department_ids: [],
        start_date: "",
        end_date: "",
        date: []
      },
      statisticsList: [
        [
          {
            label: "今日点赞(人)",
            value: "-",
            key: "today_likes_count"
          },
          {
            label: "今日浏览(次)",
            value: "-",
            key: "today_views_count"
          },
          {
            label: "总点赞数(人)",
            value: "-",
            key: "total_likes_count"
          },
          {
            label: "总浏览量(次)",
            value: "-",
            key: "total_views_count"
          }
        ],
        [
          {
            label: "点赞数(人)",
            value: "-",
            key: "day_likes_count"
          },
          {
            label: "浏览量(次)",
            value: "-",
            key: "day_views_count"
          }
        ]
      ],
      params: {
        page: 1,
        page_size: 10,
        publish_status: [],
        index: "",
        operator_ids: undefined,
        publish_ids: undefined,
        publish_start_time: undefined,
        publish_end_time: undefined,
        department_ids: undefined,
        visibility_scope: undefined,
        auditor_id: undefined,
        verify_display: undefined
      },
      searchTitle: [
        {
          props: "index",
          label: "编号",
          type: "input",
          show: true
        },
        {
          props: "operator_ids",
          label: "创建人",
          type: "course_staff",
          show: true,
          is_leave: true,
          selectOptions: []
        },
        {
          props: "publish_ids",
          label: "发布人",
          type: "course_staff",
          show: true,
          is_leave: true,
          selectOptions: []
        },
        {
          props: "publish_time",
          label: "发布时间",
          type: "date",
          has_options: true
        },
        {
          props: "publish_status",
          label: "发布状态",
          type: "mutipleSelect",
          show: true,
          selectOptions: publish_status_options
        },
        {
          props: "visibility_scope",
          label: "可见范围",
          type: "mutipleSelect",
          show: true,
          selectOptions: visible_range_options
        },
        {
          props: "auditor_id",
          label: "审核人",
          type: "course_staff",
          show: true,
          is_leave: true,
          selectOptions: []
        },
        {
          props: "verify_display",
          type: "bool",
          content: "仅展示待审核内容",
          show: true
        }
      ],
      list: [],
      selectedRows: [],
      loading: false,
      total: 0,
      publish_status_options,
      table_title: [
        {
          props: "index",
          fixed: "left",
          label: "编号",
          width: 60,
          show: true
        },
        {
          props: "content",
          label: "内容",
          fixed: "left",
          width: 200,
          show_overflow_tooltip: true,
          show: true
        },
        {
          props: "image_url",
          label: "图片/视频",
          width: 120,
          show: true
        },
        {
          props: "operator_name",
          label: "创建人",
          width: 100,
          show: true
        },
        {
          props: "publish_status",
          label: "发布状态",
          width: 100,
          show: true
        },
        {
          props: "publish_name",
          label: "发布人",
          width: 100,
          show: true
        },
        {
          props: "auditor_name",
          label: "审核人",
          width: 100,
          show: true
        },
        {
          props: "publish_time",
          label: "发布时间",
          width: 180,
          show: true
        },
        {
          props: "department_name",
          label: "可见校区",
          width: 180,
          show: true,
          show_overflow_tooltip: true
        },
        {
          props: "visibility_scope",
          label: "可见范围",
          width: 100,
          show: true,
          show_overflow_tooltip: true
        },
        {
          props: "top_status",
          label: "置顶状态",
          width: 80,
          show: true
        },
        {
          props: "likes_count",
          label: "点赞人数(+虚拟)",
          width: 140,
          show: true
        },
        {
          props: "today_views",
          label: "今日浏览",
          width: 100,
          show: true
        },
        {
          props: "total_views",
          label: "总浏览",
          width: 100,
          show: true
        }
      ]
    };
  },
  created() {
    this.statisticsParams.department_ids = this.school_id;
    this.params.department_ids = this.school_id;
    let date = [];

    const start = new Date(new Date(new Date().toLocaleDateString()).getTime());
    const end = new Date(
      new Date(new Date().toLocaleDateString()).getTime() +
        24 * 60 * 60 * 1000 -
        1
    );
    const dayNum = this.choose === "week" ? 6 : 29;
    start.setTime(end.getTime() - 3600 * 1000 * 24 * dayNum);
    date = [
      moment(start).format("YYYY-MM-DD"),
      moment(end).format("YYYY-MM-DD")
    ];
    this.statisticsParams.date = date;
    if (date.length) {
      this.statisticsParams.start_date = date[0];
      this.statisticsParams.end_date = date[1];
    } else {
      this.statisticsParams.start_date = "";
      this.statisticsParams.end_date = "";
    }
    this.params.visibility_scope = this.visible_range_options.map(
      (item) => item.id
    );
    this.params.publish_status = this.publish_status_options.map(
      (item) => item.id
    );
    this.init();
    if (this.$_has({ m: "nedawCircle", o: "listStatistics" })) {
      this.getIndexStatistics();
    }
  },
  methods: {
    getIndexStatistics() {
      nedawCircleManagement
        .ListStatistics(this.statisticsParams)
        .then((res) => {
          const statisticsList = this.statisticsList;
          statisticsList.forEach((item) => {
            item.forEach((item1) => {
              item1.value = res.data[item1.key];
            });
          });
        });
    },
    changeTime(val, index) {
      this.hover_index = index;
      this.choose = val;

      const start = new Date(
        new Date(new Date().toLocaleDateString()).getTime()
      );
      const end = new Date(
        new Date(new Date().toLocaleDateString()).getTime() +
          24 * 60 * 60 * 1000 -
          1
      );
      let date = [];
      if (val !== "custom") {
        const dayNum = val === "week" ? 6 : 29;
        start.setTime(end.getTime() - 3600 * 1000 * 24 * dayNum);

        date = [
          moment(start).format("YYYY-MM-DD"),
          moment(end).format("YYYY-MM-DD")
        ];
        this.statisticsParams.date = date;
      } else {
        date = [];
      }
      // const date = quickDate.GetDate(val);
      if (date.length) {
        this.statisticsParams.start_date = date[0];
        this.statisticsParams.end_date = date[1];
      } else {
        this.statisticsParams.start_date = "";
        this.statisticsParams.end_date = "";
      }

      this.statisticsParams.date = date;
      this.$nextTick(() => {
        this.getIndexStatistics();
      });
    },
    changeDate(val) {
      this.hover_index = 2;
      if (val?.length) {
        this.statisticsParams.start_date = val[0];
        this.statisticsParams.end_date = val[1];
      } else {
        this.statisticsParams.start_date = "";
        this.statisticsParams.end_date = "";
      }
      this.getIndexStatistics();
    },
    getRowKeys(row) {
      return row.id;
    },
    init() {
      nedawCircleManagement.List(this.params).then((res) => {
        const { code, data, message } = res;
        if (code === 0) {
          this.list = data.results.map((i) => ({
            ...i,
            image_url: i.image_url?.length ? i.image_url : []
          }));
          this.$refs.table.clearSelection();
          this.total = data.count;
        } else {
          this.$message.error(message);
        }
      });
    },
    searchList() {
      this.params.page = 1;
      if (this.params.publish_time && this.params.publish_time.length) {
        this.params.publish_start_time = this.params.publish_time[0];
        this.params.publish_end_time = this.params.publish_time[1];
      } else {
        this.params.publish_start_time = "";
        this.params.publish_end_time = "";
      }
      console.log(this.params);
      this.init();
    },
    reset() {
      this.params = {
        page: 1,
        page_size: 10,
        publish_status: this.publish_status_options.map((item) => item.id),
        index: "",
        operator_ids: undefined,
        publish_ids: undefined,
        publish_time: undefined,
        publish_start_time: "",
        publish_end_time: "",
        department_ids: this.school_id,
        visibility_scope: this.visible_range_options.map((item) => item.id),
        auditor_id: undefined,
        verify_display: false
      };
      this.init();
    },
    openPreview(row) {
      console.log(row, "row");
      this.preview_url = row.image_url[0].url;
      this.poster = row.cover_url;
      this.preview_visible = true;
    },
    add() {
      this.controlsType = "create";
      this.controlsListVisible = true;
    },
    openCustomizeNumberLikes(row) {
      this.rowInfo = row;
      this.customizeNumberLikesVisible = true;
    },
    currentChange(val) {
      this.params.page = val;
      this.init();
    },
    handleSizeChange(size) {
      this.params.page_size = size;
      this.init();
    },
    openInfo(row) {
      this.rowInfo = row;
      this.controlsType = "info";
      this.controlsListVisible = true;
    },
    getMediaType(image_url) {
      console.log(image_url);
      if (image_url.length <= 1) {
        const videoTypes = ["mp4", "mpeg", "webm", "ogg", "mov"];
        if (
          videoTypes.some((type) => image_url[0]?.url?.endsWith(`.${type}`))
        ) {
          return "video";
        } else {
          return "image";
        }
      } else {
        return "image";
      }
    },
    openContent(row) {
      nedawCircleManagement.Detail({ id: row.id }).then((res) => {
        if (res.code === 0) {
          const { content, image_url, cover_url, publish_name } = res.data;
          this.rowInfo = {
            content,
            cover_url,
            publish_name,
            image_urls: image_url.map((item) => ({
              name: item,
              url: item.url || ""
            }))
          };
          this.mediaType = this.getMediaType(image_url);
          this.previewVisible = true;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    updateRow(row, type) {
      this.controlsType = "update";
      this.controlsListVisible = true;
      this.rowInfo = row;
    },
    deleteRow(row) {
      const tips = "确认删除当前内容？";
      this.$confirm(tips, "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        nedawCircleManagement
          .DeleteMoment({
            id: row.id
          })
          .then((res) => {
            if (res.code === 0) {
              this.init();
              this.$message.success("操作成功");
            } else {
              this.$message.error(res.message);
            }
          });
      });
    },
    async changeTopStatus(row) {
      row.top_status = row.top_status === 2 ? 1 : 2;
      let top_status = 2;
      if (row.top_status === 2) {
        const { data, message, code } =
          await nedawCircleManagement.GetTopStatus({
            id: row.id
          });
        console.log(code, data, message);
        top_status = data.top_status;
      }
      const tips =
        row.top_status === 1
          ? "确认取消置顶当前内容？"
          : top_status === 2
          ? "置顶内容将在校区圈内置顶展示，确认置顶当前内容？"
          : "当前校区已有置顶内容，如置顶本条内容，将取消当前置顶内容，确认置顶？";
      this.$confirm(tips, "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        nedawCircleManagement
          .UpdateTopStatus({
            id: row.id,
            top_status: row.top_status === 2 ? 1 : 2
          })
          .then((res) => {
            if (res.code === 0) {
              this.init();
              this.$message.success("操作成功");
            } else {
              row.top_status = 2;
              this.$message.error(res.message);
            }
          });
      });
    },
    handleSelectionChange(checkedRows) {
      console.log(checkedRows);
      this.selectedRows = checkedRows;
    },
    batchAudit(publish_status) {
      const tips =
        publish_status === 4
          ? `确认审核通过${this.selectedRows.length}条内容？`
          : `确认审核驳回${this.selectedRows.length}条内容？`;
      this.$confirm(tips, "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        nedawCircleManagement
          .UpdateVerify({
            id: this.selectedRows.map((item) => item.id),
            publish_status
          })
          .then((res) => {
            if (res.code === 0) {
              this.init();
              this.$refs.table.clearSelection();
              this.$message.success("操作成功");
            } else {
              this.$message.error(res.message);
            }
          });
      });
    },
    updateStatus(row, publish_status) {
      const tips =
        publish_status === 2
          ? "发布内容后，将在聂道圈内进行展示，确认发布？"
          : row.top_status === 1
          ? "您下架的内容是置顶内容，如下架后将取消置顶，确认下架？"
          : "下架内容后小程序端将不再显示，确认下架？";
      this.$confirm(tips, "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        nedawCircleManagement
          .UpdateVerify({
            id: [row.id],
            publish_status
          })
          .then((res) => {
            if (res.code === 0) {
              this.init();
              this.$message.success("操作成功");
            } else {
              this.$message.error(res.message);
            }
          });
      });
    },
    statistics(row) {
      this.$router.push({
        path: "/nedawCircleStatistics",
        query: {
          id: row.id
        }
      });
    }
  }
};
</script>

<style scoped lang="less">
::v-deep .resource-preview {
  video {
    height: 100%;
    object-fit: none;
  }
}
.nedaw-circle-management {
  .statistics {
    .statistics-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 50px;
    }
    .statistics-content {
      display: flex;
      .box-card {
        margin-top: 16px;
        margin-right: 30px;
        flex: 1;
        &:last-child {
          margin-right: 0;
        }
      }
      .statistics-title-item {
        display: flex;
        align-items: center;
        height: 32px;
        font-weight: 500;
        font-size: 19px;
        margin-bottom: 10px;
        .btn--left {
          background: #ebf4ff;
          height: 32px;
          border-radius: 4px;
          padding: 4px 6px;
          width: 400px;
          display: flex;
          align-items: center;
          margin-right: 16px;
          box-sizing: border-box;
          span {
            display: inline-block;
            width: 25%;
            height: 24px;
            text-align: center;
            line-height: 24px;
            color: @text-color_second;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: @text-size_normal;
            font-family: @text-famliy_medium;
            cursor: pointer;
            transition: 0.3s;
            margin-right: 6px;
          }
          span:last-child {
            margin-right: 0;
          }
          span.choose {
            color: #fff;
            background: @base-color;
            transition: 0.3s;
          }
        }
      }
      .statistics-item {
        display: flex;
        background: #fff;
        // padding: 15px 30px;
        border-radius: 8px;
        justify-content: space-around;
        &:last-child {
          margin-right: 0;
        }
        .statistics-item-block {
          margin-right: 36px;
          text-align: center;
          &:last-child {
            margin-right: 0;
          }
          .statistics-item-value {
            font-size: 32px;
            font-weight: 500;
            color: #333;
          }
          .statistics-item-label {
            font-size: 16px;
            font-weight: 500;
            color: #333;
          }
        }
      }
    }
  }
}
::v-deep .el-select {
  .el-input--suffix {
    width: 240px;
  }
}
</style>
