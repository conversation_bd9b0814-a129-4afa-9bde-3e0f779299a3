<template>
  <el-dialog
    title="自定义点赞数量"
    :visible.sync="visible"
    width="width"
    :before-close="cancel"
  >
    <el-form :model="form">
      <el-form-item required label="自定义点赞数量">
        <el-input
          v-model="form.custom_likes_count"
          :maxlength="4"
          placeholder="请输入自定义点赞数量"
        />
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button class="tg-button--plain" type="plain" @click="cancel"
        >取 消</el-button
      >
      <el-button class="tg-button--primary" type="primary" @click="save"
        >确 定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import nedawCircleManagement from "@/api/nedawCircleManagement";
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      form: {
        custom_likes_count: ""
      }
    };
  },
  mounted() {
    this.form.custom_likes_count = this.row.custom_likes_count;
  },
  methods: {
    cancel() {
      this.form.custom_likes_count = 0;
      this.$emit("update:visible", false);
    },
    save() {
      const likesCount = Number(this.form.custom_likes_count);
      console.log(likesCount);
      if (Number.isNaN(likesCount)) {
        this.$message.error("请输入正整数");
        return;
      }
      if (!Number.isInteger(likesCount)) {
        this.$message.error("请输入整数");
        return;
      }
      if (likesCount < 0) {
        this.$message.error("请输入大于0的数字");
        return;
      }
      if (likesCount > 9999) {
        this.$message.error("请输入小于9999的数字");
        return;
      }
      nedawCircleManagement
        .UpdateCustomCount({
          id: this.row.id,
          custom_likes_count: Number(this.form.custom_likes_count)
        })
        .then((res) => {
          if (res.code === 0) {
            this.$emit("update:visible", false);
            this.$emit("refresh");
            this.$message.success("操作成功");
          } else {
            this.$message.error(res.message);
          }
        });
    }
  }
};
</script>

<style></style>
