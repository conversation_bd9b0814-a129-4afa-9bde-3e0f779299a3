<template>
  <el-dialog
    title="查看"
    :visible.sync="visible"
    :before-close="dialogBeforeClose"
  >
    <div class="tg-table__box">
      <div class="tg-box--border"></div>
      <el-table
        ref="table"
        class="tg-table"
        tooltip-effect="dark"
        :data="list"
        :cell-style="{ borderRightColor: '#e0e6ed75' }"
        :header-cell-style="{ borderRightColor: '#e0e6ed75' }"
        border
      >
        <template v-for="(item, index) in table_title">
          <el-table-column
            v-if="item.show"
            :key="index"
            :prop="item.props"
            :width="item.width"
            :label="item.label"
          >
            <template slot-scope="scope">
              <div v-if="item.props === 'updated_at'">
                {{
                  moment(scope.row[scope.column.property]).format(
                    "YYYY-MM-DD HH:mm:ss"
                  )
                }}
              </div>
              <div v-else-if="item.props === 'mobile'">
                <mobileHyposensitization
                  :mobileTemInfo="{
                    row: scope.row,
                    has_eye_limit: scope.row?.has_eye_limit,
                    mobile: scope.row.mobile
                  }"
                ></mobileHyposensitization>
              </div>
              <div v-else-if="item.props === 'student_type'">
                {{
                  visible_range_options.find(
                    (item) => item.id === scope.row[scope.column.property]
                  )?.name
                }}学员
              </div>
              <span v-else>{{ scope.row[scope.column.property] }}</span>
            </template>
          </el-table-column>
        </template>
      </el-table>
      <div class="tg-pagination">
        <span class="el-pagination__total">共 {{ total }} 条</span>
        <el-pagination
          background
          layout="prev, pager, next,jumper"
          :total="total"
          :page-size="params.page_size"
          :current-page="params.page"
          @current-change="currentChange"
        >
        </el-pagination>
      </div>
    </div>
    <div slot="footer">
      <el-button
        class="tg-button--primary"
        type="primary"
        @click="dialogBeforeClose"
        >关闭</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { visible_range_options } from "@/public/dict";
import nedawCircleManagement from "@/api/nedawCircleManagement";
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    date: {
      type: String,
      default: ""
    },
    type: {
      type: String,
      default: "likes_count"
    }
  },
  data() {
    return {
      visible_range_options,
      table_title: [
        {
          label: "学号",
          props: "student_number",
          show: true
        },
        {
          label: "姓名",
          props: "student_name",
          show: true
        },
        {
          label: "绑定关系",
          props: "binding_relation",
          show: true
        },
        {
          label: "手机号",
          props: "mobile",
          show: true
        },
        {
          label: "学员状态",
          props: "student_type",
          show: true
        },
        {
          label: "查看时间/点赞时间",
          props: "updated_at",
          show: true,
          width: 180
        }
      ],
      list: [],
      params: {
        page: 1,
        page_size: 10,
        moment_id: ""
      },
      total: 0
    };
  },
  created() {
    this.params.moment_id = this.$route.query.id;
    this.params.date = this.date;
    this.getList();
  },
  methods: {
    async getList() {
      const res =
        this.type === "likes_count"
          ? await nedawCircleManagement.StudentLikeList(this.params)
          : await nedawCircleManagement.StudentViewList(this.params);
      if (res.code === 0) {
        this.total = res.data.count;
        this.list = res.data.results;
      } else {
        this.$message.error(res.message);
      }
    },
    dialogBeforeClose() {
      this.$emit("update:visible", false);
    },
    currentChange(page) {
      this.params.page = page;
      this.getList();
    }
  }
};
</script>

<style></style>
