<template>
  <el-dialog
    :title="titles[type]"
    :visible.sync="visible"
    width="72%"
    :before-close="dialogBeforeClose"
  >
    <div style="display: flex">
      <div class="form">
        <el-form :model="form" ref="form" :disabled="type === 'info'">
          <el-form-item label="编辑内容">
            <el-input
              v-model="form.content"
              :disabled="isDetail"
              :rows="10"
              maxlength="500"
              type="textarea"
              placeholder="写点什么..."
            ></el-input>
            <div
              style="
                color: #999;
                font-size: 12px;
                text-align: right;
                margin-bottom: 10px;
              "
            >
              {{ form.content.length }}/500
            </div>
          </el-form-item>
          <template v-if="['', 'image'].includes(defaultMediaType)">
            <el-form-item class="upload-img" label="上传图片">
              <div style="color: #999; font-size: 12px; margin-bottom: 10px">
                仅支持上传png/jpg/jpeg/webp格式图片
              </div>
              <div :class="[isDetail ? 'is-hidden' : '']">
                <el-upload
                  action="#"
                  list-type="picture-card"
                  accept="image/png,image/jpeg,image/webp"
                  :file-list="form.image_urls"
                  ref="uploadImgs"
                  :disabled="isDetail"
                  :before-upload="(file) => beforeUpload(file, 'image')"
                  :http-request="uploadImg"
                >
                  <i slot="default" class="el-icon-plus"></i>
                  <div style="height: 100%" slot="file" slot-scope="{ file }">
                    <img
                      class="el-upload-list__item-thumbnail"
                      :src="file?.url"
                      alt=""
                    />
                    <span class="el-upload-list__item-actions">
                      <span
                        class="el-upload-list__item-preview"
                        @click="handlePictureCardPreview(file?.url)"
                      >
                        <i class="el-icon-zoom-in"></i>
                      </span>
                      <span
                        v-if="!isDetail"
                        class="el-upload-list__item-delete"
                        @click="handleRemove(file)"
                      >
                        <i class="el-icon-delete"></i>
                      </span>
                    </span>
                  </div>
                </el-upload>
              </div>
            </el-form-item>
          </template>
          <template v-if="['', 'video'].includes(defaultMediaType)">
            <el-form-item label="上传视频" prop="content">
              <div style="color: #999; font-size: 12px; margin-bottom: 10px">
                仅支持上传mp4/mpeg/webm/mov格式视频
              </div>
              <el-upload
                class="upload-demo"
                style="margin-left: 66px"
                action="#"
                :disabled="isDetail"
                accept=".mp4, .webm, .mov"
                :show-file-list="false"
                ref="uploadImgs"
                :before-upload="(file) => beforeUpload(file, 'video')"
                :http-request="handleUpload"
              >
                <el-button
                  type="primary"
                  style="margin-bottom: 10px"
                  :loading="uploadLoading"
                  :disabled="isDetail"
                  size="medium"
                  >上传</el-button
                >
                <span class="el-upload__tip">注意：视频文件不能大于200M</span>
              </el-upload>
              <p
                style="
                  line-height: 20px;
                  padding-left: 66px;
                  font-size: 12px;
                  margin-top: 0;
                "
              >
                {{ form.image_urls[0]?.url }}
                <el-button
                  v-if="form.image_urls[0]?.url"
                  type="text"
                  @click="handleRemoveVideo"
                  >删除</el-button
                >
              </p>
            </el-form-item>
            <el-form-item
              :required="!!form.back_url"
              label="视频封面"
              class="tg-box--margin video-cover"
            >
              <div style="color: #999; font-size: 12px; margin-bottom: 10px">
                仅支持上传png/jpg/jpeg/webp格式图片
              </div>
              <el-upload
                class="el-upload avatar-uploader el-upload--picture-card"
                action="#"
                :disabled="isDetail"
                :show-file-list="false"
                accept="image/png, image/jpeg,image/webp"
                :before-upload="(file) => beforeUpload(file, 'image')"
                :http-request="uploadImgPoster"
              >
                <img
                  v-if="form.cover_url"
                  :src="form.cover_url"
                  class="avatar"
                />
                <i
                  v-else
                  slot="default"
                  class="el-icon-plus avatar-uploader-icon"
                ></i>
              </el-upload>
              <div
                v-if="form.cover_url"
                style="width: 270px; display: flex; justify-content: center"
              >
                <el-button
                  v-if="type !== 'info'"
                  type="text"
                  @click="handleRemove('cover_url')"
                  >删除</el-button
                >
                <el-button
                  type="text"
                  @click="handlePictureCardPreview(form.cover_url)"
                  >查看</el-button
                >
              </div>
              <el-dialog
                title="预览"
                :append-to-body="true"
                :visible.sync="dialogPosterVisible"
              >
                <img width="100%" :src="this.form.cover_url" alt="" />
              </el-dialog>
            </el-form-item>
          </template>
          <el-form-item required label="选择校区">
            <el-input
              placeholder="请选择校区"
              readonly
              show-word-limit
              :disabled="isDetail"
              :validate-event="false"
              @click.native="isDetail ? '' : (school_tree_visible = true)"
              v-model="form.department_name"
              class="tg-select tg-select--dialog custom-input"
              @mouseenter.native="school_flag = true"
              @mouseleave.native="school_flag = false"
            >
              <img
                slot="suffix"
                :src="
                  !school_flag
                    ? require('@/assets/图片/icon_more.png')
                    : require('@/assets/图片/icon_more_ac.png')
                "
                alt=""
                class="btn__img--dotted"
              />
            </el-input>
            <school-tree
              :flag.sync="school_tree_visible"
              v-if="school_tree_visible"
              :id.sync="form.department_ids"
              :name.sync="form.department_name"
              :type="'chooseSchool'"
              :use_store_options="false"
            >
            </school-tree>
          </el-form-item>
          <el-form-item required label="可见范围">
            <div>
              <el-radio @change="handleRadioChange" v-model="radio" label="open"
                >公开</el-radio
              >
              <el-radio
                @change="handleRadioChange"
                v-model="radio"
                label="other"
                >部分状态用户可见</el-radio
              >
            </div>
            <div class="checkbox-group" v-if="radio === 'other'">
              <el-checkbox
                :indeterminate="isIndeterminate"
                v-model="checkAll"
                @change="handleCheckAllChange"
                >全选</el-checkbox
              >
              <el-checkbox-group
                v-model="form.visibility_scope"
                @change="handleCheckedCitiesChange"
              >
                <el-checkbox
                  v-for="item in visibleRangeOptions"
                  :label="item.id"
                  :key="item.id"
                >
                  {{ item.name }}
                </el-checkbox>
              </el-checkbox-group>
            </div>
            <!-- <el-select
              :disabled="isDetail"
              class="custom-input"
              multiple
              v-model="form.visibility_scope"
              placeholder="请选择可见范围"
            >
              <el-option
                v-for="item in visibleRangeOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select> -->
          </el-form-item>
        </el-form>
      </div>
      <preview :form="form" :mediaType="mediaType"
        ><div class="title">预览</div></preview
      >
      <el-dialog
        title="预览"
        :append-to-body="true"
        :visible.sync="dialogVisible"
      >
        <img width="100%" :src="dialogImageUrl" alt="" />
      </el-dialog>
    </div>
    <div slot="footer" v-if="type !== 'info'">
      <el-button
        class="tg-button--plain"
        type="plain"
        @click="dialogBeforeClose"
        >取消</el-button
      >
      <el-button
        class="tg-button--primary"
        type="primary"
        :loading="btnLoading"
        v-throttle="() => save(1)"
        >{{
          btnLoading
            ? btnLoadingType === "upload"
              ? "文件上传中..."
              : "保存中..."
            : "保存"
        }}</el-button
      >
      <el-button
        :loading="btnLoading"
        v-if="row.publish_status === 1 || row.publish_status === undefined"
        class="tg-button--primary"
        type="primary"
        v-throttle="() => save(2)"
        >{{
          btnLoading
            ? btnLoadingType === "upload"
              ? "文件上传中..."
              : "发布中..."
            : "发布"
        }}</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import schoolTree from "@/components/schoolTree/schoolTree.vue";
import nedawCircleManagement from "@/api/nedawCircleManagement";
import { v4 as uuidv4 } from "uuid";
import { visible_range_options } from "@/public/dict";
import preview from "@/views/nedawCircleManagement/components/preview.vue";
export default {
  components: {
    schoolTree,
    preview
  },
  props: {
    type: {
      type: String,
      default: "create"
    },
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    isDetail() {
      return this.type === "info";
    },
    info() {
      return this.$store.getters.doneGetLoginInfo;
    },
    defaultMediaType() {
      console.log(this.form.image_urls, "this.form.image_urls");
      const videoTypes = ["mp4", "mpeg", "webm", "mov"];
      if (this.form.image_urls.length === 0) {
        return "";
      } else {
        const mediaType = this.form.image_urls.some((item) =>
          videoTypes.some((type) =>
            item?.url
              ? item?.url.endsWith(`.${type}`)
              : item?.endsWith(`.${type}`)
          )
        );
        if (mediaType) {
          return "video";
        } else {
          return "image";
        }
      }
    }
  },
  data() {
    return {
      radio: "open",
      uploadLoading: false,
      checkAll: false,
      isIndeterminate: false,
      btnLoading: false,
      btnLoadingType: "",
      titles: {
        create: "新增",
        update: "编辑",
        info: "详情"
      },
      fileList: [],
      dialogPosterVisible: false,
      form: {
        content: "",
        cover_url: "",
        file_name: "",
        file_type: "",
        department_ids: [],
        department_name: [],
        image_urls: [],
        visibility_scope: ["open"],
        publish_status: 1
      },
      mediaType: "",
      phoneBg: require("@/views/questionManagement/imgs/phone-bg.webp"),
      visibleRangeOptions: visible_range_options.filter(
        (item) => item.id !== "open"
      ),
      previewList: [],
      dialogVisible: false,
      dialogImageUrl: "",
      school_tree_visible: false,
      school_flag: false
    };
  },
  created() {
    this.Oss.getAliyun();
    if (this.type !== "create") {
      this.getDetail();
    }
  },
  methods: {
    // 获取图片宽高
    getImageDimensions(file) {
      return new Promise((resolve, reject) => {
        const img = new Image();
        const reader = new FileReader();

        reader.onload = (e) => {
          img.src = e.target.result;
        };

        img.onload = () => {
          resolve({
            width: img.width,
            height: img.height
          });
        };

        img.onerror = (error) => {
          reject(error);
        };

        reader.readAsDataURL(file);
      });
    },
    async getVideoDimensions(file) {
      return new Promise((resolve) => {
        const video = document.createElement("video");
        video.src = URL.createObjectURL(file);

        video.onloadedmetadata = () => {
          resolve({
            width: video.videoWidth,
            height: video.videoHeight
          });
          URL.revokeObjectURL(video.src); // 释放内存
        };
      });
    },
    handleRadioChange() {
      if (this.radio === "other") {
        this.form.visibility_scope = [];
        this.checkAll = false;
        this.isIndeterminate = false;
      } else {
        this.form.visibility_scope = ["open"];
      }
      console.log(this.form.visibility_scope, "this.form.visibility_scope");
    },
    handleCheckAllChange(val) {
      this.form.visibility_scope = val
        ? this.visibleRangeOptions.map((item) => item.id)
        : [];
      this.isIndeterminate = false;
      console.log(this.form.visibility_scope, "this.form.visibility_scope");
    },
    handleCheckedCitiesChange(value) {
      console.log(value, "value");
      const checkedCount = value.length;
      this.form.visibility_scope = value;
      this.checkAll = checkedCount === this.visibleRangeOptions.length;
      this.isIndeterminate =
        checkedCount > 0 && checkedCount < this.visibleRangeOptions.length;
    },
    getDetail() {
      console.log(this.row);
      nedawCircleManagement.Detail({ id: this.row.id }).then((res) => {
        if (res.code === 0) {
          const {
            content,
            image_url,
            visibility_scope,
            cover_url,
            department_ids,
            id,
            publish_name,
            department_name
          } = res.data;
          this.form = {
            id,
            content,
            cover_url,
            image_urls: image_url.map((item) => ({
              url: item.url || "",
              width: item.width,
              height: item.height,
              size: item.size
            })),
            visibility_scope,
            department_ids,
            department_name,
            publish_name
          };
          if (visibility_scope.length === 1 && visibility_scope[0] === "open") {
            this.radio = "open";
          } else {
            this.radio = "other";
          }
          if (image_url.length <= 1) {
            const videoTypes = ["mp4", "mpeg", "webm", "mov"];
            if (
              videoTypes.some((type) =>
                image_url[0]?.url
                  ? image_url[0]?.url.endsWith(`.${type}`)
                  : image_url[0]?.endsWith(`.${type}`)
              )
            ) {
              this.mediaType = "video";
            } else {
              this.mediaType = "image";
            }
          } else {
            this.mediaType = "image";
          }
        } else {
          this.$message.error(res.message);
        }
      });
    },
    uploadImgPoster(upload) {
      const f = upload.file;
      const suffix = f.name.match(/[^.]+$/)[0];
      const name = `${uuidv4()}.${suffix}`;
      const copyFile = new File([f], name);
      this.Oss.uploadFile(copyFile).then((res) => {
        if (res.code === 0) {
          this.fileList.push({
            name: res.objectKey,
            url: res.url
          });
          this.form.cover_url = res.url;
        }
      });
    },
    readFile(file) {
      return new Promise((resolve) => {
        const reader = new FileReader();
        reader.readAsBinaryString(file);
        reader.onload = (ev) => {
          resolve(ev.target.result);
        };
      });
    },
    async handleUpload(upload) {
      this.btnLoading = true;
      this.uploadLoading = true;
      this.btnLoadingType = "upload";
      const f = upload.file;
      const { width, height } = await this.getVideoDimensions(f);
      console.log(width, height, "width, height");
      const orginName = f.name.substring(0, f.name.lastIndexOf("."));
      const suffix = f.name.match(/[^.]+$/)[0];
      const name = `${orginName}_${uuidv4()}.${suffix}`;
      const copyFile = new File([f], name);
      if (suffix === "sgf") {
        // sgf 棋谱解析
        const data = await this.readFile(upload.file);
        this.$message.success("上传成功");
        this.mediaType = "video";
        this.form.image_urls = [
          { name: data, url: data, width, height, size: f.size }
        ];
        this.form.file_name = orginName;
        this.form.file_type = suffix;
        this.btnLoading = false;
        this.uploadLoading = false;
      } else {
        this.Oss.uploadFile(copyFile)
          .then((res) => {
            if (res.code === 0) {
              // this.$message.success("上传成功");
              this.mediaType = "video";
              this.form.image_urls = [
                {
                  name: res.objectKey,
                  url: res.url,
                  size: f.size,
                  width,
                  height
                }
              ];
              this.form.file_name = orginName;
              this.form.file_type = suffix;
              this.btnLoading = false;
              this.uploadLoading = false;
            } else {
              this.btnLoading = false;
              this.uploadLoading = false;
              this.$message.error(res.msg);
            }
          })
          .catch(() => {
            this.btnLoading = false;
            this.uploadLoading = false;
          });
      }
    },
    beforeUpload(file, type) {
      const fileSuffix = file.name.substring(file.name.lastIndexOf(".") + 1);
      // const { uploadFiles } = this.$refs["uploadImgs"];
      console.log(file, this.form.image_urls.length, type);
      if (type === "image") {
        const whiteList = ["png", "jpg", "jpeg", "webp"];
        if (whiteList.indexOf(fileSuffix) === -1) {
          this.$message.info("上传图片格式仅支持：" + whiteList.join(","));
          return false;
        }
        if (this.form.image_urls.length >= 9) {
          this.$message.info("最多只能上传9张图片");
          return false;
        }
      } else {
        const whiteList = ["mp4", "mpeg", "webm", "mov"];
        if (whiteList.indexOf(fileSuffix) === -1) {
          this.$message.info("上传视频格式仅支持：" + whiteList.join(","));
          return false;
        }
        const isLt500M = file.size / 1024 / 1024 > 200;
        if (isLt500M) {
          this.$message.info("上传视频大小不能超过200MB!");
          return false;
        }
      }
      // const isLt20M = file.size / 1024 / 1024 > 20;
      // if (isLt20M) {
      //   this.$message.info("上传图片大小不能超过 20MB!");
      //   return false;
      // }
    },
    async uploadImg(upload) {
      const { width, height } = await this.getImageDimensions(upload.file);
      console.log(width, height, "width, height");
      const f = upload.file;
      const suffix = f.name.match(/[^.]+$/)[0];
      const name = `${uuidv4()}.${suffix}`;
      const copyFile = new File([f], name);
      console.log(suffix, upload, "upload");
      this.btnLoading = true;
      this.btnLoadingType = "upload";
      this.Oss.uploadFile(copyFile).then((res) => {
        if (res.code === 0) {
          console.log("done");
          this.btnLoading = false;
          this.mediaType = "image";
          this.form.image_urls.push({
            width,
            height,
            size: f.size,
            name: res.objectKey,
            url: res.url
          });
          console.log(this.form.image_urls, "this.form.image_url");
        }
      });
    },
    handleRemove(file) {
      if (file === "cover_url") {
        this.form.cover_url = "";
      } else {
        this.form.image_urls.map((item, index) => {
          if (file.uid === item.uid) {
            this.form.image_urls.splice(index, 1);
          }
        });
      }
    },
    handleRemoveVideo() {
      this.form.image_urls = [];
      this.form.file_name = "";
      this.form.file_type = "";
      this.mediaType = "";
    },
    handlePictureCardPreview(file, row) {
      console.log(file, row);
      this.dialogImageUrl = file;
      this.dialogVisible = true;
    },
    dialogBeforeClose() {
      this.$emit("update:visible", false);
    },
    async save(publish_status) {
      const params = this.$lodash.cloneDeep(this.form);
      console.log(params);
      if (!params.content.length && !params.image_urls.length) {
        this.$message.error("请输入内容或上传图片");
        return;
      }
      if (params.content.length > 500) {
        this.$message.error("内容长度不能超过500字");
        return;
      }
      if (params.cover_url && !params.image_urls.length) {
        this.$message.error("请上传视频");
        return;
      }
      if (params.department_ids.length === 0) {
        this.$message.error("请选择校区");
        return;
      }
      if (params.visibility_scope.length === 0) {
        this.$message.error("请选择可见范围");
        return;
      }
      this.btnLoadingType = "save";
      this.btnLoading = true;
      params.image_url = params.image_urls.map((item) => {
        return {
          width: item.width,
          height: item.height,
          size: item.size,
          url: item.url
        };
      });
      params.publish_status = publish_status;
      params.image_type = this.mediaType === "video" ? 2 : 1;
      params.department_ids =
        typeof params.department_ids === "string"
          ? params.department_ids.split(",")
          : params.department_ids;
      delete params.department_names;
      const res =
        this.type === "create"
          ? await nedawCircleManagement.Create(params)
          : await nedawCircleManagement.Update(params);
      if (res.code === 0) {
        this.$message.success("操作成功");
        this.$emit("refresh");
        this.dialogBeforeClose();
      } else {
        this.$message.error(res.message);
      }
      this.btnLoading = false;
    }
  }
};
</script>
<style scoped lang="less">
/deep/ .el-form-item {
  margin-bottom: 22px !important;
}
.is-hidden {
  /deep/ .el-upload--picture-card {
    display: none;
  }
}
.el-upload--picture-card {
  margin-left: 66px;
}
/deep/ .el-upload--text {
  width: 100%;
  text-align: left;
}
.video-cover {
  /deep/ .el-upload--text {
    text-align: center;
  }
}
.el-upload__tip {
  color: @base-red;
  margin-left: 6px;
}
.form {
  flex: 1;
}
.avatar {
  width: 100%;
  height: 145px;
  border-radius: 6px;
}
// 上传图片
.upload-img {
  /deep/ .el-form-item__content {
    padding-left: 66px;
  }
}
.checkbox-group {
  display: flex;
  align-items: center;
  /deep/ .el-checkbox {
    margin-right: 10px;
  }
}
</style>
