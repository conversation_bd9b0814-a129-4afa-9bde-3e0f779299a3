<template>
  <div class="preview">
    <slot></slot>
    <div
      class="preview-container"
      :style="{
        backgroundImage: `url(${phoneBg})`
      }"
    >
      <div class="header-txt">详情</div>
      <div class="preview-container-content">
        <div class="preview-container-content-left">
          <img src="@/assets/图片/icon_profile.png" class="profile" />
        </div>
        <div class="preview-container-content-right">
          <div class="name">
            {{ form.publish_name ? form.publish_name : info.name }}
          </div>
          <el-input
            v-model="form.content"
            disabled
            autosize
            resize="none"
            type="textarea"
          ></el-input>
          <div
            class="imgs"
            v-if="mediaType === 'image' && form.image_urls.length"
          >
            <el-image
              v-for="item in form.image_urls"
              :key="item.url"
              class="img-item"
              :src="item.url"
              :preview-src-list="form.image_urls.map((item) => item.url)"
            >
            </el-image>
          </div>
          <div
            class="video"
            v-if="mediaType === 'video' && form.image_urls.length"
          >
            <video
              style="max-width: 100%; max-height: 100%; object-fit: contain"
              controls
              :poster="form.cover_url"
            >
              <source
                v-for="item in [
                  'video/mp4',
                  'video/mpeg',
                  'video/webm',
                  'video/ogg'
                ]"
                :key="item"
                :src="form.image_urls[0].url"
                :type="item"
              />
              您的浏览器不支持 video 标签。
            </video>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    form: {
      type: Object,
      default: () => {}
    },
    mediaType: {
      type: String,
      default: ""
    }
  },
  computed: {
    info() {
      return this.$store.getters.doneGetLoginInfo;
    }
  },
  mounted() {
    console.log(this.form, "form");
  },
  data() {
    return {
      phoneBg: require("@/views/questionManagement/imgs/phone-bg.webp")
    };
  }
};
</script>

<style lang="less" scoped>
.preview {
  width: 390px;
  height: 669px;
  flex-grow: 0;
  flex-shrink: 0;
  overflow: hidden;
  .preview-container {
    width: 100%;
    height: 100%;
    padding: 105px 40px 67px 40px;
    background-position: 0 0;
    background-size: 100% 100%;
    position: relative;
    .header-txt {
      position: absolute;
      top: 77px;
      left: 50%;
      background-color: #fff;
      font-weight: 500;
      font-size: 16px;
      width: 60px;
      text-align: center;
      height: 30px;
      transform: translateX(-50%);
    }
  }
  .preview-container-content {
    padding: 15px;
    display: flex;
    height: 100%;
    overflow: auto;
    .preview-container-content-right {
      flex: 1;
    }
    .profile {
      width: 32px;
      height: 32px;
      margin-right: 16px;
      border-radius: 50%;
    }
    .name {
      font-size: 16px;
      font-weight: 600;
      color: #333333;
    }
    .content {
      font-size: 14px;
      color: #666666;
    }
    /deep/ .el-textarea__inner {
      background: none !important;
      color: #333 !important;
      border: none !important;
      padding: 0 !important;
    }
    .imgs {
      display: flex;
      flex-wrap: wrap;
      margin-top: 5px;
      .img-item {
        width: 73px;
        height: 73px;
        margin: 2px;
      }
    }
  }
}
::v-deep .el-textarea__inner::webkit-scrollbar {
  display: none;
}
</style>
