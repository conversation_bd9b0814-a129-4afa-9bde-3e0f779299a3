<template>
  <div class="statistics-page">
    <div class="tg-header__subtitle tg-info__back">
      <img src="@/assets/图片/icon_menu_down_ac.png" alt @click="onBack" />
      <span @click="onBack">返回</span>
    </div>
    <div class="header">
      <div class="survey-info">
        <div class="survey-name">{{ meta_info.operator_name }}</div>
        <div class="time-info">
          <div class="crate-time">编号：{{ meta_info.index }}</div>
          <div class="active-item">发布时间：{{ meta_info.publish_time }}</div>
        </div>
      </div>
    </div>
    <div class="statistics-block">
      <div class="title-block">
        <div class="bg"></div>
        <div class="title">数据概况</div>
      </div>
      <div class="statistics-list">
        <div
          :class="['statistics-item', item.key]"
          v-for="item in statisticsList"
          :key="item.key"
        >
          <div class="statistics-info">
            <div class="value">{{ item.value }}</div>
            <div class="label">{{ item.label }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="statistics-block">
      <div class="title-block">
        <div class="bg"></div>
        <div class="title">数据趋势</div>
      </div>
      <div class="statistics-content">
        <div class="statistics-search">
          <div class="statistics-search-item">
            <span class="label">数据指标</span>
            <div class="btn--left">
              <span
                v-for="(item, index) in dataIndexOptions"
                :key="index"
                @click="changeDataIndex(item.value, index)"
                :class="{ choose: dataIndex.includes(item.value) }"
                :style="{
                  background: dataIndex.includes(item.value) ? item.bgc : ''
                }"
                >{{ item.name }}</span
              >
            </div>
          </div>
          <div class="statistics-search-item">
            <span class="label">日期范围</span>
            <div class="btn--left">
              <span
                v-for="(item, index) in selectOptions"
                :key="index"
                @click="changeTime(item.value, index)"
                :class="{ choose: hover_index === index }"
                >{{ item.name }}</span
              >
            </div>
            <el-date-picker
              v-model="params.date"
              type="daterange"
              range-separator="至"
              :clearable="true"
              start-placeholder="开始日期"
              @change="changeDate($event)"
              end-placeholder="结束日期"
              popper-class="tg-date-picker tg-date--range"
              :pickerOptions="pickerOptions"
              value-format="yyyy-MM-dd"
            ></el-date-picker>
          </div>
        </div>
        <div class="echar-block">
          <div ref="lineEcharts" class="line-echarts"></div>
        </div>
        <div class="table-block">
          <div class="label">数据详情</div>
          <div class="tg-table__box">
            <div class="tg-box--border"></div>
            <el-table
              ref="table"
              class="tg-table"
              tooltip-effect="dark"
              show-summary
              :data="viewList"
              :cell-style="{ borderRightColor: '#e0e6ed75' }"
              :header-cell-style="{ borderRightColor: '#e0e6ed75' }"
              border
            >
              <template v-for="(item, index) in table_title">
                <el-table-column
                  v-if="item.show"
                  :key="index"
                  :prop="item.props"
                  :label="item.label"
                >
                  <template slot-scope="scope">
                    <div
                      v-if="['likes_count', 'views_count'].includes(item.props)"
                    >
                      <el-button
                        type="text"
                        @click="openDetail(item, scope.row)"
                      >
                        {{ scope.row[scope.column.property] }}
                      </el-button>
                    </div>
                    <span v-else>{{ scope.row[scope.column.property] }}</span>
                  </template>
                </el-table-column>
              </template>
            </el-table>
            <div class="tg-pagination">
              <span class="el-pagination__total">共 {{ total }} 条</span>
              <el-pagination
                background
                layout="prev, pager, next,jumper"
                :total="total"
                :page-size="pageSize"
                :current-page="1"
                @current-change="currentChange"
              >
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
    <statisticsDialog
      v-if="dialogVisible"
      :date="curDate"
      :visible.sync="dialogVisible"
      :type="dialogType"
    />
  </div>
</template>

<script>
import * as echarts from "echarts";
import nedawCircleManagement from "@/api/nedawCircleManagement";
// import { picker_options } from "@/public/datePickerOptions";
import statisticsDialog from "./dialog/statisticsDialog.vue";
import moment from "moment";
export default {
  components: {
    statisticsDialog
  },
  data() {
    return {
      meta_info: {},
      dataIndex: ["likes_count", "views_count"],
      hover_index: 0,
      choose: "week",
      // pickerOptions: picker_options,
      dataIndexOptions: [
        { name: "点赞", value: "likes_count", bgc: "#5470c6" },
        { name: "浏览", value: "views_count", bgc: "#91cc75" }
      ],
      selectOptions: [
        { name: "近七日", value: "week" },
        { name: "近30日", value: "month" },
        { name: "自定义", value: "custom" }
      ],
      curDate: "",
      params: {
        page: 1,
        page_size: 90,
        date: [],
        start_date: "",
        end_date: ""
      },
      pickerOptions: {
        disabledDate: (date) => {
          // 如果已经选择了开始日期
          if (this.params.start_date) {
            const startDate = new Date(this.params.start_date).getTime();
            const minDate = startDate - 90 * 24 * 60 * 60 * 1000; // 90 天前
            const maxDate = startDate + 90 * 24 * 60 * 60 * 1000; // 90 天后
            return date.getTime() < minDate || date.getTime() > maxDate;
          }
          return false;
        }
      },
      statisticsList: [
        {
          label: "今日点赞(人)",
          value: "",
          key: "today_likes_count"
        },
        {
          label: "今日浏览(次)",
          value: "",
          key: "today_views_count"
        },
        {
          label: "总点赞数(人)",
          value: "",
          key: "likes_count"
        },
        {
          label: "总浏览量(次)",
          value: "",
          key: "views_count"
        }
      ],
      lineEcharts: null,
      daily_statistics_list: [],
      pageSize: 5,
      viewList: [],
      table_title: [
        {
          label: "日期",
          props: "date",
          show: true
        },
        {
          label: "点赞(人)",
          props: "likes_count",
          show: true
        },
        {
          label: "浏览(次)",
          props: "views_count",
          show: true
        }
      ],
      dialogVisible: false,
      dialogType: ""
    };
  },
  created() {
    this.params.moment_id = this.$route.query.id;

    let date = [];

    const start = new Date(new Date(new Date().toLocaleDateString()).getTime());
    const end = new Date(
      new Date(new Date().toLocaleDateString()).getTime() +
        24 * 60 * 60 * 1000 -
        1
    );
    const dayNum = this.choose === "week" ? 6 : 29;
    start.setTime(end.getTime() - 3600 * 1000 * 24 * dayNum);
    date = [
      moment(start).format("YYYY-MM-DD"),
      moment(end).format("YYYY-MM-DD")
    ];
    this.params.date = date;
    if (date.length) {
      this.params.start_date = date[0];
      this.params.end_date = date[1];
    }
    this.getStatistics();
    this.getDailyStatistics();
  },
  methods: {
    onBack() {
      this.$router.go(-1);
    },
    getStatistics() {
      nedawCircleManagement
        .MomentsStatistics({
          id: this.$route.query.id
        })
        .then((res) => {
          this.meta_info = res.data;
          for (let i = 0; i < this.statisticsList.length; i++) {
            const item = this.statisticsList[i];
            item.value = res.data[item.key];
          }
        });
    },
    getDailyStatistics() {
      nedawCircleManagement.DailyStatistics(this.params).then((res) => {
        this.daily_statistics_list = res.data.results.daily_statistics_list.map(
          (i) => ({
            ...i,
            date: moment(i.date).format("YYYY-MM-DD")
          })
        );
        // 时间正序排列
        const list = res.data.results.daily_statistics_list.sort((a, b) => {
          return new Date(a.date) - new Date(b.date);
        });
        console.log(list, "list");
        console.log(res.data, "res.data");
        this.total = res.data.count;
        this.initLineOption(list);
        this.viewList = this.daily_statistics_list.slice(0, this.pageSize);
      });
    },
    initLineOption(data) {
      const seriesList = [
        {
          name: "点赞",
          type: "line",
          key: "likes_count",
          data: data.map((item) => item.likes_count)
        },
        {
          name: "浏览",
          type: "line",
          key: "views_count",
          data: data.map((item) => item.views_count)
        }
      ];
      const series = seriesList.filter((item) =>
        this.dataIndex.includes(item.key)
      );
      console.log(this.dataIndex, series, "series");
      const option = {
        tooltip: {
          trigger: "axis"
        },
        grid: {
          top: "3%",
          left: "3%",
          right: "3%",
          bottom: "7%",
          containLabel: true
        },
        toolbox: {
          feature: {
            saveAsImage: {
              show: false
            }
          }
        },
        dataZoom: [
          {
            orient: "horizontal",

            show: true, // 控制滚动条显示隐藏

            realtime: true, // 拖动滚动条时是否动态的更新图表数据

            height: 15, // 滚动条高度

            start: 0, // 滚动条开始位置（共100等份）

            bottom: "3%",

            zoomLock: true // 控制面板是否进行缩放
          },

          {
            type: "inside",

            brushSelect: true,

            start: 0,

            end: 100,

            xAxisIndex: [0]
          }
        ],
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: data.map((item) => item.date)
        },
        yAxis: {
          type: "value"
        },
        series
      };
      console.log(option, "option");
      this.lineEcharts = echarts.init(this.$refs.lineEcharts);
      this.lineEcharts.setOption(option, true);
      const that = this;
      this.$nextTick(() => {
        window.addEventListener("resize", () => {
          that.lineEcharts.resize();
        });
      });
    },
    changeDataIndex(val) {
      if (this.dataIndex.includes(val)) {
        this.dataIndex.splice(this.dataIndex.indexOf(val), 1);
      } else {
        this.dataIndex.push(val);
      }
      this.initLineOption(this.daily_statistics_list);
    },
    changeTime(val, index) {
      this.hover_index = index;
      this.choose = val;

      const start = new Date(
        new Date(new Date().toLocaleDateString()).getTime()
      );
      const end = new Date(
        new Date(new Date().toLocaleDateString()).getTime() +
          24 * 60 * 60 * 1000 -
          1
      );
      let date = [];
      if (val !== "custom") {
        const dayNum = val === "week" ? 6 : 29;
        start.setTime(end.getTime() - 3600 * 1000 * 24 * dayNum);

        date = [
          moment(start).format("YYYY-MM-DD"),
          moment(end).format("YYYY-MM-DD")
        ];
      } else {
        date = [];
      }

      this.params.date = date;
      if (date.length) {
        this.params.start_date = date[0];
        this.params.end_date = date[1];
      } else {
        this.params.start_date = "";
        this.params.end_date = "";
      }
      if (index !== 2) {
        this.$nextTick(() => {
          this.getDailyStatistics();
        });
      }
    },
    changeDate(val) {
      this.hover_index = 2;
      if (val && val.length) {
        this.params.start_date = val[0];
        this.params.end_date = val[1];
      } else {
        this.params.start_date = "";
        this.params.end_date = "";
      }
      this.getDailyStatistics();
    },
    openDetail(item, row) {
      this.dialogVisible = true;
      this.dialogType = item.props;
      this.curDate = row.date;
    },
    currentChange(page) {
      this.viewList = this.daily_statistics_list.slice(
        (page - 1) * this.pageSize,
        page * this.pageSize
      );
    }
  }
};
</script>

<style scoped lang="less">
.statistics-page {
  background: #e2f3ff;
  padding: 25px 15px;
  .tg-info__back {
    margin-bottom: 16px;
    img {
      width: 10px;
      height: 6px;
      margin-right: 10px;
      transform: rotate(90deg);
      -moz-transform: rotate(90deg);
      -ms-transform: rotate(90deg);
      -o-transform: rotate(90deg);
      -webkit-transform: rotate(90deg);
      cursor: pointer;
    }
    span {
      cursor: pointer;
    }
  }
  .header {
    margin-bottom: 16px;
    display: flex;
    padding: 0 27px;
    .survey-info {
      .survey-name {
        color: #2d80ed;
        font-size: 24px;
        font-weight: 600;
        padding-bottom: 8px;
      }
      .time-info {
        display: flex;
        color: #8492a6;
        font-size: 14px;
        font-weight: 500;
        .crate-time {
          margin-right: 16px;
        }
      }
    }
    .school-select {
      display: flex;
      align-items: center;
      .label {
        color: #8492a6;
        font-size: 14px;
        font-weight: 500;
      }
      /deep/ .tg-select {
        margin: 0 16px;
        .el-input {
          width: 300px;
        }
      }
    }
  }
  .statistics-block {
    padding: 18px 41px;
    width: 100%;
    background: #fff;
    border-radius: 20px;
    margin-bottom: 11px;
    .title-block {
      position: relative;
      color: #1f2d3d;
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 16px;
      .bg {
        width: 80px;
        height: 12px;
        border-radius: 8px;
        position: absolute;
        left: 0;
        bottom: 0px;
        background: linear-gradient(
          90deg,
          rgba(45, 128, 237, 0.48) 0%,
          rgba(45, 128, 237, 0) 100%
        );
      }
    }
    .statistics-list {
      display: flex;
      justify-content: space-between;
      .statistics-item {
        margin-right: 40px;
        border-radius: 8px;
        display: flex;
        padding: 20px 55px;
        align-items: center;
        &:last-child {
          margin-right: 0;
        }
        &.today_likes_count {
          background: #ebf4ff;
          .value {
            color: #2d80ed;
          }
        }
        &.today_views_count {
          background: #fff5d9;
          .value {
            color: #ffbb38;
          }
        }
        &.likes_count {
          background: #dcfaf8;
          .value {
            color: #16dbcc;
          }
        }
        &.views_count {
          background: #dcdffa;
          .value {
            color: #6116db;
          }
        }
        .statistics-info {
          white-space: nowrap;
          text-align: center;
          .label {
            margin-top: 10px;
            font-size: 14px;
            color: #1f2d3d;
            font-weight: 500;
          }
          .value {
            font-size: 40px;
            line-height: 40px;
            font-weight: 600;
          }
        }
      }
    }
    .statistics-content {
      .statistics-search {
        .statistics-search-item {
          display: flex;
          align-items: center;
          margin-bottom: 10px;
          .label {
            color: #8492a6;
            font-size: 14px;
            font-weight: 500;
            margin-right: 16px;
          }
          .btn--left {
            background: #ebf4ff;
            height: 32px;
            border-radius: 4px;
            padding: 4px 6px;
            width: 400px;
            display: flex;
            align-items: center;
            margin-right: 16px;
            box-sizing: border-box;
            span {
              display: inline-block;
              width: 25%;
              height: 24px;
              text-align: center;
              line-height: 24px;
              color: @text-color_second;
              border-radius: 4px;
              box-sizing: border-box;
              font-size: @text-size_normal;
              font-family: @text-famliy_medium;
              cursor: pointer;
              transition: 0.3s;
              margin-right: 6px;
            }
            span:last-child {
              margin-right: 0;
            }
            span.choose {
              color: #fff;
              background: @base-color;
              transition: 0.3s;
            }
          }
        }
      }
      .echar-block {
        width: 100%;
        height: 400px;
        margin-top: 16px;
        margin-bottom: 25px;
        .line-echarts {
          width: 100%;
          height: 100%;
        }
      }
      .table-block {
        .label {
          color: #8492a6;
          font-size: 14px;
          font-weight: 500;
          margin-bottom: 16px;
        }
      }
    }
  }
}
</style>
