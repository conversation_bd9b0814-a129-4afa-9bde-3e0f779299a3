<template>
  <div class="connect-record-statistics">
    <tg-search
      class="connect-search"
      :searchTitle.sync="search_title"
      :form.sync="searchForm"
      :showNum="1"
      :isExport="isExport"
      @reset="reset"
      @educe="exportExcel"
      @search="search"
      :loadingState="exportLoading"
      :defaultSpread="false"
      ref="search"
    ></tg-search>
    <div class="tg-table__box statistics-table">
      <el-table
        ref="table"
        :data="normalDataList"
        tooltip-effect="dark"
        class="tg-table conversion-table"
        v-loading="false"
        :height="height + 'px'"
        border
        :span-method="objectSpanMethod"
        :row-class-name="tableRowClassName"
        show-summary
        :summary-method="getSummaries"
      >
        <el-table-column
          align="center"
          prop="area_name"
          label="区域"
          min-width="100"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.area_name }}</span>
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          prop="department_name"
          label="校区"
          min-width="100"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.department_name }}</span>
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          prop="employee_name"
          label="市场（主管/专员）"
          min-width="100"
        >
          <template slot="header">
            <span>市场（主管/专员）</span>
            <el-tooltip
              class="item"
              effect="dark"
              placement="top"
              popper-class="custom-tooltip"
            >
              <div slot="content">
                <p>
                  渠道：一级渠道：【线下-市场地面推广】，意向客户列表中市场专员字段的人员全部抓取；
                </p>
                <p>市场专员字段为空的单独一列【空白】；</p>
                <p>
                  如收据业绩归属人市场专员不在全部抓取范围内，则单独增加对应的一条市场专员
                </p>
              </div>
              <i
                class="el-icon-question"
                style="margin-left: 4px; color: #409eff; cursor: pointer"
              ></i>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <span>{{ scope.row.employee_name }}</span>
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          prop="valid_num"
          label="有效信息"
          min-width="100"
        >
          <template slot="header">
            <span>有效信息</span>
            <el-tooltip
              class="item"
              effect="dark"
              placement="top"
              popper-class="custom-tooltip"
            >
              <div slot="content">
                <p>一级渠道：【线下-市场地面推广】；</p>
                <p>意向客户有效性字段标记有效；</p>
                <p>生成日期：等于筛选日期</p>
              </div>
              <i
                class="el-icon-question"
                style="margin-left: 4px; color: #409eff; cursor: pointer"
              ></i>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <span>{{ scope.row.valid_num }}</span>
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          prop="audition_num"
          label="试听人数"
          min-width="100"
        >
          <template slot="header">
            <span>试听人数</span>
            <el-tooltip
              class="item"
              effect="dark"
              placement="top"
              popper-class="custom-tooltip"
            >
              <div slot="content">
                <p>渠道：一级渠道：【线下-市场地面推广】；</p>
                <p>试听管理中试听状态【已报名，试听中已上课】；</p>
                <p>试听日期：等于筛选日期</p>
              </div>
              <i
                class="el-icon-question"
                style="margin-left: 4px; color: #409eff; cursor: pointer"
              ></i>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <span>{{ scope.row.audition_num }}</span>
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          prop="new_num"
          label="新招正课人数"
          min-width="100"
        >
          <template slot="header">
            <span>新招正课人数</span>
            <el-tooltip
              class="item"
              effect="dark"
              placement="top"
              popper-class="custom-tooltip"
            >
              <div slot="content">
                <div>
                  <p>渠道：一级渠道：【线下-市场地面推广】；</p>
                  <p>收据类型：收费；收费类型：新增；</p>
                  <p>
                    收费日期：等于筛选日期；收据-业绩归属人销售角色：市场专员；
                  </p>
                  <p>如业绩归属人（销售角色无市场专员）算作空白的</p>
                </div>
              </div>
              <i
                class="el-icon-question"
                style="margin-left: 4px; color: #409eff; cursor: pointer"
              ></i>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <span>{{ scope.row.new_num }}</span>
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          prop="training_new_num"
          label="新招拓科人数"
          min-width="100"
        >
          <template slot="header">
            <span>新招拓科人数</span>
            <el-tooltip
              class="item"
              effect="dark"
              placement="top"
              popper-class="custom-tooltip"
            >
              <div slot="content">
                <div>
                  <p>渠道：一级渠道：【线下-市场地面推广】；</p>
                  <p>收据类型：收费；收费类型：集训新增；</p>
                  <p>日期：等于筛选日期；收据-业绩归属人销售角色：市场专员；</p>
                  <p>如业绩归属人（销售角色无市场专员）算作空白的</p>
                </div>
              </div>
              <i
                class="el-icon-question"
                style="margin-left: 4px; color: #409eff; cursor: pointer"
              ></i>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <span>{{ scope.row.training_new_num }}</span>
          </template>
        </el-table-column>

        <template slot="empty">
          <div style="margin-top: 2%">
            <loading v-if="loading"></loading>
            <div class="empty-container" v-else>暂无数据～</div>
          </div>
        </template>
      </el-table>
    </div>
  </div>
</template>
<script>
import quickTime from "@/public/quickTime";
import reportApi from "@/api/report";
import loading from "../loading";
export default {
  data() {
    return {
      exportLoading: false,
      searchForm: {
        date_range: []
      },
      isExport: true,
      search_title: [
        {
          props: "date_range",
          label: "时间范围",
          type: "date",
          show: true,
          selectOptions: [],
          has_options: true
        }
      ],

      page: 1,
      list: [],
      processedList: [],
      normalDataList: [],
      grandTotalData: null,
      spanMap: {},
      staffConnectVisible: false,
      connectSearch: {},
      loading: false,
      height: 0
    };
  },
  components: {
    loading
  },
  watch: {
    school_id: {
      handler(val) {
        if (this.$_has({ m: "marketTaskIndicator", o: "conversionList" })) {
          this.search();
        }
      },
      deep: true
    }
  },
  created() {
    this.height = window.innerHeight - 230;
    this.isExport = this.$_has({
      m: "marketTaskIndicator",
      o: "conversionListExport"
    });
  },
  mounted() {
    if (this.$_has({ m: "marketTaskIndicator", o: "conversionList" })) {
      this.setSearchDefault();
      this.search();
    }
  },

  updated() {
    // 确保合计行在DOM更新后正确显示
    this.$nextTick(() => {
      if (this.$refs.table) {
        this.$refs.table.doLayout();
      }
    });
  },
  computed: {
    school_id() {
      return this.$store.getters.doneGetSchoolId;
    }
  },
  methods: {
    search() {
      this.page = 1;
      const q = this.getSearch();
      if (
        this.searchForm.date_range === null ||
        this.searchForm.date_range.length === 0
      ) {
        this.$message.error("请选择日期");
        return;
      }
      this.getStatisticsList(q);
    },

    async getStatisticsList(search) {
      this.loading = true;
      const res = await reportApi.getConversionList(search);
      this.list = res.data.data || [];
      this.processTableData();
      this.loading = false;
    },

    // 处理表格数据，添加合计行
    processTableData() {
      if (!this.list || this.list.length === 0) {
        this.processedList = [];
        this.normalDataList = [];
        this.grandTotalData = null;
        return;
      }

      // 按区域分组
      const groupedData = {};
      this.list.forEach((item) => {
        const areaId = item.area_id;
        if (!groupedData[areaId]) {
          groupedData[areaId] = [];
        }
        groupedData[areaId].push(item);
      });

      const processedData = [];
      // 总合计数据从任意一条记录中获取（因为所有记录的region_*_total都是相同的）
      const firstRecord = this.list[0];
      const totalValidNum = firstRecord.region_valid_total || 0;
      const totalAuditionNum = firstRecord.region_audition_total || 0;
      const totalNewNum = firstRecord.region_new_total || 0;
      const totalTrainingNewNum = firstRecord.region_training_new_total || 0;

      // 处理每个区域的数据
      Object.keys(groupedData).forEach((areaId) => {
        const areaData = groupedData[areaId];

        // 添加该区域的所有数据行
        areaData.forEach((item) => {
          processedData.push({
            ...item,
            rowType: "data"
          });
        });

        // 区域合计使用dept_*_total字段
        const areaValidTotal = areaData[0].dept_valid_total || 0;
        const areaAuditionTotal = areaData[0].dept_audition_total || 0;
        const areaNewTotal = areaData[0].dept_new_total || 0;
        const areaTrainingNewTotal = areaData[0].dept_training_new_total || 0;

        // 添加区域合计行
        processedData.push({
          area_id: areaId,
          area_name: areaData[0].area_name,
          department_name: "合计",
          employee_name: "",
          valid_num: areaValidTotal,
          audition_num: areaAuditionTotal,
          new_num: areaNewTotal,
          training_new_num: areaTrainingNewTotal,
          rowType: "areaTotal"
        });
      });

      this.processedList = processedData;
      this.normalDataList = processedData; // 只包含普通数据行和区域合计行

      // 存储总合计数据，用于底部合计行
      this.grandTotalData = {
        valid_num: totalValidNum,
        audition_num: totalAuditionNum,
        new_num: totalNewNum,
        training_new_num: totalTrainingNewNum
      };

      this.calculateSpanMap();
    },

    // 计算合并单元格的映射
    calculateSpanMap() {
      this.spanMap = {};
      const areaGroupedData = {};
      const deptGroupedData = {};

      // 按区域和校区分组计算
      this.processedList.forEach((item, index) => {
        if (item.rowType === "data") {
          const areaId = item.area_id;
          const deptId = item.department_id;

          // 区域分组
          if (!areaGroupedData[areaId]) {
            areaGroupedData[areaId] = [];
          }
          areaGroupedData[areaId].push(index);

          // 校区分组
          if (!deptGroupedData[deptId]) {
            deptGroupedData[deptId] = [];
          }
          deptGroupedData[deptId].push(index);
        }
      });

      // 设置区域列的合并（第0列）
      Object.values(areaGroupedData).forEach((indexes) => {
        if (indexes.length > 0) {
          // 第一行显示区域名称，后续行隐藏
          this.spanMap[`${indexes[0]}_0`] = [indexes.length, 1];
          for (let i = 1; i < indexes.length; i++) {
            this.spanMap[`${indexes[i]}_0`] = [0, 0];
          }
        }
      });

      // 设置校区列的合并（第1列）
      Object.values(deptGroupedData).forEach((indexes) => {
        if (indexes.length > 0) {
          // 第一行显示校区名称，后续行隐藏
          this.spanMap[`${indexes[0]}_1`] = [indexes.length, 1];
          for (let i = 1; i < indexes.length; i++) {
            this.spanMap[`${indexes[i]}_1`] = [0, 0];
          }
        }
      });
    },

    // 合并单元格的方法
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      const key = `${rowIndex}_${columnIndex}`;

      // 区域合计行的合并逻辑
      if (row.rowType === "areaTotal") {
        if (columnIndex === 1) {
          // 第二列（校区）和第三列（市场）合并
          return [1, 2];
        } else if (columnIndex === 2) {
          // 第三列被合并到第二列，隐藏
          return [0, 0];
        }
        return [1, 1];
      }

      // 普通数据行的区域列和校区列合并
      if (columnIndex === 0 || columnIndex === 1) {
        const span = this.spanMap[key];
        if (span) {
          return span;
        }
      }

      return [1, 1];
    },

    // 表格行类名
    tableRowClassName({ row, rowIndex }) {
      if (row.rowType === "areaTotal") {
        return "area-total-row";
      }
      return "";
    },

    // Element Table 内置合计方法
    getSummaries(param) {
      const { columns } = param;
      const sums = [];

      if (!this.grandTotalData) {
        // 如果没有合计数据，返回空合计行
        columns.forEach((column, index) => {
          if (index === 0) {
            sums[index] = "合计";
          } else if (index === 1 || index === 2) {
            sums[index] = ""; // 第二、三列为空，用于合并效果
          } else {
            sums[index] = "0";
          }
        });
        return sums;
      }

      columns.forEach((column, index) => {
        const property = column.property;

        if (index === 0) {
          sums[index] = "合计";
        } else if (index === 1 || index === 2) {
          sums[index] = ""; // 第二、三列为空，用于合并效果
        } else {
          // 根据列的property属性来匹配数据
          switch (property) {
            case "valid_num":
              sums[index] = this.grandTotalData.valid_num;
              break;
            case "audition_num":
              sums[index] = this.grandTotalData.audition_num;
              break;
            case "new_num":
              sums[index] = this.grandTotalData.new_num;
              break;
            case "training_new_num":
              sums[index] = this.grandTotalData.training_new_num;
              break;
            default:
              sums[index] = "--";
          }
        }
      });

      return sums;
    },

    async exportExcel() {
      // 前端通过html导出表格
      // 创建不可见的虚拟表格
      this.exportLoading = true;
      const table = document.createElement("table");
      table.id = "virtualTable";
      table.style.display = "none";
      const head_html = $(".conversion-table .el-table__header-wrapper>table")
        .children("thead")
        .html();
      const body_html = $(".conversion-table .el-table__body-wrapper>table")
        .children("tbody")
        .html();
      table.innerHTML = head_html + body_html;
      // 将表格转换为工作表，使用 { raw: true } 选项来保留原始值
      const worksheet = XLSX.utils.table_to_sheet(table, { raw: true });
      // 设置统一的列宽
      const columnWidth = { wpx: 120 }; // 120像素宽度，每列相同
      worksheet["!cols"] = Array(Object.keys(worksheet).length).fill(
        columnWidth
      );

      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, `市场周转化率`);
      XLSX.writeFile(workbook, `市场周转化率.xlsx`);
      this.exportLoading = false;
      this.$message.success("导出成功");
      table.remove();
    },
    reset() {
      this.searchForm = {
        date_range: []
      };
      this.setSearchDefault();
      const q = this.getSearch();
      this.getStatisticsList(q);
    },
    currentChange(val) {
      this.page = val;
      const q = this.getSearch();
      this.getStatisticsList(q);
    },

    getSearch() {
      const newForm = JSON.parse(JSON.stringify(this.searchForm));
      const query = {
        page: this.page,
        department_id: this.school_id,
        ...newForm
      };

      if (this.searchForm.date_range && this.searchForm.date_range.length) {
        const [start, end] = query.date_range;
        query.start_time = start || "";
        query.end_time = end || "";
        delete query.date_range;
      }
      return query;
    },

    setSearchDefault() {
      const week = quickTime.GetDate("month");
      this.$set(this.searchForm, "date_range", week);
    }
  }
};
</script>
<style lang="less" scoped>
.connect-record-statistics {
  margin-top: 16px;
}
.connect-search {
  margin: 0 6px;
  width: calc(100% - 12px);
}
::v-deep .statistics-table {
  width: calc(100% - 12px);
  border: 1px solid @base-color;
  .el-table th.is-leaf {
    border: none;
  }
  .el-table td:last-child {
    border-right: none;
  }
  .el-table {
    padding: 0;
    border: none;
  }
  th {
    background: @light-color;
  }
  .el-table th:first-child > .cell {
    padding-left: 26px;
  }
  .el-table td:first-child > .cell {
    padding-left: 26px;
  }
  .el-table__body-wrapper {
    height: calc(100% - 46px);
    .empty-container {
      // position: absolute;
      top: 50%;
      left: 50%;
      height: 80px;
    }
    .loading-container {
      position: absolute;
      top: 15%;
      left: 1%;
      width: 100%;
      background: transparent;
      .box {
        height: 100%;
      }
    }
  }

  // 区域合计行样式
  .area-total-row {
    background-color: #f5f7fa !important;
    font-weight: bold !important;

    td {
      background-color: #f5f7fa !important;
      font-weight: bold !important;
    }
  }
}

// Element Table 合计行样式
::v-deep .el-table__footer-wrapper {
  .el-table__footer {
    td {
      background-color: #e6f7ff !important;
      font-weight: bold !important;
      color: #1890ff !important;
    }
  }
}

// Tooltip 自定义样式
::v-deep .custom-tooltip {
  max-width: 400px;

  .el-tooltip__popper {
    max-width: 400px !important;

    .el-tooltip__popper[x-placement^="top"] {
      margin-bottom: 8px;
    }
  }
}

// 表头问号图标样式
::v-deep .el-table th .cell {
  display: flex;
  align-items: center;
  justify-content: center;

  .el-icon-question {
    font-size: 14px;
    margin-left: 4px;
    color: #409eff;
    cursor: pointer;

    &:hover {
      color: #66b1ff;
    }
  }
}
</style>
