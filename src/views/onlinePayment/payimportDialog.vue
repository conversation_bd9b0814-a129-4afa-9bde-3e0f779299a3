<template>
  <el-dialog
    :visible="true"
    width="1016px"
    :title="dialogTitle"
    :show-close="true"
    class="batch-import"
    :modal-append-to-body="false"
    :close-on-press-escape="false"
    top="7vh"
    :before-close="handleClose"
  >
    <div class="content">
      <el-upload
        drag
        :multiple="false"
        accept=".xls,.xlsx"
        action="#"
        :show-file-list="false"
        :on-change="toJson"
        :auto-upload="false"
        class="tg-upload"
      >
        <img src="../../assets/图片/icon_import.png" alt="" />
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      </el-upload>
      <el-button type="plain" class="tg-button--plain" @click="downloadTemplate"
        >下载模版</el-button
      >
    </div>
    <div class="batch--top tg-table--custom">
      <div class="tg-table__box">
        <div class="tg-box--border"></div>
        <el-table width="100%" height="183" :data="importData" class="tg-table">
          <el-table-column
            v-for="(item, index) in importTitle"
            :key="index"
            :label="item.label"
            :prop="item.prop"
            :width="item.width"
          >
          </el-table-column>
        </el-table>
      </div>
      <div class="tg-upload__button">
        <div class="tg-upload__tips">
          <img src="../../assets/图片/icon_info.png" alt="" />
          <span
            >只能上传<em>excel文件</em>，注意数据标题为首行，上方为导入数据预览</span
          >
        </div>
        <el-button
          type="primary"
          :disabled="importData.length == 0"
          @click="dataUpload"
          class="tg-button--primary"
          >上传</el-button
        >
      </div>
      <div class="tg-upload__loading" v-if="isImporting">
        <el-progress type="circle" :percentage="percentage"></el-progress>
      </div>
    </div>
    <div class="error-table">
      <div class="errTitle">
        <span class="error-table__title">错误数据</span>
        <el-button
          type="primary"
          :disabled="errorData.length > 0 ? false : true"
          @click="downloadExcel"
          class="tg-button--primary"
          >导出</el-button
        >
      </div>
      <div class="tg-table__box">
        <div class="tg-box--border"></div>
        <el-table
          width="100%"
          height="200"
          :data="errorData"
          class="tg-table"
          ref="tgTable"
        >
          <el-table-column
            v-for="(item, index) in errorTitle"
            :key="index"
            :label="item.label"
            :prop="item.prop"
            :width="item.width"
          >
          </el-table-column>
        </el-table>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button
        @click="handleClose"
        class="tg-button--plain"
        :disabled="isImporting ? true : false"
        >关闭</el-button
      >
    </span>
  </el-dialog>
</template>
<script>
import { readExcel } from "@/public/importExcel";
import { BigNumber } from "bignumber.js";
import onlinePaymentApi from "@/api/onlinePayment"; // 在线支付相关API
import { exportTableToExcel } from "@/public/downloadExcel";
import timeFormat from "@/public/timeFormat";

export default {
  props: {
    importType: {
      type: String,
      default: "dianping" // dianping: 大众点评, union: 银联, bank: 银行转账
    }
  },
  data() {
    return {
      fullscreenLoading: false,
      isImporting: false,
      importTitle: [],
      tableTitle: [], // 导入数据表头
      errorTitle: [], // 错误数据表头
      importData: [],
      errorData: [],
      percentage: 0,
      excelTitle: []
    };
  },
  computed: {
    // 根据导入类型生成对应的字段配置
    importTitleConfig() {
      const configs = {
        dianping: [
          {
            prop: "account_name",
            label: "账户名称",
            width: 120,
            type: "String"
          },
          {
            prop: "business_type",
            label: "业务类型",
            width: 120,
            type: "String"
          },
          {
            prop: "order_type",
            label: "订单类型",
            width: 120,
            type: "String"
          },
          {
            prop: "coupon_no",
            label: "券号",
            width: 120,
            type: "String"
          },
          {
            prop: "order_time",
            label: "下单时间",
            width: 150,
            type: "String"
          },
          {
            prop: "pay_time",
            label: "验券/退款/调整时间",
            width: 180,
            type: "String"
          },
          {
            prop: "goods_name",
            label: "套餐名",
            width: 120,
            type: "String"
          },
          {
            prop: "shop_id",
            label: "消费门店Id",
            width: 120,
            type: "String"
          },
          {
            prop: "shop_name",
            label: "消费门店",
            width: 120,
            type: "String"
          },
          {
            prop: "charge_date",
            label: "入账日期",
            width: 120,
            type: "String"
          },
          {
            prop: "status",
            label: "打款状态",
            width: 120,
            type: "String"
          },
          {
            prop: "third_order_id",
            label: "打款单号",
            width: 120,
            type: "String"
          },
          {
            prop: "settle_price",
            label:
              "结算价(总收入-美团点评技术服务费-商家营销费用-消费后退-其他调整)（元）",
            width: 350,
            type: "Number"
          },
          {
            prop: "income_price",
            label: "总收入（元）",
            width: 120,
            type: "Number"
          },
          {
            prop: "expense_ratio",
            label: "技术服务费费率",
            width: 150,
            type: "Number"
          },
          {
            prop: "expense_price",
            label: "平台技术服务费（元）",
            width: 180,
            type: "Number"
          }
        ],
        union: [
          {
            prop: "charge_date",
            label: "收据日期",
            type: "String"
          },
          {
            prop: "actual_price",
            label: "实交金额",
            type: "Number"
          },
          {
            prop: "department_name",
            label: "校区",
            width: 120,
            type: "String"
          },
          {
            prop: "third_order_id",
            label: "第三方订单号",
            type: "String"
          }
        ],
        bank: [
          {
            prop: "charge_date",
            label: "收据日期",
            type: "String"
          },
          {
            prop: "actual_price",
            label: "实交金额",
            type: "Number"
          },
          { prop: "department_name", label: "校区", type: "String" },
          {
            prop: "third_order_id",
            label: "第三方订单号",
            type: "String"
          }
        ]
      };
      return configs[this.importType] || configs.dianping;
    },

    // 对话框标题
    dialogTitle() {
      const titles = {
        dianping: "大众点评导入",
        union: "银联导入",
        bank: "银行转账导入"
      };
      return titles[this.importType] || "导入";
    }
  },

  watch: {
    importType: {
      handler() {
        this.importTitle = this.importTitleConfig;
        this.errorTitle = [
          ...this.importTitle,
          { prop: "msg", label: "错误原因", width: 120 }
        ];
        // 清空之前的数据
        this.importData = [];
        this.errorData = [];
      },
      immediate: true
    }
  },

  mounted() {
    // 初始化已在 watch 中处理
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
    async toJson(file) {
      this.isImporting = false;
      this.fullscreenLoading = this.$loading({
        lock: true,
        text: "Loading",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)"
      });
      const extension = file.name.split(".").pop().toLowerCase();
      if (extension !== "xlsx") {
        this.$message.error("只能上传xlsx文件");
        this.$nextTick(() => {
          // 以服务的方式调用的 Loading 需要异步关闭
          this.fullscreenLoading.close();
        });
        return; // 阻止上传
      }
      try {
        const fileData = await readExcel(file);
        const importData = fileData[0].sheet;
        this.excelTitle = [];
        for (const key in importData[0]) {
          this.excelTitle.push({ label: key, prop: key });
        }
        // 原始数据
        const initObj = {};
        this.importTitle.forEach((item) => {
          initObj[item.prop] = "";
        });
        const newData = importData.map((item) => {
          const obj = JSON.parse(JSON.stringify(initObj));
          for (const i in item) {
            const titleObj = this.importTitle.find((it) => it.label === i);
            if (titleObj) {
              obj[titleObj.prop] =
                item[i] === "null"
                  ? ""
                  : titleObj.type === "String"
                  ? item[i].toString()
                  : item[i];
            }
          }
          return obj;
        });
        this.$nextTick(() => {
          // 以服务的方式调用的 Loading 需要异步关闭
          this.fullscreenLoading.close();
        });
        this.importData = newData;
      } catch (err) {
        this.$nextTick(() => {
          // 以服务的方式调用的 Loading 需要异步关闭
          this.fullscreenLoading.close();
        });
      }
    },
    async dataUpload() {
      this.errorData = [];
      this.isImporting = true;
      this.percentage = 0;

      // 根据导入类型选择对应的API方法
      const apiMethods = {
        dianping:
          onlinePaymentApi.importDianping || onlinePaymentApi.importPayment,
        union: onlinePaymentApi.importUnion || onlinePaymentApi.importPayment,
        bank: onlinePaymentApi.importBank || onlinePaymentApi.importPayment
      };

      const apiMethod =
        apiMethods[this.importType] || onlinePaymentApi.importPayment;

      for (const value of this.importData) {
        const save_obj = Object.assign({}, value);

        // 根据导入类型处理不同的日期字段
        if (this.importType === "dianping") {
          // 大众点评特殊字段处理
          save_obj.order_time = save_obj.order_time
            ? this.getDate(save_obj.order_time)
            : "";
          save_obj.voucher_time = save_obj.voucher_time
            ? this.getDate(save_obj.voucher_time)
            : "";
          save_obj.entry_date = save_obj.entry_date
            ? this.getDate(save_obj.entry_date)
            : "";
        } else {
          // 银联和银行转账的日期处理
          save_obj.charge_date = save_obj.charge_date
            ? this.getDate(save_obj.charge_date)
            : "";
        }

        // 添加导入类型标识
        save_obj.import_type = this.importType;

        await apiMethod(save_obj)
          .then((res) => {
            const length = this.importData.length;
            const b = new BigNumber(this.percentage);
            const a = new BigNumber(Math.round((1 / length) * 10000) / 100.0);
            const c = Number(b.plus(a));
            this.percentage = c;
            if (res?.status !== 200 || res?.data.code !== 0) {
              this.errorData.push({
                ...value,
                msg: res?.data?.message ? res?.data?.message : "未知错误"
              });
            }
          })
          .catch(() => {
            const length = this.importData.length;
            const b = new BigNumber(this.percentage);
            const a = new BigNumber(Math.round((1 / length) * 10000) / 100.0);
            const c = Number(b.plus(a));
            this.percentage = c;
            this.errorData.push({ ...value, msg: "未知错误" });
          });
      }
      // 导入完成后自动关闭进度条遮罩层
      this.isImporting = false;

      if (this.errorData.length === 0) {
        this.$message.success("上传成功");
      } else {
        this.$message.warning("上传结束，请检查错误数据");
      }
    },
    getDate(time) {
      return timeFormat.GetDate(time);
    },
    downloadExcel() {
      const tableRef = this.$refs.tgTable;
      const filenames = {
        dianping: "大众点评导入错误数据",
        union: "银联导入错误数据",
        bank: "银行转账导入错误数据"
      };
      const filename = filenames[this.importType] || "导入错误数据";
      const tableHeader = this.errorTitle.map((item) => item.label);
      exportTableToExcel(tableRef, filename, tableHeader);
    },
    downloadTemplate() {
      const templateConfigs = {
        dianping: {
          href: "./dazhong.xlsx",
          filename: "大众点评导入模版.xlsx"
        },
        union: {
          href: "./yinhang.xlsx",
          filename: "银联导入模版.xlsx"
        },
        bank: {
          href: "./yinhang.xlsx",
          filename: "银行转账导入模版.xlsx"
        }
      };

      const config =
        templateConfigs[this.importType] || templateConfigs.dianping;

      const downloadElement = document.createElement("a");
      downloadElement.href = config.href + "?v=" + window.__APP_VERSION__;
      downloadElement.download = config.filename;
      document.body.appendChild(downloadElement);
      downloadElement.click(); // 点击下载
      document.body.removeChild(downloadElement);
    }
  }
};
</script>
<style lang="less" scoped>
.batch-import {
  .batch--top {
    // display: flex;
    // flex-direction: row;
    padding-bottom: 16px;
    position: relative;
    .tg-upload__loading {
      position: absolute;
      top: 0;
      left: 0;
      background: rgba(0, 0, 0, 0.5);
      margin: auto;
      right: 0;
      bottom: 0;
      width: 100%;
      height: inherit;
      z-index: 10;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      ::v-deep .el-progress {
        z-index: 11;
        .el-progress__text {
          color: white !important;
        }
      }
    }
  }
  .tg-upload__button {
    // text-align: right;
    margin-top: 16px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }
  .tg-table--custom {
    // width: calc(100% - 400px);
    width: 100%;
    ::v-deep .el-table {
      padding: 0;
      tr td .cell {
        padding-left: 26px;
      }
    }
    ::v-deep .el-table__header tr {
      background-color: #f5f8fc;
      & > th {
        background-color: transparent;
      }
    }
    ::v-deep .el-table__header {
      background-color: #f5f8fc;
      padding: 0 16px;
    }
  }
  .error-table {
    width: 100%;
    .error-table__title {
      font-size: 14px;
      font-weight: bold;
      color: @text-color_second;
      font-family: @text-famliy_medium;
      padding-left: 12px;
      position: relative;
      &::after {
        content: "";
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background-color: @base-color;
        position: absolute;
        top: 7px;
        left: 0;
      }
    }
  }
  .content {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    height: 32px;
    align-items: center;
    .el-select + .el-select {
      margin-left: 16px;
    }
  }
  ::v-deep .el-upload-dragger {
    border: 1px dashed #d0dce7;
    border-radius: 4px;
    height: 32px;
    background-color: #f7f8fa;
    width: 220px;
    img {
      width: 20px;
      height: 16px;
      margin-right: 12px;
      vertical-align: middle;
      margin-top: -3px;
    }
    .el-upload__text {
      display: inline-block;
      font-size: 10px;
      font-family: @text-famliy_medium;
      line-height: 30px;
    }
  }
  .tg-upload__tips {
    img {
      width: 12px;
      height: 12px;
      margin-right: 8px;
      vertical-align: middle;
      margin-top: -1px;
    }
    span {
      font-size: 12px;
      font-family: @text-famliy_medium;
      color: #b3b7c6;
    }
    em {
      color: #ff0317;
      font-style: normal;
    }
  }
  .tg-table__box {
    margin-left: 0;
    margin-right: 0;
    box-shadow: none;
    .tg-box--border {
      width: calc(100% - 1px);
    }
  }
  ::v-deep .el-dialog__body {
    padding: 16px;
  }
  ::v-deep .el-dialog__footer {
    padding: 11px 16px 7px 16px;
  }
  ::v-deep .el-dialog__body {
    height: 531px;
  }
  .tg-text--disabled {
    color: #8492a6;
    em {
      color: #455569;
    }
  }
  .tg-upload--disabled {
    ::v-deep .el-upload-dragger {
      cursor: not-allowed;
    }
  }
  .tg-row {
    display: inline-block;
  }
  .tg-row + .tg-row {
    margin-left: 40px;
  }
  .errTitle {
    display: inline-flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
}
</style>
