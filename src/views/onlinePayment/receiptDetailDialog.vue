<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="80%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="receipt-detail-header">
      <el-button
        type="primary"
        @click="exportReceiptDetail"
        :loading="exportLoading"
        size="small"
        v-has="{ m: 'onlinePayment', o: 'receiptDetailExport' }"
      >
        导出明细
      </el-button>
    </div>
    <div class="receipt-detail-table tg-table__box statistics-table">
      <el-table
        :data="detailData"
        v-loading="loading"
        border
        :height="400"
        class="tg-table"
      >
        <!-- 收据明细字段 -->
        <template v-if="detailType === 'receipt'">
          <el-table-column
            prop="receipt_no"
            label="收据号"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="charge_date"
            label="收据时间"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="fee_type_str"
            label="收费类型"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="student_number"
            label="学号"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="student_name"
            label="姓名"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="mobile"
            label="手机号"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="original_price"
            label="应交金额"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="actual_price"
            label="实交金额"
            align="center"
          ></el-table-column>
        </template>

        <!-- 第三方金额明细字段（待确认具体字段） -->
        <template v-else-if="detailType === 'thirdParty'">
          <el-table-column
            prop="transaction_no"
            label="交易号"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="transaction_time"
            label="交易时间"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="third_party_amount"
            label="第三方金额"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="payment_channel"
            label="支付渠道"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="transaction_status"
            label="交易状态"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="student_name"
            label="学生姓名"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="order_no"
            label="订单号"
            align="center"
          ></el-table-column>
          <!-- 注释：第三方金额明细的具体字段待确认 -->
        </template>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="tg-pagination">
      <span class="el-pagination__total">共 {{ pagination.total }} 条</span>
      <el-pagination
        background
        layout="sizes,prev,pager,next,jumper"
        :total="pagination.total"
        :page-size="pagination.pageSize"
        :current-page="pagination.page"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        :page-sizes="[10, 20, 50, 100]"
      ></el-pagination>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import onlinePaymentApi from "@/api/onlinePayment";
import { export_excel_sync_new } from "@/public/asyncExport";
export default {
  name: "ReceiptDetailDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    paymentName: {
      type: String,
      default: ""
    },
    paymentKey: {
      type: String,
      default: ""
    },
    rowData: {
      type: Object,
      default: () => ({})
    },
    detailType: {
      type: String,
      default: "receipt" // receipt: 收据明细, thirdParty: 第三方金额明细
    },
    receipt_date: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    dialogTitle() {
      if (this.detailType === "thirdParty") {
        return `${this.paymentName}金额明细`;
      }
      return `${this.paymentName}收据明细`;
    }
  },
  data() {
    return {
      dialogVisible: this.visible,
      detailData: [],
      loading: false,
      exportLoading: false,
      pagination: {
        page: 1,
        page_size: 10,
        total: 0
      }
    };
  },
  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val;
        if (val) {
          this.loadDetailData();
        }
      },
      immediate: true
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit("update:visible", false);
      }
    }
  },
  methods: {
    async loadDetailData() {
      this.loading = true;
      const params = {
        page: this.pagination.page,
        page_size: this.pagination.page_size,
        department_id: this.rowData.department_id,
        department_name: this.rowData.department_name,
        search_begin: this.receipt_date[0],
        search_end: this.receipt_date[1],
        pay_type: this.paymentKey
      };
      const { data } = await onlinePaymentApi.getReceiptDetail(params);
      if (data.code === 0) {
        this.detailData = data.data.results;
        this.pagination.total = data.data.count;
      } else {
        this.$message.error(data.message);
      }
      // 模拟API调用获取明细
      this.loading = false;
    },
    exportReceiptDetail() {
      this.exportLoading = true;
      const params = {
        page: this.pagination.page,
        page_size: this.pagination.page_size,
        department_id: this.rowData.department_id,
        department_name: this.rowData.department_name,
        search_begin: this.receipt_date[0],
        search_end: this.receipt_date[1],
        pay_type: this.paymentKey
      };
      const opt = {
        vm: this,
        api_url: "/api/order-service/admin/trade-bill/detail-receipt-export",
        file_name: "收据明细",
        success_msg: "收据明细导出成功！",
        error_msg: "收据明细导出失败！",
        query: {
          ...params
        }
      };
      export_excel_sync_new(opt);
    },
    handleClose() {
      this.dialogVisible = false;
      this.pagination.page = 1; // 重置分页
      this.$emit("close");
    },
    handleSizeChange(size) {
      this.pagination.page_size = size;
      this.pagination.page = 1;
      this.loadDetailData();
    },
    handleCurrentChange(page) {
      this.pagination.page = page;
      this.loadDetailData();
    }
  }
};
</script>

<style lang="less" scoped>
.receipt-detail-header {
  margin-bottom: 16px;
  //   text-align: right;
}

.dialog-footer {
  //   text-align: center;
}
</style>
