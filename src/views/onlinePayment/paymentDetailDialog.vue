<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="80%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="payment-detail-header">
      <el-button
        type="primary"
        @click="exportPaymentDetail"
        :loading="exportLoading"
        size="small"
        v-if="paymentShow"
      >
        导出明细
      </el-button>
    </div>
    <div class="payment-detail-table tg-table__box statistics-table">
      <el-table
        :data="detailData"
        v-loading="loading"
        border
        :height="400"
        class="tg-table"
      >
        <!-- 易宝详情字段 -->
        <template v-if="paymentKey === 'yop'">
          <el-table-column
            prop="pay_time"
            label="下单时间"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="merchant_name"
            label="子商户名称"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="trade_amount"
            label="订单金额"
            align="center"
          ></el-table-column>
          <el-table-column label="入账金额" align="center">
            <template slot-scope="scope">
              {{ scope.row.trade_amount - scope.row.trade_fee }}
            </template>
          </el-table-column>
          <el-table-column
            prop="trade_fee"
            label="手续费"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="memo"
            label="对账备注"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="status"
            label="订单状态"
            align="center"
          ></el-table-column>
        </template>

        <!-- 财付通详情字段 -->
        <template v-else-if="paymentKey === 'cft'">
          <el-table-column
            prop="trade_time"
            label="交易时间"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="total_amount"
            label="订单金额"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="service_fee"
            label="手续费"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="trade_state"
            label="交易状态"
            align="center"
          ></el-table-column>
        </template>

        <!-- 默认支付详情字段 -->
        <template v-else>
          <el-table-column
            prop="transaction_no"
            label="交易号"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="transaction_time"
            label="交易时间"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="payment_amount"
            label="支付金额"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="payment_channel"
            label="支付渠道"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="transaction_status"
            label="交易状态"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="student_name"
            label="学生姓名"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="order_no"
            label="订单号"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="department_name"
            label="地区名称"
            align="center"
          ></el-table-column>
        </template>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="tg-pagination">
      <span class="el-pagination__total">共 {{ pagination.total }} 条</span>
      <el-pagination
        background
        layout="sizes,prev,pager,next,jumper"
        :total="pagination.total"
        :page-size="pagination.page_size"
        :current-page="pagination.page"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        :page-sizes="[10, 20, 50, 100]"
      ></el-pagination>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import onlinePaymentApi from "@/api/onlinePayment";
import { export_excel_sync_new } from "@/public/asyncExport";

export default {
  name: "PaymentDetailDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    paymentName: {
      type: String,
      default: ""
    },
    paymentKey: {
      type: String,
      default: ""
    },
    rowData: {
      type: Object,
      default: () => ({})
    },
    receipt_date: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    dialogTitle() {
      return `${this.paymentName}金额明细`;
    }
  },
  data() {
    return {
      dialogVisible: this.visible,
      detailData: [],
      loading: false,
      exportLoading: false,
      pagination: {
        page: 1,
        page_size: 10,
        total: 0
      },
      paymentShow: false
    };
  },
  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val;
        if (val) {
          this.loadDetailData();
        }
      },
      immediate: true
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit("update:visible", false);
      }
    }
  },
  methods: {
    async loadDetailData() {
      if (
        this.$_has({ m: "onlinePayment", o: "yopDetailExport" }) &&
        this.paymentKey === "yop"
      ) {
        this.paymentShow = true;
      } else if (
        this.$_has({ m: "onlinePayment", o: "cftDetailExport" }) &&
        this.paymentKey === "cft"
      ) {
        this.paymentShow = true;
      } else {
        this.paymentShow = false;
      }
      this.loading = true;
      try {
        const params = {
          page: this.pagination.page,
          page_size: this.pagination.page_size,
          department_id: this.rowData.department_id,
          department_name: this.rowData.department_name,
          search_begin: this.receipt_date[0],
          search_end: this.receipt_date[1]
        };

        let res;
        // 根据支付方式调用不同的API
        if (this.paymentKey === "yop") {
          res = await onlinePaymentApi.getYopDetail(params);
        } else if (this.paymentKey === "cft") {
          res = await onlinePaymentApi.getCftDetail(params);
        } else {
          // 默认API
          params.payment_key = this.paymentKey;
          res = await onlinePaymentApi.getPaymentDetail(params);
        }

        if (res.status === 200 && res.data.code === 0) {
          this.detailData = res.data.data.results || [];
          this.pagination.total = res.data.data.count || 0;
        } else {
          this.$message.error(res.data.message || "获取明细失败");
        }
      } catch (error) {
        console.error("获取支付明细失败:", error);
        this.$message.error("获取明细失败");
      } finally {
        this.loading = false;
      }
    },
    exportPaymentDetail() {
      this.exportLoading = true;

      const params = {
        department_id: this.rowData.department_id,
        department_name: this.rowData.department_name
      };

      // 根据支付方式使用不同的导出API
      let apiUrl = "";
      let fileName = "";

      if (this.paymentKey === "yop") {
        apiUrl = "/api/order-service/admin/trade-bill/detail-yop-export";
        fileName = "易宝金额明细";
      } else if (this.paymentKey === "cft") {
        apiUrl = "/api/order-service/admin/trade-bill/detail-cft-export";
        fileName = "财付通金额明细";
      } else {
        // 默认导出API
        apiUrl = "/api/order-service/admin/trade-bill/payment-detail-export";
        fileName = `${this.paymentName}金额明细`;
        params.payment_key = this.paymentKey;
      }

      const opt = {
        vm: this, // vue组件实例
        api_url: apiUrl, // 接口地址
        file_name: fileName, // 文件名
        success_msg: `${fileName}导出成功！`, // 导出成功的提示语
        error_msg: `${fileName}导出失败！`, // 导出失败的提示语
        query: {
          ...params
        }
      };

      export_excel_sync_new(opt);
      this.exportLoading = false;
    },
    handleClose() {
      this.dialogVisible = false;
      this.pagination.page = 1; // 重置分页
      this.$emit("close");
    },
    handleSizeChange(size) {
      this.pagination.page_size = size;
      this.pagination.page = 1;
      this.loadDetailData();
    },
    handleCurrentChange(page) {
      this.pagination.page = page;
      this.loadDetailData();
    }
  }
};
</script>

<style lang="less" scoped>
.payment-detail-header {
  margin-bottom: 16px;
}

.dialog-footer {
  // text-align: center;
}
</style>
