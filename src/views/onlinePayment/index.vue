<template>
  <div class="online-payment-container">
    <div class="online-payment-header">
      <tg-search
        :searchTitle.sync="search_title"
        :form.sync="searchForm"
        :showNum="2"
        @educe="exportDataNew"
        :loadingState="exportLoading"
        :isExport="isExport"
        @reset="reset"
        @search="searchVal"
        class="tg-box--margin"
        :hasDefaultDate="false"
      ></tg-search>
    </div>
    <el-row class="tg-box--margin tg-shadow--margin tg-row--height">
      <el-button
        type="plain"
        class="tg-button--plain"
        @click="exportDataNew"
        v-has="{ m: 'onlinePayment', o: 'export' }"
        >导出</el-button
      >
      <el-button
        type="plain"
        class="tg-button--plain"
        @click="importData('dianping')"
        v-has="{ m: 'onlinePayment', o: 'importDianping' }"
        >大众点评导入</el-button
      >
      <el-button
        type="plain"
        class="tg-button--plain"
        @click="importData('union')"
        v-has="{ m: 'onlinePayment', o: 'importUnion' }"
        >银联导入</el-button
      >
      <el-button
        type="plain"
        class="tg-button--plain"
        @click="importData('bank')"
        v-has="{ m: 'onlinePayment', o: 'importBank' }"
        >银行转账导入</el-button
      >
      <el-button
        type="plain"
        class="tg-button--plain"
        @click="wechatRemind"
        v-has="{ m: 'onlinePayment', o: 'list' }"
        >企业微信一键提醒</el-button
      >
    </el-row>
    <div class="tg-table__box--title">
      收据总金额：¥{{ totalAmount ? formatNumber(totalAmount) : "0.00" }}
    </div>
    <div class="tg-table__box">
      <el-table
        :data="tableData"
        ref="table"
        class="tg-table conversioned-table statistics-table"
        border
        :span-method="objectSpanMethod"
        :show-summary="tableData.length > 0"
        :summary-method="getSummaries"
        v-loading="tableLoading"
        :height="height + 'px'"
        :header-cell-class-name="headerCellClassName"
        :cell-class-name="cellClassName"
        v-summary-color
      >
        <!-- 地区名称列 -->
        <el-table-column
          prop="department_name"
          label="校区名称"
          width="130"
          fixed
        >
        </el-table-column>

        <!-- 动态生成支付方式列 -->
        <el-table-column
          v-for="payment in filteredPaymentMethods"
          :key="payment.key"
          :label="payment.label"
          align="center"
        >
          <el-table-column
            v-for="column in payment.columns"
            :key="column.prop"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            align="center"
          >
            <template slot-scope="scope">
              <span
                v-if="column.type === 'difference'"
                :style="{
                  color: scope.row[column.prop] > 0 ? '#f56c6c' : '#000'
                }"
              >
                {{ formatNumber(scope.row[column.prop]) }}
              </span>
              <el-button
                v-else-if="column.type === 'receipt'"
                type="text"
                class="tg-text--blue"
                @click="viewReceiptDetail(scope.row, payment.key, column.prop)"
              >
                {{ formatNumber(scope.row[column.prop]) }}
              </el-button>
              <el-button
                v-else-if="column.type === 'clickable'"
                type="text"
                class="tg-text--blue"
                @click="
                  viewThirdPartyDetail(scope.row, payment.key, column.prop)
                "
              >
                {{ formatNumber(scope.row[column.prop]) }}
              </el-button>
              <span v-else>
                {{ formatNumber(scope.row[column.prop]) }}
              </span>
            </template>
          </el-table-column>
        </el-table-column>
        <template slot="empty">
          <div style="margin-top: 15%; width: 100%">
            <TgLoading v-if="tableLoading"></TgLoading>
            <div class="empty-container" v-else>暂无数据～</div>
          </div>
        </template>
      </el-table>
    </div>

    <!-- 收据明细弹窗 -->
    <receipt-detail-dialog
      :visible.sync="receiptDetailVisible"
      :payment-name="currentPaymentName"
      :payment-key="currentPaymentKey"
      :row-data="currentRowData"
      :detail-type="currentDetailType"
      @close="handleDialogClose"
      :receipt_date="searchForm.receipt_date"
    />

    <!-- 支付详情弹窗 -->
    <payment-detail-dialog
      :visible.sync="paymentDetailVisible"
      :payment-name="currentPaymentName"
      :payment-key="currentPaymentKey"
      :row-data="currentRowData"
      @close="handlePaymentDialogClose"
      :receipt_date="searchForm.receipt_date"
    />

    <!-- 导入弹窗 -->
    <payimport-dialog
      v-if="importDialogVisible"
      :import-type="importType"
      @close="handleImportDialogClose"
    />
  </div>
</template>
<script>
import receiptDetailDialog from "./receiptDetailDialog.vue";
import paymentDetailDialog from "./paymentDetailDialog.vue";
import payimportDialog from "./payimportDialog.vue";
import onlinePaymentApi from "@/api/onlinePayment";
import { export_excel_sync_new } from "@/public/asyncExport";
export default {
  name: "onlinePayment",
  components: {
    receiptDetailDialog,
    paymentDetailDialog,
    payimportDialog
  },
  directives: {
    summaryColor: {
      inserted(el) {
        // 等待DOM渲染完成
        setTimeout(() => {
          const summaryRow = el.querySelector(".el-table__footer tr");
          if (summaryRow) {
            const cells = summaryRow.querySelectorAll("td");
            cells.forEach((cell, index) => {
              // 差额列：每3列中的第3列（索引3,6,9...）
              if (index > 0 && index % 3 === 0) {
                const value = parseFloat(cell.textContent);
                if (!isNaN(value) && value > 0) {
                  cell.style.color = "#f56c6c";
                  cell.style.fontWeight = "bold";
                }
              }
            });
          }
        }, 300);
      },
      update(el) {
        // 数据更新时重新应用样式
        setTimeout(() => {
          const summaryRow = el.querySelector(".el-table__footer tr");
          if (summaryRow) {
            const cells = summaryRow.querySelectorAll("td");
            cells.forEach((cell, index) => {
              if (index > 0 && index % 3 === 0) {
                const value = parseFloat(cell.textContent);
                if (!isNaN(value) && value > 0) {
                  cell.style.color = "#f56c6c";
                  cell.style.fontWeight = "bold";
                } else {
                  cell.style.color = "";
                  cell.style.fontWeight = "";
                }
              }
            });
          }
        }, 300);
      }
    }
  },
  data() {
    return {
      search_title: [
        {
          props: "receipt_date",
          label: "收据日期",
          type: "date_no_clear",
          show: true
        },
        {
          props: "has_differ",
          label: "有无差异",
          type: "select",
          selectOptions: [
            {
              name: "不限",
              id: 0
            },
            {
              name: "有差异",
              id: 1
            },
            {
              name: "无差异",
              id: 2
            }
          ],
          show: true
        },
        {
          props: "pay_type",
          label: "收款方式",
          type: "checkboxGroup",
          option: [
            {
              name: "易宝",
              value: "yop",
              key: "yop"
            },
            {
              name: "财付通",
              value: "cft",
              key: "cft"
            },
            {
              name: "大众点评",
              value: "dianping",
              key: "dianping"
            },
            {
              name: "银行转账",
              value: "transfer",
              key: "transfer"
            },
            {
              name: "银联",
              value: "union",
              key: "union"
            },
            {
              name: "小红书店铺",
              value: "xhs_shop",
              key: "xhs_shop"
            },
            {
              name: "抖音店铺",
              value: "tiktok_shop",
              key: "tiktok_shop"
            },
            {
              name: "视频号",
              value: "view_account",
              key: "view_account"
            },
            {
              name: "快手店铺",
              value: "kuaishou_shop",
              key: "kuaishou_shop"
            },
            {
              name: "淘宝店铺",
              value: "taobao_shop",
              key: "taobao_shop"
            },
            {
              name: "万物心选店铺",
              value: "wwxx_shop",
              key: "wwxx_shop"
            },
            {
              name: "妈觅店铺",
              value: "mami_shop",
              key: "mami_shop"
            },
            {
              name: "小红书私信通",
              value: "xhs_sxt",
              key: "xhs_sxt"
            },
            {
              name: "内部平台",
              value: "inner_platform",
              key: "inner_platform"
            },
            {
              name: "其他达人平台",
              value: "star_platform",
              key: "star_platform"
            },
            {
              name: "抖音本地生活",
              value: "tiktok_local",
              key: "tiktok_local"
            },
            {
              name: "百度",
              value: "baidu",
              key: "baidu"
            },
            {
              name: "直营校-小红书",
              value: "xhs_zyx",
              key: "xhs_zyx"
            },
            {
              name: "高德",
              value: "gaode",
              key: "gaode"
            }
          ],
          show: false
        }
      ],
      searchForm: {
        receipt_date: "",
        has_differ: 0,
        pay_type: [
          "yop",
          "cft",
          "dianping",
          "transfer",
          "union",
          "xhs_shop",
          "tiktok_shop",
          "view_account",
          "kuaishou_shop",
          "taobao_shop",
          "wwxx_shop",
          "mami_shop",
          "xhs_sxt",
          "inner_platform",
          "star_platform",
          "tiktok_local",
          "baidu",
          "xhs_zyx",
          "gaode"
        ]
      },
      exportLoading: false,
      isExport: false,

      tableData: [],
      tableLoading: false,
      height: 0,
      totalAmount: 0,
      summaryData: {}, // 存储汇总数据
      receiptDetailVisible: false,
      paymentDetailVisible: false,
      currentPaymentName: "",
      currentPaymentKey: "",
      currentRowData: {},
      currentDetailType: "receipt", // receipt: 收据明细, thirdParty: 第三方金额明细

      // 导入弹窗相关
      importDialogVisible: false,
      importType: "dianping" // dianping: 大众点评, union: 银联, bank: 银行转账
    };
  },
  computed: {
    // 获取收款方式配置
    paymentMethodOptions() {
      const paymentConfig = this.search_title.find(
        (item) => item.props === "pay_type"
      );
      return paymentConfig ? paymentConfig.option : [];
    },
    // 根据搜索条件过滤显示的支付方式列
    filteredPaymentMethods() {
      if (!this.searchForm.pay_type || this.searchForm.pay_type.length === 0) {
        return [];
      }

      const result = this.paymentMethodOptions
        .filter((option) => this.searchForm.pay_type.includes(option.value))
        .map((option) => ({
          key: option.key,
          value: option.value,
          label: option.name,
          columns: [
            {
              prop: `${option.key}_receipt_price`,
              label: "收据金额",
              width: 100,
              type: "receipt" // 标识为收据金额列
            },
            {
              prop: `${option.key}_price`,
              label: `${option.name}金额`,
              width: 200,
              type: ["yop", "cft"].includes(option.key) ? "clickable" : "normal" // 易宝、财付通可点击
            },
            {
              prop: `${option.key}_differ_price`,
              label: "差额",
              width: 180,
              type: "difference"
            }
          ]
        }));

      return result;
    },
    school_id() {
      return this.$store.getters.doneGetSchoolId;
    }
  },
  watch: {
    // 监听收款方式变化，重新布局表格
    "searchForm.pay_type": {
      handler() {
        this.$nextTick(() => {
          if (this.$refs.table) {
            this.$refs.table.doLayout();
          }
        });
      },
      deep: true
    },
    school_id() {
      this.getList();
    },
    tableData: {
      handler() {
        this.$nextTick(() => {
          this.updateSummaryRowColors();
        });
      },
      deep: true
    }
  },
  created() {
    this.setSearchDefault();
    this.height = window.innerHeight - 352;
    this.getList();
  },
  mounted() {
    // 监听表格渲染完成，添加合计行差额颜色
    this.$nextTick(() => {
      this.updateSummaryRowColors();
    });
  },
  methods: {
    // 格式化数字为两位小数
    formatNumber(value) {
      if (value === null || value === undefined || value === "") {
        return "0.00";
      }
      const num = Number(value);
      if (isNaN(num)) {
        return "0.00";
      }
      return num.toFixed(2);
    },
    async exportDataNew() {
      this.exportLoading = true;
      const params = {
        ...this.searchForm,
        department_id: this.school_id,
        pay_type: this.searchForm.pay_type
      };
      const opt = {
        vm: this, // vue组件实例，
        api_url: "/api/order-service/admin/trade-bill/list-export", // 接口地址
        file_name: "在线支付对账", // 文件名
        success_msg: "在线支付对账导出成功！", // 导出成功的提示语
        error_msg: "在线支付对账导出失败！", // 导出失败的提示语,
        query: {
          ...params
        }
      };
      export_excel_sync_new(opt);
      this.exportLoading = false;
    },
    searchVal() {
      this.getList();
    },
    reset() {
      this.searchForm = {
        receipt_date: "",
        has_differ: 0,
        pay_type: [
          "yop",
          "cft",
          "dianping",
          "transfer",
          "union",
          "xhs_shop",
          "tiktok_shop",
          "view_account",
          "kuaishou_shop",
          "taobao_shop",
          "wwxx_shop",
          "mami_shop",
          "xhs_sxt",
          "inner_platform",
          "star_platform",
          "tiktok_local",
          "baidu",
          "xhs_zyx",
          "gaode"
        ] // 重置时默认全选
      };
      this.setSearchDefault();
      this.getList();
    },
    importData(type) {
      this.importType = type;
      this.importDialogVisible = true;
    },
    handleImportDialogClose() {
      this.getList();
      this.importDialogVisible = false;
    },
    async wechatRemind() {
      try {
        await this.getList(true);
      } catch (error) {
        this.$message.error("企业微信提醒发送失败");
      }
    },
    async getList(to_notify = false) {
      this.tableLoading = true;
      try {
        if (this.searchForm.receipt_date) {
          this.searchForm.search_end = this.searchForm.receipt_date[1];
          this.searchForm.search_begin = this.searchForm.receipt_date[0];
        }
        const params = {
          ...this.searchForm,
          department_id: this.school_id,
          to_notify
          // pay_type: this.searchForm.pay_type.join(",")
        };

        const res = await onlinePaymentApi.getList(params);
        if (to_notify) {
          this.$message.success("企业微信提醒发送成功");
          return;
        }
        if (res.status === 200 && res.data.code === 0) {
          this.tableData = res.data.data.results || [];
          this.totalAmount = res.data.data.total_price || 0;
          // 存储汇总数据
          this.summaryData = res.data.data || {};
          this.$nextTick(() => {
            this.$refs.table.doLayout();
          });
        } else {
          // API报错时清空数据
          this.tableData = [];
          this.totalAmount = 0;
          this.summaryData = {};
          this.$message.error(res.data.message || "获取数据失败");
        }
      } catch (error) {
        // 出错时清空数据
        this.tableData = [];
        this.totalAmount = 0;
        this.summaryData = {};
        this.$message.error("获取数据失败");
      } finally {
        this.tableLoading = false;
        // 数据加载完成后更新合计行颜色
        this.$nextTick(() => {
          this.updateSummaryRowColors();
        });
      }
    },
    // 表格合并行方法
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      // 可以根据需要实现行合并逻辑
      return [1, 1];
    },
    // 合计行计算
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      // 如果正在加载或没有数据，不显示合计
      if (this.tableLoading || !data || data.length === 0) {
        return [];
      }

      // 如果没有汇总数据，返回空的合计行
      if (!this.summaryData || Object.keys(this.summaryData).length === 0) {
        columns.forEach((column, index) => {
          if (index === 0) {
            sums[index] = "合计";
          } else {
            sums[index] = "0.00";
          }
        });
        return sums;
      }

      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "合计";
          return;
        }

        // 从汇总数据中获取对应字段的值
        const columnProp = column.property;

        // 根据列属性名生成对应的汇总字段名
        let summaryKey = "";
        if (columnProp) {
          summaryKey = columnProp + "_sum";
        }

        if (
          columnProp &&
          this.summaryData &&
          this.summaryData[summaryKey] !== undefined
        ) {
          // 如果存在对应的汇总字段，直接使用
          sums[index] = Number(this.summaryData[summaryKey]).toFixed(2);
        } else {
          // 如果没有对应的汇总字段，使用原来的计算方式
          const values = data.map((item) => Number(item[columnProp]));
          if (!values.every((value) => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              } else {
                return prev;
              }
            }, 0);
            sums[index] = sums[index].toFixed(2);
          } else {
            sums[index] = "";
          }
        }
      });

      // 在组件mounted后，手动给差额列添加样式
      this.$nextTick(() => {
        this.updateSummaryRowColors();
      });

      return sums;
    },
    // 手动更新合计行差额列的颜色
    updateSummaryRowColors() {
      setTimeout(() => {
        const summaryRow = document.querySelector(
          ".el-table__footer-wrapper .el-table__footer tr"
        );
        if (summaryRow) {
          const cells = summaryRow.querySelectorAll("td");

          cells.forEach((cell, index) => {
            if (index > 0) {
              // 每个支付方式有3列：收据金额、第三方金额、差额
              // 差额列的索引规律：3, 6, 9, 12...（即每3列中的第3列）
              const isThirdColumnInGroup = index % 3 === 0;

              if (isThirdColumnInGroup) {
                const cellText = cell.textContent.trim();
                if (cellText && !isNaN(parseFloat(cellText))) {
                  const value = parseFloat(cellText);
                  if (value > 0) {
                    cell.style.color = "#f56c6c";
                    cell.style.fontWeight = "bold";
                  }
                }
              }
            }
          });
        }
      }, 200);
    },
    viewReceiptDetail(row, paymentKey, columnProp) {
      if (!this.$_has({ m: "onlinePayment", o: "receiptDetail" })) {
        this.$message.error("您没有权限查看收据明细");
        return;
      }
      this.currentPaymentName =
        this.paymentMethodOptions.find((option) => option.key === paymentKey)
          ?.name || "未知支付方式";
      this.currentPaymentKey = paymentKey;
      this.currentRowData = row;
      this.currentDetailType = "receipt";
      this.receiptDetailVisible = true;
    },
    viewThirdPartyDetail(row, paymentKey, columnProp) {
      if (
        !this.$_has({ m: "onlinePayment", o: "yopDetail" }) &&
        paymentKey === "yop"
      ) {
        this.$message.error("您没有权限查看易宝明细");
        return;
      } else if (
        !this.$_has({ m: "onlinePayment", o: "cftDetail" }) &&
        paymentKey === "cft"
      ) {
        this.$message.error("您没有权限查看财付通明细");
        return;
      }
      this.currentPaymentName =
        this.paymentMethodOptions.find((option) => option.key === paymentKey)
          ?.name || "未知支付方式";
      this.currentPaymentKey = paymentKey;
      this.currentRowData = row;
      this.paymentDetailVisible = true;
    },
    handleDialogClose() {
      this.receiptDetailVisible = false;
      this.currentPaymentName = "";
      this.currentPaymentKey = "";
      this.currentRowData = {};
      this.currentDetailType = "receipt"; // 关闭弹窗时重置类型
    },
    handlePaymentDialogClose() {
      this.paymentDetailVisible = false;
      this.currentPaymentName = "";
      this.currentPaymentKey = "";
      this.currentRowData = {};
    },
    headerCellClassName({ column, columnIndex }) {
      if (column.property && column.property.includes("_difference")) {
        return "difference-column";
      }
      return "";
    },
    cellClassName({ column, columnIndex }) {
      if (column.property && column.property.includes("_difference")) {
        return "difference-column";
      }
      return "";
    },
    setSearchDefault() {
      const today = new Date();
      const firstDayOfMonth = new Date(
        today.getFullYear(),
        today.getMonth(),
        1
      );

      // 格式化日期为YYYY-MM-DD格式
      const formatDate = (date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        return `${year}-${month}-${day}`;
      };

      const startDate = formatDate(firstDayOfMonth);
      const endDate = formatDate(today);

      this.$set(this.searchForm, "receipt_date", [startDate, endDate]);
    }
  }
};
</script>
<style lang="less" scoped>
.online-payment-container {
  padding: 16px;
}

.online-payment-table {
  margin-top: 16px;
}

// 差额为正数时显示红色
.el-table .cell {
  .difference-positive {
    color: #f56c6c;
  }
}

::v-deep .statistics-table {
  width: calc(100% - 12px);
  border: 1px solid @base-color;
  .el-table th.is-leaf {
    // border: none;
  }
  .el-table td:last-child {
    // border-right: none;
  }
  .el-table {
    padding: 0;
    border: none;
  }
  th {
    background: @light-color;
  }
  .el-table th:first-child > .cell {
    padding-left: 26px;
  }
  .el-table td:first-child > .cell {
    padding-left: 26px;
  }
  .el-table__body-wrapper {
    height: calc(100% - 46px);
    .empty-container {
      // position: absolute;
      top: 50%;
      left: 50%;
      height: 80px;
    }
    .loading-container {
      position: absolute;
      top: 15%;
      left: 1%;
      width: 100%;
      background: transparent;
      .box {
        height: 100%;
      }
    }
  }

  // 区域合计行样式
  .area-total-row {
    background-color: #f5f7fa !important;
    font-weight: bold !important;

    td {
      background-color: #f5f7fa !important;
      font-weight: bold !important;
    }
  }

  // 总合计行样式
  .grand-total-row {
    background-color: #e6f7ff !important;
    font-weight: bold !important;
    color: #1890ff !important;

    td {
      background-color: #e6f7ff !important;
      font-weight: bold !important;
      color: #1890ff !important;
    }
  }
}
.tg-table__box--title {
  color: red;
  font-size: 23px;
  font-weight: bold;
  // text-align: right;
  padding-right: 16px;
  margin-top: 16px;
}

// 合计行样式
.el-table__footer-wrapper .el-table__footer .cell {
  font-weight: bold;
}

// 强制为合计行的差额列添加颜色
::v-deep .statistics-table .el-table__footer {
  td:nth-child(4),
  td:nth-child(7),
  td:nth-child(10),
  td:nth-child(13),
  td:nth-child(16) {
    color: #f56c6c !important;
    font-weight: bold !important;
  }
}
::v-deep .el-table {
  padding: 0;
}
::v-deep .el-table__fixed-body-wrapper {
  left: 0 !important;
}
::v-deep .el-table__fixed-header-wrapper {
  left: 0 !important;
}
::v-deep .el-table__fixed-footer-wrapper {
  left: 0 !important;
}
</style>
