<!-- 仓库设置 -->
<template>
  <div class="labelListPage container">
    <!-- <pageHeader></pageHeader> -->
    <tg-search
      :searchTitle.sync="search_title"
      :form.sync="searchForm"
      :showNum="3"
      @reset="reset"
      @search="search"
    ></tg-search>
    <el-row class="tg-box--margin tg-shadow--margin tg-box--width">
      <el-button
        type="primary"
        class="tg-button--primary tg-button__icon"
        @click="handler('add')"
        v-has="{ m: 'dianpingConfig', o: 'create' }"
      >
        <img
          src="@/assets/图片/jh.png"
          alt
          class="tg-button__icon--normal"
        />新增
      </el-button>
    </el-row>

    <div class="tg-table__box">
      <div class="tg-box--border"></div>
      <el-table
        ref="table"
        :data="list.data"
        tooltip-effect="dark"
        class="tg-table"
        border
      >
        <el-table-column
          v-for="(item, index) in list.tableColumn"
          :key="index"
          :prop="item.prop"
          :label="item.label"
        >
          <template slot-scope="scope">
            <span>
              {{ scope.row[scope.column.property] }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <!-- v-if="scope.row.status == 2" -->
            <el-button
              @click="handler('edit', scope.row)"
              type="text"
              size="small"
              class="tg-text--blue tg-span__divide-line"
              v-has="{ m: 'dianpingConfig', o: 'create' }"
              >修改</el-button
            >

            <!-- <el-button
              @click="handler('uninstall', scope.row)"
              type="text"
              size="small"
              class="tg-text--red tg-span__divide-line"
              v-has="{ m: 'dianpingConfig', o: 'uninstall' }"
              >解绑</el-button
            > -->
          </template>
        </el-table-column>
        <template slot="empty">
          <div style="margin-top: 15%">
            <TgLoading v-if="loading"></TgLoading>
            <div class="empty-container" v-else>暂无数据～</div>
          </div>
        </template>
      </el-table>
      <div class="tg-pagination">
        <span class="el-pagination__total">共 {{ list.total }} 条</span>
        <el-pagination
          background
          layout="sizes,prev,pager,next,jumper"
          :total="list.total"
          :page-size.sync="list.pageSize"
          :current-page.sync="list.page"
          @current-change="currentChange"
          @size-change="sizeChange"
          :page-sizes="[10, 20, 50, 100]"
        >
        </el-pagination>
      </div>
    </div>
    <handlerDialog ref="handlerDialog" @refresh="getData()"></handlerDialog>
  </div>
</template>

<script>
import handlerDialog from "./create.vue";
import dianpingConfigApi from "@/api/dianpingConfig";
export default {
  components: {
    handlerDialog
  },
  data() {
    return {
      search_title: [
        {
          label: "商户名称",
          props: "name",
          type: "input",
          show: true
        },
        {
          props: "department_id",
          label: "绑定校区",
          show: true,
          selectOptions: [],
          type: "school", // 表单类型
          school_choose_type: "radio", // 校区是否单选
          use_store_options: true
        }
      ],

      searchForm: {
        name: "",
        department_id: ""
      },

      list: {
        data: [],
        tableColumn: [
          {
            prop: "name",
            label: "商户名称"
          },
          {
            prop: "department_name",
            label: "关联校区"
          },
          {
            prop: "binding_at",
            label: "绑定时间"
          },
          {
            prop: "operator",
            label: "操作人"
          }
        ],
        total: 0,
        pageSize: 10,
        page: 1
      },
      loading: false
    };
  },
  mounted() {
    this.getData();
  },

  methods: {
    currentChange(page) {
      this.list.page = page;
      this.getData();
    },
    sizeChange(val) {
      this.list.page = 1;
      this.list.pageSize = val;
      this.getData();
    },
    getData() {
      this.list.data = [];
      this.loading = true;
      const data = {
        page: this.list.page,
        page_size: this.list.pageSize,
        ...this.searchForm
      };
      dianpingConfigApi.getList(data).then((res) => {
        if (res.data.code === 0) {
          this.loading = false;
          this.list.data = res.data.data.results;
          this.list.total = res.data.data.count;
        }
      });
    },
    handler(type, row) {
      if (type === "uninstall") {
        this.uninstallPos(row);
      } else if (type === "sync") {
        this.getData("sync");
      } else {
        this.$refs.handlerDialog.openDialog(type, row);
      }
    },
    uninstallPos(row) {
      this.$confirm("此操作将解绑该商户, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        eBaoPayApi
          .getMerchantDelete({
            department_id: row.department_id,
            merchant_no: row.app_id
          })
          .then((res) => {
            const { code, message } = res.data;
            if (code === 0) {
              this.$message.success("解绑成功!");
              this.getData();
            } else {
              this.$message.error(message);
            }
          });
      });
    },
    // 重置查询条件
    reset() {
      this.searchForm = {
        merchant_no: "",
        department_id: ""
      };
      this.list.page = 1;
      this.list.pageSize = 10;
      this.search();
    },
    // 查询信息
    search() {
      this.list.page = 1;
      this.getData();
    },
    deletes() {
      //   this.$confirm("此操作将删除此仓库, 是否继续?", "提示", {
      //     confirmButtonText: "确定",
      //     cancelButtonText: "取消",
      //     type: "warning",
      //   })
      //     .then(() => {
      //       warehouseApi.delWarehouse({ id: row.id }).then((res) => {
      //         if (+res.status === 200 && +res.data.code === 0) {
      //           this.$message.success("删除数据成功!");
      //           this.getData();
      //         }
      //       });
      //     })
      //     .catch(() => {});
    }
  }
};
</script>

<style lang="less">
.labelListPage {
  padding-top: 16px;
  box-sizing: border-box;
  .dialog_content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  .button-container {
    width: 100%;
  }
  ::v-deep .el-table__body-wrapper {
    height: calc(100% - 46px);
    .empty-container {
      position: absolute;
      top: 50%;
      left: 50%;
    }
    .loading-container {
      position: absolute;
      top: 30%;
      left: 1%;
      background: transparent;
      .box {
        height: 100%;
      }
    }
  }
}
</style>
