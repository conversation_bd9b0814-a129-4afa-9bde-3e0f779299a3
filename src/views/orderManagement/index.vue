<template>
  <div class="container">
    <tg-search
      :searchTitle.sync="search_title"
      :form.sync="searchForm"
      :showNum="2"
      @reset="reset"
      @search="searchVal"
      :hasDefaultDate="true"
      class="tg-box--margin"
    ></tg-search>
    <el-row class="tg-box--margin tg-shadow--margin">
      <el-button
        type="plain"
        class="tg-button--plain"
        v-has="{ m: 'settlement', o: 'create' }"
        @click="addToll"
        >新增收费</el-button
      >
      <el-button
        type="plain"
        class="tg-button--plain"
        v-has="{ m: 'settlement', o: 'create' }"
        @click="batchPush"
        :disabled="+checked_order.length === 0"
        >重新推送</el-button
      >
      <!-- <el-button
        type="plain"
        class="tg-button--plain"
        @click="batchClose"
        :disabled="+checked_order.length === 0"
        >批量关闭</el-button
      >
      <el-button
        type="plain"
        class="tg-button--plain"
        @click="batchDele"
        :disabled="+checked_order.length === 0"
        >批量删除</el-button
      > -->
      <el-button
        type="plain"
        class="tg-button--plain"
        @click="downloadTemplate"
        v-has="{ m: 'order', o: 'import' }"
      >
        下载模版
      </el-button>
      <el-button
        type="plain"
        v-has="{ m: 'order', o: 'export' }"
        class="tg-button--plain"
        @click="exportTable"
        :loading="exportLoading"
        >导出</el-button
      >
      <el-button
        type="plain"
        class="tg-button--plain"
        @click="importTemplate"
        v-has="{ m: 'order', o: 'import' }"
      >
        导入
      </el-button>
      <select-field
        :allFields.sync="table_title"
        :btnType="'button'"
      ></select-field>
    </el-row>
    <div class="tg-table__box">
      <div class="tg-box--border"></div>
      <el-table
        ref="table"
        class="tg-table"
        tooltip-effect="dark"
        :row-key="getRowKeys"
        :data="tableInfo.list"
        :cell-style="{ borderRightColor: '#e0e6ed75' }"
        :header-cell-style="{ borderRightColor: '#e0e6ed75' }"
        border
        @selection-change="handleSelectionChange"
        @sort-change="sortChange"
      >
        <el-table-column
          type="selection"
          width="50"
          :reserve-selection="true"
          :fixed="true"
        >
        </el-table-column>
        <template v-for="(item, index) in table_title">
          <el-table-column
            v-if="item.show"
            :key="index"
            :fixed="item.fixed"
            :prop="item.props"
            :sortable="item.sort"
            :label="item.label"
            :min-width="item.width"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <div v-if="item.props === 'order_id'" class="copy_name">
                <el-button
                  class="tg-text--blue tg-table__name--ellipsis t-a-c"
                  :class="{ 'tg-text--black': !can_see }"
                  type="text"
                  @click="toDetail(scope.row)"
                  >{{ scope.row.order_info["order_id"] }}</el-button
                >
                <div v-copy="scope.row.order_info.order_id"></div>
              </div>

              <!-- <el-button
                v-if="item.props === 'order_id'"
                class="tg-text--blue tg-table__name--ellipsis t-a-c"
                :class="{ 'tg-text--black': !can_see }"
                type="text"
                @click="toDetail(scope.row)"
                >{{ scope.row.order_info["order_id"] }}</el-button
              > -->
              <span v-else-if="item.props === 'department_name'">
                {{ scope.row.order_info["department_name"] }}
              </span>
              <span v-else-if="item.props === 'channel_name'">
                {{ scope.row.order_info["channel_name"] }} </span
              ><span v-else-if="item.props === 'sub_channel_name'">
                {{ scope.row.order_info["sub_channel_name"] }}
              </span>
              <span v-else-if="item.props === 'targetMobile'">
                <mobileHyposensitization
                  :mobileTemInfo="{
                    row: scope.row,
                    has_eye_limit: scope.row?.has_eye_limit,
                    mobile: scope.row.user_info.mobile
                  }"
                ></mobileHyposensitization>
              </span>
              <span v-else-if="item.props === 'created_at'">
                {{ scope.row.order_info["created_at"] }}
              </span>
              <span v-else-if="item.props === 'targetNumber'">
                {{ scope.row.user_info["student_number"] }}
              </span>
              <span v-else-if="item.props === 'student_name'">
                {{ scope.row.user_info["student_name"] }}
              </span>
              <span v-else-if="item.props === 'productName'">
                {{ scope.row | getFilterProductName }}
              </span>

              <span v-else-if="item.props === 'operator'">
                {{ scope.row.creator["employee_name"] }}
              </span>
              <span
                v-else-if="
                  item.props === 'pay_time' &&
                  scope.row.order_info.status == 'paid'
                "
              >
                {{ scope.row.order_info["pay_time"] }}
              </span>
              <span v-else-if="item.props === 'charge_date'">
                {{ scope.row.order_info["charge_date"] }}
              </span>
              <span v-else-if="item.props === 'tw_source_id'">
                {{ scope.row.order_info["tw_source_id"] }}
              </span>
              <span v-else-if="item.props === 'channel_type'">{{
                channel_type_filter(scope.row.order_info.channel_type)
              }}</span>
              <span v-else-if="item.props === 'tw_period'">
                {{ scope.row.order_info["tw_period"] }}
              </span>
              <span v-else-if="item.props === 'status'">
                {{
                  scope.row.order_info.status
                    | getFilterOrderStatusCh(orderStateList)
                }}
              </span>
              <span v-else-if="item.props === 'order_type_ch'">
                {{ scope.row.order_info.order_type_ch }}
              </span>
              <span v-else-if="item.props === 'paymentMethod'">
                {{
                  scope.row.payment | getFilterPaymentMethodCh(payWayOptions)
                }}
              </span>
              <!-- <span v-else-if="item.props === 'order_type'">
                <span v-if="scope.row.order_info.order_type === 'front_end'"
                  >前台订单</span
                >
              </span> -->
              <span v-else-if="item.props === 'employee_name'">
                {{ scope.row.performance | getFilterPerformanceUsers }}
              </span>
              <span v-else-if="item.props === 'is_ylb'">
                <el-tag
                  type="success"
                  v-if="scope.row.order_info.is_ylb === 'yes'"
                >
                  是
                </el-tag>
                <el-tag type="info" v-else>否</el-tag>
              </span>
              <span v-else-if="item.props === 'delivery_method'">
                {{
                  scope.row.order_info.delivery_method == "arrive"
                    ? "到店领取"
                    : scope.row.order_info.delivery_method == "jd"
                    ? "快递"
                    : ""
                }}
              </span>
              <span v-else>{{ scope.row[scope.column.property] }}</span>
            </template>
          </el-table-column>
        </template>
        <el-table-column label="操作" fixed="right">
          <template slot-scope="scope">
            <el-dropdown
              v-if="
                scope.row.order_info.status !== 'paid' &&
                scope.row.order_info.status !== 'cancel' &&
                scope.row.order_info.status !== 'voided' &&
                scope.row.order_info.order_type !== 'wechat_mini'
              "
            >
              <span
                class="el-dropdown-link tg-text--blue"
                style="cursor: pointer"
              >
                操作
              </span>
              <el-dropdown-menu slot="dropdown">
                <!-- 按钮的展示与否等待接口返回对应字段时进行添加 -->
                <el-dropdown-item
                  v-if="scope.row.order_info.status == 'unpaid'"
                  @click.native="toll(scope.row)"
                  v-has="{ m: 'order', o: 'info' }"
                  ><span>收费</span>
                </el-dropdown-item>
                <!-- <el-dropdown-item @click.native="pushAgain(scope.row)"
                  ><span>重新推送</span>
                </el-dropdown-item> -->
                <el-dropdown-item
                  v-has="{ m: 'order', o: 'cancel' }"
                  v-if="scope.row.order_info.status == 'unpaid'"
                  @click.native="cancelOrder(scope.row)"
                  ><span>取消订单</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
        <template slot="empty">
          <div style="margin-top: 15%">
            <TgLoading v-if="tableInfo.loading"></TgLoading>
            <div class="empty-container" v-else>暂无数据～</div>
          </div>
        </template>
      </el-table>
      <!-- 分页 -->
      <div class="tg-pagination">
        <span class="el-pagination__total">共 {{ tableInfo.total }} 条</span>
        <el-pagination
          background
          layout="sizes,prev,pager,next,jumper"
          :total="tableInfo.total"
          :page-size="tableInfo.size"
          :current-page="tableInfo.page"
          @current-change="currentChange"
          @size-change="sizeChange"
          :page-sizes="[10, 20, 50, 100]"
        >
        </el-pagination>
      </div>
    </div>
    <order-detail
      v-if="detail_visible"
      :orderId="order_id"
      :orderInfo="curr_row"
      @close="detailClose"
    ></order-detail>
    <!-- <order-settlement
      v-if="settlement_order_visible"
      @close="settClose"
      :order_id="order_id"
    ></order-settlement> -->
    <settlement
      v-if="settlement_order_visible"
      @close="(settlement_order_visible = false), (to_allocation_ids = [])"
      :settlement_visible="settlement_order_visible"
      :order_id="order_id"
      page_from="order_list"
      @paySuccess="
        clearSelection();
        search();
        to_allocation_ids = [];
      "
      :to_allocation_ids="to_allocation_ids"
      :is_lease_package="true"
    ></settlement>
    <settlementPackage
      v-if="teacher_aid_visible"
      :order_id="order_id"
      :table_list="[]"
      page_from="order_list"
      @close="teacher_aid_visible = false"
      @paySuccess="
        clearSelection();
        search();
      "
    >
    </settlementPackage>
    <priceDialog
      v-if="price_visible"
      :order_id.sync="order_id"
      page_from="order_list"
      @close="price_visible = false"
      @paySuccess="
        clearSelection();
        search();
      "
    >
    </priceDialog>
    <orderImport v-if="importOrder_dialog" @close="closeWindow"></orderImport>
  </div>
</template>

<script>
import chargeApi from "@/api/charge";
import orderApi from "@/api/order";
import { mapState } from "vuex";
import SelectField from "@/components/selectField/selectField.vue";
// import OrderDetail from "@/components/common/orderInfo.vue";
import OrderDetail from "./detail.vue";
import { sleep } from "../../public/utils";
import { downLoadFile } from "@/public/downLoadFile";
import priceDialog from "@/views/charge/priceDialog.vue";
import settlement from "@/views/charge/settlement.vue";
import orderImport from "./common/orderImport";
import quickTime from "@/public/quickTime";
import settlementPackage from "@/views/charge/settlementPackage.vue";
import { tw_channel_list } from "@/public/dict";
export default {
  name: "OrderManagementIndex",
  data() {
    return {
      exportLoading: false,
      importOrder_dialog: false,
      price_visible: false,
      teacher_aid_visible: false,
      height: window.innerHeight - 370,

      search_title: [
        {
          props: "order_id",
          label: "订单信息",
          type: "input",
          show: true,
          placeholder: "请输入订单编号"
        },
        {
          props: "student_info",
          label: "学员信息",
          type: "input",
          show: true,
          placeholder: "请输入学员姓名/学号/手机号"
        },
        {
          props: "created_date",
          label: "下单日期",
          type: "date",
          show: true,
          selectOptions: [],
          has_options: true
        },
        {
          props: "pay_time",
          label: "付款日期",
          type: "date",
          show: false,
          selectOptions: [],
          has_options: true
        },
        {
          props: "charge_date",
          label: "收费日期",
          type: "date",
          show: false,
          selectOptions: [],
          has_options: true
        },
        {
          props: "tw_source_id",
          label: "渠道Id",
          type: "input",
          show: false,
          placeholder: "请输入渠道Id"
        },
        {
          props: "tw_period",
          label: "报名期次",
          type: "input",
          show: false,
          placeholder: "请输入期次"
        },
        {
          // props: "creator_id",
          props: "createdByUserId",
          label: "制单人",
          type: "course_staff",
          show: false,
          is_leave: true,
          selectOptions: []
        },
        {
          // props: "performance_id",
          props: "performanceUserId",
          label: "业绩归属人",
          type: "mark_staff",
          show: false,
          is_leave: true,
          selectOptions: []
        },
        {
          props: "status",
          label: "订单状态",
          type: "select",
          show: false,
          selectOptions: []
        },
        {
          props: ["channel_id", "sub_channel_id", "channel_ids"],
          label: "渠道",
          type: "choose_channel",
          show: false
        },
        // {
        //   props: "student_number",
        //   label: "学号",
        //   type: "input",
        //   show: false,
        //   placeholder: "请输入学员学号"
        // },
        // {
        //   props: "mobile",
        //   label: "电话",
        //   type: "input",
        //   show: false,
        //   placeholder: "请输入学员电话"
        // },
        {
          props: "pay_method",
          // props: "paymentMethod",
          label: "支付方式",
          type: "select",
          show: false,
          selectOptions: []
        },
        {
          props: "order_type",
          // props: "paymentMethod",
          label: "订单类型",
          type: "select",
          show: false,
          selectOptions: [
            {
              id: "",
              name: "不限"
            },
            {
              id: "front_end",
              name: "前台订单"
            },
            {
              id: "beginning",
              name: "期初导入"
            },
            {
              id: "tw_h5",
              name: "TW支付"
            },
            {
              id: "tw_import",
              name: "TW导入"
            },
            {
              id: "wechat_mini",
              name: "微信小程序"
            }
          ]
        },
        {
          props: "goods_name",
          label: "购买产品",
          type: "input",
          show: false,
          placeholder: "请输入购买产品名称"
        },
        {
          props: "is_ylb",
          label: "元萝卜订单",
          type: "select",
          show: true,
          selectOptions: [
            { name: "不限", id: "" },
            { name: "是", id: "yes" },
            { name: "否", id: "no" }
          ]
        },
        // 发货方式
        {
          props: "delivery_method",
          label: "发货方式",
          type: "select",
          show: false,
          selectOptions: [
            {
              name: "不限",
              id: ""
            },
            {
              name: "到店领取",
              id: "arrive"
            },
            {
              name: "快递",
              id: "jd"
            }
          ]
        }
      ],
      searchForm: {
        order_id: "",
        created_date: null,
        pay_time: null,
        goods_name: "",
        charge_date: null,
        tw_source_id: "",
        tw_period: "",
        pay_method: "",
        performanceUserId: "",
        createdByUserId: "",
        student_name: "",
        mobile: "",
        student_number: "",
        status: "",
        sort: "",
        channel_id: "",
        sub_channel_id: "",
        is_ylb: "",
        delivery_method: ""
        // channal_ids: [],
        // channal_ids_name: []
      },
      tableInfo: {
        list: [],
        loading: false,
        total: 0,
        size: 10,
        page: 1
      },
      table_title: [
        {
          props: "order_id",
          label: "订单编号",
          show: true,
          width: 160,
          fixed: true,
          sort: "custom"
        },
        {
          props: "created_at",
          label: "下单日期",
          show: true,
          width: 160,
          sort: "custom"
        },
        {
          props: "department_name",
          label: "下单校区",
          show: true,
          width: 140,
          sort: "custom"
        },
        {
          props: "student_name",
          label: "学员姓名",
          show: true,
          width: 140,
          sort: "custom"
        },
        {
          props: "targetNumber",
          label: "学号",
          show: true,
          width: 140,
          sort: "custom"
        },
        {
          props: "targetMobile",
          label: "手机号",
          show: true,
          width: 140,
          sort: "custom"
        },
        {
          props: "productName",
          label: "购买产品",
          show: true,
          width: 140,
          sort: "custom"
        },
        // {
        //   props: "paymentAmount",
        //   label: "订单金额",
        //   show: true,
        //   width: 140,
        // },
        // {
        //   props: "firstPrice",
        //   label: "首次实交金额",
        //   show: true,
        //   width: 140,
        // },
        {
          props: "status",
          label: "订单状态",
          show: true,
          width: 140,
          sort: "custom"
        },
        {
          props: "order_type_ch",
          label: "订单类型",
          show: true,
          width: 140,
          sort: "custom"
        },
        {
          props: "channel_name",
          label: "订单一级渠道",
          show: true,
          width: 150,
          sort: "custom"
        },
        {
          props: "sub_channel_name",
          label: "订单二级渠道",
          show: true,
          sort: "custom",
          width: 200
        },
        {
          props: "channel_type",
          label: "渠道类型",
          show: true,
          width: 180,
          sort: "custom"
        },
        {
          props: "pay_time",
          label: "付款日期",
          show: true,
          width: 160,
          sort: "custom"
        },
        {
          props: "charge_date",
          label: "收费日期",
          show: true,
          width: 160,
          sort: "custom"
        },
        {
          props: "tw_source_id",
          label: "渠道Id",
          show: true,
          width: 160,
          sort: "custom"
        },
        {
          props: "tw_period",
          label: "报名期次",
          show: true,
          width: 160,
          sort: "custom"
        },
        {
          props: "paymentMethod",
          label: "支付方式",
          show: true,
          width: 140
        },
        {
          props: "operator",
          label: "制单人",
          show: true,
          width: 140,
          sort: "custom"
        },
        {
          props: "employee_name",
          label: "业绩归属人",
          show: true,
          width: 140
        },
        {
          props: "is_ylb",
          label: "元萝卜订单",
          show: true,
          width: 140
        },
        {
          props: "delivery_method",
          label: "发货方式",
          show: true,
          width: 140
        }
      ],
      checked_order: [],
      payWayOptions: [],
      orderStateList: [],
      // discount_list: [],
      settlement_order_visible: false,
      detail_visible: false,
      order_id: "",
      curr_row: null,
      studentInfo: null,
      can_see: false,
      firstCome: true,
      to_allocation_ids: []
    };
  },
  watch: {
    table_title: {
      handler() {
        this.$nextTick(() => {
          this.$refs.table.doLayout();
        });
      },
      immediate: true,
      deep: true
    },
    school_id: {
      handler() {
        this.page = 1;
        this.clearSelection();
        if (this.firstCome) {
          this.setSearchDefault();
        }
        this.searchForm.channel_id = "";
        this.searchForm.sub_channel_id = "";
        this.searchForm.channel_ids = [];
        this.searchForm.channel_ids_name = [];
        this.search();
      },
      immediate: false
    },
    "searchForm.created_date": {
      handler(val) {
        this.firstCome = false;
      },
      deep: true
    }
  },
  filters: {
    getFilterProductName({ items, order_info }) {
      if (items) {
        const is_teacher_package = order_info.is_teach_aid_package;
        if (is_teacher_package) {
          return items
            .filter((item) => item.pid === "")
            .map((item) => item.goods_name)
            .join("、");
        } else {
          const arr = items.map((item) => item.goods_name);
          return arr.join("、");
        }
      }
      return "";
    },
    getFilterPerformanceUsers(list) {
      if (list) {
        const arr = list.map((item) => item.employee_name);
        return arr.join("、");
      }
      return "";
    },
    getFilterOrderStatusCh(val, list) {
      if (list) {
        const arr = list.filter((item) => item.id === val);
        return arr.length > 0 ? arr[0].name : "";
      }
      return "";
    },
    getFilterPaymentMethodCh(val, list) {
      if (val) {
        const payCh = [];
        val.map((item) => {
          const arr2 = list.filter((item2) => {
            return item2.id === item.pay_method;
          });
          const name = arr2.length > 0 ? arr2[0].name : "";
          payCh.push(name);
        });
        return payCh.join("、");
      }
      return "";
    }
  },
  computed: {
    ...mapState({
      ChargeCategory: (state) => state.dictionary.ChargeCategory
    }),
    school_id() {
      return this.$store.getters.doneGetSchoolId;
    }
  },
  mounted() {
    this.setSearchDefault();
    this.$store.dispatch("getChargeCategory", {
      channel: "all"
    });
    this.search();
  },
  components: {
    SelectField,
    OrderDetail,
    settlement,
    priceDialog,
    orderImport,
    settlementPackage
  },
  async created() {
    // this.getDiscount();
    await this.getPaymentMethod();
    await this.getOrderStatus();
    if (this.$_has({ m: "order", o: "info" })) {
      this.can_see = true;
    }
  },
  methods: {
    channel_type_filter(val) {
      const obj = tw_channel_list.find((item) => item.value === val);
      return obj ? obj.label : "";
    },
    clearSelection() {
      this.$nextTick(() => {
        this.$refs.table.clearSelection();
      });
    },
    closeWindow() {
      this.clearSelection();
      this.search();
      this.importOrder_dialog = false;
    },
    // 导入模版
    importTemplate() {
      this.importOrder_dialog = true;
    },
    // 下载模版
    downloadTemplate() {
      const downloadElement = document.createElement("a");
      downloadElement.href =
        "./orderInfo_excel.xlsx?v=" + window.__APP_VERSION__;
      downloadElement.download = `订单信息模版.xlsx`; // 下载后文件名
      document.body.appendChild(downloadElement);
      downloadElement.click(); // 点击下载
      document.body.removeChild(downloadElement);
    },
    detailClose() {
      this.detail_visible = false;
    },
    settClose() {
      this.settlement_order_visible = false;
    },
    // async getDiscount() {
    //   let { data } = await discountApi.getDiscountList();
    //   this.discount_list = data.data;
    // },
    // 收费类型
    getChargeTypeList() {
      this.charge_type_list = [];
      const { ChargeCategory } = this;
      for (const key in ChargeCategory) {
        this.charge_type_list.push({ label: ChargeCategory[key], value: key });
      }
    },
    async getOrderStatus() {
      const res = await orderApi.getOrderStatus();
      const { data } = res.data;
      if (data) {
        for (const key in data) {
          this.orderStateList.push({ name: data[key], id: key });
        }
        for (let i = 0; i < this.search_title.length; i++) {
          if (this.search_title[i].props === "status") {
            this.search_title[i].selectOptions = this.orderStateList;
            this.search_title[i].selectOptions.unshift({
              id: "",
              name: "不限"
            });
          }
        }
      }
    },
    async getPaymentMethod() {
      const res = await chargeApi.paymentMethod();
      const { data } = res.data;
      if (data) {
        data.map((item) => {
          this.payWayOptions.push({ name: item.value, id: item.key });
        });
        for (let i = 0; i < this.search_title.length; i++) {
          if (this.search_title[i].props === "pay_method") {
            this.search_title[i].selectOptions = this.payWayOptions;
            this.search_title[i].selectOptions.unshift({
              id: "",
              name: "不限"
            });
          }
        }
      }
    },

    // 查看订单详情
    toDetail(row) {
      if (this.$_has({ m: "order", o: "info" })) {
        this.curr_row = row.order_info;
        this.order_id = row.order_info.order_id;
        this.detail_visible = true;
      }
    },
    // 重置查询条件
    reset() {
      this.searchForm = {
        order_id: "",
        createdDate: null,
        completedDate: null,
        productName: "",
        paymentMethod: "",
        pay_method: "",
        performanceUserId: "",
        createdByUserId: "",
        target: "",
        status: "",
        charge_date: null,
        tw_source_id: "",
        tw_period: "",
        sort: "",
        channel_id: "",
        sub_channel_id: "",
        delivery_method: "",
        is_ylb: ""
      };
      this.$refs.table.clearSort();
      this.clearSelection();
      this.tableInfo.page = 1;
      this.tableInfo.size = 10;
      this.search();
    },
    searchVal() {
      this.tableInfo.page = 1;
      this.clearSelection();
      this.search();
    },
    sortChange(val) {
      const { prop, order } = val;
      let _oreder = "";
      if (order === "ascending") {
        _oreder = "asc";
      } else if (order === "descending") {
        _oreder = "desc";
      }
      this.searchForm.sort = `${prop} ${_oreder}`;
      this.searchVal();
    },
    // 查询信息
    search() {
      const { page, size } = this.tableInfo;
      const { createdByUserId, performanceUserId } = this.searchForm;

      const query = {
        page,
        page_size: size,
        ...this.searchForm,
        creator_id: createdByUserId,
        performance_id: performanceUserId,
        department_id: this.school_id
      };
      if (!performanceUserId) {
        delete query.performanceUserId;
      }
      delete query?.channel_ids;
      this.tableInfo.list = [];
      this.tableInfo.loading = true;
      orderApi
        .getOrderList(query)
        .then((res) => {
          const { data } = res.data;
          if (res.data.code === 0) {
            this.tableInfo.list = data && data.results;
            this.tableInfo.total = data && data.count;
            this.tableInfo.loading = false;
          }
        })
        .catch((error) => {
          this.$message.error(error.message);
        });
    },
    // 获取选中项
    handleSelectionChange(val) {
      this.checked_order = val;
    },
    getRowKeys(row) {
      return row.id;
    },
    rowStuInfo(row) {
      const { order_info } = row;
      this.studentInfo = {
        student_id: order_info.student_id,
        student_name: order_info.student_name,
        student_number: order_info.student_number,
        department_id: order_info.department_id,
        department_name: order_info.department_name,
        student_mobile: order_info.mobile,
        student_type: "",
        student_category_chn: "",
        stu_useWallet: 0
      };
    },
    // 订单收费
    toll(row) {
      const { order_info, items } = row;

      this.rowStuInfo(row);
      this.order_id = order_info.order_id;
      if (items.length === 1 && items[0].goods_type === "prestore") {
        this.price_visible = true;
        console.log(1);
      } else if (order_info.is_teach_aid_package) {
        this.teacher_aid_visible = true;
      } else {
        console.log(2);
        this.settlement_order_visible = true;
      }
    },
    // 重新推送
    pushAgain() {},
    // 取消订单
    cancelOrder(row) {
      this.$confirm("此操作将取消订单，确定要进行此操作吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          orderApi
            .cancelOrder({
              order_id: row.order_info.order_id
            })
            .then((res) => {
              if (res.data.code === 0) {
                this.$message.success("取消成功！");
                sleep(300).then(() => {
                  this.clearSelection();
                  this.search();
                });
              } else {
                this.$message.error("取消失败！");
              }
            });
        })
        .catch(() => {});
    },
    // 删除订单
    deleOrder(row) {
      this.$confirm("此操作将删除订单，确定要进行删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          orderApi
            .deleteOrder({
              orderId: row.order_id
            })
            .then((res) => {
              if (res.data.code === 0) {
                this.$message.success("删除成功！");
                sleep(300).then(() => {
                  this.clearSelection();
                  this.search();
                });
              } else {
                this.$message.error(res.data.message);
              }
            });
        })
        .catch(() => {});
    },
    // 新增收费
    addToll() {
      this.$router.push({
        path: "/chargeIndex"
      });
    },
    // 批量推送
    batchPush() {
      if (this.checked_order.length <= 0) return;
      console.log(this.checked_order);
    },
    // 批量关闭
    batchClose() {
      if (this.checked_order.length <= 0) return;
      console.log(this.checked_order);
    },
    // 批量删除
    batchDele() {
      if (this.checked_order.length <= 0) return;
      console.log(this.checked_order);
    },
    // 导出
    exportTable() {
      const { createdByUserId, performanceUserId } = this.searchForm;
      this.exportLoading = true;
      const query = {
        ...this.searchForm,
        creator_id: createdByUserId,
        performance_id: performanceUserId,
        department_id: this.school_id
      };
      orderApi.exportExcel({ ...query }).then((res) => {
        downLoadFile(res, "订单列表");
        this.exportLoading = false;
      });
    },
    // 改变页数
    currentChange(page) {
      this.tableInfo.page = page;
      this.search();
    },
    sizeChange(val) {
      this.tableInfo.size = val;
      this.tableInfo.page = 1;
      this.search();
    },
    setSearchDefault() {
      const today = quickTime.GetDate("today");
      this.$set(this.searchForm, "created_date", today);
    }
  }
};
</script>

<style lang="less" scoped>
::v-deep .el-table td:last-child {
  border-right-color: transparent !important;
}
::v-deep .el-table__header th:last-child {
  border-right-color: transparent !important;
}
.t-a-c {
  text-align: center !important;
}

.tg-shadow--margin {
  width: 100%;
}

/deep/ .tg-search {
  div.tg-search__box:first-child .el-input {
    width: 280px;
  }
}

/deep/ .el-button--text {
  color: #157df0;
}
::v-deep .el-table__body-wrapper {
  height: calc(100% - 46px);
  .empty-container {
    position: absolute;
    top: 50%;
    left: 50%;
  }
  .loading-container {
    position: absolute;
    top: 30%;
    left: 1%;
    background: transparent;
    .box {
      height: 100%;
    }
  }
}
</style>
