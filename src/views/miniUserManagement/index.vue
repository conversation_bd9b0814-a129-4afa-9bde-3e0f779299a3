<template>
  <div class="mini-user-management">
    <tg-search
      :searchTitle.sync="searchTitle"
      :form.sync="params"
      @reset="reset"
      :isExport="isExport"
      @educe="exportExcel"
      :hasDefaultDate="true"
      @search="searchList"
      class="tg-box--margin"
    ></tg-search>
    <!-- </div> -->
    <el-row style="margin: 16px 0 0 6px; width: 100%">
      <div style="display: flex">
        <span
          v-for="item in totalList"
          v-has="item.has"
          :key="item.valueKey"
          style="margin-right: 40px"
        >
          {{ item.label }}：<el-button
            type="text"
            @click="getList('is_visitor', item.is_visitor, item.valueKey)"
          >
            {{ item.value }}
          </el-button>
          人</span
        >
        <!-- <span
          style="margin-right: 40px"
          v-has="{ m: 'miniUserManagement', o: 'num' }"
          >当前游客人数：<el-button
            type="text"
            @click="getList('is_visitor', 1)"
            >{{ curTouristNum }}
          </el-button>
          人</span
        >
        <span
          style="margin-right: 40px"
          v-has="{ m: 'miniUserManagement', o: 'customerBindNum' }"
          >当前仅绑定意向人数：<el-button
            type="text"
            @click="getList('is_visitor', 2)"
            >{{ curCustomerNum }}
          </el-button>
          人</span
        >
        <span v-has="{ m: 'miniUserManagement', o: 'studentBindNum' }"
          >当前已绑定学员人数：<el-button
            type="text"
            @click="getList('is_visitor', 3)"
            >{{ curStudentNum }}
          </el-button>
          人</span
        > -->
      </div>
    </el-row>
    <div class="tg-table__box">
      <div class="tg-box--border"></div>
      <el-table
        ref="table"
        class="tg-table"
        tooltip-effect="dark"
        :data="list"
        v-loading="loading"
        :cell-style="{ borderRightColor: '#e0e6ed75' }"
        :header-cell-style="{ borderRightColor: '#e0e6ed75' }"
        border
      >
        <template v-for="(item, index) in table_title">
          <el-table-column
            v-if="item.show"
            :key="index"
            :fixed="item.fixed"
            :prop="item.props"
            :label="item.label"
            :min-width="item.width"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <div v-if="item.props === 'visitor'">
                <span v-if="scope.row.is_visitor === 3">是</span>
                <span v-else>否</span>
              </div>
              <div v-if="item.props === 'is_visitor'">
                <span>{{
                  scope.row.is_visitor === 1
                    ? "游客"
                    : scope.row.is_visitor === 2
                    ? "意向"
                    : "学员"
                }}</span>
              </div>
              <div v-else-if="item.props === 'mobile_code'">
                <template v-if="scope.row.mobile_province_code">
                  {{ scope.row.mobile_province_code }}/{{
                    scope.row.mobile_city_code
                  }}
                </template>
              </div>
              <div v-else-if="item.props === 'we_chat_code'">
                <template v-if="scope.row.we_chat_province_code">
                  {{ scope.row.we_chat_province_code }}/{{
                    scope.row.we_chat_city_code
                  }}
                </template>
              </div>
              <span v-else>{{ scope.row[scope.column.property] }}</span>
            </template>
          </el-table-column>
        </template>
        <el-table-column label="操作" width="200">
          <template slot-scope="scope">
            <el-button
              v-has="{ m: 'miniUserManagement', o: 'bindList' }"
              @click="showStudentList(scope.row)"
              type="text"
              class="tg-text--blue tg-span__divide-line"
              >查看</el-button
            >
            <!-- <el-button
              v-has="{ m: 'surveyCoupon', o: 'studentCouponRedeem' }"
              @click="assignStudent(scope.row)"
              type="text"
              class="tg-text--blue tg-span__divide-line"
              >分配</el-button
            > -->
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="tg-pagination">
        <span class="el-pagination__total">共 {{ totalList[0].value }} 条</span>

        <el-pagination
          background
          layout="sizes,prev,pager,next,jumper"
          :current-page.sync="params.page"
          :page-size="params.page_size"
          :total="totalList[0].value"
          @size-change="handleSizeChange"
          @current-change="currentChange"
        />
      </div>
    </div>
    <el-dialog
      title="查看绑定意向/学员"
      :visible.sync="visible"
      :before-close="handleBindSurveyClose"
    >
      <div class="tg-table__box">
        <div class="tg-box--border"></div>
        <el-table
          ref="table"
          :data="bindStudentList"
          tooltip-effect="dark"
          heigt="500"
          class="tg-table"
          v-loading="false"
        >
          <template v-for="field in bindUserTableTitle">
            <el-table-column
              :key="field.prop"
              :prop="field.prop"
              :label="field.label"
              :width="field.width"
              :min-width="field.width || field.minWidth"
            >
              <template slot-scope="scope">
                <template v-if="$_has({ m: 'survey', o: 'getSurveyStudent' })">
                  <el-button
                    type="text"
                    @click="
                      openSurveyStatusModal(
                        scope.row,
                        scope.row['postStatus'] === '未发布'
                      )
                    "
                    :class="[
                      'num',
                      scope.row['postStatus'] === '未发布' ? 'disabled' : ''
                    ]"
                    v-if="field.prop === 'readNum'"
                    >{{ scope.row[field.prop] }}</el-button
                  >
                  <el-button
                    type="text"
                    @click="
                      openSurveyStatusModal(
                        scope.row,
                        scope.row['postStatus'] === '未发布'
                      )
                    "
                    :class="[
                      'num',
                      scope.row['postStatus'] === '未发布' ? 'disabled' : ''
                    ]"
                    v-else-if="field.prop === 'writeNum'"
                    >{{ scope.row[field.prop] }}</el-button
                  >
                </template>
                <span
                  v-if="!['readNum', 'writeNum'].includes(field.prop)"
                  class="cell-span"
                  >{{ scope.row[field.prop] }}</span
                >
              </template>
            </el-table-column>
          </template>
          <template slot="empty">
            <div style="margin-top: 2%; height: 300px">
              <loading v-if="isLoading"></loading>
              <div class="empty-container" v-else>暂无数据～</div>
            </div>
          </template>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import miniUserManagement from "@/api/miniUserManagement";
import loading from "@/views/loading";
import { export_excel_sync_new } from "@/public/asyncExport";
export default {
  components: { loading },
  data() {
    return {
      totalRegisterNum: 0, // 总注册人数
      curTouristNum: 0, // 当前游客人数
      curStudentNum: 0, // 当前学员人数
      curCustomerNum: 0, // 当前客户人数
      isExport: false,
      totalList: [
        {
          label: "总注册人数",
          is_visitor: undefined,
          valueKey: "totalRegisterNum",
          value: 0
        },
        {
          label: "当前游客人数",
          is_visitor: 1,
          valueKey: "curTouristNum",
          has: { m: "miniUserManagement", o: "num" },
          value: 0
        },
        {
          label: "当前仅绑定意向人数",
          is_visitor: 2,
          valueKey: "curCustomerNum",
          has: { m: "miniUserManagement", o: "customerBindNum" },
          value: 0
        },
        {
          label: "当前已绑定学员人数",
          is_visitor: 3,
          valueKey: "curStudentNum",
          has: { m: "miniUserManagement", o: "studentBindNum" },
          value: 0
        }
      ],
      searchTitle: [
        {
          props: "mobile",
          label: "手机号",
          type: "input",
          show: true
        },
        {
          props: "mobile_area",
          label: "省份/市区",
          placeholder: "请选择省市",
          type: "area"
        },
        {
          props: "we_chat_area",
          label: "微信授权区域",
          placeholder: "请选择省市",
          type: "area"
        },
        {
          props: "is_visitor",
          label: "选择角色",
          type: "select",
          show: true,
          selectOptions: [
            {
              id: "",
              name: "不限"
            },
            {
              id: 1,
              name: "游客"
            },
            {
              id: 2,
              name: "意向"
            },
            {
              id: 3,
              name: "学员"
            }
          ]
        },
        {
          props: "department_id",
          label: "意向校区",
          type: "school",
          show: false,
          selectOptions: [],
          school_choose_type: "radio",
          use_store_options: true
        }
      ],
      search: {},
      params: {
        page: 1,
        page_size: 10,
        mobile_area: [],
        we_chat_area: [],
        department_id: "",
        is_visitor: "",
        mobile_province: "",
        mobile_city: "",
        we_chat_province: "",
        we_chat_city: ""
      },
      total: 0,
      list: [],
      loading: false,
      isLoading: false,
      table_title: [
        {
          props: "mobile",
          label: "手机号",
          width: 100,
          show: true
        },
        {
          props: "created_at_str",
          label: "注册时间",
          width: 120,
          show: true
        },
        {
          props: "is_visitor",
          label: "角色",
          width: 120,
          show: true
        },
        {
          props: "mobile_code",
          label: "省份/市区",
          width: 120,
          show: true
        },
        {
          props: "location_city_code",
          label: "微信授权区域",
          width: 120,
          show: true
        },
        {
          props: "visitor",
          label: "是否绑定学员",
          show: true
        },
        {
          props: "department_name",
          label: "意向校区",
          show: true
        }
      ],
      visible: false,
      bindStudentList: [],
      bindUserTableTitle: [
        {
          prop: "student_name",
          label: "姓名"
        },
        {
          prop: "student_type_cha",
          label: "学员状态"
        },
        {
          prop: "student_number",
          label: "学号"
        },
        {
          prop: "department_name",
          label: "所属校区"
        },
        {
          prop: "created_at_str",
          width: 190,
          label: "绑定时间"
        }
      ]
    };
  },
  created() {
    if (this.$_has({ m: "miniUserManagement", o: "export" })) {
      this.isExport = true;
    }
    this.getList();
    this.getNum();
  },
  methods: {
    getNum() {
      this.getVisitorNum();
      this.getvisitorStudentBindNum();
      this.getvisitorCustomerBindNum();
    },
    async getList(key, value, valueKey) {
      console.log(key, value, valueKey, "key, value, valueKey");
      if (key) {
        this.params[key] = value;
      }
      for (let i = 0; i < this.totalList.length; i++) {
        const item = { ...this.totalList[i] };
        item.value = 0;
      }
      const res = await miniUserManagement.VisitorList(this.getParams());
      const { code, data, message } = res;
      if (code === 0) {
        this.geCurTotaltNum("totalRegisterNum", data.count);
        this.list = data.results;
        if (valueKey === "totalRegisterNum") {
          this.getNum();
        }
        if (valueKey === "curTouristNum") {
          this.getVisitorNum(valueKey, data.count);
        }
        if (valueKey === "curCustomerNum") {
          this.getvisitorCustomerBindNum();
        }
        if (valueKey === "curStudentNum") {
          this.getvisitorStudentBindNum();
        }
      } else {
        this.$message.error(message);
      }
    },

    async exportExcel() {
      const opt = {
        vm: this, // vue组件实例，
        api_url:
          "/api/questionnaire-service/admin/miniProgram/visitor/visitorListExport", // 接口地址
        file_name: "小程序用户管理", // 文件名
        success_msg: "小程序用户管理导出成功！", // 导出成功的提示语
        error_msg: "小程序用户管理导出失败！", // 导出失败的提示语,
        query: this.getParams()
      };
      export_excel_sync_new(opt);
    },
    currentChange(page) {
      this.params.page = page;
      this.getList();
    },
    handleSizeChange(size) {
      this.params.page_size = size;
      this.getList();
    },
    reset() {
      this.params = {
        page: 1,
        page_size: 10,
        mobile_area: [],
        we_chat_area: [],
        department_id: "",
        is_visitor: "",
        mobile_province: "",
        mobile_city: "",
        we_chat_province: "",
        we_chat_city: ""
      };
      this.getNum();
      this.getList();
    },
    getParams() {
      const params = this.params;
      if (params.mobile_area.length) {
        const mobileArea = params.mobile_area.map((item) =>
          item.replace(/[省市]$/, "")
        );
        params.mobile_province = mobileArea[0];
        params.mobile_city = mobileArea[1];
      } else {
        params.mobile_province = "";
        params.mobile_city = "";
      }
      if (params.we_chat_area.length) {
        const weChatArea = params.we_chat_area.map((item) =>
          item.replace(/[省市]$/, "")
        );
        params.we_chat_province = weChatArea[0];
        params.we_chat_city = weChatArea[1];
      } else {
        params.we_chat_province = "";
        params.we_chat_city = "";
      }
      console.log(params, "params");
      return params;
    },
    async getVisitorNum() {
      const res = await miniUserManagement.VisitorNum(this.params);
      this.geCurTotaltNum("curTouristNum", res.data);
    },
    async getvisitorStudentBindNum() {
      const res = await miniUserManagement.VisitorStudentBindNum(this.params);
      this.geCurTotaltNum("curStudentNum", res.data);
    },
    async getvisitorCustomerBindNum() {
      const res = await miniUserManagement.VisitorCustomerBindNum(this.params);
      this.geCurTotaltNum("curCustomerNum", res.data);
    },
    geCurTotaltNum(key, value) {
      for (let i = 0; i < this.totalList.length; i++) {
        const item = this.totalList[i];
        if (item.valueKey === key) {
          item.value = value;
        }
      }
    },
    searchList() {
      this.params.page = 1;
      this.getNum();
      this.getList();
    },
    handleBindSurveyClose() {
      this.visible = false;
    },
    showStudentList(row) {
      this.visible = true;
      this.getBindStudentList(row);
    },
    async getBindStudentList(row) {
      const res = await miniUserManagement.VisitorBindList({
        open_id: row.open_id
      });
      if (res.code === 0) {
        this.bindStudentList = res.data;
      } else {
        this.$message.error(res.message);
      }
    }
  }
};
</script>

<style></style>
