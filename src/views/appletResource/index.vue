<template>
  <div class="applet-resource">
    <div class="left-panel">
      <div class="tg-type-title">类别</div>
      <div
        v-for="(item, index) in resource_type_list"
        :class="{ active: index === resource_type }"
        @click="changeType(index)"
        :key="item"
        class="item-box"
      >
        {{ item }}
      </div>
    </div>
    <div class="center">
      <!-- 筛选 -->
      <div class="heard">
        <tg-search
          ref="search"
          :searchTitle.sync="search_title"
          :form.sync="formInline"
          :showNum="4"
          @reset="reset"
          @search="searchVal"
        ></tg-search>
        <el-row class="tg-box--margin tg-shadow--margin tg-row--height">
          <el-button
            type="plain"
            class="tg-button--plain"
            @click="add(1, '', 1)"
            v-has="{ m: 'appletResource', o: 'create' }"
            >新建分类</el-button
          >
        </el-row>
      </div>
      <div class="table_box">
        <div class="tg-table__box" style="width: 55%">
          <div class="tg-box--border"></div>
          <!-- @sort-change="sortChange" -->
          <el-table
            :data="coursewareData"
            style="width: 100%"
            :default-sort="{ prop: '', order: '' }"
            @sort-change="sortChange"
            row-key="id"
            :tree-props="{ children: 'children' }"
            ref="table"
            class="tg-table"
            highlight-current-row
            @row-click="handleCurrentChange"
            :cell-style="{ borderRightColor: '#e0e6ed75' }"
            :header-cell-style="{ borderRightColor: '#e0e6ed75' }"
            border
          >
            <el-table-column type="index" width="50" label="序号">
              <template slot-scope="scope">
                <div>
                  {{ scope.row.level === 1 ? scope.row.index : "" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="name"
              width="250"
              :label="resource_type_list[resource_type] + '资源分类'"
            >
              <template slot-scope="scope">
                <div class="channel-table--inline">
                  <!-- <div
                    class="el-table__expand-icon"
                    style="display: inline-block; margin-right: 10px"
                    @click="handleRowClick(scope.row)"
                  >
                    <i style="display: inline-block"></i>
                  </div> -->
                  <span :class="{ 'tg-text--disabled': !scope.row.status }">{{
                    scope.row.name
                  }}</span>
                  <!-- <el-tag
                    size="mini"
                    :type="scope.row.level === 1 ? 'success' : 'warning'"
                    :class="{ 'tg-text--disabled': !scope.row.status }"
                  >
                    {{ scope.row.level === 1 ? "一级分类" : "二级分类" }}</el-tag
                  > -->
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="80">
              <template slot-scope="scope">
                <span class="tg-text--success" v-if="scope.row.status == 1"
                  >可用</span
                >
                <span class="tg-text--msg" v-else>不可用</span>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" min-width="170" label="创建时间">
              <template slot-scope="scope">
                <div>
                  {{
                    moment(scope.row.created_at).format("YYYY-MM-DD HH:mm:ss")
                  }}
                </div>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="250">
              <template slot-scope="scope">
                <div class="tg-btn">
                  <span
                    class="tg-text--primary tg-span__divide-line"
                    v-has="{ m: 'appletResource', o: 'info' }"
                    @click="add(2, scope.row.id, scope.row.level)"
                    >编辑</span
                  >
                  <template v-if="scope.row.status == 1">
                    <span
                      class="tg-text--primary tg-span__divide-line"
                      @click="add(3, scope.row.id, scope.row.level)"
                      v-if="scope.row.level < 3"
                      :class="{ 'tg-text--disabled': !scope.row.status }"
                      >添加子分类</span
                    >
                    <span
                      class="tg-text--primary tg-span__divide-line"
                      @click="handleUpDate(scope.row)"
                      :class="{ 'tg-text--disabled': !scope.row.status }"
                      v-has="{ m: 'appletResource', o: 'file_list' }"
                      >上传文件</span
                    >
                    <span
                      class="tg-span__divide-line tg-text--primary"
                      v-has="{ m: 'appletResource', o: 'school_auth' }"
                      @click="openDepart(scope.row)"
                      v-if="scope.row.level === 1"
                      >授权</span
                    >
                  </template>
                </div>
                <!-- <div v-else class="tg-btn">
                  <span
                    class="tg-text--primary tg-span__divide-line"
                    @click="openDialog(5, scope.row)"
                    v-has="{ m: 'subchannel', o: 'update' }"
                    >编辑</span
                  >
                </div> -->
              </template>
            </el-table-column>
            <template slot="empty">
              <div style="margin-top: 15%">
                <TgLoading v-if="loading"></TgLoading>
                <div class="empty-container" v-else>暂无数据～</div>
              </div>
            </template>
          </el-table>
          <div class="tg-pagination" v-show="tableInfo.total > 0">
            <pagination
              v-show="tableInfo.total > 0"
              :total="tableInfo.total"
              :page.sync="tableInfo.page"
              :limit.sync="tableInfo.page_size"
              @pagination="getList"
            />
          </div>
        </div>
        <div class="tg-table__box" style="width: 40%">
          <el-button
            type="primary"
            style="margin-bottom: 10px"
            @click="courseUpload"
            :disabled="courseware.databank_id == '' || courseware.status == 2"
            v-has="{ m: 'appletResource', o: 'upload' }"
            >上传</el-button
          >
          <div class="tg-box--border" style="top: 50px"></div>
          <el-table
            :data="coursewareDataChild"
            style="width: 100%"
            ref="table"
            class="tg-table"
            :cell-style="{ borderRightColor: '#e0e6ed75' }"
            :header-cell-style="{ borderRightColor: '#e0e6ed75' }"
            border
          >
            <el-table-column type="index" width="50" label="序号">
              <template slot-scope="scope">
                <div>
                  {{ scope.$index + 1 }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="名称" prop="name"> </el-table-column>
            <el-table-column prop="file_name" width="180" label="文件名称">
            </el-table-column>
            <el-table-column
              fixed="right"
              label="操作"
              width="130"
              align="center"
            >
              <template slot-scope="scope">
                <div class="tg-btn">
                  <span
                    class="tg-span__divide-line tg-text--blue"
                    @click="openResourcePreview(scope.row)"
                    >查看</span
                  >
                  <span
                    class="tg-span__divide-line tg-text--error"
                    @click="delData(scope.row)"
                    v-has="{ m: 'appletResource', o: 'delete' }"
                    >删除</span
                  >
                </div>
              </template>
            </el-table-column>
            <template slot="empty">
              <div style="margin-top: 15%">
                <TgLoading v-if="wareLoading"></TgLoading>
                <div class="empty-container" v-else>暂无数据～</div>
              </div>
            </template>
          </el-table>
          <!-- <div class="tg-pagination" v-show="uploadInfo.total > 0">
            <pagination
              :total="uploadInfo.total"
              :page.sync="uploadInfo.page"
              :limit.sync="uploadInfo.page_size"
              @pagination="getFileList"
            />
          </div> -->
        </div>
      </div>
      <addCoursewareType @addSort="addSort" ref="addCoursewareType" />

      <courseUpload
        :resource_type="resource_type"
        ref="courseUpload"
        @handleFileUpload="handleFileUpload"
      />
      <schools-authorize
        v-if="school_tree_visible"
        :depart_ids="checked_ids"
        type="edit"
        @close="school_tree_visible = false"
        @save="saveSchool"
      ></schools-authorize>
      <resourcePreview
        v-if="preview_visible"
        :url="preview_url"
        :poster="poster"
        @close="preview_visible = false"
      ></resourcePreview>
    </div>
  </div>
</template>

<script>
import appletResource from "@/api/appletResource";
import addCoursewareType from "./diag/add_coursewareType.vue";
import SchoolsAuthorize from "@/views/charge/schoolsAuthorize.vue";
import resourcePreview from "./diag/resourcePreview.vue";
import courseUpload from "./diag/course_upload.vue";
import pagination from "@/components/Pagination";
export default {
  name: "appletResourceIndex",
  props: {},
  components: {
    addCoursewareType,
    courseUpload,
    pagination,
    SchoolsAuthorize,
    resourcePreview
  },
  data() {
    return {
      school_tree_visible: false,
      authorizeSchool: [],
      preview_visible: false,
      preview_url: "",
      poster: "",
      resource_type: 0,
      resource_type_list: ["图片库", "视频库", "话术库"],
      search_title: [
        {
          props: "name",
          label: "一级分类名称",
          type: "input",
          show: true,
          placeholder: "一级分类名称"
        },
        {
          props: "status",
          label: "状态",
          type: "select",
          show: true,
          selectOptions: [
            { name: "不限", id: 0 },
            { name: "可用", id: 1 },
            { name: "不可用", id: 2 }
          ]
        }
      ],
      formInline: {
        name: "",
        status: 0
      },
      tableInfo: {
        page: 1,
        page_size: 10,
        total: 0
      },
      uploadInfo: {
        page: 1,
        page_size: 10,
        total: 0
      },
      coursewareData: [],
      coursewareDataChild: [],
      loading: false,
      wareLoading: false,
      sortData: {},
      keyValue: false,
      courseware: {
        databank_id: "",
        level: 1,
        status: 1
      },
      department_id: "",
      checked_ids: []
    };
  },
  // 计算属性
  computed: {},
  // 侦听器
  watch: {},

  methods: {
    openResourcePreview(row) {
      this.preview_url = row?.content ?? "";
      this.poster = row?.poster ?? "";
      this.preview_visible = true;
    },
    setAuthDepartmentIds(ids, schoolsData) {
      const { authorizeSchool } = this;
      const all_department_ids = schoolsData.map((item) => {
        if (item.pid !== "") {
          return item.id;
        }
      });
      // 找出authorizeSchool中不在all_department_ids中的值
      const diff_ids = authorizeSchool.filter(
        (item) => !all_department_ids.includes(item)
      );
      return ids.concat(diff_ids);
    },
    saveSchool(ids, schoolsData) {
      const department_ids = this.setAuthDepartmentIds(ids, schoolsData);
      if (ids) {
        appletResource
          .school_auth({
            departments: department_ids,
            databank_id: this.courseware.databank_id
          })
          .then((res) => {
            if (res.data.code === 0) {
              this.$message.success("授权成功");
              this.getList();
            } else {
              this.$message.error(res.data.message);
            }
          });
      }
    },
    changeType(t) {
      this.resource_type = t;
      this.getList();
      this.coursewareDataChild = [];
      this.courseware.databank_id = "";
    },
    async add(type, id, level) {
      const level_firet = type === 2 ? level : level + 1;
      this.wareType = type;
      this.$refs.addCoursewareType.onShow(type, id, level_firet);
    },
    async getList() {
      this.loading = true;
      const res = await appletResource.getList({
        ...this.formInline,
        ...this.tableInfo,
        category_id: (this.resource_type + 1).toString()
      });
      this.loading = false;
      this.coursewareData = res.data.data.results ?? [];
      this.tableInfo.total = res.data.data.count ?? 0;
      console.log(this.tableInfo.total);
    },
    reset() {
      // this.formInline,
      Object.assign(this.formInline, this.$options.data().formInline);
      Object.assign(this.uploadInfo, this.$options.data().uploadInfo);
      Object.assign(this.courseware, this.$options.data().courseware);
      this.tableInfo.page = 1;
      this.uploadInfo.page = 1;
      this.coursewareDataChild = [];
      this.getList();
      // this.getFileList();
    },
    searchVal() {
      this.tableInfo.page = 1;
      this.uploadInfo.page = 1;
      this.getList();
    },
    async addSort(data) {
      const { resource_type } = this;
      // const { level } = data;
      const res = await appletResource.create({
        ...data,
        category_id: (resource_type + 1).toString()
      });
      if (res.data.code === 0) {
        this.$message.success("创建成功！");
        this.$refs.addCoursewareType.handleClose();
        if (this.wareType === 1) {
          this.reset();
          this.getList();
        } else {
          this.getList();
        }
      } else {
        this.$message.error(res.data.message);
      }
    },

    delData(row) {
      this.$confirm("此操作将删除该课件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        appletResource
          .del({
            name: row.name,
            databank_id: row.databank_id
          })
          .then((res) => {
            if (res.data.code === 0) {
              this.$message.success("删除成功");
              this.getFileList();
            } else {
              this.$message.error(res.data.message);
            }
          });
      });
    },
    sortChange({ column }) {
      this.saveSort = column.order;
      this.refreshRow(-1);
    },
    refreshRow(id) {
      for (const i in this.$refs.table.store.states.treeData) {
        if (id !== i) {
          this.$refs.table.store.states.treeData[i].loaded = false;
          this.$refs.table.store.states.treeData[i].expanded = false;
        }
      }
    },
    // 授权打开校区
    openDepart(row) {
      // this.school_data = res.data.department_ids ?? [];
      this.checked_ids = row.departments ?? [];
      this.school_tree_visible = true;

      this.courseware.databank_id = row.id;
    },
    handleUpDate(row) {
      this.courseware.databank_id = row.id;
      this.courseware.level = row.level;
      this.courseware.status = row.status;
      this.authorizeSchool = row.departments ?? [];
      this.getFileList();
    },

    // 上传资源
    handleFileUpload(fileData) {
      // 如果是话术
      if (this.resource_type === 2) {
        fileData.file_type = "text";
      }
      appletResource
        .upload({
          ...fileData,
          category_id: (this.resource_type + 1).toString()
        })
        .then((res) => {
          if (res.data.code === 0) {
            this.$message.success("保存成功");
            this.$refs.courseUpload.handleClose();
            this.getFileList();
            // this.getList();
          } else {
            this.$message.error(res.data.message);
          }
        });
    },
    courseUpload() {
      this.$refs.courseUpload.onShow(this.courseware.databank_id);
    },
    async getFileList() {
      const res = await appletResource.getFileList({
        ...this.courseware
        // ...this.uploadInfo
      });
      this.coursewareDataChild = res.data.data.results ?? [];
    },
    // 表格高亮
    handleCurrentChange(val) {
      this.$refs.table.setCurrentRow(val);
      this.handleUpDate(val);
    }
  },
  created() {},
  mounted() {
    this.getList();
  }
};
</script>

<style lang="less" scoped>
@baseColor: #2f7fff;
@successColor: #29cb97;
@msgColor: #b3b7c6;
@errColor: #fd5461;
.center {
  margin-top: 20px;
  width: calc(100% - 230px - 16px);
  .heard {
    width: 100%;
  }
}
.table_box {
  display: flex;
  // min-height: 300px;
  .tg-table__box {
    // height: 400px;
    /deep/ .el-table {
      height: 100% !important;
      padding: 0 !important;
      .el-table__body-wrapper {
        overflow: auto;
      }
      .tg-text--primary {
        color: @baseColor;
      }
      .el-table__fixed-header-wrapper {
        right: 0 !important;
      }
      .el-table__fixed-body-wrapper {
        right: 0 !important;
      }
      .tg-text--error {
        color: @errColor;
        font-size: 13px;
      }
      .channel-table--inline {
        display: inline-block;
        span + .el-tag {
          margin-left: 10px;
        }
      }
      .tg-text--success {
        color: @successColor;
      }
      .tg-text--msg {
        color: @msgColor;
      }
      .tg-span__divide-line {
        cursor: pointer;
        position: relative;
        &:after {
          content: "";
          background: #cbcfda;
          width: 1px;
          height: 13px;
          position: absolute;
          top: 3px;
          margin-left: 4px;
        }
      }
      span:last-child.tg-span__divide-line {
        &::after {
          background: transparent;
        }
      }
      .tg-span__divide-line + .tg-span__divide-line {
        margin-left: 10px;
      }
    }
  }
}
.applet-resource {
  display: flex;
  height: 100%;
  .left-panel {
    margin-top: 20px;
    background-color: #fff;
    width: 230px;
    margin-right: 16px;
    .tg-type-title {
      padding: 16px 20px;
    }
    .item-box {
      font-size: 13px;
      font-family: "PingFangSC-Medium, sans-serif,Arial";
      color: #475669;
      display: inline-block;
      white-space: nowrap;
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      padding: 0px 16px;
      cursor: pointer;
      height: 48px;
      line-height: 48px;
      transition: font-size 0.3s;
      &:hover,
      &.active {
        background-color: #ebf4ff;
        border-left: 2px solid #2d80ed;
        font-size: 14px;
      }
    }
  }
}
</style>
