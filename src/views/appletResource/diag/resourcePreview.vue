<template>
  <el-dialog
    title="资源预览"
    :visible="true"
    width="1016px"
    class="custom_edit_dialogs"
    append-to-body
    :before-close="back"
  >
    <div v-if="file_type" class="resourcePreview">
      <div v-if="file_type === 'image'" class="image-content">
        <img :src="url" alt="" />
      </div>
      <div v-else-if="file_type === 'video'" class="video-content">
        <video :poster="poster" :src="url" controls controlsList="nodownload" />
      </div>
      <div v-else class="text-content">
        <el-input
          class="textarea"
          disabled
          type="textarea"
          :value="url"
        ></el-input>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button class="tg-button--plain" type="plain" @click="back"
        >关闭</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: "resourcePreview",
  props: {
    // 文件url
    url: {
      type: String,
      default: "",
      required: true
    },
    poster: {
      type: String,
      default: "",
      required: false
    }
  },
  data() {
    return {
      file_type: ""
    };
  },
  mounted() {
    //  file_accept:"image/png,image/jpg,image/jpeg,video/mp4,video/mpeg,video/webm,video/ogg",
    if (this.url.match(/\.(jpg|jpeg|png|gif)$/)) {
      this.file_type = "image";
    } else if (this.url.match(/\.(mp4|mpeg|webm|ogg)$/)) {
      this.file_type = "video";
    } else {
      this.file_type = "text";
    }
  },
  methods: {
    back() {
      this.$emit("close");
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .el-dialog__body {
  background-color: #f1f1f1;
}
.video-content {
  width: 800px;
  height: 600px;
  background-color: #000;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #000;
  video {
    width: 100%;
    height: 100%;
    background-color: #000;
  }
}
.image-content {
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    min-width: 100px;
    max-width: 100%;
  }
}
.textarea {
  /deep/ .el-textarea__inner {
    height: 300px;
    border: none;
    background: transparent;
    color: #000;
    cursor: auto;
  }
}
</style>
