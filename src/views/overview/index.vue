<template>
  <div class="dashboard-container">
    <!-- 全局加载遮罩 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="spinner"></div>
      <div class="loading-text">加载中...</div>
    </div>
    <div class="tg-search__box">
      <tg-search
        :searchTitle.sync="search_title"
        :form.sync="searchForm"
        :showNum="1"
        :isExport="false"
        @reset="reset"
        @search="searchVal"
        class="tg-box--margin"
        :hasDefaultDate="false"
      >
      </tg-search>
      <el-button
        @click="goWechatOverview"
        type="primary"
        class="tg-button--primary"
        >切换企微看板</el-button
      >
    </div>

    <!-- 第一行：三个指标数值展示 -->
    <div class="metrics-row">
      <!-- 预算流 -->
      <div class="metric-card is-cash">
        <div class="metric-header">
          <h3>净现金流</h3>
          <div class="metric-icon cash-flow">
            <i class="el-icon-money"></i>
          </div>
        </div>
        <div class="metric-main-value">
          <span class="main-number"
            >¥{{ formatCurrency(budgetMetrics.cashFlow.current) }}</span
          >
        </div>
        <div class="metric-current-label">当前值</div>
        <div class="metric-bottom">
          <div class="metric-target">
            <span class="target-label">目标值</span>
            <span class="target-value green"
              >¥{{ formatCurrency(budgetMetrics.cashFlow.target) }}</span
            >
          </div>
          <div class="metric-target">
            <div class="metric-percentage green">
              {{ budgetMetrics.cashFlow.percentage }}%
            </div>
            <span class="target-label">完成率</span>
          </div>
        </div>
        <div class="metric-progress">
          <el-progress
            :percentage="progressAnimated.cashFlow"
            :stroke-width="8"
            color="#10b981"
            :show-text="false"
          ></el-progress>
        </div>
      </div>

      <!-- 利润 -->
      <div class="metric-card is-profit">
        <div class="metric-header">
          <h3>净利润</h3>
          <div class="metric-icon profit">
            <i class="el-icon-s-finance"></i>
          </div>
        </div>
        <div class="metric-main-value">
          <span class="main-number blue"
            >¥{{ formatCurrency(budgetMetrics.profit.current) }}</span
          >
        </div>
        <div class="metric-current-label">当前值</div>
        <div class="metric-bottom">
          <div class="metric-target">
            <span class="target-label">目标值</span>
            <span class="target-value blue"
              >¥{{ formatCurrency(budgetMetrics.profit.target) }}</span
            >
          </div>
          <div class="metric-target">
            <div class="metric-percentage blue">
              {{ budgetMetrics.profit.percentage }}%
            </div>
            <span class="target-label">完成率</span>
          </div>
        </div>
        <div class="metric-progress">
          <el-progress
            :percentage="progressAnimated.profit"
            :stroke-width="8"
            color="#2d80ed"
            :show-text="false"
          ></el-progress>
        </div>
      </div>

      <!-- 人数 -->
      <div class="metric-card is-headcount">
        <div class="metric-header">
          <h3>正课在读人数</h3>
          <div class="metric-icon headcount">
            <i class="el-icon-user-solid"></i>
          </div>
        </div>
        <div class="metric-main-value">
          <span class="main-number">{{ budgetMetrics.headcount.current }}</span>
        </div>
        <div class="metric-current-label">当前值</div>
        <div class="metric-bottom purple">
          <div class="metric-target">
            <span class="target-label">目标值</span>
            <span class="target-value purple"
              >{{ budgetMetrics.headcount.target }}人</span
            >
          </div>
          <div class="metric-target">
            <div class="metric-percentage purple">
              {{ budgetMetrics.headcount.percentage }}%
            </div>
            <span class="target-label">完成率</span>
          </div>
        </div>
        <div class="metric-progress">
          <el-progress
            :percentage="progressAnimated.headcount"
            :stroke-width="8"
            color="#7c3aed"
            :show-text="false"
          ></el-progress>
        </div>
      </div>
    </div>

    <!-- 第二行：预算完成度 + 校区健康度 + 业务构成 -->
    <div class="charts-row">
      <!-- 预算完成度 -->
      <div class="chart-card budget-completion-card">
        <div class="budget-header">
          <h3>预算完成度</h3>
          <div class="budget-icon">
            <i class="el-icon-s-data"></i>
          </div>
        </div>
        <div class="budget-list">
          <!-- 营收（税后） -->
          <div class="budget-row">
            <div class="left">
              <div class="icon-box teal">
                <i class="el-icon-money"></i>
              </div>
              <div class="texts">
                <div class="title">营收（税后）</div>
                <div class="sub">
                  ¥{{ formatCurrency(budgetCompletion.revenue.current) }} / ¥{{
                    formatCurrency(budgetCompletion.revenue.target)
                  }}
                </div>
              </div>
            </div>
            <div class="right">
              <div class="percent teal">
                {{ budgetCompletion.revenue.percentage }}%
              </div>
              <div class="mini-bar">
                <div
                  class="fill teal"
                  :style="{
                    width:
                      (barsReady ? budgetCompletion.revenue.percentage : 0) +
                      '%'
                  }"
                ></div>
              </div>
            </div>
          </div>

          <!-- 成本 -->
          <div class="budget-row">
            <div class="left">
              <div class="icon-box rose">
                <i class="el-icon-tickets"></i>
              </div>
              <div class="texts">
                <div class="title">成本</div>
                <div class="sub">
                  ¥{{ formatCurrency(budgetCompletion.cost.current) }} / ¥{{
                    formatCurrency(budgetCompletion.cost.target)
                  }}
                </div>
              </div>
            </div>
            <div class="right">
              <div class="percent rose">
                {{ budgetCompletion.cost.percentage }}%
              </div>
              <div class="mini-bar">
                <div
                  class="fill rose"
                  :style="{
                    width:
                      (barsReady ? budgetCompletion.cost.percentage : 0) + '%'
                  }"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 校区健康度（对照原型） -->
      <div class="chart-card health-card">
        <h3>校区健康度</h3>
        <div class="health-blocks">
          <!-- 净现金流 -->
          <div class="health-block emerald">
            <div class="left">
              <div class="icon-box emerald">
                <i class="el-icon-s-help"></i>
              </div>
              <div class="texts">
                <div class="title">净现金流</div>
              </div>
            </div>
            <div class="right">
              <div class="value emerald">
                ¥{{ formatCurrency(healthMetrics.netCashFlow.amount) }}
              </div>
            </div>
          </div>

          <!-- 净利润 -->
          <div class="health-block blue">
            <div class="left">
              <div class="icon-box blue">
                <i class="el-icon-pie-chart"></i>
              </div>
              <div class="texts">
                <div class="title">净利润</div>
              </div>
            </div>
            <div class="right">
              <div class="value blue">
                ¥{{ formatCurrency(healthMetrics.netProfit.amount) }}
              </div>
            </div>
          </div>

          <!-- 人数净增长 -->
          <div class="health-block purple">
            <div class="left">
              <div class="icon-box purple">
                <i class="el-icon-user"></i>
              </div>
              <div class="texts">
                <div class="title">人数净增长</div>
              </div>
            </div>
            <div class="right">
              <div class="value purple">
                {{ healthMetrics.headcountNet.amount }}人
              </div>
              <div class="status purple">
                {{ healthMetrics.headcountNet.percent }}%
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 业务构成+业绩分析（对照原型） -->
      <div class="chart-card business-card">
        <div class="business-header">
          <h3>业绩分析</h3>
          <!-- <div class="inline-status">
            <span class="status-dot healthy"></span>
            <span class="status-text">健康</span>
          </div> -->
        </div>
        <div ref="businessAnalysisChart" class="chart-container"></div>
      </div>
    </div>

    <!-- 第三行：地图 + 校区指标详情 -->
    <div class="bottom-row">
      <!-- 全国校区分布图（支持地区切换） -->
      <div class="chart-card map-card">
        <div class="map-header">
          <h3>全国校区分布图</h3>
          <!-- <div class="map-controls">
            <label class="control-label">地区</label>
            <select v-model="selectedRegion" class="region-select">
              <option
                v-for="opt in regionOptions"
                :key="opt.value"
                :value="opt.value"
              >
                {{ opt.label }}
              </option>
            </select>
          </div> -->
        </div>
        <div ref="mapChart" class="chart-container large-chart"></div>
      </div>

      <!-- 校区指标详情 -->
      <div class="chart-card details-card">
        <div class="details-header">
          <h3>校区三正指标详情</h3>
          <div class="filters">
            <span class="funnel" aria-hidden>综合状态</span>
            <el-select
              v-model="statusFilter"
              filterable
              placeholder="全部状态"
              class="status-select"
            >
              <el-option
                v-for="opt in statusOptions"
                :key="opt.value"
                :label="opt.label"
                :value="opt.value"
              />
            </el-select>
            <el-button size="small" @click="exportCampusDetailsXls"
              >导出</el-button
            >
          </div>
        </div>
        <div class="details-table">
          <div class="table-header">
            <div class="header-cell">校区</div>
            <div class="header-cell">现金流</div>
            <div class="header-cell">利润</div>
            <div class="header-cell">人数</div>
            <div class="header-cell">综合状态</div>
          </div>
          <div class="table-body">
            <vue-seamless-scroll
              :data="filteredCampusDetails"
              :class-option="seamlessOption"
              class="seamless-warp"
              :style="{ height: detailsScrollHeight }"
            >
              <div class="scroll-list">
                <div
                  v-for="(campus, index) in filteredCampusDetails"
                  :key="index"
                  class="table-row"
                >
                  <div class="table-cell campus-name">{{ campus.name }}</div>
                  <div class="table-cell">
                    <span
                      class="status-dot"
                      :class="campus.cashFlowStatus"
                    ></span>
                    <span :class="campus.cashFlowClass">{{
                      campus.cashFlowDisplay
                    }}</span>
                  </div>
                  <div class="table-cell">
                    <span
                      class="status-dot"
                      :class="campus.profitStatus"
                    ></span>
                    <span :class="campus.profitClass">{{
                      campus.profitDisplay
                    }}</span>
                  </div>
                  <div class="table-cell">
                    <span
                      class="status-dot"
                      :class="campus.headcountStatus"
                    ></span>
                    <span :class="campus.headcountClass">{{
                      campus.headcount
                    }}</span>
                  </div>
                  <div class="table-cell">
                    <span class="status-pill" :class="campus.compositeStatus">
                      <span
                        class="pill-icon"
                        :class="campus.compositeStatus"
                        aria-hidden
                      ></span>
                      <span class="pill-text">{{
                        compositeText(campus.compositeStatus)
                      }}</span>
                    </span>
                  </div>
                </div>
              </div>
            </vue-seamless-scroll>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import vueSeamlessScroll from "vue-seamless-scroll";
import overviewApi from "@/api/overview";

export default {
  name: "Overview",
  components: {
    "vue-seamless-scroll": vueSeamlessScroll
  },
  data() {
    return {
      search_title: [
        {
          label: "日期",
          props: "date_range",
          type: "date",
          show: true,
          selectOptions: [],
          has_options: true
        }
      ],
      searchForm: {
        date_range: []
      },

      // 新的预算指标数据（按原型图设计）
      budgetMetrics: {
        cashFlow: {
          current: 0,
          target: 0,
          percentage: 0
        },
        profit: {
          current: 0,
          target: 0,
          percentage: 0
        },
        headcount: {
          current: 0,
          target: 0,
          percentage: 0
        }
      },

      // 进度条动画用状态
      progressAnimated: {
        cashFlow: 0,
        profit: 0,
        headcount: 0
      },
      barsReady: false,

      // 预算完成度数据（按原型：仅展示营收与成本）
      budgetCompletion: {
        revenue: {
          current: 0,
          target: 0,
          percentage: 0
        },
        cost: {
          current: 0,
          target: 0,
          percentage: 0
        }
      },

      // 校区详情数据
      campusDetails: [],
      // 详情筛选（综合状态）
      statusFilter: "all",
      statusOptions: [
        { label: "全部状态", value: "all" },
        { label: "健康", value: "healthy" },
        { label: "危险", value: "danger" },
        { label: "不合格", value: "warning" }
      ],
      animateRows: false,
      businessChart: null,
      businessAnalysisChart: null,
      mapChart: null,
      mapInitRetry: 0,
      chinaGeoLoaded: false,
      // 页面加载状态（接口未完成前显示 loading）
      isLoading: false,
      // 三种状态的地区点位（可由外部传入）
      statusData: {
        healthy: [],
        danger: [],
        unqualified: []
      },
      // 预警数量统计
      earlyWarningCounts: {
        green: 0,
        orange: 0,
        red: 0
      },
      // 常用城市/省会经纬度（用于将地区名称转换为坐标）；如名称不存在请外部直接传入坐标
      coordMap: {},
      // 地图数据与地区选项
      mapData: [],
      regionOptions: [
        // { label: "全国", value: "national" },
        // { label: "华北", value: "north" },
        // { label: "华东", value: "east" },
        // { label: "华南", value: "south" },
        // { label: "西南", value: "southwest" }
      ],
      selectedRegion: "national",
      // 地区 -> 省份映射（用于动态高亮）
      regionProvinceMap: {
        national: [],
        north: ["北京", "天津", "河北", "山西", "内蒙古"],
        east: ["上海", "江苏", "浙江", "安徽", "福建", "江西", "山东", "台湾"],
        south: ["广东", "广西", "海南", "香港", "澳门"],
        southwest: ["重庆", "四川", "贵州", "云南", "西藏"]
      },

      // 业务构成数据
      businessData: [],
      // 业绩分析堆叠数据（单位：万）- 与月份一一对应
      businessStackSeries: {},
      // 校区健康度数据（对照原型）
      healthMetrics: {
        netCashFlow: {
          amount: 0,
          statusText: "健康"
        },
        netProfit: {
          amount: 0,
          profitRate: 0
        },
        headcountNet: {
          amount: 0,
          percent: 0
        }
      }
    };
  },
  watch: {
    school_id: {
      handler(val) {
        this.searchVal();
      },
      deep: true
    }
  },

  computed: {
    school_id() {
      return this.$store.getters.doneGetSchoolId;
    },
    // 对齐 cockpit 老师业绩排行滚动配置
    seamlessOption() {
      return {
        step: 0.5, // 滚动速度，数值越大越快
        limitMoveNum: 7, // 始终开启无缝（不受条数限制）
        hoverStop: true, // 悬停暂停
        direction: 1, // 0下 1上 2左 3右
        openWatch: true, // 监听数据变化
        singleHeight: 0,
        singleWidth: 0,
        waitTime: 1000
      };
    },
    // 详情滚动区域高度，可按需调整/计算
    detailsScrollHeight() {
      // 约等于卡片内容高度，留出表头空间
      return "320px";
    },
    // 按综合状态筛选后的校区明细
    filteredCampusDetails() {
      if (this.statusFilter === "all") return this.campusDetails;
      return (this.campusDetails || []).filter(
        (c) => c.compositeStatus === this.statusFilter
      );
    }
  },

  async mounted() {
    await this.reset();
    // 初始化图表与表格动画
    await this.initCharts();
    await this.startTableAnimation();
    await this.fetchMapData();
    // 地区切换监听，动态更新地图
    this.$watch(
      () => this.selectedRegion,
      () => {
        this.updateMapChartOption();
      }
    );
    // 监听窗口尺寸，保证图表自适应，避免容器尺寸变化导致白屏
    window.addEventListener("resize", this.handleResize);

    // 触发顶部进度条与小进度条动画
    this.$nextTick(() => {
      this.animateProgress();
      // 小进度条使用 CSS 过渡，从 0 到目标值
      setTimeout(() => {
        this.barsReady = true;
      }, 100);
    });
  },

  beforeDestroy() {
    if (this.businessChart) {
      this.businessChart.dispose();
    }
    if (this.businessAnalysisChart) {
      this.businessAnalysisChart.dispose();
    }
    if (this.mapChart) {
      this.mapChart.dispose();
    }
    window.removeEventListener("resize", this.handleResize);
  },

  methods: {
    goWechatOverview() {
      window.open("/wechatOverview", "_blank");
    },
    async searchVal() {
      await this.fetchOverviewData({
        start_time:
          this.searchForm?.date_range?.length > 0
            ? this.searchForm?.date_range[0]
            : undefined,
        end_time:
          this.searchForm?.date_range?.length > 0
            ? this.searchForm?.date_range[1]
            : undefined,
        department_id: this.school_id
      });
      // 数据就绪后，触发动画和图表更新
      this.barsReady = false;
      this.$nextTick(() => {
        this.animateProgress();
        setTimeout(() => (this.barsReady = true), 100);
        // 重新初始化或更新图表
        this.initBusinessAnalysisChart();
        this.updateMapChartOption();
      });
    },
    reset() {
      this.searchForm.date_range = [];
      this.$nextTick(async () => {
        const formatDate = (date) => moment(date).format("YYYY-MM-DD");
        const end = new Date();
        // 生效时间默认30天
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setTime(thirtyDaysAgo.getTime() - 3600 * 1000 * 24 * 29);
        this.searchForm.date_range = [
          formatDate(thirtyDaysAgo),
          formatDate(end)
        ];

        this.searchVal();
      });
    },

    // 顶部3个 el-progress 由 0 平滑过渡到目标百分比
    animateProgress() {
      const targets = {
        cash: this.budgetMetrics.cashFlow.percentage,
        profit: this.budgetMetrics.profit.percentage,
        head: this.budgetMetrics.headcount.percentage
      };

      const duration = 700; // ms
      const start = performance.now();
      const easeOutCubic = (t) => 1 - Math.pow(1 - t, 3);

      const tick = (now) => {
        const p = Math.min(1, (now - start) / duration);
        const e = easeOutCubic(p);
        this.progressAnimated.cashFlow = Math.round(targets.cash * e);
        this.progressAnimated.profit = Math.round(targets.profit * e);
        this.progressAnimated.headcount = Math.round(targets.head * e);
        if (p < 1) {
          requestAnimationFrame(tick);
        }
      };
      requestAnimationFrame(tick);
    },
    // 格式化数字显示
    formatNumber(num) {
      return num.toLocaleString();
    },

    // 格式化货币显示
    formatCurrency(num) {
      return num.toLocaleString("zh-CN", {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    },

    // 综合状态显示文案
    compositeText(status) {
      const map = { healthy: "健康", warning: "不合格", danger: "危险" };
      return map[status] || "健康";
    },

    // 触发浏览器下载
    downloadAsFile(blob, filename) {
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    },

    // 导出：.xls（兼容 Excel）
    exportCampusDetailsXls() {
      try {
        const rows = this.filteredCampusDetails?.length
          ? this.filteredCampusDetails
          : this.campusDetails || [];
        const html = this.buildCampusXlsTable(rows);
        const blob = new Blob(["\ufeff" + html], {
          type: "application/vnd.ms-excel;charset=utf-8;"
        });
        this.downloadAsFile(blob, "校区三正指标详情.xls");
      } catch (e) {
        console.error("导出 XLS 失败:", e);
      }
    },

    buildCampusXlsTable(list = []) {
      const escapeHtml = (s) =>
        String(s ?? "")
          .replace(/&/g, "&amp;")
          .replace(/</g, "&lt;")
          .replace(/>/g, "&gt;")
          .replace(/"/g, "&quot;");
      const rows = (list || [])
        .map((c) => {
          const name = escapeHtml(c?.name ?? "");
          const cash = escapeHtml(c?.cashFlowDisplay ?? "");
          const profit = escapeHtml(c?.profitDisplay ?? "");
          const headcount = escapeHtml(c?.headcount ?? "");
          const composite = escapeHtml(
            this.compositeText(c?.compositeStatus || "healthy")
          );
          return `<tr><td>${name}</td><td>${cash}</td><td>${profit}</td><td>${headcount}</td><td>${composite}</td></tr>`;
        })
        .join("");
      const table = `<!DOCTYPE html><html><head><meta charset="UTF-8" /></head><body>
        <table border="1" cellspacing="0" cellpadding="4">
          <thead><tr>
            <th>校区</th><th>现金流</th><th>利润</th><th>人数</th><th>综合状态</th>
          </tr></thead>
          <tbody>${rows}</tbody>
        </table>
      </body></html>`;
      return table;
    },

    // 初始化图表
    initCharts() {
      this.$nextTick(() => {
        this.initBusinessAnalysisChart();
        this.initMapChart();
      });
    },

    // 初始化业绩分析（四组双柱：实际 vs 目标；单位：万）
    initBusinessAnalysisChart() {
      if (!this.$refs.businessAnalysisChart) return;
      if (this.businessAnalysisChart) {
        this.businessAnalysisChart.dispose();
      }
      this.businessAnalysisChart = echarts.init(
        this.$refs.businessAnalysisChart
      );

      const categories = (this.businessData || []).map((d) => d.name);
      const { actual = [], target = [] } = this.businessStackSeries || {};
      const a = actual.slice(0, categories.length);
      const t = target.slice(0, categories.length);
      // 防御：将无效值归零并统一保留两位小数，避免 NaN 显示
      const safeArr = (arr) =>
        (arr || []).map((v) => {
          const n = Number(v);
          return Number.isFinite(n) ? Number(n.toFixed(2)) : 0;
        });
      const aSafe = safeArr(a);
      const tSafe = safeArr(t);

      const option = {
        tooltip: {
          trigger: "axis",
          axisPointer: { type: "shadow" },
          formatter: (params) => {
            const lines = [params?.[0]?.axisValueLabel || ""];
            (params || []).forEach((p) => {
              const val = Number(p?.value ?? 0);
              const v2 = isNaN(val) ? 0 : Number(val.toFixed(2));
              lines.push(`${p.marker}${p.seriesName}: ${v2}万`);
            });
            return lines.join("<br/>");
          }
        },
        legend: {
          bottom: 4,
          left: "center",
          data: ["实际", "目标"]
        },
        grid: {
          left: "4%",
          right: "6%",
          top: 48,
          bottom: 46,
          containLabel: true
        },
        xAxis: {
          type: "category",
          data: categories,
          axisLabel: { show: true, interval: 0, rotate: 0, margin: 12 },
          axisTick: { alignWithLabel: true }
        },
        yAxis: {
          type: "value",
          name: "",
          axisLabel: {
            formatter: (val) => {
              const n = Number(val);
              return `${Number.isFinite(n) ? n.toFixed(2) : 0}万`;
            }
          }
        },
        series: [
          {
            name: "实际",
            type: "bar",
            data: aSafe,
            barWidth: "28%",
            barGap: "30%",
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 1, color: "#2563eb" }
              ])
            },
            label: { show: true, position: "top", formatter: () => "实际" },
            z: 3
          },
          {
            name: "目标",
            type: "bar",
            data: tSafe,
            barWidth: "28%",
            barGap: "50%",
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 1, color: "#f43f5e" }
              ])
            },
            label: { show: true, position: "top", formatter: () => "目标" },
            z: 3
          }
        ],
        animationEasing: "elasticOut"
      };

      this.businessAnalysisChart.setOption(option);
    },
    // 初始化地图 - 使用中国地图并支持地区筛选
    initMapChart() {
      try {
        if (!this.$refs.mapChart) return;
        if (this.mapChart) {
          this.mapChart.dispose();
        }
        const el = this.$refs.mapChart;
        const w = el.clientWidth;
        const h = el.clientHeight || 300;
        console.log(w, h);
        if (!w || !h) {
          if (this.mapInitRetry < 20) {
            this.mapInitRetry++;
            setTimeout(() => this.initMapChart(), 150);
          } else {
            console.warn("mapChart 容器尺寸为0，多次重试仍失败");
          }
          return;
        }

        const ensureGeoLoaded = async () => {
          if (this.chinaGeoLoaded) return true;
          const urls = [
            // 本地静态资源（方案A，推荐）
            "/geo/100000_full.json",
            // 备用 CDN 源（如需可调整为你信任的镜像）
            "https://cdn.jsdelivr.net/gh/airyifeng/china-geojson@main/geo/100000_full.json",
            // 最后回退阿里云（本地/镜像不可用时才触发）
            "https://geo.datav.aliyun.com/areas_v3/bound/geojson?code=100000_full"
          ];
          for (const url of urls) {
            try {
              const resp = await fetch(url, { credentials: "omit" });
              if (!resp.ok) {
                console.warn("GeoJSON 源不可用:", url, resp.status);
                continue;
              }
              const geoJson = await resp.json();
              if (!geoJson || !geoJson.type) {
                console.warn("GeoJSON 数据异常:", url);
                continue;
              }
              echarts.registerMap("china", geoJson);
              this.chinaGeoLoaded = true;
              return true;
            } catch (err) {
              console.warn("加载 GeoJSON 失败:", url, err);
            }
          }
          console.error(
            "所有 GeoJSON 源均加载失败，请确认 public/geo/100000_full.json 是否存在"
          );
          return false;
        };

        ensureGeoLoaded().then((ok) => {
          if (!ok) return;
          this.mapChart = echarts.init(el);
          this.mapInitRetry = 0;
          this.updateMapChartOption();
        });
      } catch (e) {
        console.error("初始化地图图表失败:", e);
      }
    },

    updateMapChartOption() {
      if (!this.mapChart || !this.chinaGeoLoaded) return;

      // 底图（geo）+ 三种状态圆点（effectScatter）
      const healthy = this.toCoordPoints(this.statusData.healthy);
      const danger = this.toCoordPoints(this.statusData.danger);
      const unqualified = this.toCoordPoints(this.statusData.unqualified);

      // 固定系列名与图例项，数量通过 legend.formatter 动态展示，避免系列名变化导致 setOption 合并异常
      const legendItems = ["健康", "危险", "不合格"];
      // 注意：formatter 的 this 非 Vue 实例，这里用局部变量捕获数量，避免 this 失效
      const counts = {
        healthy: Number(this.earlyWarningCounts.green || 0),
        danger: Number(this.earlyWarningCounts.orange || 0),
        unqualified: Number(this.earlyWarningCounts.red || 0)
      };

      const option = {
        title: {
          text: "全国校区分布监控",
          left: "center",
          textStyle: { fontSize: 14, color: "#374151" }
        },
        tooltip: {
          trigger: "item",
          backgroundColor: "transparent",
          borderColor: "transparent",
          borderWidth: 0,
          textStyle: { color: "#111827" },
          extraCssText: "box-shadow:none;padding:0;",
          formatter: (p) => {
            const name = p?.name || "";
            const status = p?.seriesName || "";
            const colorMap = {
              健康: "#10b981",
              危险: "#f59e0b",
              不合格: "#ef4444"
            };
            const color = colorMap[status] || p?.color || "#374151";
            // 名称 + 换行 + 有色状态（带小圆点）
            return (
              `<div style="background:rgba(255,255,255,0.98);border:1px solid ${color};border-radius:6px;padding:8px 10px;">` +
              `${name}<br/>` +
              `<span style="display:inline-block;margin-right:4px;border-radius:50%;width:8px;height:8px;background:${color};vertical-align:middle;"></span>` +
              `<span style="color:${color};vertical-align:middle;">状态：${status}</span>` +
              `</div>`
            );
          }
        },
        legend: {
          bottom: 8,
          right: 10,
          data: legendItems,
          formatter: (name) => {
            const map = {
              健康: counts.healthy,
              危险: counts.danger,
              不合格: counts.unqualified
            };
            return `${name} ${map[name]}`;
          },
          itemWidth: 10,
          itemHeight: 10
        },
        geo: {
          map: "china",
          roam: true,
          zoom: 1.2, // 初始放大
          layoutCenter: ["50%", "80%"],
          layoutSize: "120%",
          label: { show: false },
          itemStyle: {
            borderColor: "#ffffff",
            borderWidth: 1,
            areaColor: "#eef2ff"
          },
          emphasis: {
            label: { show: false },
            itemStyle: { areaColor: "#93c5fd" }
          }
        },
        series: [
          {
            name: "健康",
            type: "effectScatter",
            coordinateSystem: "geo",
            zlevel: 2,
            rippleEffect: { brushType: "stroke" },
            symbolSize: 10,
            itemStyle: { color: "#10b981" },
            data: healthy
          },
          {
            name: "危险",
            type: "effectScatter",
            coordinateSystem: "geo",
            zlevel: 2,
            rippleEffect: { brushType: "stroke" },
            symbolSize: 10,
            itemStyle: { color: "#f59e0b" },
            data: danger
          },
          {
            name: "不合格",
            type: "effectScatter",
            coordinateSystem: "geo",
            zlevel: 2,
            rippleEffect: { brushType: "stroke" },
            symbolSize: 10,
            itemStyle: { color: "#ef4444" },
            data: unqualified
          }
        ]
      };

      try {
        this.mapChart.setOption(option, true);
      } catch (e) {
        console.error("更新地图图表失败:", e);
      }
    },
    // 外部设置状态地区并刷新
    setStatusRegions({ healthy = [], danger = [], unqualified = [] } = {}) {
      this.statusData.healthy = healthy;
      this.statusData.danger = danger;
      this.statusData.unqualified = unqualified;
      this.updateMapChartOption();
    },

    // 将地区列表转换为带坐标的点位数据
    // 输入项支持两种形式：
    // 1) { name: '上海浦东' } -> 会尝试从 coordMap 查找坐标
    // 2) { name: '自定义', coord: [lng, lat], value: 1 }
    toCoordPoints(list = []) {
      return (list || [])
        .map((it) => {
          if (Array.isArray(it.coord) && it.coord.length >= 2) {
            const val = typeof it.value === "number" ? it.value : 1;
            return {
              name: it.name || "",
              value: [it.coord[0], it.coord[1], val]
            };
          }
          const coord = this.coordMap[it.name];
          if (!coord) return null;
          const val = typeof it.value === "number" ? it.value : 1;
          return { name: it.name, value: [coord[0], coord[1], val] };
        })
        .filter(Boolean);
    },

    // 构建省份数据：选中地区的省份赋值为1，用于高亮
    buildProvinceMapData() {
      const provinces = [
        "北京",
        "天津",
        "上海",
        "重庆",
        "河北",
        "河南",
        "云南",
        "辽宁",
        "黑龙江",
        "湖南",
        "安徽",
        "山东",
        "新疆",
        "江苏",
        "浙江",
        "江西",
        "湖北",
        "广西",
        "甘肃",
        "山西",
        "内蒙古",
        "陕西",
        "吉林",
        "福建",
        "贵州",
        "广东",
        "青海",
        "西藏",
        "四川",
        "宁夏",
        "海南",
        "台湾",
        "香港",
        "澳门"
      ];
      const selected = new Set(
        this.regionProvinceMap[this.selectedRegion] || []
      );
      const data = provinces.map((name) => ({
        name,
        value: selected.has(name) ? 1 : 0
      }));
      return { data, max: 1 };
    },

    // 窗口尺寸调整时自适应所有图表
    handleResize() {
      try {
        if (this.businessAnalysisChart) this.businessAnalysisChart.resize();
        if (this.mapChart) this.mapChart.resize();
      } catch (e) {
        console.error("图表自适应失败:", e);
      }
    },

    // 获取地图数据
    async fetchMapData() {
      try {
        // 如需从后端获取省份数据，可在此处请求后更新地图
        // const resp = await this.$http.get('/api/campus/province-stats')
        // this.provinceStats = resp.data
        // this.updateMapChartOption()
      } catch (error) {
        console.error("获取地图数据失败:", error);
      }
    },

    // 启动表格动画
    startTableAnimation() {
      setTimeout(() => {
        this.animateRows = true;
      }, 300);
    },

    // 拉取并应用概览数据
    async fetchOverviewData(params = {}) {
      this.isLoading = true;
      try {
        const res = await overviewApi.getDriveList(params);
        const data = res?.data?.data || {};
        this.applyOverviewData(data);
      } catch (e) {
        console.error("获取概览数据失败:", e);
      } finally {
        this.isLoading = false;
      }
    },

    // 将接口数据映射到现有页面状态结构
    applyOverviewData(data = {}) {
      // 顶部三项：现金流/利润/人数
      if (data.cash_flow) {
        const cf = data.cash_flow;
        this.budgetMetrics.cashFlow.current = Number(cf.actual_value || 0);
        this.budgetMetrics.cashFlow.target = Number(cf.target_value || 0);
        this.budgetMetrics.cashFlow.percentage = Number(
          cf.completion_rate || 0
        );
      }
      if (data.profit) {
        const p = data.profit;
        this.budgetMetrics.profit.current = Number(p.actual_value || 0);
        this.budgetMetrics.profit.target = Number(p.target_value || 0);
        this.budgetMetrics.profit.percentage = Number(p.completion_rate || 0);
      }
      if (data.student_stats) {
        const s = data.student_stats;
        this.budgetMetrics.headcount.current = Number(s.actual_value || 0);
        this.budgetMetrics.headcount.target = Number(s.target_value || 0);
        this.budgetMetrics.headcount.percentage = Number(
          s.completion_rate || 0
        );
      }

      // 预算完成度：营收、成本
      if (data.revenue) {
        const r = data.revenue;
        this.budgetCompletion.revenue.current = Number(r.actual_value || 0);
        this.budgetCompletion.revenue.target = Number(r.target_value || 0);
        this.budgetCompletion.revenue.percentage = Number(
          r.completion_rate || 0
        );
      }
      if (data.cost) {
        const c = data.cost;
        this.budgetCompletion.cost.current = Number(c.actual_value || 0);
        this.budgetCompletion.cost.target = Number(c.target_value || 0);
        this.budgetCompletion.cost.percentage = Number(c.completion_rate || 0);
      }

      // 校区健康度模块
      if (data.net_cash_flow) {
        const ncf = data.net_cash_flow;
        this.healthMetrics.netCashFlow.amount = Number(ncf.actual_value || 0);
        this.healthMetrics.netCashFlow.inflow = Number(ncf.inflow || 0);
        this.healthMetrics.netCashFlow.outflow = Number(ncf.outflow || 0);
        // 简单用完成率判断文案
        const rate = Number(ncf.completion_rate || 0);
        this.healthMetrics.netCashFlow.statusText =
          rate >= 50 ? "健康" : rate >= 30 ? "一般" : "偏弱";
      }
      if (data.net_profit) {
        const np = data.net_profit;
        this.healthMetrics.netProfit.amount = Number(np.actual_value || 0);
        this.healthMetrics.netProfit.profitRate = Number(
          np.completion_rate || 0
        );
      }
      if (data.net_increase_num) {
        const ni = data.net_increase_num;
        this.healthMetrics.headcountNet.amount = Number(ni.actual_value || 0);
        this.healthMetrics.headcountNet.inflow = Number(ni.inflow || 0);
        this.healthMetrics.headcountNet.outflow = Number(ni.outflow || 0);
        this.healthMetrics.headcountNet.percent = Number(
          ni.completion_rate || 0
        );
      }

      // 业绩分析（四组：新签/续费/其他/总计；每组 实际 vs 目标；单位：万元）
      if (data.performance && typeof data.performance === "object") {
        const p = data.performance || {};
        // 横向分组名称（用于 tooltip/axisValueLabel）
        this.businessData = [
          { name: "新签业绩" },
          { name: "续费业绩" },
          { name: "其他业绩" },
          { name: "总业绩" },
          { name: "退费业绩" }
        ];
        // 构建 实际/目标 数组（单位转换：元 -> 万元）
        const toWan = (v) => Number((Number(v || 0) / 10000).toFixed(2));
        const actual = [
          toWan(p.new_actual),
          toWan(p.renew_actual),
          toWan(p.other_actual),
          toWan(p.total_actual),
          toWan(p.refund_actual)
        ];
        const target = [
          toWan(p.new_target),
          toWan(p.renew_target),
          toWan(p.other_target),
          toWan(p.total_target),
          toWan(p.refund_target)
        ];
        this.businessStackSeries = { actual, target };
      }

      // 校区指标详情表（dept_stats）
      if (Array.isArray(data.dept_stats)) {
        const mapStatus = (s) => {
          if (!s) return "healthy";
          const v = String(s).toLowerCase();
          if (v === "green") return "healthy";
          if (v === "orange") return "warning";
          if (v === "red") return "danger";
          return "healthy";
        };
        const toClass = (s) => {
          // 值颜色类名复用同一套
          return mapStatus(s) === "danger"
            ? "negative"
            : mapStatus(s) === "warning"
            ? "warning"
            : "positive";
        };
        // compositeStatus
        this.campusDetails = data.dept_stats.map((d) => {
          const cashNum = Number(d.cash_flow || 0);
          const profitNum = Number(d.profit || 0);
          const cashStatus = mapStatus(d.cash_flow_status);
          const profitStatus = mapStatus(d.profit_status);
          const cashPrefix = cashStatus === "danger" ? "-" : "+";
          const profitPrefix = profitStatus === "danger" ? "-" : "+";
          const cashStr = this.formatCurrency(Math.abs(cashNum));
          const profitStr = this.formatCurrency(Math.abs(profitNum));
          const headStatus = mapStatus(d.student_num_status);
          // 计算综合状态：取三项中最差者（danger > warning > healthy）
          // const statuses = [cashStatus, profitStatus, headStatus];
          // const rank = { danger: 3, warning: 2, healthy: 1 };
          const compositeStatus =
            d.health === "red"
              ? "warning"
              : d.health === "orange"
              ? "danger"
              : "healthy";
          // statuses.reduce(
          //   (acc, s) => (rank[s] > rank[acc] ? s : acc),
          //   "healthy"
          // );
          return {
            name: d.department_name,
            // 显示按原型：绿/黄前缀 "+"，红前缀 "-"
            cashFlow: this.formatCurrency(cashNum),
            cashFlowDisplay: `${cashPrefix}${cashStr}`,
            cashFlowStatus: cashStatus,
            cashFlowClass: toClass(d.cash_flow_status),
            profit: this.formatCurrency(profitNum),
            profitDisplay: `${profitPrefix}${profitStr}`,
            profitStatus,
            profitClass: toClass(d.profit_status),
            headcount: `${Number(d.student_num || 0)}人`,
            headcountStatus: headStatus,
            // 数值颜色使用与现金流/利润相同的正负色阶类（positive/warning/negative）
            headcountClass: toClass(d.student_num_status),
            // 新增：综合状态
            compositeStatus
          };
        });
        console.log(this.campusDetails);
      }

      // 地图点位（green/orange/red）与数量
      if (data.dept_distribute) {
        const ew = data.dept_distribute.early_warning || {};
        this.earlyWarningCounts = {
          green: Number(ew.green_num || 0),
          orange: Number(ew.orange_num || 0),
          red: Number(ew.red_num || 0)
        };

        const normalize = (lon, lat) => {
          const LON = Number(lon);
          const LAT = Number(lat);
          // 若经纬看起来互换（如 lon 在 0-90、lat 在 90-180），则交换
          if (LON >= 0 && LON <= 90 && LAT >= 90 && LAT <= 180) {
            return [LAT, LON];
          }
          return [LON, LAT];
        };

        const greenList = Array.isArray(data.dept_distribute.green_locations)
          ? data.dept_distribute.green_locations.map((l) => ({
              name: l.department_name,
              coord: normalize(l.longitude, l.latitude),
              value: 1
            }))
          : [];
        const orangeList = Array.isArray(data.dept_distribute.orange_locations)
          ? data.dept_distribute.orange_locations.map((l) => ({
              name: l.department_name,
              coord: normalize(l.longitude, l.latitude),
              value: 1
            }))
          : [];
        const redList = Array.isArray(data.dept_distribute.red_locations)
          ? data.dept_distribute.red_locations.map((l) => ({
              name: l.department_name,
              coord: normalize(l.longitude, l.latitude),
              value: 1
            }))
          : [];

        this.setStatusRegions({
          healthy: greenList,
          danger: orangeList,
          unqualified: redList
        });
      }
    }
  }
};
</script>

<style lang="less" scoped>
.dashboard-container {
  width: 100%;
  min-height: 100vh;
  overflow-y: auto;
  padding: 20px;
  -webkit-overflow-scrolling: touch;
  /* 提升滚动合成层，减少重绘 */
  transform: translateZ(0);
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);

  ::v-deep .tg-box--margin {
    margin-bottom: 20px;
    margin-top: 0;
  }
  .tg-search__box {
    position: relative;
    .tg-button--primary {
      position: absolute;
      right: 24px;
      top: 24px;
    }
  }
  .metrics-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;

    .metric-card {
      background: #ffffff;
      border: 1px solid rgba(255, 255, 255, 0.2);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
      border-radius: 12px;
      padding: 24px;
      transition: all 0.2s ease;

      &:hover {
        // transform: translateY(-2px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
      }

      .metric-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #374151;
        }

        .metric-icon {
          width: 40px;
          height: 40px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 18px;

          &.cash-flow {
            background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
            box-shadow: 0 6px 16px rgba(16, 185, 129, 0.35);
          }

          &.profit {
            background: linear-gradient(135deg, #2d80ed 0%, #60a5fa 100%);
            box-shadow: 0 6px 16px rgba(45, 128, 237, 0.35);
          }

          &.headcount {
            background: linear-gradient(135deg, #7c3aed 0%, #a78bfa 100%);
            box-shadow: 0 6px 16px rgba(124, 58, 237, 0.35);
          }
        }
      }

      .metric-main-value {
        margin-bottom: 8px;
        text-align: center;

        .main-number {
          font-size: 28px;
          font-weight: 700;
          color: #111827;
        }

        // 按模块主数字配色（严格对照原型）
        :deep(.metric-card.is-cash) & .main-number,
        .metric-card.is-cash & .main-number {
          color: #059669;
          /* 深绿 */
        }

        :deep(.metric-card.is-profit) & .main-number,
        .metric-card.is-profit & .main-number {
          color: #2563eb;
          /* 深蓝 */
        }

        :deep(.metric-card.is-headcount) & .main-number,
        .metric-card.is-headcount & .main-number {
          color: #6d28d9;
          /* 深紫 */
        }
      }

      .metric-current-label {
        font-size: 12px;
        color: #6b7280;
        // margin: 0 auto;
        margin-bottom: 16px;
        text-align: center;
      }

      .green {
        color: rgb(16, 185, 129);
      }

      .blue {
        color: rgb(37, 99, 235);
      }

      .purple {
        color: rgb(125, 58, 237);
      }

      .metric-bottom {
        padding: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        background-color: #f9fafb;
        border-radius: 12px;

        .metric-target {
          display: flex;
          flex-direction: column;
          align-items: flex-start;

          .target-label {
            font-size: 12px;
            color: #6b7280;
            margin-bottom: 2px;
          }

          .target-value {
            font-size: 14px;
            font-weight: 600;
          }
        }

        .metric-percentage {
          font-size: 18px;
          font-weight: 600;
          // color: #374151;
        }
      }

      .metric-progress {
        margin-bottom: 0;
      }

      // 保留原有target样式兼容性
      .metric-target {
        display: flex;
        justify-content: space-between;
        align-items: center;
        // margin-bottom: 8px;

        .target-label {
          font-size: 12px;
          color: #6b7280;
        }

        .target-value {
          font-size: 14px;
          font-weight: 600;
        }
      }

      // 保留原有样式兼容性
      .metric-value {
        margin-bottom: 12px;

        .value {
          font-size: 32px;
          font-weight: 700;
          color: #111827;
          margin-right: 8px;
        }

        .unit {
          font-size: 14px;
          color: #6b7280;
        }
      }

      .metric-change {
        display: flex;
        align-items: center;
        font-size: 14px;
        font-weight: 500;

        &.positive {
          color: #10b981;
        }

        &.negative {
          color: #ef4444;
        }

        i {
          margin-right: 4px;
        }
      }
    }
  }

  .charts-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;

    .chart-card {
      background: #ffffff;
      border: 1px solid rgba(255, 255, 255, 0.2);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
      border-radius: 12px;
      padding: 24px;
      transition: all 0.2s ease;

      &:hover {
        // transform: translateY(-2px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
      }

      h3 {
        margin: 0 0 20px 0;
        font-size: 16px;
        font-weight: 600;
        color: #374151;
      }

      &.business-card {
        .business-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;

          h3 {
            margin: 0;
          }

          .inline-status {
            display: inline-flex;
            align-items: center;
            gap: 6px;

            .status-dot {
              width: 8px;
              height: 8px;
              border-radius: 50%;

              &.healthy {
                background-color: #10b981;
              }
            }

            .status-text {
              font-size: 12px;
              color: #10b981;
            }
          }
        }
      }

      &.map-card {
        .map-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;

          h3 {
            margin: 0;
          }

          .map-controls {
            display: inline-flex;
            align-items: center;
            gap: 8px;

            .control-label {
              font-size: 12px;
              color: #6b7280;
            }

            .region-select {
              height: 28px;
              padding: 2px 10px;
              border: 1px solid #e5e7eb;
              border-radius: 6px;
              font-size: 12px;
              color: #374151;
              background: #fff;
              outline: none;

              &:focus {
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.15);
              }
            }
          }
        }
      }

      &.budget-card {
        .budget-progress {
          .progress-item {
            margin-bottom: 20px;

            &:last-child {
              margin-bottom: 0;
            }

            .progress-label {
              display: flex;
              justify-content: space-between;
              margin-bottom: 8px;
              font-size: 14px;

              .progress-value {
                font-weight: 600;
                color: #374151;
              }
            }
          }
        }
      }

      &.health-card {
        // 新版块状布局（对照 admin-dashboard.html）
        .health-blocks {
          display: flex;
          flex-direction: column;
          gap: 12px;

          .health-block {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px;
            border-radius: 8px;

            &.emerald {
              background: #ecfdf5;
            }

            &.blue {
              background: #eff6ff;
            }

            &.purple {
              background: #f5f3ff;
            }

            .left {
              display: flex;
              align-items: center;
              gap: 12px;

              .icon-box {
                width: 32px;
                height: 32px;
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #fff;

                &.emerald {
                  background: #10b981;
                }

                &.blue {
                  background: #3b82f6;
                }

                &.purple {
                  background: #8b5cf6;
                }
              }

              .texts {
                .title {
                  font-size: 14px;
                  font-weight: 600;
                  color: #111827;
                  line-height: 1.2;
                }

                .sub {
                  font-size: 12px;

                  &.emerald {
                    color: #059669;
                  }

                  &.blue {
                    color: #2563eb;
                  }

                  &.purple {
                    color: #7c3aed;
                  }
                }
              }
            }

            .right {
              display: flex;
              flex-direction: column;
              align-items: flex-end;
              gap: 4px;

              .value {
                font-size: 16px;
                font-weight: 700;

                &.emerald {
                  color: #059669;
                }

                &.blue {
                  color: #2563eb;
                }

                &.purple {
                  color: #7c3aed;
                }
              }

              .status {
                font-size: 12px;

                &.emerald {
                  color: #10b981;
                }

                &.blue {
                  color: #3b82f6;
                }

                &.purple {
                  color: #8b5cf6;
                }
              }
            }
          }
        }
      }

      .chart-container {
        height: 250px;
        border: 1px solid #e5e7eb; // 与原型一致的浅灰边框
        border-radius: 8px;

        &.large-chart {
          height: 350px;
        }
      }

      // 预算完成度卡片样式
      &.budget-completion-card {
        .budget-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;

          h3 {
            margin: 0;
          }

          .budget-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: #10b981;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
          }
        }

        // 新版列表布局（对照 admin-dashboard.html）
        .budget-list {
          display: flex;
          flex-direction: column;
          gap: 12px;

          .budget-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px;
            background: #f9fafb;
            border-radius: 8px;

            .left {
              display: flex;
              align-items: center;
              gap: 12px;

              .icon-box {
                width: 32px;
                height: 32px;
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #fff;

                &.teal {
                  background: #14b8a6;
                }

                &.rose {
                  background: #f43f5e;
                }
              }

              .texts {
                .title {
                  font-size: 14px;
                  font-weight: 600;
                  color: #111827;
                  line-height: 1.2;
                }

                .sub {
                  font-size: 12px;
                  color: #6b7280;
                }
              }
            }

            .right {
              display: flex;
              flex-direction: column;
              align-items: flex-end;
              gap: 6px;

              .percent {
                font-size: 16px;
                font-weight: 700;

                &.teal {
                  color: #14b8a6;
                }

                &.rose {
                  color: #f43f5e;
                }
              }

              .mini-bar {
                width: 64px;
                height: 6px;
                border-radius: 999px;
                background: #e5e7eb;
                overflow: hidden;

                .fill {
                  height: 100%;
                  border-radius: 999px;
                  transition: width 800ms ease;

                  &.teal {
                    background: #14b8a6;
                  }

                  &.rose {
                    background: #f43f5e;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .bottom-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;

    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
    }

    .chart-card {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      border-radius: 12px;
      padding: 24px;
      transition: all 0.2s ease;

      &:hover {
        // transform: translateY(-2px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
      }

      h3 {
        margin: 0 0 20px 0;
        font-size: 16px;
        font-weight: 600;
        color: #374151;
      }

      &.details-card {
        .details-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 12px;
          .filters {
            display: flex;
            // align-items: center;
            // gap: 8px;
            .funnel {
              color: #6b7280;
              line-height: 32px;
            }
            .status-select {
              height: 28px;
              padding: 0 10px;
              // border: 1px solid #e5e7eb;
              border-radius: 6px;
              background: #ffffff;
              color: #374151;
            }
            .export-btn {
              height: 28px;
              padding: 0 10px;
              border: 1px solid #e5e7eb;
              border-radius: 6px;
              background: #ffffff;
              color: #374151;
              cursor: pointer;
            }
          }
        }
        .details-table {
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          .table-header {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
            gap: 12px;
            padding: 12px 0;
            background: #f9fafb;
            border-radius: 8px;
            margin-bottom: 8px;

            .header-cell {
              font-size: 14px;
              font-weight: 600;
              color: #374151;
              text-align: center;

              &:first-child {
                text-align: left;
                padding-left: 16px;
              }
            }
          }

          .table-body {
            /* 由 vue-seamless-scroll 控制内部滚动高度与无缝衔接 */
            max-height: none;
            overflow: visible;

            /* 对齐 cockpit 的容器样式，隐藏原生滚动条，启用无缝滚动 */
            .seamless-warp {
              overflow: hidden;
              width: 100%;
              display: block;
            }

            .scroll-list {
              margin: 0;
              padding: 0;
            }

            .table-row {
              display: grid;
              grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
              gap: 12px;
              padding: 10px 0;
              border-bottom: 1px solid #f3f4f6;
              min-height: 44px;
              /* 固定最小行高，确保首尾无缝 */
              transition: none;
              /* 避免位移动画影响滚动计算 */

              &:last-child {
                /* 去掉最后一行的下边框，避免拼接处出现"缝" */
                border-bottom: none;
              }

              &:hover {
                background: #f9fafb;
              }

              .table-cell {
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 14px;
                white-space: nowrap;
                /* 避免换行导致行高变化 */

                .status-pill {
                  display: inline-flex;
                  align-items: center;
                  padding: 4px 10px;
                  border-radius: 999px;
                  font-size: 12px;
                  font-weight: 600;
                  background: #dcfce7;
                  color: #10b981;
                }
                .status-pill.healthy {
                  background: #dcfce7;
                  color: #10b981;
                }
                .status-pill.warning {
                  background: #fee2e2;
                  color: #ef4444;
                }
                .status-pill.danger {
                  background: #fef3c7;
                  color: #f59e0b;
                }
                .status-pill .pill-icon {
                  display: inline-flex;
                  width: 16px;
                  height: 16px;
                  align-items: center;
                  justify-content: center;
                  border-radius: 50%;
                  margin-right: 6px;

                  color: #ffffff;
                  font-size: 12px;
                  line-height: 1;
                }
                .status-pill .pill-icon.healthy {
                  background: #27ae60;
                }
                .status-pill .pill-icon.warning {
                  background: #e74c3c;
                }
                .status-pill .pill-icon.danger {
                  background: #e67e22;
                }
                .status-pill .pill-icon.healthy::before {
                  content: "✔";
                }
                .status-pill .pill-icon.warning::before {
                  content: "!";
                }
                .status-pill .pill-icon.danger::before {
                  content: "⚠";
                }

                &.campus-name {
                  justify-content: flex-start;
                  padding-left: 16px;
                  font-weight: 600;
                  color: #374151;
                }

                .status-dot {
                  width: 6px;
                  height: 6px;
                  border-radius: 50%;
                  margin-right: 6px;

                  &.healthy {
                    background-color: #10b981;
                  }

                  &.warning {
                    background-color: #f59e0b;
                  }

                  &.danger {
                    background-color: #ef4444;
                  }
                }

                .positive {
                  color: #10b981;
                  font-weight: 600;
                }

                .warning {
                  color: #f59e0b;
                  font-weight: 600;
                }

                .negative {
                  color: #ef4444;
                  font-weight: 600;
                }

                .normal {
                  color: #374151;
                }
              }
            }
          }
        }
      }
    }
  }
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;

    .metrics-row {
      grid-template-columns: 1fr;
    }

    .charts-row {
      grid-template-columns: 1fr;
    }

    .bottom-row {
      grid-template-columns: 1fr;
    }
  }
}

.chart-container {
  height: 250px;
  border: 1px solid #e5e7eb; // 与原型一致的浅灰边框
  border-radius: 8px;

  &.large-chart {
    height: 350px;
  }
}

/* 全局加载遮罩样式 */
.loading-overlay {
  position: fixed;
  inset: 0;
  background: rgba(255, 255, 255, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: none; // 避免高开销模糊
}

.loading-overlay .loading-text {
  margin-top: 12px;
  color: #374151;
  font-size: 14px;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e5e7eb;
  border-top-color: #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
::v-deep .is-focus::after {
  border: none !important;
}

.seamless-warp > div {
  transform: none !important;
  animation: none !important;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>
