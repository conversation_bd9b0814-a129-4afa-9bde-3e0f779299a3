<template>
  <div class="dashboard-container">
    <div class="tg-iframe-box">
      <iframe
        :src="`${tgH5Host}/humanPoweredSys/index?from=tg_admin&employee_id=${employee_id}`"
        frameborder="0"
        style="border: none"
        class="tg-iframe"
      ></iframe>
    </div>
  </div>
</template>

<script>
export default {
  name: "Overview",
  components: {},
  data() {
    return {
      employee_id: "",
      tgH5Host: process.env.VUE_APP_TG_H5_HOST || ""
    };
  },
  created() {
    const { employee_id } = JSON.parse(localStorage.getItem("user_info")) || {};
    if (employee_id) {
      this.employee_id = employee_id;
    } else {
      this.$message.error("用户信息异常!");
    }
  },
  mounted() {},
  methods: {}
};
</script>

<style lang="less" scoped>
.tg-iframe-box {
  width: 480px;
  height: calc(100vh - 70px);
  overflow: hidden;
  margin: 0 auto;
}
.tg-iframe {
  width: 100%;
  height: 100%;
}
</style>
