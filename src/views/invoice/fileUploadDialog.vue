<template>
  <div>
    <el-dialog
      :visible="true"
      title="上传文件"
      width="500px"
      append-to-body
      :before-close="handleClose"
    >
      <el-upload
        action="#"
        :on-success="handleSuccess"
        :on-error="handleError"
        :before-upload="beforeUpload"
        :http-request="uploadImg"
        :accept="file_accept"
        :file-list="fileList"
        :limit="10"
        drag
      >
        <div class="el-upload__box">
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">
            只能上传jpg/png/pdf文件，且不超过20MB
          </div>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button class="tg-button--plain" type="plain" @click="handleClose"
          >取消</el-button
        >
        <el-button
          class="tg-button--primary"
          type="primary"
          v-throttle="handleConfirm"
          >提交</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { v4 as uuidv4 } from "uuid";
export default {
  props: {
    type: {
      type: String,
      default: ""
    },
    review_attachment: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dialogVisible: false,
      file_accept: "",
      fileList: []
    };
  },
  created() {
    this.Oss.getAliyun();
    this.file_accept =
      "image/png,image/jpg,image/gif,image/jpeg,application/pdf";
  },
  methods: {
    handleSuccess(response, file, fileList) {
      console.log(response, file, fileList);
    },
    handleError(error, file, fileList) {
      console.log(error, file, fileList);
    },
    beforeUpload(file) {
      // const { uploadFiles } = this.$refs["uploadImgs"];
      const isLt20M = file.size / 1024 / 1024 > 20;
      if (isLt20M) {
        this.$message.info("上传头像图片大小不能超过 20MB!");
        return false;
      }
    },
    handleRemove(file) {
      console.log(file);
      this.fileList.map((item, index) => {
        if (item.uid === file.uid) {
          this.fileList.splice(index, 1);
          this.$emit("update:review_attachment", this.fileList);
        }
      });
    },
    handleClose() {
      this.$emit("close");
    },
    handleConfirm() {
      this.$emit("update:review_attachment", this.fileList);
      this.$emit("confirm");
      this.$emit("close");
    },
    uploadImg(upload) {
      console.log("upload :>> ", upload);
      const f = upload.file;
      const orginName = f.name.substring(0, f.name.lastIndexOf("."));
      const suffix = f.name.match(/[^.]+$/)[0];
      const name = `${orginName}_${uuidv4()}.${suffix}`;
      const copyFile = new File([f], name);
      this.Oss.uploadFile(copyFile).then((res) => {
        if (res.code === 0) {
          this.fileList.push({
            name: res.objectKey,
            url: res.url
          });
          this.$emit("update:review_attachment", this.fileList);
        }
      });
    }
  }
};
</script>
<style lang="less" scoped>
/deep/ .el-dialog__body {
  text-align: center;
}
</style>
