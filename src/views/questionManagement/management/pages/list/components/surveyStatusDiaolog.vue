<template>
  <div>
    <el-dialog
      title="已读/填写状态查看"
      width="60%"
      :visible="true"
      :before-close="handleClose"
    >
      <tg-search
        :searchTitle.sync="search_title"
        :form.sync="tableParams"
        :showNum="4"
        :isExport="isExport"
        @reset="reset"
        @educe="exportExcel"
        @search="getSearchList"
        :loadingState="exportLoading"
        :searchLoadingState="searchLoading"
      ></tg-search>
      <div class="tg-table__box" v-if="'readNum' in tableHeaderStatistics">
        <div class="tg-box--border"></div>
        <el-table
          ref="table"
          :data="tableData"
          tooltip-effect="dark"
          class="tg-table"
          style="width: 100%"
          @sort-change="sortChange"
          :row-key="getRowKeys"
        >
          <el-table-column
            v-for="item in surveyStatusTableColumn"
            :key="item.prop"
            :label="item.label"
            :prop="item.prop"
            :width="item.width"
            :sortable="item.sortable"
          >
            <template
              v-if="
                ['studentName', 'readStatus', 'writeStatus'].includes(item.prop)
              "
              slot="header"
            >
              <span
                >{{ item.label }}（{{
                  tableHeaderStatistics[tableHeaderFieldStatistics[item.prop]]
                }}）</span
              >
            </template>
            <template slot-scope="scope">
              <template
                v-if="
                  ['writeStatus', 'readStatus', 'pushStatus'].includes(
                    item.prop
                  )
                "
              >
                <div
                  class="survey-status not-started"
                  v-if="scope.row[item.prop] === 2"
                ></div>
                <div class="survey-status is-done" v-else></div>
              </template>
              <div v-else-if="item.prop === 'studentMobile'">
                <mobileHyposensitization
                  :mobileTemInfo="{
                    row: scope.row,
                    has_eye_limit: scope.row?.has_eye_limit,
                    mobile: scope.row.studentMobile
                  }"
                ></mobileHyposensitization>
              </div>
              <div v-else-if="item.prop === 'writeTime'">
                {{
                  scope.row[item.prop].indexOf("0001-01-01") === 0
                    ? ""
                    : moment(new Date(scope.row[item.prop])).format(
                        "YYYY-MM-DD HH:mm:ss"
                      )
                }}
              </div>
              <template v-else>
                {{ scope.row[item.prop] }}
              </template>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template slot-scope="scope">
              <el-button
                v-if="
                  scope.row.writeStatus === 1 &&
                  $_has({ m: 'survey', o: 'surveyShow' })
                "
                @click="toSurveyPage(scope.$index, tableData)"
                type="text"
                size="small"
              >
                查看问卷
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="tg-pagination">
          <span class="el-pagination__total">共 {{ total }} 条</span>
          <el-pagination
            background
            layout="prev, pager, next,jumper"
            :total="total"
            :page-size="tableParams.page_size"
            :current-page="tableParams.page"
            @current-change="currentChange"
          >
          </el-pagination>
        </div>
      </div>
    </el-dialog>
    <previewSurvey
      :key="previewSurveyKey"
      :visible="isShowPreviewSurvey"
      :surveyId="curSurveyId"
      :surveyInfo="curSurveyInfo"
      :studentId="curStudentId"
      :surveyType="surveyType"
      @closeDrawer="handlePreviewSurveyClose"
    ></previewSurvey>
  </div>
</template>

<script>
import { surveyStatusTableColumn } from "../config/index";
import { getSurveyStudent } from "@/api/questionnaire";
import previewSurvey from "@/views/questionManagement/management/pages/list/components/previewSurvey";
import { export_excel_sync_new } from "@/public/asyncExport";
export default {
  components: { previewSurvey },
  props: {
    surveyId: {
      type: String
    },
    surveyType: {
      type: String
    }
  },
  data() {
    return {
      isExport: false,
      tableHeaderFieldStatistics: {
        studentName: "studentNum",
        readStatus: "readNum",
        writeStatus: "writeNum"
      },
      previewSurveyKey: 0,
      isShowPreviewSurvey: false,
      curSurveyId: "",
      curStudentId: "",
      exportLoading: false,
      searchLoading: false,
      search_title: [
        {
          props: "student_name",
          label: "学员姓名",
          type: "input",
          show: true
        },
        {
          props: "student_mobile",
          label: "手机号",
          type: "input",
          show: true
        },
        {
          label: "已读状态",
          props: "read_status",
          type: "select",
          show: true,
          selectOptions: [
            { name: "不限", id: undefined },
            { name: "已读", id: 1 },
            { name: "未读", id: 2 }
          ]
        },
        {
          label: "填写状态",
          props: "write_status",
          type: "select",
          show: true,
          selectOptions: [
            { name: "不限", id: undefined },
            { name: "已填写", id: 1 },
            { name: "未填写", id: 2 }
          ]
        },
        {
          props: "department_id",
          label: "归属校区",
          type: "school",
          show: false,
          selectOptions: [],
          school_choose_type: "chooseSchool",
          use_store_options: true
        }
      ],
      surveyStatusTableColumn,
      total: 0,
      tableData: [],
      curSurveyInfo: {},
      tableHeaderStatistics: {},
      tableParams: {
        page: 1,
        page_size: 10,
        survey_id: "",
        student_name: "",
        student_mobile: "",
        department_id: undefined,
        read_status: undefined,
        write_status: undefined
      }
    };
  },
  created() {
    console.log("surveyType,", this.surveyType);
    this.init();
    if (this.$_has({ m: "survey", o: "studentExport" })) {
      this.isExport = true;
    }
  },
  computed: {
    school_id() {
      // 获取当前登录用户所属的学校ID
      return this.$store.getters.doneGetSchoolId;
    }
  },
  methods: {
    getSearchList() {
      this.tableParams.page = 1;
      this.init();
    },
    async init() {
      this.tableParams.survey_id = this.surveyId;
      this.tableParams.department_id = this.school_id;
      console.log("tableParams", this.tableParams);
      const { code, data } = await getSurveyStudent(this.tableParams);
      if (code === 0) {
        this.tableData = data.results.students;
        this.total = data.count;
        this.tableHeaderStatistics = data.results.statistics;
      }
    },
    sortChange(val) {
      const { prop, order } = val;
      let _oreder = "";
      if (order === "ascending") {
        _oreder = "asc";
      } else if (order === "descending") {
        _oreder = "desc";
      }
      this.tableParams.sort = `${prop} ${_oreder}`;
      this.init();
    },
    toSurveyPage(index, data) {
      const curSurvey = data[index];
      this.curSurveyId = curSurvey.surveyId;
      this.curStudentId = curSurvey.studentId;
      this.curSurveyInfo = curSurvey;
      this.previewSurveyKey++;
      console.log(curSurvey.surveyId);
      this.isShowPreviewSurvey = true;
    },
    handlePreviewSurveyClose() {
      this.isShowPreviewSurvey = false;
    },
    currentChange(page) {
      this.tableParams.page = page;
      this.init();
    },
    reset() {
      this.tableParams = {
        page: 1,
        page_size: 10,
        survey_id: "",
        student_name: "",
        student_mobile: "",
        // department_id: undefined,
        read_status: "",
        write_status: "",
        sort: ""
      };
      this.$refs.table.clearSort();
      this.init();
    },
    async exportExcel() {
      this.exportLoading = true;
      const opt = {
        vm: this, // vue组件实例，
        api_url: "/api/questionnaire-service/admin/survey/surveyStudentExport", // 接口地址
        file_name: "问卷填写/已读状态详情", // 文件名
        success_msg: "问卷填写/已读状态详情导出成功！", // 导出成功的提示语
        error_msg: "问卷填写/已读状态详情导出失败！", // 导出失败的提示语,
        query: {
          exportData: 1,
          ...this.tableParams
        }
      };
      export_excel_sync_new(opt);
      this.exportLoading = false;
    },
    getRowKeys(row) {
      return row.id;
    },
    handleClose() {
      console.log("close");
      this.$emit("close");
    }
  }
};
</script>

<style scoped lang="scss">
.survey-status {
  width: 20px;
  height: 20px;
  border: 1px solid #999;
  border-radius: 50%;
  &.is-done {
    border: 1px solid #2d80ed;
    background: #2d80ed;
  }
}
.tg-table__box {
  width: 100%;
  margin-left: 0;
  margin-right: 16px;
}
::v-deep .el-table {
  padding: 0;
  th {
    background: #f5f8fc;
  }
  .el-table__header {
    padding: 0 16px;
    background: #f5f8fc;
  }
  .el-table__body {
    padding: 0 16px;
  }
}
</style>
