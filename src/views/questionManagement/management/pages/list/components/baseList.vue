<template>
  <div class="tableview-root">
    <tg-search
      :searchTitle.sync="search_title"
      :form.sync="listParams"
      @educe="exportExcel"
      @reset="reset"
      @search="onSubmit"
    ></tg-search>
    <el-row class="tg-box--margin tg-shadow--margin tg-row--height">
      <el-button
        type="plain"
        v-if="$_has({ m: 'survey', o: 'create' })"
        class="tg-button--plain"
        @click="onCreate"
        >创建问卷
      </el-button>
    </el-row>
    <div class="tg-table__box">
      <div class="tg-box--border"></div>
      <el-table
        ref="table"
        :data="dataList"
        tooltip-effect="dark"
        heigt="500"
        class="tg-table"
        v-loading="false"
      >
        <template v-for="(field, index) in table_title">
          <el-table-column
            :key="index"
            :prop="field.prop"
            :label="field.label"
            :width="field.width"
            :min-width="field.width || field.minWidth"
          >
            <template slot-scope="scope">
              <template v-if="field.comp">
                <component
                  :is="field.comp"
                  type="table"
                  v-if="$_has({ m: 'survey', o: 'updateEnabledStatus' })"
                  @handleSwitchChange="
                    (val) => handleSwitchChange(scope.row.surveyId, val)
                  "
                  :value="scope.row"
                />
              </template>
              <template v-else>
                <template v-if="$_has({ m: 'survey', o: 'getSurveyStudent' })">
                  <span
                    @click="
                      openSurveyStatusModal(
                        scope.row,
                        scope.row['postStatus'] === '未发布'
                      )
                    "
                    :class="[
                      'num',
                      scope.row['postStatus'] === '未发布' ? 'disabled' : ''
                    ]"
                    v-if="field.prop === 'readNum'"
                    >{{ scope.row[field.prop] }}</span
                  >
                  <span
                    @click="
                      openSurveyStatusModal(
                        scope.row,
                        scope.row['postStatus'] === '未发布'
                      )
                    "
                    :class="[
                      'num',
                      scope.row['postStatus'] === '未发布' ? 'disabled' : ''
                    ]"
                    v-else-if="field.prop === 'writeNum'"
                    >{{ scope.row[field.prop] }}</span
                  >
                </template>
                <span
                  v-if="!['readNum', 'writeNum'].includes(field.prop)"
                  class="cell-span"
                  >{{ scope.row[field.prop] }}</span
                >
              </template>
            </template>
          </el-table-column>
        </template>
        <el-table-column label="操作" :width="250" class-name="table-options">
          <template slot-scope="scope">
            <ToolBar
              :data="scope.row"
              type="list"
              :tools="getToolConfig(scope.row)"
              :tool-width="50"
              @on-delete="onDelete"
              @on-publish="onPublish"
            />
          </template>
        </el-table-column>
        <template slot="empty">
          <div style="margin-top: 2%; height: 300px">
            <loading v-if="loading"></loading>
            <div class="empty-container" v-else>暂无数据～</div>
          </div>
        </template>
      </el-table>
      <!-- 分页 -->
      <div class="tg-pagination">
        <span class="el-pagination__total">共 {{ total }} 条</span>
        <el-pagination
          background
          layout="sizes, prev, pager, next, jumper"
          :total="total"
          :current-page.sync="listParams.page"
          @current-change="handleCurrentChange"
          @size-change="sizeChange"
          :page-sizes="[10, 20, 50, 100]"
        >
        </el-pagination>
      </div>
    </div>
    <surveyStatusDiaolog
      v-if="showSurveyStatusDialog"
      :surveyId="curRowSurveyId"
      :surveyType="curSurveyType"
      @close="handleClose"
    ></surveyStatusDiaolog>
  </div>
</template>

<script>
import { cloneDeep } from "lodash";
import moment from "moment";
// 引入中文
import "moment/locale/zh-cn";
import empty from "../../../components/empty";
import loading from "@/views/loading";
import mySwitch from "./switch";
import surveyStatusDiaolog from "./surveyStatusDiaolog.vue";
import ToolBar from "./toolBar";
import {
  stateOptions,
  noListDataConfig,
  noSearchDataConfig,
  selectOptionsDict,
  buttonOptionsDict
} from "../config";
import { QOP_MAP } from "@/views/questionManagement/management/utils/constant";
import {
  getPostList,
  deleteSurvey,
  updateEnabledStatus,
  updatePostStatus,
  getTypeList
} from "@/api/questionnaire.js";
// 设置中文
moment.locale("zh-cn");

export default {
  name: "BaseList",
  props: ["surveyType"],
  provide() {
    return {
      curSurveyType: this.curSurveyType
    };
  },
  data() {
    return {
      search_title: [
        {
          label: "问卷标题",
          props: "title",
          type: "input",
          show: true
        },
        {
          props: "createdAt",
          label: "创建日期",
          type: "date",
          show: true,
          selectOptions: [],
          has_options: true
        },
        {
          props: "is_enabled",
          label: "问卷状态",
          type: "select",
          selectOptions: stateOptions,
          show: true
        }
      ],
      table_title: [
        {
          label: "问卷ID",
          prop: "surveyId",
          width: 100
        },
        {
          label: "问卷类型",
          width: 100,
          prop: "surveyTypeName"
        },
        {
          label: "问卷标题",
          width: 140,
          prop: "title"
        },
        {
          label: "创建人",
          width: 100,
          prop: "operatorName"
        },
        {
          label: "创建时间",
          prop: "createdAt",
          width: 170
        },
        {
          label: "活动时间",
          prop: "startTime",
          width: 240
        },
        {
          label: "问卷状态",
          prop: "updateDate",
          comp: "mySwitch",
          minWidth: 80
        },
        {
          label: "已读状态",
          prop: "readNum",
          minWidth: 80
        },
        {
          label: "填写状态",
          prop: "writeNum",
          minWidth: 80
        },
        {
          label: "发布状态",
          prop: "postStatus",
          minWidth: 80
        }
      ],
      typeList: [],
      createTime: [],
      listParams: {
        page: 1,
        page_size: 10,
        create_start: "",
        create_end: "",
        title: "",
        survey_type: "",
        is_enabled: ""
      },
      showSurveyStatusDialog: false,
      modifyType: "",
      loading: false,
      curSurveyType: "",
      stateOptions,
      noListDataConfig,
      noSearchDataConfig,
      questionInfo: {},
      total: 0,
      data: [],
      curRowSurveyId: "",
      currentPage: 1,
      searchVal: "",
      selectOptionsDict,
      buttonOptionsDict
    };
  },
  computed: {
    dataList() {
      const timeFormat = (time, format = "YYYY-MM-DD") => {
        return moment(time).format(format);
      };
      const data = cloneDeep(this.data);
      return data.map((item) => {
        return {
          ...item,
          createdAt: timeFormat(
            new Date(item.createdAt),
            "YYYY-MM-DD HH:mm:ss"
          ),
          surveyTypeName: this.typeList.find((i) => i.key === item.surveyType)
            ?.value,
          startTime:
            timeFormat(new Date(item.startTime)) +
            "至" +
            timeFormat(new Date(item.endTime)),
          endTime: timeFormat(new Date(item.endTime)),
          postStatus: item.postStatus === 1 ? "已发布" : "未发布",
          readNum: item.readNum + "/" + item.postNum,
          writeNum: item.writeNum + "/" + item.postNum
        };
      });
    },
    school_id() {
      // 获取当前登录用户所属的学校ID
      return this.$store.getters.doneGetSchoolId;
    }
  },
  watch: {
    surveyType: {
      immediate: true,
      handler(val) {
        console.log(val);
        this.listParams.survey_type = val;
        this.init();
      }
    }
  },
  methods: {
    async init() {
      this.loading = true;
      try {
        const { data } = await getPostList({
          ...this.listParams,
          department_id:
            this.surveyType === "satisfaction" ? undefined : this.school_id
        });
        this.loading = false;
        this.total = data.count;
        this.data = data.results;
        this.getQuestionType();
      } catch (error) {
        this.loading = false;
      }
    },
    async getQuestionType() {
      const { data } = await getTypeList();
      this.typeList = data;
    },
    getToolConfig(data) {
      const isShowPublish = ({ postStatus }) => {
        if (postStatus === "未发布") {
          if (data.isEnabled === 1) {
            return true;
          } else {
            return false;
          }
        } else {
          return false;
        }
      };
      const isShowEdit = () => {
        if (data.postStatus === "未发布" && data.isEnabled === 2) {
          return true;
        } else {
          return false;
        }
      };
      const isShowExport = (data) => {
        if (this.$_has({ m: "survey", o: "resultExport" })) {
          return true;
        } else {
          return false;
        }
      };
      const funcList = [
        {
          key: QOP_MAP.PUBLISH,
          label: "发布",
          o: "",
          show: isShowPublish(data)
        },
        {
          key: QOP_MAP.SHOW,
          label: "查看",
          o: ""
        },
        {
          key: QOP_MAP.EDIT,
          label: "修改",
          o: "",
          show: isShowEdit()
        },
        {
          key: QOP_MAP.DELETE,
          label: "删除",
          o: "delete",
          show: data.isEnabled === 2
        },
        {
          key: QOP_MAP.EXPORT,
          label: "导出",
          o: "export",
          show: isShowExport(data)
        },
        {
          key: QOP_MAP.STATISTICS,
          label: "统计"
        }
      ];
      return funcList;
    },
    async handleSwitchChange(surveyId, val) {
      const res = await updateEnabledStatus({
        isEnabled: val,
        surveyId
      });
      if (res.code === 1) {
        const message = res.message.split(";");
        let h = ``;
        for (let i = 0; i < message.length; i++) {
          h += `<p>${message[i]}</p>`;
        }
        this.$notify.error({
          dangerouslyUseHTMLString: true,
          duration: 6000,
          message: h
        });
      } else {
        this.$message.success("问卷状态更新成功！");
      }
      this.init();
    },
    async onDelete(row) {
      try {
        await this.$confirm("是否确认删除？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        });
      } catch (error) {
        return;
      }

      await deleteSurvey({ surveyId: row.surveyId });
      this.$message.success("删除成功");
      this.init();
    },
    handleCurrentChange(current) {
      this.listParams.page = current;
      this.init();
    },
    sizeChange(val) {
      this.listParams.page = 1;
      this.listParams.page_size = val;
      this.init();
    },
    async onPublish(data) {
      try {
        await this.$confirm("确定要发布吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        });
      } catch (error) {
        return;
      }
      await updatePostStatus({
        surveyId: data.surveyId,
        postStatus: 1
      });
      this.$message.success("发布成功");
      this.init();
    },
    onSubmit() {
      this.listParams.page = 1;
      if (this.listParams.createdAt) {
        this.listParams.create_start = this.listParams.createdAt[0];
        this.listParams.create_end = this.listParams.createdAt[1];
      } else {
        this.listParams.create_start = "";
        this.listParams.create_end = "";
      }
      this.init();
    },
    onCreate() {
      if (this.surveyType === "satisfaction" && this.dataList.length === 1) {
        this.$message.error("该类型问卷只能创建一个！");
        return;
      }
      this.$router.push({
        name: "createSurvey"
      });
    },
    reset() {
      this.listParams = {
        page: 1,
        page_size: 10,
        survey_type: sessionStorage.surveyType,
        create_start: "",
        create_end: "",
        title: "",
        is_enabled: ""
      };
      this.init();
    },
    openSurveyStatusModal(row, isNotPublish) {
      if (isNotPublish) {
        return;
      }
      console.log(
        row.surveyType,
        "row",
        this.listParams,
        this.listParams.survey_type
      );
      this.curSurveyType = row.surveyType;
      console.log(this.curSurveyType);
      this.curRowSurveyId = row.surveyId;
      this.showSurveyStatusDialog = true;
    },
    handleClose() {
      this.showSurveyStatusDialog = false;
    }
  },
  components: {
    empty,
    loading,
    surveyStatusDiaolog,
    // eslint-disable-next-line vue/no-reserved-component-names
    mySwitch,
    ToolBar
  }
};
</script>

<style lang="scss" rel="stylesheet/scss" scoped>
.tableview-root {
  width: calc(100% - 230px);
  padding: 20px;
  .filter-wrap {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    .select {
      display: flex;
    }
    .search {
      display: flex;
      padding-bottom: 20px;
    }
  }

  .num {
    color: #2d80ed;
    cursor: pointer;
    &.disabled {
      color: #c0c4cc;
    }
  }
  .list-table {
    min-height: 620px;
    padding: 10px 20px;
  }
  .list-pagination {
    margin-top: 20px;
    ::v-deep .el-pagination {
      display: flex;
      justify-content: flex-end;
    }
  }
  ::v-deep .el-table__header {
    .tableview-header .el-table__cell {
      .cell {
        height: 24px;
        color: #4a4c5b;
        font-size: 14px;
      }
    }
  }
  ::v-deep .tableview-row {
    .tableview-cell {
      padding: 5px 0;
      &.link {
        cursor: pointer;
      }
      .cell .cell-span {
        font-size: 14px;
      }
    }
  }
}
.el-select-dropdown__wrap {
  background: #eee;
}
.el-select-dropdown__item.hover {
  background: #fff;
}
</style>
