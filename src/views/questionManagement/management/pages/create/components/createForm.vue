<template>
  <div>
    <div class="tg-header__subtitle tg-info__back">
      <img
        src="../../../../../../assets/图片/icon_menu_down_ac.png"
        alt
        @click="onBack"
      />
      <span @click="onBack">返回</span>
    </div>
    <div class="right-side">
      <p class="type-title">{{ title }}</p>
      <el-form
        class="new-form"
        label-position="right"
        ref="ruleForm"
        :model="form"
        label-width="100px"
        :rules="rules"
        @submit.native.prevent
      >
        <el-form-item prop="surveyType" label="问卷类型">
          <el-select
            @change="handleSurveyTypeChange"
            :disabled="isDisabled || isEdit"
            v-model="form.surveyType"
            placeholder="请选择"
          >
            <el-option
              v-for="item in typeList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="title" label="问卷标题">
          <el-input
            v-model="form.title"
            :disabled="isDisabled"
            :class="form.title ? 'nonempty' : 'empty'"
            size="small"
            maxlength="20"
            show-word-limit
            placeholder="请输入问卷名称"
          />
        </el-form-item>
        <el-form-item prop="remark" label="问卷备注">
          <el-input
            type="textarea"
            :rows="6"
            placeholder="请输入内容"
            v-model="form.remark"
            :disabled="isDisabled"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="活动时间" required>
          <el-col :span="11">
            <el-form-item prop="startTime">
              <el-date-picker
                type="date"
                placeholder="选择开始日期"
                value-format="yyyy-MM-dd"
                v-model="form.startTime"
                :disabled="isDisabled"
                style="width: 100%"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col class="line" :span="2" style="text-align: center">-</el-col>
          <el-col :span="11">
            <el-form-item prop="endTime">
              <el-date-picker
                type="date"
                placeholder="选择结束日期"
                value-format="yyyy-MM-dd"
                v-model="form.endTime"
                :disabled="isDisabled"
                style="width: 100%"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-form-item>
        <template v-if="form.surveyType !== 'satisfaction'">
          <!--  v-else -->
          <template>
            <el-form-item label="选择校区" prop="campus" ref="campusRef">
              <el-button
                :disabled="isDisabled"
                @click="openSchoolModal(studentType)"
                size="small"
                type="primary"
                >查看校区</el-button
              >
              <div style="margin-top: 10px">
                <div v-if="checkedCampusList?.length">
                  <div
                    style="
                      display: inline-block;
                      vertical-align: top;
                      line-height: 1;
                    "
                  >
                    已选择的校区：
                  </div>
                  <div style="display: inline-block; vertical-align: top">
                    <div
                      style="line-height: 1; margin-bottom: 5px"
                      v-for="item in checkedCampusList"
                      :key="item.id"
                    >
                      {{ item.school_name }}
                    </div>
                  </div>
                </div>
                <div v-else>尚未选择校区</div>
              </div>
            </el-form-item>
          </template>
          <!--  v-if="form.surveyType === 'normal'" -->
          <template v-if="form.surveyType === 'normal'">
            <el-form-item
              label="发放群体"
              prop="grantGroup"
              required
              ref="grantGroupRef"
            >
              <div>
                <el-button
                  :disabled="isDisabled"
                  @click="handleCheckedChange(studentType)"
                  size="small"
                  type="primary"
                  >按学员选择+</el-button
                >
                <el-button
                  :disabled="isDisabled"
                  @click="handleCheckedChange(classType)"
                  size="small"
                  type="primary"
                  >按班级选择+</el-button
                >
              </div>
              <div style="display: flex; margin-top: 10px">
                <span style="margin-right: 20px"
                  >已选择{{ issuingGroup.student }}个学员，{{
                    issuingGroup.class
                  }}个班级</span
                >
                <el-button type="primary" @click="showDialog" size="mini"
                  >查看</el-button
                >
              </div>
            </el-form-item>
          </template>
          <el-form-item
            label="选择模板"
            v-if="
              form.surveyType === 'normal' || form.surveyType === 'audition'
            "
          >
            <el-input
              placeholder="请选择模板"
              readonly
              show-word-limit
              :validate-event="false"
              @click.native="templateVisible = true"
              v-model="form.template_name"
              class="tg-select tg-select--dialog custom-input"
              @mouseenter.native="template_flag = true"
              @mouseleave.native="template_flag = false"
            >
              <img
                slot="suffix"
                :src="
                  !template_flag
                    ? require('@/assets/图片/icon_more.png')
                    : require('@/assets/图片/icon_more_ac.png')
                "
                alt=""
                class="btn__img--dotted"
              />
            </el-input>
          </el-form-item>
          <el-form-item label="选择优惠券">
            <el-button
              :disabled="isDisabled"
              @click="openCouponModal"
              size="small"
              type="primary"
              >查看列表</el-button
            >
            <div style="margin-top: 10px">
              <span v-if="checkedCoupun.name"
                >已选择 {{ checkedCoupun.name }}</span
              >
              <span v-else>尚未选择优惠券</span>
            </div>
          </el-form-item>
        </template>
        <el-form-item>
          <el-button
            type="primary"
            size="small"
            @click="() => submit(undefined, 'filter')"
            :disabled="isDisabled"
            :loading="btnLoading"
          >
            <!-- {{ pageType === "create" ? "开始创建" : "开始编辑" }} -->
            保存
          </el-button>
          <el-button @click="onNext" size="small"> 下一步 </el-button>
        </el-form-item>
      </el-form>
    </div>
    <choose-class
      :check_id.sync="createForm.classroom_id"
      :check_name.sync="createForm.classroom_name"
      :check_arr.sync="class_check_arr"
      :choose_class_visible="choose_class_visible"
      v-if="choose_class_visible"
      @close="choose_class_visible = false"
      :has_modal="false"
      :department_id="form.campusId"
      :useStoreSchool="true"
    ></choose-class>
    <choose-student
      v-if="choose_student_visible"
      :has_modal="false"
      :check_arr.sync="createForm.studentList"
      :department_id="form.campusId"
      @close="choose_student_visible = false"
    ></choose-student>
    <el-dialog title="已选择学员/班级详情" :visible.sync="dialogTableVisible">
      <div class="dialog-wrap">
        <div class="student-analysis">
          <div class="student-tab">
            <span
              v-for="(item, index) in menuList"
              :key="index"
              :class="{ 'student-tab--active': activeName === item.value }"
              @click="handleClick(item)"
              >{{ item.name }}</span
            >
          </div>
          <div class="tg-table__box">
            <el-table
              class="tg-table statistics-table"
              tooltip-effect="dark"
              :data="showChecked.data"
              height="350"
            >
              <el-table-column
                v-for="item in showChecked.column"
                :key="item.prop"
                :property="item.prop"
                :label="item.label"
              ></el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </el-dialog>
    <el-dialog title="选择模板" :visible.sync="templateVisible">
      <div class="tg-table__box">
        <div class="tg-box--border"></div>
        <el-table
          class="tg-table"
          tooltip-effect="dark"
          :data="templateList"
          highlight-current-row
          @current-change="handleCurrentChange"
          height="350"
        >
          <el-table-column
            v-for="item in templateColumn"
            :formatter="item.formatter"
            :key="item.prop"
            :property="item.prop"
            :label="item.label"
          ></el-table-column>
        </el-table>
        <div style="margin: 10px 0">
          <span>已选中：{{ checkedTemplate?.title }}</span>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button
            class="tg-button--plain"
            type="plain"
            @click="handleTemplateClose"
            >取 消</el-button
          >
          <el-button
            class="tg-button--primary"
            type="primary"
            @click="handleTemplateSave"
            >确 定</el-button
          >
        </span>
      </div>
    </el-dialog>
    <couponModal
      v-if="isOpenCouponModal"
      :visible="isOpenCouponModal"
      @close="handleCouponModalClose"
      :department_id="form.campusId"
      :check_coupon.sync="checkedCoupun"
      @save="handleCouponModalSave"
    ></couponModal>
    <schools-authorize
      v-if="school_tree_visible"
      :depart_ids="form.campusId"
      :type="'edit'"
      @close="school_tree_visible = false"
      @save="add"
    ></schools-authorize>
  </div>
</template>
<script>
import { SURVEY_TYPE_LIST } from "../types";
import { studentColumn, classColumn } from "../config";
import ChooseStudent from "@/components/student/chooseStudent.vue";
import SchoolsAuthorize from "@/views/charge/schoolsAuthorize.vue";
import templateApi from "@/api/template";
import couponModal from "@/components/coupon/dialog/couponList.vue";
import moment from "moment";
import {
  createSurvey,
  metaInfo,
  getTypeList,
  updateMeta
} from "@/api/questionnaire";

export default {
  name: "CreateForm",
  components: { ChooseStudent, SchoolsAuthorize, couponModal },
  props: {
    selectType: {
      type: String,
      default: "normal"
    },
    pageType: {
      type: String,
      default: "create"
    }
  },
  data() {
    const checkGrantGroup = (rule, value, callback) => {
      if (this.issuingGroup.class === 0 && this.issuingGroup.student === 0) {
        callback(new Error("请选择学员或班级"));
      } else {
        this.form.grantGroup =
          this.issuingGroup.class || this.issuingGroup.student;
        callback();
      }
    };
    const checkCampus = (rule, val, callback) => {
      console.log(this.checkedCampusList);
      if (this.checkedCampusList.length) {
        callback();
      } else {
        callback(new Error("请选择校区"));
      }
    };
    return {
      templateColumn: [
        {
          prop: "title",
          label: "模板名称"
        },
        {
          prop: "createdAt",
          label: "创建时间",
          formatter(row) {
            return moment(row.createdAt).format("YYYY-MM-DD HH:mm:ss");
          }
        },
        {
          prop: "operatorName",
          label: "创建人"
        }
      ],
      checkedTemplate: {},
      templateList: [],
      template_flag: false,
      templateVisible: false,
      surveyId: "",
      btnLoading: false,
      activeName: "deliverStudent",
      department_ids: [],
      rules: {
        surveyType: [
          { required: true, message: "请选择问卷类型", trigger: "change" }
        ],
        title: [
          { required: true, message: "请输入问卷标题", trigger: "blur" },
          {
            min: 1,
            max: 20,
            message: "问卷标题长度在 1 到 20 个字",
            trigger: "blur"
          }
        ],
        grantGroup: [{ validator: checkGrantGroup }],
        startTime: [
          {
            type: "string",
            required: true,
            message: "请开始选择日期",
            trigger: "change"
          }
        ],
        endTime: [
          {
            type: "string",
            required: true,
            message: "请结束选择日期",
            trigger: "change"
          }
        ],
        campus: [{ required: true, validator: checkCampus }]
      },
      isOpenCouponModal: false,
      checkedCoupun: {},
      index: 0,
      classType: "CLASS",
      studentType: "STUDENT",
      dialogTableVisible: false,
      showChecked: {
        data: [],
        column: studentColumn
      },
      checkedCampusList: [],
      school_tree_visible: false,
      choose_student_visible: false,
      choose_class_visible: false,
      createForm: {
        classroom_id: "",
        classroom_name: "",
        studentList: []
      },
      issuingGroup: {
        class: 0,
        student: 0
      },
      submitTime: 0,
      class_check_arr: [],
      canSubmit: true,
      typeList: [],
      form: {
        title: "",
        grantGroup: "",
        deliverGroup: {},
        campusId: [],
        PostStatus: 0,
        surveyType: "",
        remark: "",
        couponId: ""
      },
      menuList: [
        {
          name: "已选学员",
          value: "deliverStudent"
        },
        {
          name: "已选班级",
          value: "deliverClassroom"
        }
      ]
    };
  },
  computed: {
    isDisabled() {
      return this.$route.query.pageType === "show";
    },
    isEdit() {
      return this.$route.query.pageType === "edit";
    },
    SURVEY_TYPE_LIST() {
      return SURVEY_TYPE_LIST;
    },
    ids() {
      return this.$store.getters.doneGetSchoolId.toString();
    },
    title() {
      return this.SURVEY_TYPE_LIST.find((item) => item.type === this.selectType)
        ?.title;
    },
    checkedStudent() {
      const classroom_id =
        this.createForm.classroom_id === ""
          ? 0
          : this.createForm.classroom_id.split(",")?.length;
      return {
        classroom_id,
        studentList: this.createForm.studentList?.length
      };
    }
  },
  created() {
    console.log(this.pageType);
    if (this.pageType === "createSurvey") {
      this.form.surveyType = sessionStorage.surveyType;
    }
    this.getQuestionType();
    this.getTemplateList();
    this.department_ids = this.ids.split(",");
    if (
      this.pageType === "editSurvey" ||
      this.$route.query.pageType === "edit"
    ) {
      this.getMetaInfo();
    }
  },
  watch: {
    class_check_arr: {
      handler(val) {
        if (val.length && this.$refs.grantGroupRef) {
          this.$refs.grantGroupRef.clearValidate();
        }
        this.issuingGroup.class = val?.length;
        this.form.grantGroup = val?.length;
        this.form.deliverGroup.deliverClassroom = val.map((i) => {
          return {
            ...i,
            classroom_name: i.name,
            classroom_id: i.id
          };
        });
      }
    },
    "createForm.studentList": {
      handler(val) {
        if (val.length && this.$refs.grantGroupRef) {
          this.$refs.grantGroupRef.clearValidate();
        }
        this.issuingGroup.student = val?.length;
        this.form.grantGroup = val?.length;
        this.form.deliverGroup.deliverStudent = val.map((i) => i.student_base);
      }
    }
  },
  methods: {
    async getTemplateList() {
      const res = await templateApi.surveyTemplateAllList();
      if (res.code === 0) {
        this.templateList = res.data;
      } else {
        this.$message.error(res.message);
      }
    },
    handleCurrentChange(row) {
      console.log(row);
      this.checkedTemplate = row;
    },
    handleTemplateSave() {
      if (this.$route.query.pageType === "edit") {
        this.$confirm("选择新模板会替换掉现有内容，是否继续？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          this.form.template_name = this.checkedTemplate.title;
          this.form.templateId = this.checkedTemplate.surveyId;
          this.templateVisible = false;
        });
      } else {
        this.form.template_name = this.checkedTemplate.title;
        this.form.templateId = this.checkedTemplate.surveyId;
        this.templateVisible = false;
      }
    },
    handleTemplateClose() {
      this.templateVisible = false;
      this.checkedTemplate = {};
    },
    onBack() {
      this.$router.replace({
        name: "survey"
      });
    },
    async getMetaInfo() {
      const res = await metaInfo({ surveyId: this.$route.query.id });
      this.form = { ...this.form, ...res.data };
      this.form.deliverGroup.deliverClassroom =
        this.form.deliverGroup.deliverClassroom || [];
      this.form.deliverGroup.deliverStudent =
        this.form.deliverGroup.deliverStudent || [];
      this.form.startTime = moment(
        new Date(this.form.startTime).getTime()
      ).format("YYYY-MM-DD HH:mm:ss");
      this.checkedTemplate.surveyId = res.data.templateId;
      this.checkedTemplate.title = res.data.templateName;
      this.form.template_name = res.data.templateName;
      this.form.templateId = res.data.templateId;
      this.form.campusId = res.data.campus?.map((i) => i.id);
      this.checkedCampusList = res.data.campus?.map((i) => ({
        school_name: i.name,
        id: i.id
      }));
      this.checkedCoupun = res.data.couponInfo;
      this.form.couponId = res.data.couponInfo.id;
      this.form.endTime = moment(new Date(this.form.endTime).getTime()).format(
        "YYYY-MM-DD HH:mm:ss"
      );
      this.class_check_arr = this.form.deliverGroup.deliverClassroom.map(
        (i) => {
          return {
            ...i,
            id: i.classroom_id,
            name: i.classroom_name
          };
        }
      );
      this.showChecked.data = this.form.deliverGroup.deliverStudent;
      this.createForm.classroom_id = this.form.deliverGroup.deliverClassroom
        .map((i) => i.classroom_id)
        .join(",");
      this.createForm.studentList = this.form.deliverGroup.deliverStudent.map(
        (i) => {
          return {
            ...i,
            student_id: i.student_id,
            student_base: {
              student_name: i.student_name,
              student_number: i.student_number,
              student_mobile: i.student_mobile
            }
          };
        }
      );
      console.log(this.form, "form", this.createForm);
    },
    async getQuestionType() {
      const { data } = await getTypeList();
      this.typeList = data.filter((i) => {
        if (sessionStorage.surveyType !== "satisfaction") {
          return i.key !== "satisfaction";
        } else {
          return i.key === "satisfaction";
        }
      });
    },
    checkForm(fn) {
      this.$refs.ruleForm.validate((valid) => {
        valid && typeof fn === "function" && fn();
      });
    },
    showDialog() {
      this.index = 0;
      this.handleTabChange("deliverStudent");
      this.dialogTableVisible = true;
    },
    handleSurveyTypeChange() {
      this.$refs.ruleForm.clearValidate();
    },
    handleClick({ value }) {
      console.log(value);
      this.activeName = value;
      this.handleTabChange(value);
    },
    handleTabChange(type) {
      const tableColumnFiled = {
        deliverStudent: studentColumn,
        deliverClassroom: classColumn
      };
      this.showChecked.data = this.form.deliverGroup[type];
      this.showChecked.column = tableColumnFiled[type];
    },
    handleCheckedChange(type) {
      console.log(this.checkedCampusList);
      if (!this.checkedCampusList.length) {
        this.$message.error("请先选择校区！");
        return false;
      }
      if (type === "CLASS") {
        this.choose_class_visible = true;
      } else {
        this.choose_student_visible = true;
      }
    },
    openCouponModal() {
      if (!this.checkedCampusList.length) {
        this.$message.error("请先选择校区！");
        return false;
      }
      this.isOpenCouponModal = true;
    },
    handleCouponModalClose() {
      this.isOpenCouponModal = false;
    },
    handleCouponModalSave(info) {
      this.checkedCoupun = info;
      this.handleCouponModalClose();
    },
    openSchoolModal() {
      this.school_tree_visible = true;
    },
    add(val, val2) {
      const checked = val2.filter((i) => i.depart_name === "" && i.checked);
      this.checkedCampusList = checked;
      this.checkedCoupun = {};
      this.form.campusId = val;
      if (val.length) {
        this.$refs.campusRef.clearValidate();
      }
      console.log(this.form.campusId, checked);
    },
    submit(fn, isFilter) {
      if (!this.canSubmit) {
        return;
      }
      this.checkForm(async () => {
        this.btnLoading = true;
        this.canSubmit = false;
        this.form.studentId = this.createForm.studentList.map(
          (i) => i.student_id
        );
        this.form.studentList = this.createForm.studentList;
        this.form.classroomId = this.createForm.classroom_id.split(",");
        this.form.couponId = this.checkedCoupun.id;
        this.submitTime++;
        const res =
          // eslint-disable-next-line prettier/prettier
          this.pageType === "editSurvey" ||
          this.$route.query.pageType === "edit"
            ? await updateMeta(this.form)
            : await createSurvey(this.form);
        if (res && res.code === 0) {
          if (res.data || this.$route.query.id) {
            this.$message.success("问卷基础信息保存成功");
            const id = res.data.id || this.$route.query.id;
            if (isFilter && !this.surveyId) {
              this.$router.push({
                path: this.$route.path,
                query: {
                  id,
                  pageType: "edit"
                }
              });
              setTimeout(() => {
                this.getMetaInfo();
              }, 200);
            }
            this.surveyId = id;
            this.submitTime++;
            typeof fn === "function" && fn(id);
          } else {
            this.$message.error(
              res.errmsg || this.pageType === "editSurvey"
                ? "编辑失败"
                : "创建失败"
            );
          }
        } else {
          this.canSubmit = true;
          this.$message.error(res.message);
        }
        this.btnLoading = false;
        this.canSubmit = true;
      });
    },
    onNext() {
      console.log(this.isDisabled);
      if (this.isDisabled) {
        this.$router.push({
          path: `/survey/${this.$route.query.id}/edit`,
          query: this.$route.query
        });
        sessionStorage.isSatisfaction = this.form.surveyType === "satisfaction";
        return;
      }
      if (this.surveyId) {
        this.$router.push({
          path: `/survey/${this.$route.query.id}/edit`,
          query: {
            id: this.surveyId
          }
        });
        sessionStorage.isSatisfaction = this.form.surveyType === "satisfaction";
      } else {
        this.submit((id) => {
          sessionStorage.isSatisfaction =
            this.form.surveyType === "satisfaction";
          this.$router.push({
            path: `/survey/${this.$route.query.id}/edit`,
            query: {
              id
            }
          });
        });
      }
      // sessionStorage.isSatisfaction = this.form.surveyType === "satisfaction";
      console.log(
        this.form.surveyType === "satisfaction",
        sessionStorage.isSatisfaction
      );
    }
  }
};
</script>
<style lang="less">
.student-analysis {
  .student-tab {
    margin-left: -10px;
    // border-top: 1px solid #e0e6ed;
    left: 0;
    height: 46px;
    background: #fff;
    padding: 0 16px;
    box-shadow: 0 2px 6px 0 rgba(204, 208, 217, 0.35);
    width: calc(100% + 20px);
    box-sizing: border-box;
    span {
      font-family: "PingFangSC-Semibold, sans-serif,Arial";
      color: #475669;
      font-size: 14px;
      display: inline-block;
      height: 44px;
      border-bottom: 2px solid transparent;
      line-height: 44px;
      cursor: pointer;
      font-weight: bold;
    }

    span + span {
      margin-left: 32px;
    }

    span.student-tab--active {
      color: #2d80ed;
      border-color: #2d80ed;
    }
  }
  .el-table {
    padding: 0;
  }
  .statistics-table {
    width: 100%;
    border: 1px solid #2d80ed;
    .el-table th.is-leaf {
      border: none;
    }
    .el-table td:last-child {
      border-right: none;
    }
    .el-table {
      padding: 0;
      border: none;
    }
    th {
      background: #ebf4ff;
    }
    .el-table th:first-child > .cell {
      padding-left: 26px;
    }
    .el-table td:first-child > .cell {
      padding-left: 26px;
    }
    .el-table__body-wrapper {
      height: calc(100% - 46px);
      .empty-container {
        // position: absolute;
        top: 50%;
        left: 50%;
        height: 80px;
      }
      .loading-container {
        position: absolute;
        top: 15%;
        left: 1%;
        width: 100%;
        background: transparent;
        .box {
          height: 100%;
        }
      }
    }
  }
}
</style>

<style lang="scss">
.new-form {
  .el-form-item {
    margin-bottom: 15px;
  }
  .el-form-item__error {
    display: block;
    top: 100%;
  }
}
</style>
<style lang="scss" rel="stylesheet/scss" scoped>
.tg-info__back {
  margin-bottom: 16px;
  img {
    width: 10px;
    height: 6px;
    margin-right: 10px;
    transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    -o-transform: rotate(90deg);
    -webkit-transform: rotate(90deg);
    cursor: pointer;
  }
  span {
    cursor: pointer;
  }
}
.right-side {
  width: 538px;
  padding-left: 24px;
  height: 100%;
  position: relative;
  flex-shrink: 0;

  .type-title {
    font-family: PingFangSC-Medium;
    font-size: 24px;
    color: $font-color-title;
    letter-spacing: 0;
    margin-top: 30px;
    margin-bottom: 30px;
  }
}

.new-form {
  position: relative;
  right: 20px;

  .el-button.el-button--small {
    // height: 32px;
    // margin-right: 10px;
    // border: unset;
    // color: white;

    ::v-deep span {
      font-size: 14px;
    }
  }

  .create-btn {
    background-color: #2d80ed;
    margin-right: 10px;
    height: 32px;
    margin-right: 10px;
    border: unset;
    color: white;
  }
}

.form-item-tip {
  font-family: PingFangSC-Regular;
  font-size: 12px;
  color: rgb(146, 148, 157);
  letter-spacing: 0;
}
</style>
