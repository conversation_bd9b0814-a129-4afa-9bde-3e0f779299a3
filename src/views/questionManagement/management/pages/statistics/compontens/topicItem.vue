<template>
  <div>
    <div class="title">
      <span style="white-space: nowrap"
        >第{{ convertToChineseNumber(index) }}题：</span
      >
      <span v-html="topicInfo.title"></span>
      <span
        style="white-space: nowrap"
        class="is-required"
        v-show="topicInfo.isRequired"
        >（必填）</span
      >
      <span style="white-space: nowrap"
        >（{{ questionType[topicInfo.question_type] }}）</span
      >
    </div>
    <div class="tips" v-show="isQA">详细作答情况</div>
    <el-table
      :data="topicInfo.list"
      border
      v-loading="topicInfo.loading"
      :show-summary="!isQA && !isDateStr"
      :summary-method="getSummaries"
      style="width: 100%; padding: 0"
      :style="{
        marginBottom: isQA ? '' : '16px'
      }"
    >
      <el-table-column
        v-for="item in column"
        :key="item.prop"
        :label="item.label"
        :prop="item.prop"
      >
        <template slot-scope="scope">
          <template v-if="item.prop === 'student_name'">
            <span v-if="$_has({ m: 'survey', o: 'surveyShow' })">
              {{ scope.row[item.prop] }}
            </span>
            <span v-else>{{
              scope.row[item.prop]
                ? scope.row[item.prop].replace(/./g, "*")
                : ""
            }}</span>
          </template>
          <template v-else-if="item.prop === 'option_text'">
            <div v-html="scope.row[item.prop]"></div>
          </template>
          <template v-else-if="item.prop === 'created_at'">
            <span>{{ scope.row[item.prop] | createdTime }}</span>
          </template>
          <template v-else-if="item.prop === 'rate'">
            <el-progress
              color="#2D80ED"
              :stroke-width="16"
              :percentage="scope.row[item.prop]"
            ></el-progress>
          </template>
          <template v-else>
            <span>{{ scope.row[item.prop] }}</span>
          </template>
        </template>
      </el-table-column>
    </el-table>
    <!--   -->
    <div class="tg-pagination" style="margin-bottom: 16px" v-if="isQA">
      <span class="el-pagination__total"
        >共 {{ topicInfo.effective_num }} 条</span
      >
      <el-pagination
        background
        layout="prev, pager, next,jumper"
        :total="topicInfo.effective_num"
        :page-size="topicInfo.params.page_size"
        :current-page="topicInfo.params.page"
        @current-change="handleCurrentChange"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import {
  questionType,
  checkboxColumn,
  answerColumn,
  dateStrColumn
} from "../config";
import { getSurveyAnswerList } from "@/api/questionnaire";
import moment from "moment";
export default {
  props: {
    index: {
      type: Number,
      default: 0
    },
    topicInfo: {
      type: Object,
      default: () => {}
    },
    department_id: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      questionType,
      column: []
      // checkboxColumn,
      // answerColumn
    };
  },
  created() {
    if (this.isQA) {
      console.log(this.topicInfo, "isQA");
      this.topicInfo.params.survey_id = this.$route.query.id;
      this.column = answerColumn;
    } else if (this.isDateStr) {
      this.column = dateStrColumn;
    } else {
      this.column = checkboxColumn;
    }
    // console.log(this.topicInfo.question_type, this.data);
  },
  computed: {
    isQA() {
      return ["textarea", "text"].includes(this.topicInfo.question_type);
    },
    isDateStr() {
      return ["date-str"].includes(this.topicInfo.question_type);
    }
  },
  methods: {
    convertToChineseNumber(num) {
      // 定义中文数字的映射关系
      const chineseNumber = [
        "零",
        "一",
        "二",
        "三",
        "四",
        "五",
        "六",
        "七",
        "八",
        "九"
      ];
      const chineseUnit = ["", "十", "百", "千", "万", "亿"]; // 支持万、亿单位的转换，可根据需要继续扩展

      const numStr = String(num); // 将数字转换为字符串
      const len = numStr.length; // 数字的位数
      let result = ""; // 最终转换结果

      for (let i = 0; i < len; i++) {
        const digit = Number(numStr[i]); // 获取当前位的数字
        const unit = (len - 1 - i) % 4; // 当前位对应的单位索引

        if (unit === 0 && digit === 0) {
          // 对应的单位为万或亿，且当前位数字为零时，直接跳过
          continue;
        }

        if (digit !== 0) {
          // 当前位数字不为零时，加上对应的中文数字和单位
          result += chineseNumber[digit] + chineseUnit[unit];
        } else {
          // 当前位数字为零时
          if (i !== 0 && result[result.length - 1] !== "零") {
            // 上一位数字不为零时，加上中文数字“零”
            result += "零";
          }
        }
      }

      return result;
    },
    getSummaries(param) {
      const { columns } = param;
      const sums = [];
      // console.log(data, "data---");
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "本题有效填写人次";
          return;
        }
        if (index === 1) {
          sums[index] = this.topicInfo.effective_num;
        }
      });

      return sums;
    },
    handleSizeChange(page_size) {},
    handleCurrentChange(page) {
      this.topicInfo.params.page = page;
      this.topicInfo.params.field = this.topicInfo.field;
      this.getList();
      // console.log(page, this.topicInfo.field, this.$route.query.id, this.topicInfo.list);
    },
    async getList() {
      this.topicInfo.loading = true;
      const { code, data } = await getSurveyAnswerList({
        ...this.topicInfo.params,
        department_id: this.department_id
      });
      if (code === 0) {
        this.topicInfo.list = data.results;
      }
      this.topicInfo.loading = false;
    }
  },
  filters: {
    createdTime(val) {
      console.log(val, "filter");
      return moment(val).format("YYYY-MM-DD HH:mm");
    }
  }
};
</script>

<style scoped lang="scss">
::v-deep .el-table--border {
  .el-table__cell {
    &:first-child {
      .cell {
        padding-left: 35px;
      }
    }
  }
}
::v-deep .el-progress-bar {
  padding-right: 70px;
  margin-right: -70px;
}
::v-deep .el-progress-bar__outer {
  border-radius: 4px;
  .el-progress-bar__inner {
    border-radius: 4px !important;
  }
}
::v-deep .el-progress__text {
  font-size: 14px !important;
}
::v-deep .el-table {
  th {
    background-color: #69abff30;
    color: #1f2d3d;
  }
}
::v-deep .el-table--border {
  border: 1px solid #157df0;
  border-radius: 8px;
  padding: 0;
  .el-table__cell {
    border-right: 1px solid #d3dce6;
  }
}
.title {
  display: flex;
  color: #1f2d3d;
  font-weight: 600;
  line-height: 20px;
  font-size: 16px;
  margin-bottom: 16px;
  .is-required {
    color: #f63e3e;
  }
}
.tips {
  width: 96px;
  height: 40px;
  border-radius: 4px;
  background: #2d80ed;
  flex-shrink: 0;
  color: #fff;
  text-align: center;
  font-size: 13px;
  font-weight: 500;
  line-height: 35px;
  margin-bottom: -8px;
}
</style>
