<template>
  <div class="answer-page">
    <div class="answer-page-bg"></div>
    <div class="answer-page-header">
      <div class="answer-page-header-left">
        {{ data.title }}
      </div>
      <div class="answer-page-header-right">
        <img src="../../imgs/survey.png" alt="" />
      </div>
    </div>
    <div class="answer-page-content">
      <template v-if="answerStatus === 'underway'">
        <div class="answer-page-content-header">
          <div class="card active-time">
            <!-- :style="{ marginBottom: data.remark ? '0' : '8.4vw' }" -->
            <div class="title">活动时间</div>
            <div class="content">
              {{
                moment(new Date(data.start_time).getTime()).format(
                  "YYYY-MM-DD"
                )
              }}至{{
                moment(new Date(data.end_time).getTime()).format("YYYY-MM-DD")
              }}
            </div>
          </div>
          <div v-if="data.remark" class="card content-description">
            <div class="title">活动说明</div>
            <div class="content">
              {{ data.remark }}
            </div>
          </div>
        </div>
        <div class="answer-page-content-body">
          <div
            class="topic-item"
            v-for="(item, index) in topicList"
            :ref="item.field"
            :key="index"
          >
            <div class="topic-header">
              <span class="is-required" v-if="item.isRequired">*</span>
              <div class="item-header-title">
                {{ index + 1 }}、<span v-html="item.title"></span>
              </div>
              <div class="item-header-type">
                （{{ questionType[item.type] }}）
              </div>
            </div>
            <div class="topic-content">
              <div
                style="height: 100%"
                v-if="['text', 'textarea'].includes(item.type)"
              >
                <contentTextarea
                  :disabled="isFilled || isWeb || isWriteStatus"
                  style="height: 100%"
                  :itemConfig="item"
                  v-model="item.value"
                ></contentTextarea>
              </div>
              <div v-else-if="['radio', 'checkbox'].includes(item.type)">
                <checkbox-group
                  :key="item.field"
                  :type="item.type"
                  :itemConfig="item"
                  :disabled="isFilled || isWeb || isWriteStatus"
                  :checkboxGroup="item.options"
                  v-model="item.value"
                ></checkbox-group>
              </div>
              <div v-else-if="item.type === 'radio-star'" class="star">
                <rate
                  :itemConfig="item"
                  :disabled="isFilled || isWeb"
                  v-model="item.value"
                ></rate>
              </div>
            </div>
            <div class="error-meesage" v-if="item.isShowErrorMessage">
              {{ item.errorMessage }}
            </div>
          </div>
        </div>
      </template>
      <template v-else>
        <div class="result">
          <div class="title"></div>
          <div class="submit-block">
            <div class="survey-name">{{ data.title }}</div>
            <div class="status-icon">
              <img :src="submit_success" alt="" />
            </div>
            <div class="submit-title">提交成功</div>
            <div class="submit-subtitle">非常感谢您的宝贵意见</div>
            <div class="answer-page-footer" @click="onNavigateBack">完成</div>
          </div>
        </div>
      </template>
    </div>
    <!-- v-if="answerStatus === 'underway'" -->
    <div
      v-if="answerStatus === 'underway' && isFilled === false && !isWriteStatus"
      class="answer-page-footer"
      @click="handleNextClick"
    >
      提交
    </div>
    <el-dialog
      :visible.sync="dialogVisible"
      class="get-coupon-dialog"
      :before-close="handleClose"
    >
      <div class="coupon-dialog-bg">
        <div class="ray-of-light"></div>
        <div class="coupon-dialog-content">
          <img
            src="../../imgs/close.png"
            @click="close"
            class="close-icon"
            alt=""
          />
          <div class="coupon-money">
            <!-- {{ (couponInfo.discount_price / 100).toFixed(2) }} -->
            <template v-if="couponInfo.coupon_type === 1">
              <span class="unit">￥</span>
              <span class="money">{{ couponInfo.discount_price / 100 }}</span>
            </template>
            <template v-else>
              <span class="money discount">{{
                couponInfo.discount_price / 10
              }}</span>
              <span class="unit">折</span>
            </template>
          </div>
          <div class="coupon-name">{{ couponInfo.name }}</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import checkboxGroup from "../components/checkboxGroup/index.vue";
import rate from "../components/rate/index.vue";
import contentTextarea from "../components/textarea/index.vue";
import { createResponse } from "@/api/questionnaire.js";
import { questionType } from "../config/index";
export default {
  components: { checkboxGroup, rate, contentTextarea },
  props: {
    data: {
      type: Object,
      default: () => {
        return {
          code: {},
          isFill: false,
          questionFill: {}
        };
      }
    },
    surveyStudentId: {
      type: String,
      default: ""
    },
    writeStatus: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      dialogVisible: false,
      questionType,
      couponInfo: {
        discount_price: 100,
        coupon_type: 1,
        name: "100元优惠券100元优惠券"
      },
      topicIndex: 0,
      topicList: [],
      isFilled: false,
      answerStatus: "underway",
      // answerStatus: "done",
      integralIcon: require("@/views/questionManagement/imgs/integral.png"),
      submit_success: require("@/assets/图片/submit_success.png")
    };
  },
  created() {
    setTimeout(() => {
      this.getSchemaConfig();
    }, 500);
  },
  computed: {
    isWeb() {
      return this.$route.query.from === "web";
    },
    isDoneStep() {
      return this.topicIndex === this.topicList.length - 1;
    },
    isStartStep() {
      return this.topicIndex === 0;
    },
    isWriteStatus() {
      return this.writeStatus === "2" || this.writeStatus === "1";
    }
  },
  methods: {
    onNavigateBack() {
      // if (typeof wx !== "undefined" && wx.miniProgram) {
      //   wx.miniProgram.postMessage({
      //     data: {
      //       action: "navigateBack"
      //     }
      //   });
      // }
      wx.miniProgram.navigateBack();
      // wx.miniProgram.redirectTo({
      //   url: "/pages/student/questionnaireSurvey/index?back=1"
      // });
      // if (window.parent && window.parent.postMessage) {
      //   console.log(window.parent);
      //   window.parent.postMessage(
      //     {
      //       action: "navigateBack"
      //     },
      //     "*"
      //   );
      // }
      // location.href = "uniwebview://navigateBack";
    },
    init() {
      const { start_time, end_time } = this.data;
      this.data.start_time = moment(new Date(start_time).getTime()).format(
        "YYYY-MM-DD"
      );
      this.data.end_time = moment(new Date(end_time).getTime()).format(
        "YYYY-MM-DD"
      );
      console.log(this.data);
    },
    getSchemaConfig() {
      const field = {
        checkbox: "checkboxResult",
        radio: "optionHash",
        textarea: "optionHash",
        text: "optionHash",
        "radio-star": "starSort",
        "date-str": "optionHash"
      };
      const getValue = (target, origin) => {
        const obj = target.map((i) => {
          return {
            ...i,
            ...origin.find((j) => i.field === j.field)
          };
        });
        return obj;
      };
      const { code, isFill, questionFill } = this.data;
      this.isFilled = isFill;
      console.log(this.data, "this.data");
      if (isFill) {
        this.topicList = getValue(code.dataConf.dataList, questionFill).map(
          (i) => {
            return {
              ...i,
              value: i[field[i.questionType]],
              isShowErrorMessage: null,
              errorMessage: "此题为必答题"
            };
          }
        );
      } else {
        this.topicList = code.dataConf.dataList.map((i) => {
          return {
            ...i,
            value: "",
            isShowErrorMessage: null,
            errorMessage: "此题为必答题"
          };
        });
      }

      this.init();
    },
    handleNextClick(isDone) {
      console.log(1);
      const regexpMap = {
        nd: /^-?(?:\d+|\d{1,3}(?:,\d{3})+)(?:\.\d+)?$/,
        m: /^[1]([3-9])[0-9]{9}$/,
        idcard: /^(\d{15}$|^\d{18}$|^\d{17}(\d|X|x))$/,
        strictIdcard:
          /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}$)/,
        n: /^[0-9]+([.]{1}[0-9]+){0,1}$/,
        e: /^(\w-*\.*)+@(\w-?)+(\.\w{2,})+$/,
        licensePlate:
          /^([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[a-zA-Z](([DFAG]((?![IO])[a-zA-Z0-9](?![IO]))[0-9]{4})|([0-9]{5}[DF]))|[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4,5}[A-Z0-9挂学警港澳]{1})$/
      };

      const msgMap = {
        "*": "必填",
        m: "请输入正确手机号",
        idcard: "请输入正确的身份证号码",
        strictIdcard: "请输入正确的身份证号码",
        n: "请输入数字",
        nd: "请输入数字",
        e: "请输入邮箱",
        licensePlate: "请输入车牌号"
      };
      if (this.isFilled || this.isWeb) {
        if (isDone) return;
        this.topicIndex++;
      } else {
        const filedList = [];
        for (let i = 0; i < this.topicList.length; i++) {
          const { value, valid, isRequired, field } = this.topicList[i];
          if (
            (value === "" || (Array.isArray(value) && value.length === 0)) &&
            isRequired
          ) {
            filedList.push(this.$refs[field][0]);
            this.topicList[i].isShowErrorMessage = true;
          }
          if (valid && !regexpMap[valid].test(value)) {
            this.topicList[i].isShowErrorMessage = true;
            this.topicList[i].errorMessage = msgMap[valid];
            filedList.push(this.$refs[field][0]);
          }
        }
        if (filedList.length) {
          filedList[0].scrollIntoView({ behavior: "smooth" });
          this.$message.error("请检查必填项!");
        }
        if (this.topicList.every((i) => i.value)) {
          this.save();
          return;
        }
        this.topicIndex++;
      }
    },
    async save() {
      const questionData = this.topicList.map((i) => {
        return {
          field: i.field,
          questionType: i.type,
          starSort: i.type === "radio-star" ? Number(i.value) : undefined,
          hash: ["radio", "textarea", "text", "binary-choice"].includes(i.type)
            ? String(i.value)
            : undefined,
          checkboxHash: ["checkbox"].includes(i.type)
            ? i.value.map((i) => String(i))
            : undefined
        };
      });
      console.log(questionData, this.topicList);
      const params = {
        questionData,
        visitor: this.$route.query.visitor,
        operationId: this.$route.query.student_id,
        studentId: this.$route.query.student_id,
        surveyPath: this.$route.query.survey_id,
        surveyStudentId: Number(this.surveyStudentId),
        from: this.$route.query.from,
        token: this.$route.query.token
      };
      const res = await createResponse(params);
      if (res.code === 0) {
        this.answerStatus = "done";
        if (res.data.id !== 0) {
          this.dialogVisible = true;
        }
        this.couponInfo = res.data;
      } else {
        this.$message.error(res.message);
      }
    },
    handlePrevClick(isStart) {
      if (isStart) {
        this.onBack();
        return;
      }
      this.topicIndex--;
    },
    onBack() {
      this.$emit("onBack", "home");
    },
    handleClose() {
      console.log(1);
      this.dialogVisible = true;
    },
    close() {
      this.dialogVisible = false;
    },
    getCoupon() {
      this.close();
      this.$message.success("领取成功！");
    }
  }
};
</script>
<style lang="scss">
body {
  overflow-y: auto;
}
.get-coupon-dialog {
  .el-dialog {
    border: none;
    background: transparent;
    box-shadow: none;
    width: auto;
    .el-dialog__header {
      display: none;
    }
  }
  .coupon-dialog-bg {
    // width: 74.6667vw;
    // height: 61.8667vw;
    border-radius: 8vw;
    position: relative;
    .ray-of-light {
      position: absolute;
      top: -50px;
      left: 0;
      width: 100%;
      height: 117%;
      transform: scale(1.2);
      transform: scale(1.5);
      z-index: -1;
      // background: url(../../imgs/backlight.png) no-repeat;
      background-size: 100% 100%;
    }
    .coupon-dialog-content {
      width: 85vw;
      height: 69.3333vw;
      margin-left: -2.6667vw;
      background: url(../../imgs/coupon-bg.png) no-repeat;
      background-size: 100% 100%;
      padding: 30.3333vw 9.8vw 19vw 15.8vw;
      display: flex;
      align-items: center;
      position: relative;
      .close-icon {
        position: absolute;
        width: 4vw;
        height: 4vw;
        right: 6vw;
        top: 0;
      }
      .coupon-money {
        width: 37%;
        color: #ff553a;
        padding-right: 1.8667vw;
        // padding-left: 3.2vw;
        text-align: center;
        white-space: nowrap;
        .unit {
          font-size: 4.5333vw;
          font-weight: 500;
        }
        .money {
          font-weight: 600;
          font-size: 24px;
        }
      }
      .coupon-name {
        flex: 1;
        padding-left: 3.0667vw;
        padding-right: 2.1333vw;
        color: #9b3a09;
        font-size: 3.5333vw;
        font-weight: 400;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        line-height: 1.5;
        text-overflow: ellipsis;
        word-break: break-word; /* 优先在单词间断行 */
        line-break: anywhere;
      }
    }
  }
}
</style>

<style scoped lang="scss">
.answer-page {
  width: 100%;
  min-height: 100vh;
  height: auto;
  padding: 3vw 4.2vw 5.8667vw 4.2vw;
  background: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/3795ec70-1825-4d1a-984e-4dd96bb3482f.png");
  position: relative;
  background-size: 100% 100%;
  .answer-page-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url(../../imgs/survey_bg.png) no-repeat;
    background-size: 100% 18%;
    z-index: -1;
  }
  .answer-page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 38.8vw;
    position: relative;
    .answer-page-header-left {
      color: #fff;
      font-size: 6.9333vw;
      font-weight: 700;
      text-shadow: 0px 4px 4px #026be4;
      line-height: 9.6vw;
    }
    .answer-page-header-right {
      width: 32.8667vw;
      // height: 100%;
      flex-shrink: 0;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
  .answer-page-content {
    position: relative;
    .answer-page-content-header {
      border-radius: 7.3333vw 1.3333vw 7.3333vw 3.2vw;
      background: #fff;
      box-shadow: 0px 4px 46px 0px rgba(33, 78, 219, 0.64);
      padding: 7.2vw 4.2667vw 4.5333vw 4.2667vw;
      width: 100%;
      .card {
        width: 100%;
        border-radius: 2.6667vw 0px 2.6667vw 2.6667vw;
        padding: 8vw 4vw 3.7333vw 4vw;
        background: #e2f0ff;
        position: relative;
        .title {
          font-weight: 500;
          font-size: 4vw;
          padding: 1.0667vw 3.4667vw;
          color: #fff;
          left: 0;
          top: 0;
          position: absolute;
          border-radius: 2.6667vw 0px;
          background: linear-gradient(90deg, #3f99f6 1.95%, #2957e4 100%);
          box-shadow: 0px 2px 4px 0px rgba(123, 187, 255, 0.68);
        }
        .content {
          font-weight: 500;
          color: #3e5391;
          font-size: 4vw;
        }
      }
      .active-time {
        margin-bottom: 8.4vw;
      }
      .content-description {
        .content {
          font-size: 3.7333vw;
          font-weight: 400;
        }
      }
    }
    .answer-page-content-body {
      padding: 6.6667vw 4vw;
      margin-top: 4vw;
      margin-bottom: 10.4vw;
      border-radius: 3.2vw 3.2vw 9.3333vw 3.2vw;
      background: #fff;
      box-shadow: 0px 4px 46px 0px rgba(33, 78, 219, 0.64);
      padding-bottom: 5vw;
      // 最后一个topic-item
      .topic-item:last-child {
        margin-bottom: 0;
      }
      .topic-item {
        margin-bottom: 8vw;
        .topic-header {
          display: flex;
          align-items: baseline;
          margin-bottom: 3.4667vw;
          .is-required {
            color: #fc3232;
            font-size: 3.2vw;
            font-weight: 500;
            margin-right: 0.2667vw;
          }
          .item-header-title {
            font-size: 4.2667vw;
            font-weight: 500;
            color: #333;
            display: flex;
            max-width: 64vw;
          }
          .item-header-type {
            font-size: 3.2vw;
            font-weight: 400;
            color: #999;
          }
        }
        .error-meesage {
          margin-top: 0.8vw;
          color: #fe4f37;
          font-size: 13px;
          font-weight: 400;
        }
      }
    }

    .result {
      text-align: center;
      border-radius: 9.3333vw 1.3333vw 9.3333vw 3.2vw;
      background: #fff;
      box-shadow: 0px 4px 46px 0px rgba(33, 78, 219, 0.64);
      width: 100%;
      // height: 114.6667vw;
      padding: 9.3333vw 5.3333vw 7.8667vw 5.3333vw;
      position: relative;
      .survey-name {
        font-size: 5.8333vw;
        font-weight: 500;
        margin-bottom: 7.7333vw;
        color: #333;
      }
      .status-icon {
        width: 22.6667vw;
        height: 22.6667vw;
        margin: auto;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .submit-title {
        color: #333;
        font-size: 4.2667vw;
        font-weight: 500;
        margin-top: 2vw;
        margin-bottom: 2vw;
      }
      .submit-subtitle {
        font-size: 3.4667vw;
        color: #999;
        margin-bottom: 23.4vw;
        font-weight: 400;
      }
      .save-btn {
        position: absolute;
        bottom: 0;
      }
    }
  }
  .answer-page-footer {
    padding: 3.4667vw 0;
    width: 100%;
    color: #fff;
    text-align: center;
    font-size: 4.2667vw;
    font-weight: 500;
    border-radius: 10.6667vw;
    margin-bottom: 0;
    background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
    box-shadow: 0px -10px 18px 0px #f3b300 inset;
    //   0px 4px 20px 0px rgba(254, 197, 36, 0.47);
  }
}
::v-deep .el-input__count {
  color: #999;
}
</style>
