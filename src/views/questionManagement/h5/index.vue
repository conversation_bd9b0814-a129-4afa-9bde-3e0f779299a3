<template>
  <div>
    <home
      v-if="pageType === 'home'"
      :data="surveyData"
      :key="keyNum"
      @onNext="handleNext"
    ></home>
    <template v-else>
      <answer
        @onBack="onBack"
        :data="surveyData"
        :surveyStudentId="$route.query.survey_student_id"
        :writeStatus="$route.query.write_status"
      ></answer>
    </template>
  </div>
</template>

<script>
import home from "./view/home.vue";
import answer from "./view/answer.vue";
import { getSchema, getWebSchema } from "@/api/questionnaire.js";
import moment from "moment";
export default {
  components: { home, answer },
  data() {
    return {
      keyNum: 0,
      surveyData: {},
      pageType: "1"
    };
  },
  created() {
    this.init();
  },
  methods: {
    async init(fn) {
      let params = {};
      console.log(this.$route.query, this.$route.query.from);
      if (this.$route.query.from === "mini") {
        params = {
          surveyPath: this.$route.query.survey_id,
          studentId: this.$route.query.student_id,
          visitor: this.$route.query.visitor,
          token: this.$route.query.token,
          surveyStudentId: Number(this.$route.query.survey_student_id),
          from: this.$route.query.from
        };
      } else {
        params = {
          no_read: 1,
          survey_id: this.$route.query.survey_id,
          student_id: this.$route.query.student_id,
          visitor: this.$route.query.visitor,
          survey_student_id: Number(this.$route.query.survey_student_id),
          from: this.$route.query.from
        };
      }
      const res =
        this.$route.query.from === "mini"
          ? await getSchema(params)
          : await getWebSchema(params);
      if (res.code === 0) {
        this.surveyData = res.data;
        this.keyNum++;
      } else {
        this.$message.error(res.message);
      }
      typeof fn === "function" && fn(res);
    },
    onBack(type) {
      this.pageType = type;
      this.init();
    },
    handleNext(type) {
      this.init((res) => {
        if (res.code === 1) {
          this.$message.error(res.message);
          return;
        }
        if (res.data.isFill) {
          this.pageType = type;
          return;
        }
        if (res.data.isEnabled === 2) {
          this.$message.error("问卷活动已关闭！");
          return;
        }
        if (
          new Date().getTime() <
          new Date(moment(res.data.start_time).format("YYYY-MM-DD")).getTime()
        ) {
          this.$message.error("问卷活动未开始！");
          return;
        }
        // if (
        //   new Date().getTime() >
        //   new Date(moment(res.data.end_time).format("YYYY-MM-DD")).getTime() +
        //     3600 * 1000 * 24 * 1
        // ) {
        //   this.$message.error("问卷活动已结束！");
        //   return;
        // }
        this.pageType = type;
      });
    }
  }
};
</script>

<style lang="scss">
@media (max-width: 768px) {
  #app {
    width: 100vw;
    height: 100vh;
    min-height: auto;
    min-width: auto;
  }
  .el-notification {
    width: 80% !important;
  }
  .message-info {
    min-width: 80% !important;
  }
  .message-error {
    min-width: 80% !important;
  }
  .message-success {
    min-width: 80% !important;
  }
}
</style>
