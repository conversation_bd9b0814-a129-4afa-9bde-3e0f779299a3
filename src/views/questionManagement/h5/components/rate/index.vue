<template>
  <div>
    <div class="shade" v-if="disabled"></div>
    <div class="rate-wrap">
      <div
        class="rate-item"
        v-for="item in 5"
        :key="item"
        @click="handleRateClick(item)"
      >
        <div class="rate-item-icon" v-if="item > currentRate">
          <img src="../../../imgs/mark_not_active.png" alt="" />
        </div>
        <div class="rate-item-active-icon" v-else>
          <img src="../../../imgs/mark_active.png" alt="" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      currentRate: 0
    };
  },
  model: {
    event: "change",
    prop: "num"
  },
  props: {
    num: {
      // eslint-disable-next-line vue/require-prop-type-constructor
      type: String | Number,
      default: 0
    },
    itemConfig: {
      type: Object,
      default: () => {}
    },
    disabled: {
      type: <PERSON>olean,
      default: false
    }
  },
  watch: {
    num: {
      immediate: true,
      handler(val) {
        const { isRequired, errorMessage, isShowErrorMessage } =
          this.itemConfig;
        if (typeof isShowErrorMessage === "boolean") {
          if (isRequired && !val) {
            this.itemConfig.isShowErrorMessage = true;
            this.itemConfig.errorMessage = errorMessage;
          } else {
            this.itemConfig.isShowErrorMessage = false;
          }
        }
        this.currentRate = val;
      }
    }
  },
  methods: {
    handleRateClick(i) {
      console.log(i);
      if (this.disabled) return;
      if (i === this.currentRate) {
        this.currentRate = 0;
      } else {
        this.currentRate = i;
      }
      this.$emit("change", this.currentRate);
    }
  }
};
</script>

<style scoped lang="scss">
.rate-wrap {
  display: flex;
  justify-content: center;
  .rate-item {
    width: 9.3333vw;
    // height: 9.3333vh;
    margin-right: 3.2vw;
    &:last-child {
      margin-right: 0;
    }
    img {
      width: 100%;
      height: 100%;
    }
  }
}
// .shade {
//   width: 210px;
//   height: 40px;
//   position: absolute;
//   z-index: 1;
// }
// ::v-deep .el-rate {
//   height: auto;
// }
// ::v-deep .el-icon-star-off {
//   background: url(../../../imgs/icons/star-off.png) no-repeat;
//   &:before {
//     content: "";
//   }
// }
// ::v-deep .el-icon-star-on {
//   background: url(../../../imgs/icons/star-on.png) no-repeat;
//   &:before {
//     content: "";
//   }
// }
// ::v-deep .el-rate__icon {
//   width: 7.4667vw;
//   height: 7.4667vw;
//   background-size: 100% 100%;
// }
// ::v-deep .el-rate__text {
//   color: #475669;
//   font-family: "PingFang SC";
//   font-size: 4.2667vw;
//   font-style: normal;
//   font-weight: 400;
// }
</style>
