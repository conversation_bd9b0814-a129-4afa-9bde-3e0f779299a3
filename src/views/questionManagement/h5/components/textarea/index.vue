<template>
  <div>
    <el-input
      type="textarea"
      :disabled="disabled"
      :rows="3"
      :placeholder="itemConfig.placeholder"
      v-model="textarea"
      @input="handleTextareaInput"
      show-word-limit
      :maxlength="itemConfig.textRange.max.value"
    >
    </el-input>
  </div>
</template>

<script>
export default {
  data() {
    return {
      textarea: ""
    };
  },
  props: {
    itemConfig: {
      type: Object,
      default: () => {}
    },
    content: String,
    disabled: {
      type: Boolean,
      default: false
    }
  },
  model: {
    prop: "content",
    event: "change"
  },
  watch: {
    content: {
      handler(val) {
        const { isRequired, errorMessage, isShowErrorMessage } =
          this.itemConfig;
        console.log(this.itemConfig);
        if (typeof isShowErrorMessage === "boolean") {
          if (isRequired && !val) {
            this.itemConfig.isShowErrorMessage = true;
            this.itemConfig.errorMessage = errorMessage;
          } else {
            this.itemConfig.isShowErrorMessage = false;
          }
        }
        this.textarea = val;
      },
      immediate: true
    }
  },
  methods: {
    handleTextareaInput() {
      this.$emit("change", this.textarea);
    }
  }
};
</script>

<style scoped lang="scss">
::v-deep .el-textarea {
  height: 100%;
}
::v-deep .el-textarea__inner {
  border-radius: 1.8667vw;
  border: 1px solid #e9e9e9;
  background: #f5f6f7;
  height: 100%;
  padding: 2vw 2vw;
}
::v-deep .el-input__count {
  background: transparent;
}
</style>
