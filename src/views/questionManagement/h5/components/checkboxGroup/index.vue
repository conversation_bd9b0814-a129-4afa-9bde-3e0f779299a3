<template>
  <div class="checkbox-group">
    <div
      :class="[
        'checkbox-item',
        checkboxGroup1.includes(item.hash) ? 'avtive' : ''
      ]"
      v-for="item in checkboxGroup"
      :key="item.hash"
      @click="handleCheckboxChange(item)"
    >
      <!-- <span class="serial">{{ item.serial }}</span> -->
      <span class="name" v-html="item.text"></span>
    </div>
  </div>
</template>

<script>
export default {
  model: {
    prop: "checked",
    event: "change"
  },
  props: {
    type: {
      type: [Number, String],
      default: "radio"
    },
    itemConfig: {
      type: Object,
      default: () => {}
    },
    checked: {
      type: [Array, String],
      default: () => []
    },
    checkboxGroup: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      checkboxGroup1: []
    };
  },
  watch: {
    checked: {
      immediate: true,
      handler(val) {
        console.log(val);
        const { isRequired, errorMessage, isShowErrorMessage } =
          this.itemConfig;
        if (typeof isShowErrorMessage === "boolean") {
          if (isRequired && !val.length) {
            this.itemConfig.isShowErrorMessage = true;
            this.itemConfig.errorMessage = errorMessage;
          } else {
            this.itemConfig.isShowErrorMessage = false;
          }
        }
        this.checkboxGroup1 = val || [];
      }
    }
  },
  methods: {
    handleCheckboxChange(item) {
      if (this.disabled) {
        return;
      }
      const { hash } = item;
      if (this.type === "radio") {
        if (this.checkboxGroup1.includes(hash)) {
          this.checkboxGroup1 = [];
        } else {
          this.checkboxGroup1 = [hash];
        }
        console.log(hash, this.checkboxGroup1);
      } else {
        if (this.checkboxGroup1.includes(hash)) {
          this.checkboxGroup1.splice(this.checkboxGroup1.indexOf(hash), 1);
        } else {
          this.checkboxGroup1.push(hash);
        }
      }
      this.$emit("change", this.checkboxGroup1);
    }
  }
};
</script>

<style scoped lang="scss">
.checkbox-group {
  width: 100%;
  .checkbox-item {
    border-radius: 1.8667vw;
    border: 1px solid #e9e9e9;
    background: #f5f6f7;
    padding: 4.0333vw 0 4.0333vw 2vw;
    color: #999;
    cursor: pointer;
    font-size: 4.2667vw;
    margin-bottom: 3.4667vw;
    &:last-child {
      margin-bottom: 0;
    }
    &.avtive {
      border-radius: 1.8667vw;
      border: 1px solid transparent;
      background: #3992ff;
      color: #fff;
    }
    span {
      display: inline-block;
      vertical-align: middle;
      // font-weight: 500;
    }
    .serial {
      margin-right: 2.6667vw;
    }
  }
}
</style>
