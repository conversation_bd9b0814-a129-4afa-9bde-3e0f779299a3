<template>
  <div class="teacher-edit-dialog">
    <el-dialog
      :visible="visible"
      width="600px"
      :before-close="handleClose"
      :title="isEdit ? '编辑教师' : '新增教师'"
      :append-to-body="true"
      v-if="visible"
    >
      <el-form
        ref="teacherForm"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        label-position="left"
        size="small"
        class="teacher-form"
      >
        <el-form-item label="教师姓名" prop="teacher_name">
          <course-staff
            :has_modal="true"
            :check_id.sync="formData.teacher_id"
            :check_name.sync="formData.teacher_name"
            :check_mobile.sync="formData.mobile"
            :is_disabled="isEdit"
            @really="handleReally"
            staff_placeholder="请选择教师"
            :usedTeacherList="usedTeacherList"
          ></course-staff>
        </el-form-item>

        <el-form-item label="手机号码" prop="mobile">
          <el-input
            v-model="formData.mobile"
            readonly
            placeholder="请输入手机号码"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="授课校区" prop="department_ids">
          <el-input
            v-model="formData.department_names"
            readonly
            placeholder="请选择校区"
            @click.native="school_tree_visible = true"
            style="width: 368px"
          >
            <span
              slot="suffix"
              class="endText"
              :style="{
                background: '#2d80ed'
              }"
            >
              选择
            </span>
          </el-input>
        </el-form-item>
        <el-form-item label="围棋段位" prop="dan_level">
          <el-select
            v-model="formData.dan_level"
            placeholder="请选择围棋段位"
            style="width: 100%"
          >
            <el-option
              v-for="item in weiqiLevelOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="教龄" prop="teaching_year">
          <div class="mo-input--number">
            <el-input-number
              v-model="formData.teaching_year"
              placeholder="请输入教龄"
              :controls="false"
              :min="0"
              :max="99"
              data-unit="年"
            >
            </el-input-number>
            <div class="define-append">年</div>
          </div>
        </el-form-item>

        <el-form-item label="荣誉" prop="honor">
          <el-input
            v-model="formData.honor"
            type="textarea"
            :rows="3"
            placeholder="请输入荣誉信息"
            maxlength="300"
            :autosize="{ minRows: 3 }"
            show-word-limit
          ></el-input>
        </el-form-item>

        <el-form-item label="教师介绍" prop="introduce">
          <el-input
            v-model="formData.introduce"
            type="textarea"
            :rows="3"
            placeholder="请输入教师介绍"
            maxlength="500"
            show-word-limit
          ></el-input>
        </el-form-item>

        <el-form-item label="照片" prop="photo_url">
          <div class="image-upload-container">
            <el-upload
              class="avatar-uploader"
              action="#"
              :show-file-list="false"
              :before-upload="beforeUpload"
              :http-request="uploadImage"
              :disabled="uploading"
            >
              <!-- 上传进度 -->
              <el-progress
                v-if="uploading"
                type="circle"
                :percentage="uploadPercentage"
                :width="100"
                class="upload-progress"
              >
                <template #default>
                  <div class="progress-content">
                    <span>{{ uploadPercentage }}%</span>
                  </div>
                </template>
              </el-progress>

              <!-- 显示图片或上传按钮 -->
              <div v-else-if="formData.photo_url" class="image-wrapper">
                <img :src="formData.photo_url" class="avatar-image" />
                <!-- 覆盖在图片上的按钮 -->
                <div class="image-actions">
                  <el-button
                    type="primary"
                    icon="el-icon-view"
                    circle
                    size="mini"
                    @click.stop="previewImage"
                  ></el-button>
                  <el-button
                    type="danger"
                    icon="el-icon-delete"
                    circle
                    size="mini"
                    @click.stop="deleteImage"
                  ></el-button>
                </div>
              </div>
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>

            <div class="upload-tip">
              建议上传520*680像素的图片，支持jpg、png、webp格式
            </div>
          </div>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button class="tg-button--plain" type="plain" @click="handleClose">
          取消
        </el-button>
        <el-button
          class="tg-button--primary"
          type="primary"
          v-throttle="handleSave"
          :loading="loading"
        >
          保存
        </el-button>
      </div>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog
      :visible.sync="imageVisible"
      title="照片预览"
      width="40%"
      center
      :append-to-body="true"
    >
      <div class="image-preview-container">
        <img :src="formData.photo_url" class="preview-image" alt="照片" />
      </div>
    </el-dialog>
    <school-tree
      :flag.sync="school_tree_visible"
      :id.sync="formData.department_ids"
      :name.sync="formData.department_names"
      :required="true"
      :has_modal="true"
      @confirm="confirmSchool"
      :use_store_options="true"
      type="chooseSchool"
    >
    </school-tree>
  </div>
</template>

<script>
import schoolTree from "@/components/schoolTree/schoolTree";
import nedawTeacherApi from "@/api/nedawTeacher";
export default {
  name: "TeacherEditDialog",
  components: {
    schoolTree
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    teacherData: {
      type: Object,
      default: () => ({})
    },
    weiqiLevelOptions: {
      type: Array,
      default: () => []
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    usedTeacherList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      school_tree_visible: false,
      loading: false,
      uploading: false,
      uploadPercentage: 0,
      imageVisible: false,
      formData: {
        id: null,
        teacher_id: "",
        teacher_name: "",
        mobile: "",
        dan_level: "",
        teaching_year: 0,
        honor: "",
        status: 1,
        photo_url: "",
        department_ids: [],
        department_names: [],
        introduce: ""
      },
      formRules: {
        teacher_name: [
          { required: true, message: "请输入教师姓名", trigger: "blur" }
        ],
        mobile: [
          { required: true, message: "请输入手机号码", trigger: "change" }
        ],
        dan_level: [
          { required: true, message: "请选择围棋段位", trigger: "change" }
        ],
        teaching_year: [
          { required: true, message: "请输入教龄", trigger: "blur" },
          { type: "number", message: "教龄必须是数字", trigger: "blur" }
        ],
        department_ids: [
          {
            required: true,
            message: "请选择授课校区",
            trigger: "change",
            validator: (rule, value, callback) => {
              if (!value || (Array.isArray(value) && value.length === 0)) {
                callback(new Error("请选择授课校区"));
              } else {
                callback();
              }
            }
          }
        ],
        introduce: [
          { required: true, message: "请输入教师介绍", trigger: "blur" }
        ],
        honor: [{ required: true, message: "请输入荣誉", trigger: "blur" }],
        photo_url: [
          { required: true, message: "请上传教师图片", trigger: "change" }
        ]
      }
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.initFormData();
      } else {
        this.resetForm();
      }
    },
    teacherData: {
      handler() {
        if (this.visible) {
          this.initFormData();
        }
      },
      deep: true
    }
  },
  methods: {
    // 初始化表单数据
    initFormData() {
      if (this.teacherData && Object.keys(this.teacherData).length > 0) {
        // 使用深拷贝避免数据引用问题
        this.formData = {
          id: this.teacherData.id || null,
          teacher_id: this.teacherData.teacher_id || "",
          teacher_name: this.teacherData.teacher_name || "",
          mobile: this.teacherData.mobile || "",
          dan_level: this.teacherData.dan_level || "",
          teaching_year: this.teacherData.teaching_year || "",
          honor: this.teacherData.honor || "",
          status:
            this.teacherData.status !== undefined ? this.teacherData.status : 1,
          photo_url: this.teacherData.photo_url || "",
          department_ids: Array.isArray(this.teacherData.department_ids)
            ? [...this.teacherData.department_ids]
            : [],
          department_names: Array.isArray(this.teacherData.department_names)
            ? [...this.teacherData.department_names]
            : [],
          introduce: this.teacherData.introduce || ""
        };
      } else {
        this.formData = {
          id: null,
          teacher_id: "",
          teacher_name: "",
          mobile: "",
          dan_level: "",
          teaching_year: "",
          honor: "",
          status: 1,
          photo_url: "",
          department_ids: [],
          department_names: [],
          introduce: ""
        };
      }

      // 清除之前的验证状态
      this.$nextTick(() => {
        if (this.$refs.teacherForm) {
          this.$refs.teacherForm.clearValidate();
        }
      });
    },

    // 重置表单
    resetForm() {
      // 重置表单数据
      this.formData = {
        id: null,
        teacher_id: "",
        teacher_name: "",
        mobile: "",
        dan_level: "",
        teaching_year: "",
        honor: "",
        status: 1,
        photo_url: "",
        department_ids: [],
        department_names: [],
        introduce: ""
      };

      // 清除验证状态
      this.$nextTick(() => {
        if (this.$refs.teacherForm) {
          this.$refs.teacherForm.resetFields();
          this.$refs.teacherForm.clearValidate();
        }
      });

      this.imageVisible = false;
      this.school_tree_visible = false;
    },

    handleClose() {
      this.$emit("close");
    },

    // 保存
    handleSave() {
      console.log("表单数据:", this.formData);

      // 手动验证必填字段
      // const requiredFields = [
      //   { field: "teacher_name", message: "请输入教师姓名" },
      //   { field: "mobile", message: "请输入手机号码" },
      //   { field: "dan_level", message: "请选择围棋段位" },
      //   { field: "teaching_year", message: "请输入教龄" },
      //   { field: "department_ids", message: "请选择授课校区" },
      //   { field: "introduce", message: "请输入教师介绍" },
      //   { field: "honor", message: "请输入荣誉" },
      //   { field: "photo_url", message: "请上传教师图片" }
      // ];

      // for (const item of requiredFields) {
      //   const value = this.formData[item.field];
      //   if (!value || (Array.isArray(value) && value.length === 0)) {
      //     this.$message.error(item.message);
      //     return;
      //   }
      // }

      this.$refs.teacherForm.validate((valid, invalidFields) => {
        console.log("表单验证结果:", valid);
        console.log("验证失败字段:", invalidFields);

        if (valid) {
          this.loading = true;
          this.$emit("success", { ...this.formData });
        } else {
          // 显示具体的验证错误信息
          // if (invalidFields) {
          //   const firstErrorField = Object.keys(invalidFields)[0];
          //   const firstError = invalidFields[firstErrorField][0];
          //   this.$message.error(firstError.message);
          // } else {
          this.$message.error("请完善必填信息");
          // }
        }
      });
    },

    // 预览图片
    previewImage() {
      this.imageVisible = true;
    },

    // 删除图片
    deleteImage() {
      this.$confirm("确定要删除照片吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.formData.photo_url = "";
          // 清除 el-upload 组件的内部文件列表
          this.$refs.teacherForm.clearValidate("photo_url");
          // 重置上传状态
          this.uploading = false;
          this.uploadPercentage = 0;
          // 使用 nextTick 确保DOM更新完成
          this.$nextTick(() => {
            this.$forceUpdate();
          });
          //   this.$message.success("删除成功");
        })
        .catch(() => {
          //   this.$message.info("已取消删除");
        });
    },

    // 上传前验证
    beforeUpload(file) {
      const supportedFormats = ["jpeg", "jpg", "png", "webp"];
      const suffix = file.name.split(".").pop().toLowerCase();
      const isSupported = supportedFormats.includes(suffix);

      if (!isSupported) {
        this.$message.error("只支持上传jpg、png、webp格式的图片");
        return false;
      }

      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 2MB!");
        return false;
      }

      return true;
    },

    // 上传图片
    uploadImage(upload) {
      this.uploading = true;
      this.uploadPercentage = 0;

      const file = upload.file;

      // 先合成图片，然后上传
      this.compositeImages(file)
        .then((compositeFile) => {
          const suffix = compositeFile.name.split(".").pop();
          const fileName = `teacher_avatar_${Date.now()}.${suffix}`;
          const renamedFile = new File([compositeFile], fileName, {
            type: compositeFile.type
          });

          // 创建进度回调函数
          const progressCallback = (percentage) => {
            this.uploadPercentage = Math.round(percentage);
          };

          // 使用项目中的OSS上传方法
          return this.Oss.uploadFile(renamedFile, progressCallback);
        })
        .then((res) => {
          if (res.code === 0) {
            this.formData.photo_url = res.url;
            console.log(this.formData.photo_url);
            this.$message.success("上传成功");
          } else {
            this.$message.error("上传失败：" + (res.message || "未知错误"));
          }
        })
        .catch((error) => {
          console.error("上传失败:", error);
          this.$message.error("上传失败");
        })
        .finally(() => {
          this.uploading = false;
          this.uploadPercentage = 0;
        });
    },

    // 合成图片
    compositeImages(userFile) {
      return new Promise((resolve, reject) => {
        const canvas = document.createElement("canvas");
        const ctx = canvas.getContext("2d");

        // 获取设备像素比，用于高清显示
        const devicePixelRatio = window.devicePixelRatio || 1;

        // 设置画布尺寸（根据需求调整）
        const canvasWidth = 520;
        const canvasHeight = 680;

        // 设置高分辨率画布
        canvas.width = canvasWidth * devicePixelRatio;
        canvas.height = canvasHeight * devicePixelRatio;
        canvas.style.width = canvasWidth + "px";
        canvas.style.height = canvasHeight + "px";

        // 缩放上下文以匹配设备像素比
        ctx.scale(devicePixelRatio, devicePixelRatio);

        // 启用图像平滑处理，提高图片质量
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = "high";

        // 获取原始文件的格式信息
        const originalType = userFile.type;

        // 根据原始格式设置输出格式，提高输出质量
        let outputType = originalType;
        const outputQuality = 0.95; // 提高输出质量到95%

        // 确保输出格式是浏览器支持的
        if (!["image/jpeg", "image/png", "image/webp"].includes(originalType)) {
          outputType = "image/png"; // 改为PNG格式以保持更好的质量
        }

        // 加载用户上传的图片
        const userImg = new Image();
        userImg.crossOrigin = "anonymous";

        userImg.onload = () => {
          // 绘制用户图片作为背景（可以调整位置和大小）
          ctx.drawImage(userImg, 0, 0, canvasWidth, canvasHeight);

          // 加载要合成的图片
          const overlayImg = new Image();
          overlayImg.crossOrigin = "anonymous";

          overlayImg.onload = () => {
            // 在用户图片上绘制覆盖图片（放在左上角）
            const overlayWidth = 220;
            const overlayHeight = 45;
            const overlayX = 20; // 距离左边20px
            const overlayY = 10; // 距离顶部20px

            // 使用更精确的绘制方法，确保水印清晰
            ctx.drawImage(
              overlayImg,
              overlayX,
              overlayY,
              overlayWidth,
              overlayHeight
            );

            // 将canvas转换为Blob，保持高质量
            canvas.toBlob(
              (blob) => {
                if (blob) {
                  // 创建File对象，保持原始文件名和格式
                  const compositeFile = new File(
                    [blob],
                    `composite_${userFile.name}`,
                    {
                      type: outputType,
                      lastModified: Date.now()
                    }
                  );
                  resolve(compositeFile);
                } else {
                  reject(new Error("图片合成失败"));
                }
              },
              outputType,
              outputQuality
            );
          };

          overlayImg.onerror = () => {
            reject(new Error("覆盖图片加载失败"));
          };

          // 设置要合成的图片路径
          overlayImg.src =
            "https://tg-prod.oss-cn-beijing.aliyuncs.com/09094307-142d-429c-8794-7c1e2b993c46.png";
        };

        userImg.onerror = () => {
          reject(new Error("用户图片加载失败"));
        };

        // 将用户文件转换为URL
        const reader = new FileReader();
        reader.onload = (e) => {
          userImg.src = e.target.result;
        };
        reader.onerror = () => {
          reject(new Error("文件读取失败"));
        };
        reader.readAsDataURL(userFile);
      });
    },
    confirmSchool(data) {
      console.log("选择校区数据:", data);
      // 确保数据正确赋值
      // if (data) {
      //   this.formData.department_ids = data.id || [];
      //   this.formData.department_names = data.name || [];

      //   // 手动触发验证
      //   this.$nextTick(() => {
      //     if (this.$refs.teacherForm) {
      //       this.$refs.teacherForm.validateField("department_ids");
      //     }
      //   });
      // }
      this.school_tree_visible = false;
    },
    handleReally(check_id, check_name) {
      console.log(check_id, check_name);
      nedawTeacherApi.getEmployeeInfo({ employee_id: check_id }).then((res) => {
        this.formData.department_ids = [res.data.department_id] || [];
        this.formData.department_names = [res.data.department_name] || [];
      });
    }
  },
  mounted() {
    this.Oss && this.Oss.getAliyun && this.Oss.getAliyun();
  }
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  overflow: auto;
}
.teacher-form {
  //   padding: 20px 0;
}

.image-upload-container {
  position: relative;
  display: inline-block;
}

.avatar-uploader {
  position: relative;

  ::v-deep .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 100px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  ::v-deep .el-upload:hover {
    border-color: #409eff;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
  }

  .avatar-image {
    width: 100px;
    height: 100px;
    object-fit: cover;
    display: block;
  }
}

.image-wrapper {
  position: relative;
  width: 100%;
  height: 100%;

  &:hover .image-actions {
    opacity: 1;
  }
}

.image-actions {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s;
  gap: 10px;
  pointer-events: none;

  .el-button {
    pointer-events: auto;
  }
}

.upload-progress {
  display: flex;
  justify-content: center;
  align-items: center;
}

.progress-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  span {
    font-size: 12px;
    font-weight: bold;
  }
}

.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
  text-align: center;
  //   width: 100px;
}

.image-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;

  .preview-image {
    max-width: 100%;
    max-height: 400px;
    object-fit: contain;
  }
}

::v-deep .el-progress__text {
  display: none;
}

::v-deep .el-progress-circle {
  width: 100% !important;
  height: 100% !important;
}
.endText {
  width: 72px;
  height: 32px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  user-select: none;
  border-bottom-right-radius: 4px;
  border-top-right-radius: 4px;
  color: #fff;
}
.mo-input--number {
  //   border: 1px solid #dcdfe6;
  //   width: 100%;
  //   display: flex;
  //   border-radius: 4px;
  //   .el-input-number--mini {
  //     flex: 1;
  //   }
  ::v-deep .el-input__inner {
    border-right: none;
  }
}

.define-append {
  width: 40px;
  display: inline-block;
  background: #f5f7fa;
  padding: 0px 3px;
  border-left: none;
  height: 32px;
  line-height: 32px;
  color: #909399;
  font-size: 12px;
  text-align: center;
  border: 1px solid #dcdfe6;
  border-left: none;
}
</style>
