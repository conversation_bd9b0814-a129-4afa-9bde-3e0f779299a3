<template>
  <div class="teacher-team-content">
    <tg-search
      :searchTitle.sync="search_title"
      :form.sync="searchParams"
      :showNum="6"
      @reset="reset"
      @search="searchVal"
    ></tg-search>
    <el-row
      class="tg-box--margin tg-shadow--margin tg-row--height"
      v-if="visible"
    >
      <el-button
        type="plain"
        class="tg-button--plain"
        @click="handleAddTeacher"
        v-has="{ m: 'nedawTeacher', o: 'create' }"
        >新增</el-button
      >
      <!-- 批量启用 -->
      <el-button
        type="plain"
        class="tg-button--plain"
        @click="handleBatchEnable"
        :disabled="selectedTeachers.length === 0"
        v-has="{ m: 'nedawTeacher', o: 'updateStatus' }"
        >批量启用
        {{
          selectedTeachers.length > 0 ? `(${selectedTeachers.length})` : ""
        }}</el-button
      >
      <!-- 批量停用 -->
      <el-button
        type="plain"
        class="tg-button--plain"
        @click="handleBatchDisable"
        :disabled="selectedTeachers.length === 0"
        v-has="{ m: 'nedawTeacher', o: 'updateStatus' }"
        >批量停用
        {{
          selectedTeachers.length > 0 ? `(${selectedTeachers.length})` : ""
        }}</el-button
      >
    </el-row>
    <div class="tg-table__box">
      <div class="tg-box--border"></div>
      <el-table
        ref="table"
        :data="teacherList"
        tooltip-effect="dark"
        class="tg-table"
        :cell-style="{ borderRightColor: '#e0e6ed75' }"
        :header-cell-style="{ borderRightColor: '#e0e6ed75' }"
        border
        @selection-change="handleSelectionChange"
      >
        <!-- 多选框列 -->
        <el-table-column
          type="selection"
          width="55"
          align="center"
          v-if="visible"
        ></el-table-column>

        <el-table-column
          v-for="item in table_title"
          :key="item.prop"
          :prop="item.prop"
          :label="item.label"
          :width="item.width"
          :show-overflow-tooltip="item.showOverflowTooltip"
        >
          <template slot-scope="scope">
            <span v-if="item.prop === 'status'">
              {{ scope.row.status === 1 ? "已启用" : "已停用" }}
            </span>
            <span v-else-if="item.prop === 'photo_url'">
              <el-image
                :src="scope.row.photo_url"
                fit="cover"
                style="width: 50px; height: 50px"
                :preview-src-list="[scope.row.photo_url]"
              />
            </span>
            <span v-else-if="item.prop === 'department_names'">
              <span
                v-for="(department, index) in scope.row.department_names"
                :key="department"
              >
                {{ department }}
                <span v-if="index !== scope.row.department_names.length - 1"
                  >、</span
                >
              </span>
            </span>
            <span v-else-if="item.prop === 'is_leave'">
              <el-tag v-if="scope.row.is_leave" type="danger" size="small"
                >离职</el-tag
              >
              <el-tag v-else type="success" size="small">在职</el-tag>
            </span>
            <span v-else>{{ scope.row[item.prop] }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="260" fixed="right" v-if="visible">
          <template slot-scope="scope">
            <el-button
              type="text"
              @click="handleToggleStatus(scope.row)"
              v-has="{ m: 'nedawTeacher', o: 'updateStatus' }"
            >
              {{ scope.row.status === 1 ? "停用" : "启用" }}
            </el-button>
            <el-button
              type="text"
              @click="handleEdit(scope.row)"
              v-if="scope.row.status !== 1"
              v-has="{ m: 'nedawTeacher', o: 'update' }"
              >编辑</el-button
            >
            <el-button
              type="text"
              @click="handleDelete(scope.row)"
              v-if="scope.row.status !== 1"
              v-has="{ m: 'nedawTeacher', o: 'delete' }"
              >删除</el-button
            >
          </template>
        </el-table-column>
        <template slot="empty">
          <div style="margin-top: 15%">
            <TgLoading v-if="loading"></TgLoading>
            <div class="empty-container" v-else>暂无数据～</div>
          </div>
        </template>
      </el-table>

      <div class="tg-pagination">
        <span class="el-pagination__total">共 {{ total }} 条</span>
        <el-pagination
          background
          layout="sizes,prev,pager,next,jumper"
          :total="total"
          :page-size="searchParams.page_size"
          :current-page="searchParams.page"
          @current-change="currentChange"
          @size-change="sizeChange"
          :page-sizes="[10, 20, 50, 100]"
        ></el-pagination>
      </div>
    </div>

    <!-- 教师编辑弹窗 -->
    <teacher-edit-dialog
      :visible="editDialogVisible"
      :teacher-data="currentTeacher"
      @close="handleCloseDialog"
      @success="handleEditSuccess"
      :weiqiLevelOptions="weiqiLevelOptions"
      :isEdit="isEdit"
      ref="teacherEditDialog"
      :usedTeacherList="usedTeacherList"
    ></teacher-edit-dialog>
  </div>
</template>

<script>
import TeacherEditDialog from "./components/teacherEditDialog.vue";
import nedawTeacherApi from "@/api/nedawTeacher";
export default {
  name: "NedawTeacherManagement",
  components: {
    TeacherEditDialog
  },
  props: {
    department_id: {
      type: String,
      default: ""
    },
    visible: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      loading: false,
      teacherList: [],
      total: 0,
      editDialogVisible: false,
      currentTeacher: {},
      selectedTeachers: [], // 选中的教师列表
      searchParams: {
        teacher_name: "",
        mobile: "",
        dan_level: "",
        teaching_year: "",
        status: "",
        page: 1,
        page_size: 10,
        is_leave: 0
      },
      search_title: [
        {
          props: "teacher_name",
          label: "教师姓名",
          show: true,
          width: 160,
          type: "input"
        },
        {
          props: "mobile",
          label: "手机号码",
          show: true,
          width: 160,
          type: "input"
        },
        {
          props: "dan_level",
          label: "围棋段位",
          show: true,
          width: 160,
          type: "select",
          selectOptions: []
        },
        {
          props: "teaching_year",
          label: "教龄",
          show: true,
          width: 160,
          type: "input"
        },
        {
          props: "status",
          label: "状态",
          show: true,
          width: 160,
          type: "select",
          selectOptions: [
            { name: "不限", id: "" },
            { name: "启用", id: 1 },
            { name: "停用", id: 2 }
          ]
        },
        {
          props: "is_leave",
          label: "是否离职",
          show: true,
          width: 160,
          type: "select",
          selectOptions: [
            { name: "不限", id: 0 },
            { name: "在职", id: 1 },
            { name: "离职", id: 2 }
          ]
        }
      ],
      table_title: [
        {
          prop: "teacher_name",
          label: "教师姓名",
          showOverflowTooltip: false
        },
        {
          prop: "mobile",
          label: "手机号码",
          showOverflowTooltip: false
        },
        {
          prop: "department_names",
          label: "授课校区",
          showOverflowTooltip: false
        },
        {
          prop: "dan_level_name",
          label: "围棋段位",
          showOverflowTooltip: false
        },
        {
          prop: "teaching_year",
          label: "教龄",
          showOverflowTooltip: false
        },
        {
          prop: "honor",
          label: "荣誉",
          width: 200,
          showOverflowTooltip: true
        },
        {
          prop: "introduce",
          label: "教师介绍",
          width: 200,
          showOverflowTooltip: true
        },
        {
          prop: "photo_url",
          label: "图片",
          showOverflowTooltip: false
        },
        {
          prop: "status",
          label: "状态",
          showOverflowTooltip: false
        },
        {
          prop: "is_leave",
          label: "是否离职",
          showOverflowTooltip: false
        }
      ],
      weiqiLevelOptions: [],
      isEdit: false,
      usedTeacherList: []
    };
  },
  computed: {
    schoolIds() {
      return this.$store.getters.doneGetSchoolId;
    }
  },
  watch: {
    schoolIds() {
      this.getTeacherList();
    }
  },
  methods: {
    // 获取教师列表
    getTeacherList() {
      this.loading = true;

      // 实际使用时替换为真实的API调用
      nedawTeacherApi
        .nedawTeacherList({
          ...this.searchParams,
          department_ids: this.department_id
            ? [this.department_id]
            : this.schoolIds
        })
        .then(({ data, message, code }) => {
          if (code === 0) {
            this.teacherList = data.results;
            this.total = data.count;
            this.loading = false;
          } else {
            this.$message.error(message);
            this.loading = false;
          }
        });
    },

    // 重置搜索
    reset() {
      this.searchParams = {
        teacher_name: "",
        mobile: "",
        dan_level: "",
        teaching_year: "",
        status: "",
        page: 1,
        page_size: 10
      };
      this.getTeacherList();
    },

    // 搜索
    searchVal() {
      this.searchParams.page = 1;
      this.getTeacherList();
    },

    // 分页改变
    currentChange(page) {
      this.searchParams.page = page;
      this.getTeacherList();
    },

    // 每页条数改变
    sizeChange(size) {
      this.searchParams.page_size = size;
      this.searchParams.page = 1;
      this.getTeacherList();
    },

    // 编辑教师
    async handleEdit(row) {
      //   this.currentTeacher = { ...row };
      const { data, message, code } = await nedawTeacherApi.nedawTeacherDetail({
        id: row.id
      });
      if (code === 0) {
        this.currentTeacher = data;
        this.isEdit = true;
        this.editDialogVisible = true;
      } else {
        this.$message.error(message);
      }
    },

    // 启用/停用状态切换
    handleToggleStatus(row) {
      const action = row.status === 1 ? "停用" : "启用";
      this.$confirm(`确定要${action}该教师吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          // 模拟API调用
          nedawTeacherApi
            .nedawTeacherEnable({
              ids: [row.id],
              status: row.status === 1 ? 2 : 1
            })
            .then(({ data, message, code }) => {
              if (code === 0) {
                this.$message.success(`${action}成功`);
                this.getTeacherList();
              } else {
                this.$message.error(message);
              }
            });
        })
        .catch(() => {
          //   this.$message.info("已取消");
        });
    },

    // 关闭编辑弹窗
    handleCloseDialog() {
      this.editDialogVisible = false;
      this.currentTeacher = {};
    },

    // 编辑成功回调
    async handleEditSuccess(data) {
      console.log(data);
      data.department_ids = Array.isArray(data.department_ids)
        ? data.department_ids
        : data.department_ids.split(",");
      data.teaching_year = Number(data.teaching_year || 0);
      let res;
      // 更新列表数据
      if (this.isEdit) {
        res = await nedawTeacherApi.nedawTeacherUpdate(data);
      } else {
        res = await nedawTeacherApi.nedawTeacherCreate(data);
      }
      if (res.code === 0) {
        this.$message.success("操作成功");
        this.$refs.teacherEditDialog.loading = false;
        this.$refs.teacherEditDialog.resetForm();
        this.handleCloseDialog();
        this.getTeacherList();
      } else {
        this.$refs.teacherEditDialog.loading = false;
        console.log(res);
        this.$message.error(res.message);
      }
      this.$refs.teacherEditDialog.loading = false;
    },

    // 选择变化
    handleSelectionChange(selection) {
      this.selectedTeachers = selection;
    },

    // 批量启用
    async handleBatchEnable() {
      if (this.selectedTeachers.length === 0) {
        this.$message.warning("请先选择要启用的教师");
        return;
      }

      this.$confirm(
        `确定要批量启用 ${this.selectedTeachers.length} 位教师吗？`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(async () => {
          // 模拟API调用
          const { message, code } = await nedawTeacherApi.nedawTeacherEnable({
            ids: this.selectedTeachers.map((teacher) => teacher.id),
            status: 1
          });
          if (code === 0) {
            this.$message.success("批量启用成功");
            this.getTeacherList();
          } else {
            this.$message.error(message);
          }
          // 清空选择
          this.$refs.table.clearSelection();
          this.selectedTeachers = [];
        })
        .catch(() => {
          //   this.$message.info("已取消操作");
        });
    },

    // 批量停用
    handleBatchDisable() {
      if (this.selectedTeachers.length === 0) {
        this.$message.warning("请先选择要停用的教师");
        return;
      }

      this.$confirm(
        `确定要批量停用 ${this.selectedTeachers.length} 位教师吗？`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(async () => {
          // 模拟API调用
          const { message, code } = await nedawTeacherApi.nedawTeacherEnable({
            ids: this.selectedTeachers.map((teacher) => teacher.id),
            status: 2
          });
          if (code === 0) {
            this.$message.success("批量停用成功");
            this.getTeacherList();
          } else {
            this.$message.error(message);
          }
          // 清空选择
          this.$refs.table.clearSelection();
          this.selectedTeachers = [];
        })
        .catch(() => {
          //   this.$message.info("已取消操作");
        });
    },

    // 新增教师
    handleAddTeacher() {
      this.currentTeacher = {
        teacher_name: "",
        mobile: "",
        dan_level: "",
        teaching_year: "",
        status: 1
      };
      this.isEdit = false;
      this.editDialogVisible = true;
    },
    // 获取段位
    async getWeiqiLevel() {
      const { data, message, code } =
        await nedawTeacherApi.nedawTeacherWeiqiLevel();
      if (code === 0) {
        this.weiqiLevelOptions = data;
        this.search_title.find(
          (item) => item.props === "dan_level"
        ).selectOptions = JSON.parse(JSON.stringify(this.weiqiLevelOptions));
        this.search_title
          .find((item) => item.props === "dan_level")
          .selectOptions.unshift({ name: "不限", id: "" });
      } else {
        this.$message.error(message);
      }
    },
    handleDelete(row) {
      this.$confirm("确定要删除该教师吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(async () => {
        const { message, code } = await nedawTeacherApi.nedawTeacherDelete({
          id: row.id
        });
        if (code === 0) {
          this.$message.success("删除成功");
          this.getTeacherList();
        } else {
          this.$message.error(message);
        }
      });
    },
    async getUsedTeacher() {
      const { data, message, code } =
        await nedawTeacherApi.nedawTeacherListBySchool();
      if (code === 0) {
        this.usedTeacherList = data;
      } else {
        this.$message.error(message);
      }
    }
  },
  mounted() {
    this.getTeacherList();
    this.getWeiqiLevel();
    this.getUsedTeacher();
  }
};
</script>

<style lang="scss" scoped>
.teacher-team-content {
  padding-top: 20px;
  .tg-table__box {
    margin-top: 16px;
  }
}
</style>
