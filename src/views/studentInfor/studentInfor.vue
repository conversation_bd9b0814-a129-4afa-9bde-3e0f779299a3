<!--学员客户-->
<template>
  <div class="student-infor container">
    <!--  -->
    <tg-search
      :searchTitle.sync="search_title"
      :form.sync="search"
      @reset="reset"
      @search="searchStu"
      :showNum="3"
      @cascaderSelect="getStudentStatus"
      class="tg-box--margin"
    ></tg-search>
    <el-row class="tg-box--margin tg-shadow--margin">
      <!-- <el-button
        type="plain"
        class="tg-button--plain"
        v-has="{ m: 'student_infor', o: 'batch_update' }"
        >批量退学</el-button
      > -->
      <!-- <el-button
        type="plain"
        class="tg-button--plain"
        v-has="{ m: 'student_infor', o: 'batch_update' }"
        >批量休学</el-button
      > -->
      <el-button
        type="plain"
        class="tg-button--plain"
        @click="batchEditChange"
        v-has="{ m: 'student_infor', o: 'batch_update' }"
        >批量修改</el-button
      >
      <el-button
        type="plain"
        class="tg-button--plain"
        @click="batchDel"
        v-has="{ m: 'student_infor', o: 'batch_delete' }"
        >批量删除</el-button
      >
      <el-button
        type="plain"
        class="tg-button--plain"
        @click="batchCategoryEditChange"
        v-has="{ m: 'student_infor', o: 'batch_update_studentCategory' }"
        >批量修改学员类别</el-button
      >
      <!-- <el-button
        type="plain"
        class="tg-button--plain"
        @click="exportData"
        :loading="exportLoading"
        v-has="{ m: 'student_infor', o: 'export' }"
      >
        {{ exportLoading ? "导出中" : "导出" }}</el-button
      > -->
      <el-button
        type="plain"
        class="tg-button--plain"
        @click="exportDataNew"
        :loading="exportLoading"
        v-has="{ m: 'student_infor', o: 'export' }"
      >
        {{ exportLoading ? "导出中" : "导出" }}</el-button
      >
      <el-button
        type="plain"
        class="tg-button--plain"
        @click="downloadTemplate"
        v-has="{ m: 'student_infor', o: 'downloadTemplate' }"
        >下载模版</el-button
      >
      <el-button
        type="plain"
        v-has="{ m: 'student_infor', o: 'importTemplate' }"
        class="tg-button--plain"
        @click="importTemplate"
        >导入</el-button
      >
      <el-button
        type="primary"
        class="tg-button--plain"
        v-has="{ m: 'yiKeOpenCard', o: 'openCardStudent' }"
        @click="openCard"
        :disabled="!canOpenCard"
        :class="{ 'tg-button--disabled': !canOpenCard }"
        >开卡</el-button
      >
      <select-field
        :allFields.sync="table_title"
        :btnType="'button'"
      ></select-field>
    </el-row>
    <div class="tg-table__box">
      <div class="tg-box--border"></div>
      <el-table
        ref="table"
        :data="list"
        tooltip-effect="dark"
        class="tg-table"
        @selection-change="handleSelectionChange"
        :row-key="getRowKeys"
        v-loading="false"
        :cell-style="{ borderRightColor: '#e0e6ed75' }"
        :header-cell-style="{ borderRightColor: '#e0e6ed75' }"
        border
        @sort-change="sortChange"
      >
        <el-table-column
          type="selection"
          width="50"
          :reserve-selection="true"
        ></el-table-column>
        <!-- <el-table-column min-width="60" label="序号" prop="index" fixed>
        </el-table-column>-->
        <template v-for="(item, index) in table_title">
          <el-table-column
            v-if="item.show"
            :key="index"
            :prop="item.props"
            :label="item.label"
            :min-width="item.width"
            :fixed="item.fixed ? true : false"
            :sortable="item.sort"
            :show-overflow-tooltip="item.tooltip"
          >
            <template slot-scope="scope">
              <div v-if="item.type === 'name'" class="copy_name">
                <el-button
                  class="tg-text--blue tg-table__name--ellipsis"
                  type="text"
                  :class="{ 'tg-text--black': !can_see }"
                  @click="toDetail(scope.row)"
                  >{{ scope.row.student_base.student_name }}</el-button
                >
                <div v-copy="scope.row.student_base.student_name"></div>
              </div>
              <div v-else-if="item.props === 'ylb_status'">
                <ylb-tag :ylb_status="scope.row.ylb_status"></ylb-tag>
              </div>
              <!-- 出生日期单独的，因为出生日期不用准确到时分秒 -->
              <template v-else-if="item.type === 'date'">
                {{
                  scope.row[scope.column.property]
                    ? scope.row[scope.column.property].indexOf("0001-01-01") >
                      -1
                      ? ""
                      : getTime(scope.row[scope.column.property])
                    : ""
                }}
              </template>
              <template v-else-if="item.type === 'dateNotTime'">
                {{
                  scope.row[scope.column.property] &&
                  scope.row[scope.column.property] !== "0001-01-01T00:00:00Z"
                    ? moment(scope.row[scope.column.property]).format(
                        "YYYY-MM-DD"
                      )
                    : ""
                }}
              </template>
              <template v-else-if="item.props === 'intention_level'">
                <div class="el-rate">
                  <span class="el-rate__item" v-for="(item, i) in 5" :key="i">
                    <i
                      class="el-rate__icon"
                      :class="
                        Number(scope.row.intention_level) - 1 >= i
                          ? 'el-icon-star-on'
                          : 'el-icon-star-off'
                      "
                    ></i>
                  </span>
                </div>
              </template>
              <template v-else-if="item.props === 'student_base.app_used'">
                <span
                  :style="{
                    color:
                      scope.row.student_base.app_used == 'used' ? '#2d80ed' : ''
                  }"
                  >{{
                    scope.row.student_base.app_used == "not_used"
                      ? "未使用"
                      : scope.row.student_base.app_used == "used"
                      ? "已使用"
                      : ""
                  }}</span
                >
              </template>
              <template
                v-else-if="item.props === 'student_base.add_wechat_status'"
              >
                <el-select
                  v-model="scope.row.student_base.add_wechat_status"
                  class="additionalInput"
                  :disabled="!updateBindState"
                  popper-class="tg-select-dropdown"
                  @change="updateInfo(scope.row)"
                >
                  <el-option
                    v-for="(item, index) in isAddStatus"
                    :value="item.value"
                    :label="item.label"
                    :key="index"
                  ></el-option>
                </el-select>
              </template>
              <template v-else-if="item.type === 'multilevel_date'">
                {{
                  scope.row[item.props.split(".")[0]] == null
                    ? ""
                    : getDate(
                        scope.row[item.props.split(".")[0]][
                          item.props.split(".")[1]
                        ]
                      )
                }}
              </template>
              <template v-else-if="item.type === 'date_select'">
                {{
                  scope.row[scope.column.property]
                    | getDateSelect(scope.column.property)
                }}
              </template>
              <template v-else-if="item.type === 'price'">
                {{
                  parseFloat(scope.row[scope.column.property] || 0).toFixed(2)
                }}
              </template>
              <template v-else-if="item.props === 'wallet_balance'">
                {{
                  parseFloat(
                    scope.row.left_cash + scope.row.left_prepay
                  ).toFixed(2)
                }}
                <el-tooltip
                  popper-class="tg-tooltip"
                  placement="top"
                  effect="light"
                >
                  <span slot="content">
                    预存订金:
                    {{ Number(scope.row.left_prepay).toFixed(2) }}
                  </span>
                  <img
                    src="../../assets/图片/icon_question.png"
                    alt=""
                    style="
                      width: 12px;
                      margin-left: 5px;
                      vertical-align: middle;
                    "
                  />
                </el-tooltip>
              </template>
              <template v-else-if="item.type === 'select'">
                {{
                  scope.row[scope.column.property] == null
                    ? ""
                    : scope.row[scope.column.property].toString()
                }}
              </template>
              <template v-else-if="item.type === 'boolean'">{{
                scope.row[scope.column.property] ? "是" : "否"
              }}</template>
              <template
                v-else-if="item.props === 'student_base.education_name'"
              >
                {{ scope.row.student_base.education_name?.toString() }}
              </template>
              <template
                v-else-if="item.props === 'student_base.student_mobile'"
              >
                <mobileHyposensitization
                  :mobileTemInfo="{
                    row: scope.row,
                    isShowWeihu: true,
                    isShowCallLog: true,
                    has_eye_limit: scope.row?.has_eye_limit,
                    mobile: scope.row.student_base.student_mobile
                  }"
                  @createVoip="createVoip"
                  @openCallLog="openCallLog"
                ></mobileHyposensitization>
              </template>
              <template v-else-if="item.props === 'nie_dao_level'">
                <span
                  :style="{
                    color: scope.row['nie_dao_is_old'] === 2 ? '#c0c4cc' : ''
                  }"
                  >{{ scope.row[item.props] }}</span
                >
              </template>
              <template v-else-if="item.props === 'tutor_name'">
                {{ scope.row?.tutor_name?.join("、") }}
              </template>
              <template v-else-if="item.props === 'school_manager_name'">
                <span
                  :style="{
                    color:
                      scope.row.school_manager_is_modify === 1
                        ? '#F56C6C'
                        : scope.row.school_manager_is_old === 2
                        ? '#c0c4cc'
                        : ''
                  }"
                >
                  {{ scope.row?.school_manager_name?.join("、") }}
                </span>
              </template>
              <template v-else-if="item.props === 'header_teacher_name'">
                {{ scope.row.header_teacher_name?.toString() }}
              </template>
              <template v-else-if="item.type === 'tag'">
                <div
                  style="display: flex; align-items: center; position: relative"
                >
                  <el-popover
                    placement="top-start"
                    width="200"
                    trigger="hover"
                    :disabled="
                      scope.row.label_list && scope.row.label_list.length < 3
                    "
                  >
                    <Tag
                      v-for="(item, index) in scope.row.label_list"
                      :key="index"
                      :msg="item.name"
                      :color="item.color"
                      style="margin-bottom: 10px"
                    ></Tag>
                    <div
                      style="
                        max-width: 200px;
                        overflow: hidden;
                        white-space: nowrap;
                      "
                      slot="reference"
                    >
                      <Tag
                        v-for="(item, index) in scope.row.label_list"
                        :key="index"
                        :msg="item.name"
                        :color="item.color"
                      ></Tag>
                    </div>
                  </el-popover>
                  <el-button
                    type="text"
                    class="tg-text--blue"
                    v-if="updateTag"
                    @click="labelDialogShow(scope.row)"
                  >
                    <img src="../../assets/edit.png" class="edit_icon" />
                  </el-button>
                  <el-button type="text" class="tg-text--blue" v-else>
                    <img
                      src="../../assets/图片/icon_edit_ac.png"
                      class="edit_icon"
                    />
                  </el-button>
                </div>
              </template>
              <template v-else-if="item.props === 'memo2'">
                <span v-if="scope.row.student_type === 'drop_school'">
                  {{ scope.row.memo }}
                </span>
              </template>
              <template
                v-else-if="
                  item.props === 'yi_ke_account' && scope.row.yi_ke_account
                "
              >
                <div class="copy_name">
                  {{ scope.row.yi_ke_account }}
                  <div v-copy="scope.row.yi_ke_account"></div>
                </div>
              </template>
              <div
                v-else-if="
                  item.props === 'yi_ke_password' && scope.row.yi_ke_password
                "
                :style="{
                  display: 'flex',
                  'justify-content': !$_has({ m: 'all_phone', o: 'has_limit' })
                    ? 'left'
                    : 'space-between',
                  'align-items': 'center'
                }"
              >
                <mobileHyposensitization
                  :mobileTemInfo="{
                    row: scope.row,
                    has_eye_limit: scope.row?.has_eye_limit,
                    mobile: scope.row.yi_ke_password
                  }"
                ></mobileHyposensitization>
                <div v-copy="scope.row.yi_ke_password"></div>
              </div>
              <template v-else-if="item.type === 'selectInput'">
                <!-- <el-select v-model="scope.row.student_category_id" multiple>
                  <el-option
                    v-for="(item,index) in category_list"
                    :key="index"
                    :value="item.id"
                    :label="item.name"
                  ></el-option>
                </el-select>-->
                <span
                  :style="{
                    color: scope.row.category_is_old === 2 ? '#c0c4cc' : ''
                  }"
                >
                  {{
                    scope.row.student_category_name == null
                      ? ""
                      : scope.row.student_category_name.toString()
                  }}
                </span>
              </template>
              <template
                v-else-if="item.props === 'student_base.student_gender'"
              >
                {{ scope.row.student_base.gender }}
              </template>
              <template
                v-else-if="item.props === 'student_base.responsible_name'"
              >
                {{
                  scope.row.student_base.responsible_name == null
                    ? ""
                    : scope.row.student_base.responsible_name.toString()
                }}
              </template>
              <template v-else-if="item.props === 'unbind_wechat'">
                <el-button
                  :disabled="scope.row.open_id === ''"
                  type="text"
                  v-has="{ m: 'student_infor', o: 'stuBindList' }"
                  @click="openWechatManagement(scope.row)"
                  >管理</el-button
                >
              </template>
              <template v-else-if="item.props === 'devices'">
                <div v-if="scope.row.devices && scope.row.devices.length > 0">
                  {{ scope.row.devices.join(",") }}
                </div>
                <div v-else></div>
              </template>
              <template v-else-if="item.props === 'expressInformation'">
                <el-button
                  type="text"
                  @click="openExpressInformationDialog(scope.row)"
                  >查看</el-button
                >
              </template>
              <span v-else-if="item.props.indexOf('.') > -1">
                {{
                  scope.row[item.props.split(".")[0]] == null
                    ? ""
                    : scope.row[item.props.split(".")[0]][
                        item.props.split(".")[1]
                      ]
                }}
              </span>
              <span v-else-if="item.props === 'tw_period'">
                {{
                  scope.row.tw_periods ? scope.row.tw_periods.toString() : ""
                }}
              </span>
              <span v-else-if="item.type === 'area'">
                <template v-if="scope.row.student_base.province">
                  {{ scope.row.student_base.province }} -
                  {{ scope.row.student_base.city }}
                </template>
              </span>
              <span
                class="content-tab tg_ellipsis"
                @click="openInsertContactRecord(scope.row)"
                v-else-if="item.props === 'content-tab'"
              >
                {{ scope.row.last_communication || "暂无沟通记录" }}
              </span>

              <span v-else>{{ scope.row[scope.column.property] }}</span>
            </template>
          </el-table-column>
        </template>
        <el-table-column label="操作" fixed="right">
          <template slot-scope="scope">
            <el-dropdown>
              <span
                class="el-dropdown-link tg-table--operate"
                style="cursor: pointer"
              ></span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  v-has="{ m: 'classroom', o: 'shift' }"
                  v-if="scope.row.student_type == 'in_school'"
                  @click.native="
                    openDialog(
                      scope.row.student_id,
                      1,
                      scope.row.student_base.student_name
                    )
                  "
                >
                  <span>调班</span>
                </el-dropdown-item>
                <!-- <el-dropdown-item><span>一键转班</span>
                </el-dropdown-item>-->
                <!-- <el-dropdown-item>
                  <span>转校</span>
                </el-dropdown-item>
                <el-dropdown-item>
                  <span>停课/复课</span>
                </el-dropdown-item> -->
                <el-dropdown-item
                  v-has="{ m: 'student_infor', o: 'info' }"
                  @click.native="
                    openDialog(
                      scope.row.student_id,
                      5,
                      scope.row.student_base.student_name
                    )
                  "
                >
                  <span>学员进出班级记录</span>
                </el-dropdown-item>
                <el-dropdown-item
                  v-if="scope.row.student_type == 'in_school'"
                  v-has="{ m: 'student_infor', o: 'info' }"
                  @click.native="
                    openDialog(
                      scope.row.student_id,
                      6,
                      scope.row.student_base.student_name
                    )
                  "
                >
                  <span>请假</span>
                </el-dropdown-item>
                <el-dropdown-item
                  v-has="{ m: 'student_infor', o: 'outSchool' }"
                  @click.native="
                    openDialog(
                      scope.row.student_id,
                      7,
                      scope.row.student_base.student_name,
                      scope.row.department_id
                    )
                  "
                  v-if="
                    !scope.row.is_suspension &&
                    (scope.row.revoke_type == '' ||
                      scope.row.revoke_type == 'drop_school') &&
                    scope.row.student_type !== 'drop_school'
                  "
                >
                  <span>休学</span>
                </el-dropdown-item>
                <el-dropdown-item
                  v-has="{ m: 'student_infor', o: 'revokeStatus' }"
                  v-if="
                    scope.row.is_revocable &&
                    (scope.row.revoke_type == '' ||
                      scope.row.revoke_type == 'out_school')
                  "
                  @click.native="openUndoDialog(scope.row.student_id, undoRest)"
                >
                  <span>撤销休学</span>
                </el-dropdown-item>
                <el-dropdown-item
                  v-has="{ m: 'student_infor', o: 'backToSchool' }"
                  @click.native="
                    openDialog(
                      scope.row.student_id,
                      11,
                      scope.row.student_base.student_name
                    )
                  "
                  v-if="scope.row.is_suspension"
                >
                  <span>复学</span>
                </el-dropdown-item>
                <el-dropdown-item
                  v-has="{ m: 'student_infor', o: 'dropSchool' }"
                  @click.native="
                    balance = scope.row.balance;
                    openDialog(
                      scope.row.student_id,
                      8,
                      scope.row.student_base.student_name,
                      scope.row.department_id
                    );
                  "
                  v-if="
                    !scope.row.is_leave &&
                    (scope.row.revoke_type == '' ||
                      scope.row.revoke_type == 'out_school') &&
                    scope.row.student_type !== 'drop_school'
                  "
                >
                  <span>退学</span>
                </el-dropdown-item>
                <el-dropdown-item
                  v-has="{ m: 'student_infor', o: 'revokeStatus' }"
                  v-if="
                    scope.row.is_revocable &&
                    (scope.row.revoke_type == '' ||
                      scope.row.revoke_type == 'drop_school')
                  "
                  @click.native="
                    openUndoDialog(scope.row.student_id, undoLeave)
                  "
                >
                  <span>撤销退学</span>
                </el-dropdown-item>
                <el-dropdown-item
                  v-has="{ m: 'student_infor', o: 'recoverToSchool' }"
                  @click.native="
                    openDialog(
                      scope.row.student_id,
                      9,
                      scope.row.student_base.student_name
                    )
                  "
                  v-if="scope.row.is_leave"
                >
                  <span>恢复入学</span>
                </el-dropdown-item>
                <el-dropdown-item
                  v-has="{ m: 'student_infor', o: 'update' }"
                  @click.native="
                    openDialog(
                      scope.row.student_id,
                      10,
                      scope.row.student_base.student_name
                    )
                  "
                >
                  <span>修改</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
        <template slot="empty">
          <div style="margin-top: 15%">
            <loading v-if="loading"></loading>
            <div class="empty-container" v-else>暂无数据～</div>
          </div>
        </template>
      </el-table>
      <!-- 分页 -->
      <div class="tg-pagination">
        <span class="el-pagination__total">共 {{ total }} 条</span>
        <el-pagination
          background
          layout="sizes,prev,pager,next,jumper"
          :total="total"
          :page-size="page_size"
          :current-page.sync="page"
          @current-change="currentChange"
          @size-change="sizeChange"
          :page-sizes="[10, 20, 50, 100]"
        ></el-pagination>
      </div>
    </div>
    <batchEdit
      v-if="batch_show"
      :student_ids="student_ids"
      @cancel="batch_show = false"
      @really="batchEditReally"
    ></batchEdit>
    <BatchCategoryEdit
      v-if="batch_category_show"
      @cancel="batch_category_show = false"
      :category="category_list.filter((i) => i.id !== 'unspecified')"
      :student_ids="student_ids"
      @init="
        clearSelection();
        searchVal();
      "
    ></BatchCategoryEdit>
    <change-class
      v-if="change_class_visible"
      :id="row_id"
      :student_name="student_name"
      @close="change_class_visible = false"
    ></change-class>
    <in-out-class
      v-if="in_out_class_visible"
      :id="row_id"
      :student_name="student_name"
      @close="in_out_class_visible = false"
    ></in-out-class>
    <apply-for-leave
      v-if="apply_leave_visible"
      :id="row_id"
      :student_name="student_name"
      @close="apply_leave_visible = false"
    ></apply-for-leave>
    <temporary-leave
      v-if="temporary_leave_visible"
      :id="row_id"
      :student_name="student_name"
      :department_id="department_id"
      @uploadStatus="uploadStatus"
      @close="temporary_leave_visible = false"
    ></temporary-leave>
    <leave-school
      v-if="leave_school_visible"
      :id="row_id"
      :balance="balance"
      :student_name="student_name"
      :department_id="department_id"
      @close="leave_school_visible = false"
      @uploadStatus="uploadStatus"
    ></leave-school>
    <resume-school
      v-if="resume_school_visible"
      :id="row_id"
      :student_name="student_name"
      @close="resume_school_visible = false"
      @uploadStatus="uploadStatus"
    ></resume-school>
    <regainSchool
      v-if="regain_visible"
      :id="row_id"
      :student_name="student_name"
      @close="regain_visible = false"
      @uploadStatus="uploadStatus"
    ></regainSchool>
    <edit v-if="edit_visible" :id="row_id" @close="editClose"></edit>
    <undo-dialog ref="undoDialog" @uploadStatus="uploadStatus"></undo-dialog>

    <LabelDialog
      :label_arr.sync="student_info_select.label_list"
      :label_flag_visible="label_flag_visible"
      v-if="label_flag_visible"
      @close="label_flag_visible = false"
      @confirm="lableConfirm"
    ></LabelDialog>
    <importStu v-if="importStu_visible" @close="closeWindow"></importStu>
    <callLog
      v-if="record_flag_visible"
      :phone="stuMobile"
      :stuName="stuName"
      :extension="extension_id"
      @close="record_flag_visible = false"
    ></callLog>
    <insertContactRecord
      v-if="insertContactRecord_visible"
      @close="insertContactRecord_visible = false"
      :customer_id="row_customer_id"
      :id="row_id"
    ></insertContactRecord>
    <expressInformationDialog
      v-if="isShowexprEssInformationDialog"
      :department_id="department_id"
      :dialogVisible="isShowexprEssInformationDialog"
      @close="handleEssInformationDialogClose"
      :student_id="row_id"
    ></expressInformationDialog>
    <openCardDialog
      v-if="openCardDialogVisible"
      student_type="student"
      :loading="openCardLoading"
      @close="openCardDialogClose"
      @confirm="openCardDialogConfirm"
    ></openCardDialog>
    <wechatBindManagement
      v-if="wechatBindManagement_visible"
      :visible.sync="wechatBindManagement_visible"
      :student_name="student_name"
      :row_id="row_id"
      @uploadStatus="uploadStatus"
      @close="wechatBindManagement_visible = false"
    ></wechatBindManagement>
  </div>
</template>
<script>
import wechatBindManagement from "./common/wechatBindManagement.vue";
import timeFormat from "@/public/timeFormat";
import tgSearch from "@/components/search/search.vue";
import SelectField from "@/components/selectField/selectField.vue";
import changeClass from "@/components/studentInfo/changeClass.vue";
import inOutClass from "@/components/studentInfo/inOutClass.vue";
import applyForLeave from "@/components/studentInfo/applyForLeave.vue";
import temporaryLeave from "@/components/studentInfo/temporaryLeave.vue";
import leaveSchool from "@/components/studentInfo/leaveSchool.vue";
import resumeSchool from "@/components/studentInfo/resumeSchool.vue";
import regainSchool from "@/components/studentInfo/regainSchool.vue";
import batchEdit from "@/components/studentInfo/batchEdit.vue";
import edit from "@/components/studentInfo/edit.vue";
import studentInforApi from "@/api/studentInfor";
import yikeCardApi from "@/api/yikeCard";
import classRoomApi from "@/api/classroomManagement";
import undoDialog from "@/components/studentInfo/undoDialog";
import loading from "../loading";
import BatchCategoryEdit from "@/components/studentInfo/batchCategoryEdit.vue";
import importStu from "./common/studentImport.vue";
import callLog from "@/components/weihu/callLog.vue";
import insertContactRecord from "./insertContactRecord.vue";
import openCardDialog from "@/views/marketStudent/openCardDialog.vue";
import { Base64 } from "js-base64";

import { export_excel_sync_new } from "@/public/asyncExport";
import seatApi from "@/api/seatAdministration";
import expressInformationDialog from "@/views/receiptManagement/components/expressInformationDialog";
export default {
  name: "StudentInfo",
  data() {
    return {
      openCardLoading: false,
      openCardDialogVisible: false,
      wechatBindManagement_visible: false,
      insertContactRecord_visible: false,
      record_flag_visible: false,
      stuMobile: "",
      stuName: "",
      extension_id: "",
      balance: "",
      exportLoading: false,
      list: [],
      total: 0,
      loading: true,
      search_title: [
        {
          props: "name",
          label: "学员信息",
          type: "input",
          show: true,
          placeholder: "请输入学员姓名/学号/手机号"
        },

        {
          props: "gender",
          label: "性别",
          type: "select",
          show: true,
          selectOptions: [
            { id: "", name: "不限" },
            { id: "male", name: "男" },
            { id: "female", name: "女" }
          ]
        },
        {
          props: "age",
          label: "年龄",
          type: "select",
          show: true,
          selectOptions: [
            { id: "", name: "不限" },
            { id: "3", name: "3岁" },
            { id: "4", name: "4岁" },
            { id: "5", name: "5岁" },
            { id: "6", name: "6岁" },
            { id: "7", name: "7岁" },
            { id: "8", name: "8岁" },
            { id: "9", name: "9岁" },
            { id: "10", name: "10岁" },
            { id: "11", name: "11岁" },
            { id: "12", name: "12岁" }
          ]
        },
        {
          props: "label_id",
          label: "用户标签",
          type: "label_flag",
          show: false,
          selectOptions: []
        },
        {
          props: "course_id",
          label: "报读课程",
          type: "choose_course",
          show: false
        },
        {
          props: "classroom_id",
          label: "在读班级",
          type: "choose_class",
          show: false
        },

        {
          props: "student_category_id",
          label: "学员类别",
          type: "select",
          show: false,
          selectOptions: []
        },
        {
          props: "student_category",
          label: "学员类型",
          type: "cascader_select",
          show: false,
          selectOptions: []
        },
        {
          props: "student_type",
          label: "学员状态",
          type: "select",
          selectOptions: [
            {
              id: "",
              name: "不限"
            }
          ],
          show: false
        },
        {
          props: "sign_up_time",
          label: "报名日期",
          type: "date",
          show: false,
          selectOptions: [],
          has_options: true
        },
        {
          props: "leave_num",
          label: "请假次数",
          type: "input",
          show: false,
          selectOptions: []
        },
        // {
        //   props: "course_hours",
        //   label: "剩余课时",
        //   type: "num_range",
        //   show: false,
        //   selectOptions: [],
        // },
        {
          props: "teacher_id",
          label: "任课老师",
          type: "course_staff",
          show: false,
          is_leave: true,
          placeholder: "请选择任课老师",
          selectOptions: []
        },
        {
          props: "education_ids",
          label: "教务",
          type: "course_staff",
          show: false,
          is_leave: true,
          placeholder: "请选择教务",
          selectOptions: []
        },
        {
          props: "header_teacher_id",
          label: "班主任",
          type: "mark_staff",
          show: false,
          is_leave: true,
          placeholder: "请选择班主任",
          selectOptions: []
        },
        {
          props: "advisor_id",
          label: "课程顾问",
          type: "course_staff",
          show: false,
          is_leave: true,
          placeholder: "请选择课程顾问",
          selectOptions: []
        },
        {
          props: "tutor_id",
          label: "辅导老师",
          type: "course_staff",
          show: false,
          is_leave: true,
          placeholder: "请选择辅导老师"
        },
        {
          props: "tutor_has",
          label: "有无辅导老师",
          type: "select",
          selectOptions: [
            {
              id: undefined,
              name: "不限"
            },
            {
              id: "have",
              name: "有"
            },
            {
              id: "none",
              name: "无"
            }
          ],
          show: false
        },

        {
          props: "student_level",
          label: "证书棋力",
          type: "select",
          show: false,
          selectOptions: []
        },
        {
          props: "tw_period",
          label: "报名期次",
          type: "input",
          show: false
        },
        {
          props: "nie_dao_level",
          label: "聂道棋力",
          placeholder: "请输入",
          type: "select",
          show: false,
          selectOptions: [
            { name: "不限", id: "" },
            { name: "N1", id: "N1" },
            { name: "N2", id: "N2" },
            { name: "N3", id: "N3" },
            { name: "N4", id: "N4" },
            { name: "N5", id: "N5" },
            { name: "N6-N8", id: "N6-N8" }
          ]
        },
        {
          props: ["left_course_hour", "hour_condition"],
          label: "剩余课时",
          placeholder: "请输入",
          type: "input-with-select",
          show: false,
          selectOptions: [
            { name: "等于", id: "eq" },
            { name: "小于", id: "lt" },
            { name: "大于", id: "gt" }
          ]
        },
        {
          props: ["wallet_balance", "wallet_condition"],
          label: "电子钱包",
          placeholder: "请输入",
          type: "input-with-select",
          show: false,
          selectOptions: [
            { name: "等于", id: "eq" },
            { name: "小于", id: "lt" },
            { name: "大于", id: "gt" }
          ]
        },
        {
          props: ["balance", "balance_condition"],
          label: "账户余额",
          placeholder: "请输入",
          type: "input-with-select",
          show: false,
          selectOptions: [
            { name: "等于", id: "eq" },
            { name: "小于", id: "lt" },
            { name: "大于", id: "gt" }
          ]
        },
        {
          props: "app_used",
          label: "APP使用情况",
          type: "select",
          show: false,
          selectOptions: [
            { name: "不限", id: undefined },
            { name: "已使用", id: "used" },
            { name: "未使用", id: "not_used" }
          ]
        },
        {
          props: "add_wechat_status",
          label: "微信状态",
          type: "select",
          show: false,
          selectOptions: [
            { name: "不限", id: "" },
            {
              id: 1,
              name: "已加微"
            },
            {
              id: 2,
              name: "未加微"
            }
          ]
        },
        {
          props: "wechat_bind_status",
          label: "小程序绑定状态",
          type: "select",
          show: false,
          selectOptions: [
            { name: "不限", id: "" },
            {
              id: 1,
              name: "已绑定"
            },
            {
              id: 2,
              name: "未绑定"
            }
          ]
        },
        {
          props: "suspension_time",
          label: "休学日期",
          type: "date",
          show: false,
          selectOptions: [],
          has_options: true
        },
        {
          props: "leave_school_time",
          label: "退学日期",
          type: "date",
          show: false,
          selectOptions: [],
          has_options: true
        },
        {
          props: "citys",
          label: "省份/市区",
          placeholder: "请选择省市",
          type: "area",
          show: false
        },
        {
          props: "ylb_status",
          label: "元萝卜用户",
          type: "select",
          show: true,
          selectOptions: [
            { id: "", name: "不限" },
            { id: 1, name: "未购买过" },
            { id: 2, name: "已购买且持有" },
            { id: 3, name: "已购买并退费" }
          ]
        }
      ],
      table_title: [
        {
          props: "student_base.student_number",
          label: "学号",
          show: true,
          width: 90,
          fixed: true,
          sort: "custom"
        },
        {
          props: "student_base.student_name",
          label: "学员姓名",
          show: true,
          width: 120,
          fixed: true,
          type: "name",
          sort: "custom"
        },
        {
          props: "ylb_status",
          label: "元萝卜用户",
          show: true,
          width: 150,
          sort: "custom"
        },
        {
          props: "student_base.student_gender",
          label: "性别",
          show: true,
          width: 150,
          sort: "custom"
        },
        {
          props: "student_base.student_birth_day",
          label: "出生日期",
          show: true,
          width: 200,
          type: "multilevel_date",
          sort: "custom"
        },
        {
          props: "student_base.age",
          label: "年龄",
          show: true,
          width: 200,
          sort: "custom"
        },
        {
          props: "student_base.student_mobile",
          label: "手机号",
          show: true,
          width: 200,
          sort: "custom"
        },
        {
          props: "student_category_name",
          label: "学员类别",
          show: true,
          width: 180,
          type: "selectInput",
          sort: "custom"
        },
        {
          props: "student_type_chn",
          label: "学员状态",
          show: true,
          width: 110,
          sort: "custom"
        },
        {
          props: "department_name",
          label: "所属校区",
          show: true,
          width: 200,
          sort: "custom"
        },
        {
          props: "department_name_init",
          label: "来源校区",
          show: true,
          width: 200,
          sort: "custom"
        },
        {
          props: "sign_up_time",
          label: "报名日期",
          type: "date",
          show: false,
          width: 200,
          sort: "custom"
        },
        {
          props: "tw_period",
          label: "报名期次",
          show: true,
          width: 150,
          sort: "custom"
        },
        {
          props: "present_time",
          label: "最近出勤时间",
          show: true,
          width: 200,
          type: "date",
          sort: "custom"
        },
        {
          props: "on_billing_time",
          label: "最近计费时间",
          show: true,
          width: 200,
          type: "date",
          sort: "custom"
        },
        {
          props: "classroom_name",
          label: "在读班级",
          show: true,
          width: 200,
          type: "select"
        },
        {
          props: "classroom_alias",
          label: "班级别名",
          show: true,
          width: 200,
          type: "select"
        },
        {
          props: "last_out_classroom",
          label: "最新出班日期",
          show: true,
          width: 200,
          type: "date"
        },
        {
          props: "in_time",
          label: "进入待入班池时间",
          show: true,
          width: 200,
          type: "date"
        },
        {
          props: "shift_time",
          label: "进入调班池时间",
          show: true,
          width: 200,
          type: "date"
        },
        {
          props: "come_time",
          label: "进入未续费池池时间",
          show: true,
          width: 200,
          type: "date"
        },
        {
          props: "out_time",
          label: "出未续费池池时间",
          show: true,
          width: 200,
          type: "date"
        },
        {
          props: "school_manager_name",
          label: "学管师",
          show: true,
          width: 200,
          sort: "custom"
        },
        {
          props: "teacher",
          label: "任课老师",
          show: true,
          width: 200,
          type: "select",
          sort: "custom"
        },
        {
          props: "student_base.education_name",
          label: "教务",
          show: true,
          width: 200,
          sort: "custom"
        },
        {
          props: "header_teacher_name",
          label: "班主任",
          show: true,
          width: 200,
          sort: "custom"
        },
        {
          props: "left_course_hour",
          label: "剩余课时",
          show: true,
          width: 150,
          sort: "custom"
        },
        {
          props: "left_tuition",
          label: "剩余学费",
          show: true,
          width: 150,
          type: "price",
          sort: "custom"
        },
        {
          props: "wallet_balance",
          label: "电子钱包",
          show: true,
          width: 150,
          type: "balance"
        },
        {
          props: "balance",
          label: "账户余额",
          show: true,
          width: 200,
          type: "price",
          sort: "custom"
        },
        {
          props: "suspension_time",
          label: "休学日期",
          type: "date",
          show: false,
          width: 200
        },
        {
          props: "suspension_memo",
          label: "休学原因",
          show: false,
          width: 200
        },
        {
          props: "plan_resume_school_time",
          label: "预计复学日期",
          type: "date",
          show: false,
          width: 200
        },
        {
          props: "resume_school_time",
          label: "实际复学日期",
          type: "date",
          show: false,
          width: 200
        },
        {
          props: "leave_school_time",
          label: "退学日期",
          type: "date",
          show: false,
          width: 200
        },
        {
          props: "leve_school_memo",
          label: "退学原因",
          show: false,
          width: 200
        },
        {
          props: "leve_school_sub_memo",
          label: "退学二级原因",
          show: false,
          width: 200
        },
        {
          props: "memo",
          label: "备注",
          show: false,
          width: 200
        },
        {
          props: "drop_memo",
          label: "退学备注",
          show: false,
          width: 200
        },
        {
          props: "return_to_school_time",
          label: "恢复入学日期",
          type: "date",
          show: false,
          width: 200
        },
        {
          props: "province",
          label: "省份/市区",
          type: "area",
          show: true,
          width: 200,
          sort: "custom"
        },
        {
          props: "student_base.channel_name",
          label: "一级渠道",
          show: true,
          width: 200,
          sort: "custom"
        },
        {
          props: "student_base.sub_channel_name",
          label: "二级渠道",
          show: true,
          width: 200,
          sort: "custom"
        },
        {
          props: "student_base.responsible_name",
          label: "市场专员",
          show: true,
          width: 200,
          sort: "custom"
        },
        {
          props: "student_base.advisor_name",
          label: "课程顾问",
          show: true,
          width: 200,
          sort: "custom"
        },
        {
          props: "tutor_name",
          label: "辅导老师",
          show: true,
          width: 200,
          sort: "custom"
        },
        {
          props: "student_base.school_student_number",
          label: "校管家学号",
          show: true,
          width: 200,
          sort: "custom"
        },
        {
          props: "student_base.student_card_id",
          label: "身份证号",
          show: true,
          width: 200,
          sort: "custom"
        },
        {
          props: "student_base.father_name",
          label: "父亲姓名",
          show: false,
          width: 200
        },
        {
          props: "student_base.father_mobile",
          label: "父亲电话",
          show: false,
          width: 200
        },
        {
          props: "student_base.father_job",
          label: "父亲职业",
          show: false,
          width: 200
        },
        {
          props: "student_base.mother_name",
          label: "母亲姓名",
          show: false,
          width: 200
        },
        {
          props: "student_base.mother_mobile",
          label: "母亲电话",
          show: false,
          width: 200
        },
        {
          props: "student_base.mother_job",
          label: "母亲职业",
          show: false,
          width: 200
        },
        {
          props: "home_address",
          label: "家庭地址",
          show: false,
          width: 200
        },
        {
          props: "level_name",
          label: "证书棋力",
          show: false,
          width: 200
        },
        {
          props: "nie_dao_level",
          label: "聂道棋力",
          show: false,
          width: 200
        },
        {
          props: "student_base.student_number",
          label: "用户标签",
          show: true,
          width: 230,
          type: "tag"
        },
        {
          props: "leave_numb",
          label: "请假次数",
          show: true,
          width: 200,
          sort: "custom"
        },
        {
          props: "is_stop",
          label: "停课状态",
          show: true,
          width: 200,
          type: "boolean",
          sort: "custom"
        },
        {
          props: "student_base.app_name",
          label: "APP账号",
          show: true,
          width: 130,
          sort: "custom"
        },
        {
          props: "yi_ke_account",
          label: "APP账号（天弈）",
          show: true,
          width: 130
        },
        {
          props: "yi_ke_password",
          label: "APP初始密码",
          show: true,
          width: 140
        },
        {
          props: "yi_ke_vip_valid_date",
          label: "过期时间(剩余天数)",
          show: true,
          width: 150
        },
        {
          props: "",
          label: "APP二维码",
          show: true,
          width: 150
        },
        {
          props: "student_base.app_bind_type",
          label: "APP绑定状态",
          show: true,
          width: 150,
          sort: "custom"
        },
        {
          props: "student_base.app_used",
          label: "APP使用情况",
          show: true,
          width: 150,
          sort: "custom"
        },
        {
          props: "intention_level",
          label: "学员意向级别",
          show: true,
          width: 150,
          sort: "custom"
        },
        {
          props: "content-tab",
          label: "沟通内容",
          show: true,
          width: 150,
          tooltip: true
        },
        {
          props: "student_base.add_wechat_status",
          label: "微信状态",
          show: true,
          width: 150,
          sort: "custom"
        },
        {
          props: "unbind_wechat",
          label: "小程序绑定状态",
          show: true,
          width: 150,
          sort: "custom"
        },
        {
          props: "devices",
          label: "设备使用情况",
          show: true,
          width: 150,
          sort: "custom"
        },
        {
          props: "expressInformation",
          label: "快递信息",
          show: true,
          width: 120
        }
      ],
      search: {
        name: "",
        gender: "",
        age: [],
        label_id: [],
        student_category: "is_student",
        course_id: "",
        course_id_name: "",
        classroom_id: "",
        class_room_id_name: "",
        student_category_id: [],
        student_type: "in_school",
        sign_up_time: [],
        leave_num: "",
        // course_hours: [],
        teacher_id: "",
        teacher_id_name: "",
        student_level: "",
        department_id: [],
        tw_period: "",
        add_wechat_status: "",
        suspension_time: [],
        leave_school_time: [],
        app_used: undefined,
        nie_dao_level: "",
        tutor_has: undefined,
        sort: "",
        citys: [],
        province: "",
        city: "",
        ylb_status: ""
      },
      page_size: 10,
      page: 1,
      checked_student: [],
      is_loading: false,
      row_id: "",
      row_customer_id: "",
      student_name: "",
      department_id: "",
      change_class_visible: false, // 调班
      in_out_class_visible: false, // 学员进出班级
      apply_leave_visible: false, // 请假
      temporary_leave_visible: false, // 休学
      leave_school_visible: false, // 退学
      resume_school_visible: false, // 恢复入学
      regain_visible: false, // 复学
      edit_visible: false, // 编辑
      student_category: [],
      can_see: false,
      undoLeave: "确认撤销退学吗？",
      undoRest: "确认撤销休学吗？",
      label_flag_visible: false,
      student_info_select: {},
      batch_show: false,
      student_ids: [],
      batch_category_show: false,
      category_list: [],
      importStu_visible: false,
      isAddStatus: [
        { label: "添加", value: 1 },
        { label: "未添加", value: 2 }
      ],
      stu_exp_timer: null,
      isShowexprEssInformationDialog: false
    };
  },
  beforeRouteEnter(to, from, next) {
    if (from.name === "studentInforDetails") {
      next((vm) => {
        // this.clearSelection();
        vm.searchVal(vm.page);
        vm.$refs.table.clearSelection();
      });
    } else {
      // 不是从详情进入的到该页面的路由，需要重置页面
      if (from.name) {
        next((vm) => {
          // vm.reset();
          vm.$refs.table.clearSelection();
        });
      } else {
        next((vm) => {
          vm.$refs.table.clearSelection();
        });
      }
    }
  },
  watch: {
    table_title: {
      handler() {
        this.$nextTick(() => {
          // 在数据加载完，重新渲染表格
          this.$refs.table.doLayout();
        });
      },
      immediate: true,
      deep: true
    },
    school_id: {
      handler() {
        this.page = 1;
        // setTimeout(() => {
        this.getList({
          page: this.page,
          page_size: this.page_size,
          ...this.search,
          department_id: this.school_id
        });
        // }, 6000);
        this.clearSelection();
      },
      immediate: true
    },
    "search.citys": {
      handler(newVal) {
        this.search.province = newVal[0];
        this.search.city = newVal[1];
      },
      deep: true
    }
  },
  filters: {
    getDateSelect(arr, props) {
      if (arr == null) return "";
      const new_arr = [];
      arr.forEach((item) => {
        if (item[props] != null) {
          new_arr.push(this.moment(item[props]).format("YYYY-MM-DD HH:mm:ss"));
        }
      });
      return new_arr.toString();
    }
  },
  computed: {
    school_id() {
      return this.$store.getters.doneGetSchoolId;
    },
    updateTag() {
      return this.$_has({ m: "student_infor", o: "updateTag" });
    },
    updateBindState() {
      return this.$_has({ m: "student_infor", o: "updateBindState" });
    },
    canOpenCard() {
      if (this.checked_student.length === 1) {
        const { yi_ke_account } = this.checked_student[0];
        return !yi_ke_account;
      }
      return false;
    }
  },
  mounted() {
    this.getNwpLevelList({ is_enabled: true });
    this.getStudentType();
    this.getStudentStyleList();
    this.voip = null;
    // this.initCategoryList();
  },
  created() {
    if (
      !this.$_has({ m: "class", o: "list" }) &&
      !this.$_has({ m: "student_infor", o: "show_timetable" }) &&
      !this.$_has({ m: "communication", o: "list" })
    ) {
      this.can_see = false;
    } else {
      this.can_see = true;
    }
  },
  deactivated() {
    if (this.voip) {
      this.voip.webTcpSocket.WS.close();
      this.voip.webTcpSocket.closeSocketHeart();
      this.voip = null;
    }
  },
  methods: {
    openCard() {
      this.openCardDialogVisible = true;
    },
    openCardDialogClose() {
      this.openCardDialogVisible = false;
    },
    openCardDialogConfirm(data) {
      this.openCardLoading = true;
      const { student_id } = this.checked_student[0];
      yikeCardApi
        .sendStudentMembership({
          p_type: 1, // 1 正式学员
          card_type: +data.card_type,
          validity_type: +data.validity_type,
          student_id
        })
        .then((res) => {
          const { code, message } = res.data;
          if (code === 0) {
            this.$message.success("开卡成功");
            this.openCardDialogVisible = false;
            this.searchStu(this.page);
          } else {
            this.$message.error(message);
          }
        })
        .finally(() => {
          this.openCardLoading = false;
        });
    },

    openInsertContactRecord(row) {
      this.row_customer_id = row.student_base.customer_id;
      this.row_id = row.student_id;
      this.insertContactRecord_visible = true;
    },
    openWechatManagement(row) {
      this.row_id = row.student_id;
      this.student_name = row.student_base.student_name;
      this.wechatBindManagement_visible = true;
    },
    contentTabClick(row) {
      const id = row.student_id;
      const customer_id = row.student_base.customer_id;
      const department_id = row.department_id;
      if (this.can_see) {
        this.$router.push({
          name: `studentInforDetails`,
          query: { id, customer_id, department_id, tab: "4" }
        });
      }
    },
    callPhone(row) {
      // 拨号
      this.$set(row, "isCalled", true);
      const { student_mobile } = row.student_base;
      this.voip.CallPhone(student_mobile, (data) => {
        console.log(data);
        if (+data.code === 0) {
          this.$message.success("呼叫" + student_mobile + "成功！");
        } else {
          this.$message.error("拔号失败：" + data.message);
          this.$set(row, "isCalled", false);
        }
      });
    },
    createVoip(row) {
      const { employee_id } = JSON.parse(localStorage.getItem("user_info"));
      if (employee_id) {
        seatApi
          .getSeatInfo({
            employee_id
          })
          .then((res) => {
            const { data, code } = res.data;
            if (code === 0) {
              const { student_mobile } = row.student_base;
              if (student_mobile) {
                const serviceUrl = "https://voip.800ing.com/";
                if (this.voip) {
                  this.callPhone(row);
                } else {
                  // eslint-disable-next-line no-undef, new-cap
                  this.voip = new voipCall();
                  this.voip.init(serviceUrl);
                  const username = data.extension_id;
                  this.extension_id = data.extension_id;
                  const password = Base64.decode(data.password);
                  // const username = "101832800";
                  // const password = "41153943";

                  this.voip.userlogin(username, password);
                  // 登录返回，code =0登录成功，其它为失败
                  this.voip.CallBack_login = (code, message) => {
                    if (+code === 0) {
                      this.callPhone(row);
                    } else {
                      this.$message.error("拔号失败：" + message);
                      this.$set(row, "isCalled", false);
                    }
                  };
                }
                // 挂机回调
                this.voip.CallBack_HangUp = (kind, phone, obj) => {
                  this.$set(row, "isCalled", false);
                  // this.voip.webTcpSocket.WS.close();
                  this.$message.info("通话结束！");
                };
              }
            } else {
              this.$message.error("获取当前账号坐席信息失败！");
            }
          })
          .catch(() => {
            this.$message.error("获取当前账号坐席信息失败！");
          });
      } else {
        this.$message.error("获取员工信息失败，请重新登录！");
      }
    },
    openCallLog(row) {
      this.record_flag_visible = true;
      const { student_name, student_mobile } = row.student_base;
      this.stuName = student_name;
      this.stuMobile = student_mobile;
    },
    clearSelection() {
      this.$nextTick(() => {
        this.$refs.table.clearSelection();
      });
    },
    sortChange(val) {
      let { prop, order } = val;
      let _oreder = "";
      if (order === "ascending") {
        _oreder = "asc";
      } else if (order === "descending") {
        _oreder = "desc";
      }
      if (prop.startsWith("student_base.")) {
        prop = prop.replace("student_base.", "");
      }
      this.search.sort = `${prop} ${_oreder}`;
      this.searchStu();
    },
    searchStu(page) {
      this.clearSelection();
      this.searchVal(page || 1);
    },
    closeWindow() {
      this.importStu_visible = false;
      this.clearSelection();
      this.searchVal();
    },
    // 年月日
    getTime(time) {
      return timeFormat.GetTime(time);
    },
    getDate(time) {
      return timeFormat.GetDate(time);
    },
    updateInfo(row) {
      const data = {
        human_id: row.HumanId,
        add_wechat_status: row.student_base.add_wechat_status
      };
      studentInforApi.updateWechatStatus(data).then((res) => {
        if (+res.status === 200) {
          this.$message.success("数据更新成功!");
        }
      });
    },
    back() {
      this.visible = false;
    },
    reset() {
      this.search = {
        name: "",
        gender: "",
        age: [],
        student_category: "is_student",
        course_id: "",
        course_id_name: "",
        classroom_id: "",
        class_room_id_name: "",
        student_category_id: [],
        student_type: "in_school",
        sign_up_time: [],
        leave_num: "",
        course_hours: [],
        teacher_id: "",
        teacher_id_name: "",
        student_level: "",
        department_id: [],
        tw_period: "",
        nie_dao_level: "",
        add_wechat_status: "",
        sort: "",
        citys: [],
        province: "",
        city: ""
      };
      this.$refs.table.clearSort();
      this.clearSelection();
      this.search_title[8].selectOptions = [
        {
          id: "",
          name: "不限"
        }
      ];
      this.page = 1;
      this.page_size = 10;
      this.getStudentStyleList();
      const search = this.changeSearch();
      this.getList({
        page: this.page,
        page_size: this.page_size,
        department_id: this.school_id,
        ...search
      });
    },
    // initCategoryList() {
    //   categoryApi.getStudentCrtegoryList().then((res) => {
    //     if (res.data.code == 0) {
    //       this.category_list = res.data.data;
    //     }
    //   });
    // },
    openExpressInformationDialog(row) {
      this.department_id = this.school_id;
      this.row_id = row.student_id;
      this.isShowexprEssInformationDialog = true;
    },
    handleEssInformationDialogClose() {
      this.isShowexprEssInformationDialog = false;
    },
    editClose() {
      this.edit_visible = false;
      const search = this.changeSearch();
      this.getList({
        page: this.page,
        page_size: this.page_size,
        ...search,
        department_id: this.school_id
      });
    },
    changeSearch() {
      const search = JSON.parse(JSON.stringify(this.search));
      if (search.sign_up_time && search.sign_up_time.length > 0)
        search.sign_up_begin_time = search.sign_up_time[0];
      if (search.sign_up_time && search.sign_up_time.length > 0)
        search.sign_up_over_time = search.sign_up_time[1];
      if (search.suspension_time && search.suspension_time.length > 0) {
        search.suspension_start_time = search.suspension_time[0];
        search.suspension_end_time = search.suspension_time[1];
      }
      if (search.leave_school_time && search.leave_school_time.length > 0) {
        search.leave_school_start_time = search.leave_school_time[0];
        search.leave_school_end_time = search.leave_school_time[1];
      }
      search.department_id = this.school_id;
      search.course_id = search.course_id.split(",");
      search.classroom_id = search.classroom_id.split(",");
      search.teacher_id = search.teacher_id.split(",");
      search.student_type =
        search.student_type.length > 0
          ? search.student_type.toString().split(",")
          : "";
      search.student_category_id =
        search.student_category_id.length > 0
          ? search.student_category_id.toString().split(",")
          : [];
      search.student_category =
        search.student_category.length > 0
          ? search.student_category.toString().split(",")
          : "";
      delete search.sign_up_time;
      delete search.class_room_id_name;
      delete search.course_id_name;
      delete search.suspension_time;
      delete search.leave_school_time;
      return search;
    },
    searchVal(val) {
      this.page = val || 1;
      const search = this.changeSearch();
      this.getList({
        page: this.page,
        page_size: this.page_size,
        ...search,
        department_id: this.school_id
      });
    },
    handleSelectionChange(val) {
      this.checked_student = val;
      this.student_ids = this.checked_student.map((item) => item.student_id);
    },
    currentChange(val) {
      this.page = val;
      // this.loading = true;
      // this.list = [];
      const search = this.changeSearch();
      this.getList({
        page: this.page,
        page_size: this.page_size,
        ...search,
        department_id: this.school_id
      });
    },
    sizeChange(val) {
      this.page_size = val;
      const search = this.changeSearch();
      this.getList({
        page: 1,
        page_size: this.page_size,
        ...search,
        department_id: this.school_id
      });
    },
    getRowKeys(row) {
      // this.$refs.form.clearSelection();完成后需要手动清空
      return row.student_id;
    },
    handleClose(done) {
      this.$confirm("确认关闭？").then(() => {
        done();
      });
    },
    openDialog(id, type, name, department_id) {
      this.row_id = id;
      this.student_name = name;
      this.department_id = department_id;
      if (+type === 1) {
        this.change_class_visible = true;
      }
      if (+type === 5) {
        this.in_out_class_visible = true;
      }
      if (+type === 6) {
        this.apply_leave_visible = true;
      }
      if (+type === 7) {
        this.temporary_leave_visible = true;
      }
      if (+type === 8) {
        this.leave_school_visible = true;
      }
      if (+type === 9) {
        this.resume_school_visible = true;
      }
      if (+type === 10) {
        this.edit_visible = true;
      }
      if (+type === 11) {
        this.regain_visible = true;
      }
    },
    batchDel() {
      this.$confirm("是否确定删除已选学员信息？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        if (this.checked_student.length === 0) return;
        const ids = this.checked_student.map((item) => item.student_id);
        studentInforApi.delStudent({ student_ids: ids }).then(() => {
          this.clearSelection();
          this.searchVal();
        });
      });
    },
    getList(data) {
      this.list = [];
      this.loading = true;
      if (data.student_category_id) {
        data.student_category_id = [data.student_category_id];
      }
      studentInforApi
        .getStudentInforList(data)
        .then((res) => {
          this.loading = false;
          this.list = res.data.results == null ? [] : res.data.results;
          this.total = res.data.count;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // import { export_excel_sync_new } from "@/public/asyncExport";
    exportDataNew() {
      const query = this.changeSearch();
      const opt = {
        vm: this, // vue组件实例，
        api_url: "/api/report-center-service/admin/student/export", // 导出接口地址
        file_name: "学员信息管理", // 文件名
        success_msg: "导出学员信息成功！", // 导出成功的提示语
        error_msg: "导出学员信息失败！", // 导出失败的提示语,
        query // 列表的筛选值
      };
      export_excel_sync_new(opt);
    },
    // 下载模版
    downloadTemplate() {
      const downloadElement = document.createElement("a");
      downloadElement.href =
        "./studentInfo_excel.xlsx?v=" + window.__APP_VERSION__;
      downloadElement.download = `学员信息模版.xlsx`; // 下载后文件名
      document.body.appendChild(downloadElement);
      downloadElement.click(); // 点击下载
      document.body.removeChild(downloadElement);
    },
    // 导入模板
    importTemplate() {
      this.importStu_visible = true;
    },
    getNwpLevelList(data) {
      classRoomApi.GetNwpLevelList(data).then((res) => {
        this.search_title[17].selectOptions = res.data;
        this.search_title[17].selectOptions.unshift({ id: "", name: "不限" });
      });
    },
    toDetail(row) {
      const id = row.student_id;
      const customer_id = row.student_base.customer_id;
      const department_id = row.department_id;
      if (this.can_see) {
        this.$router.push({
          name: `studentInforDetails`,
          query: { id, customer_id, department_id }
        });
      }
    },
    getStudentStatus() {
      this.search.student_type = "";
      this.search_title[8].selectOptions = [
        {
          id: "",
          name: "不限"
        }
      ];
      const { student_category } = this.search;
      if (student_category) {
        // console.log(1);
        // this.student_category.forEach((item) => {
        //   console.log(this.search.student_category);
        //   this.search_title[8].selectOptions = [
        //     {
        //       name: "不限",
        //       id: ""
        //     },
        //     ...item.class_data
        //   ];
        // }); // 为student_status
        const obj = this.student_category.find(
          (item) => item.student_style === student_category
        );
        this.search_title[8].selectOptions =
          this.search_title[8].selectOptions.concat(obj?.class_data ?? []);
      } else {
        const allOptions = this.student_category
          .map((item) => item.class_data)
          .flat();
        console.log(allOptions);
        allOptions.unshift({ id: "", name: "不限" });
        this.search_title[8].selectOptions = allOptions;
      }
    },
    getStudentStyleList(data) {
      studentInforApi.getStudentStyleList(data).then((res) => {
        this.student_category = res.data;
        console.log(res, "res");
        this.search_title[7].selectOptions = res.data.map((item) => {
          return {
            name: item.name,
            id: item.student_style
          };
        });
        this.search_title[7].selectOptions.unshift({ name: "不限", id: "" });
        this.search.student_category = res.data
          ? res.data[0].student_style
          : "";
        const arr = res.data ? res.data[0].class_data : [];
        this.search_title[8].selectOptions =
          this.search_title[8].selectOptions.concat(arr);
        console.log(this.search_title[8]);
      }); // 为student_type
    },
    getStudentType(data) {
      studentInforApi.getStudentType({ status: 1 }).then((res) => {
        if (+res.status === 200 && +res.data.code === 0) {
          this.category_list = res.data.data;
          this.search_title[6].selectOptions = res.data.data;
        }
        const defaultOptions = [
          { id: "", name: "不限" },
          { id: "unspecified", name: "未分类" }
        ];
        this.search_title[6].selectOptions.unshift(...defaultOptions);
      }); // 为student_categroy
    },
    uploadStatus() {
      this.clearSelection();
      this.searchVal(this.page);
    },
    openUndoDialog(id, text) {
      const obj = {
        id,
        text
      };
      this.$refs.undoDialog.openDialog(obj);
    },
    labelDialogShow(row) {
      this.student_info_select = row;
      this.label_flag_visible = true;
    },
    lableConfirm(val) {
      const form = {
        label_ids: val || [],
        student_id: this.student_info_select.student_id,
        student_mobile: this.student_info_select.student_base.student_mobile,
        student_name: this.student_info_select.student_base.student_name
      };
      studentInforApi.saveStudentLabel(form).then(() => {
        this.clearSelection();
        this.searchVal(this.page);
      });
    },
    batchEditChange() {
      if (this.checked_student.length > 0) {
        this.batch_show = true;
      }
    },
    batchEditReally() {
      const search = this.changeSearch();
      this.getList({
        page: this.page,
        page_size: this.page_size,
        department_id: this.school_id,
        ...search
      });
      this.batch_show = false;
    },
    batchCategoryEditChange() {
      if (this.checked_student.length > 0) {
        this.batch_category_show = true;
      }
    }
  },
  components: {
    tgSearch,
    SelectField,
    changeClass,
    inOutClass,
    applyForLeave,
    temporaryLeave,
    leaveSchool,
    resumeSchool,
    edit,
    regainSchool,
    undoDialog,
    loading,
    batchEdit,
    BatchCategoryEdit,
    importStu,
    callLog,
    insertContactRecord,
    expressInformationDialog,
    openCardDialog,
    wechatBindManagement
  }
};
</script>
<style lang="less" scoped>
.student-infor {
  ::v-deep .el-table td:last-child {
    border-right-color: transparent !important;
  }
  ::v-deep .el-table__header th:last-child {
    border-right-color: transparent !important;
  }
  .uploadBtn {
    display: inline-block;
    margin-left: 10px;
  }
  ::v-deep .el-rate__icon.el-icon-star-off {
    background: url("../../assets/图片/icon_rate.png");
    background-size: 100%;
    background-repeat: no-repeat;
    width: 16px;
    height: 16px;
  }
  ::v-deep .el-icon-star-off:before {
    content: "1";
    visibility: hidden;
  }
  ::v-deep .el-rate__icon.el-icon-star-on {
    background: url("../../assets/图片/icon_rate_ac.png");
    background-size: 100%;
    background-repeat: no-repeat;
    width: 16px;
    height: 16px;
  }
  ::v-deep .el-icon-star-on:before {
    content: "1";
    visibility: hidden;
  }
  ::v-deep .tg-search {
    div.tg-search__box:first-child .el-input {
      width: 280px;
    }
  }
  ::v-deep .el-tooltip__popper,
  ::v-deep .el-tooltip__popper.is-dark {
    max-width: 60% !important;
    line-height: 24px;
    color: pink !important;
    background-color: #fff !important;
  }
  ::v-deep .el-table__body-wrapper {
    height: calc(100% - 46px);
    .empty-container {
      position: absolute;
      top: 50%;
      left: 50%;
    }
    .loading-container {
      position: absolute;
      top: 30%;
      left: 1%;
      background: transparent;
      .box {
        height: 100%;
      }
    }
  }
  .tg-box--margin {
    width: 100%;
  }
  .edit_icon {
    width: 12px;
    height: 12px;
  }
  ::v-deep .cell {
    .el-input {
      width: 130px;
    }
  }
  .content-tab {
    color: @base-color;
    cursor: pointer;
    display: block;
    height: 40px;
    line-height: 40px;
  }
}
.additionalInput {
  width: 100px;
  ::v-deep .el-input {
    width: 100%;
  }
}
</style>
