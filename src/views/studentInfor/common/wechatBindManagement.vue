<template>
  <el-dialog
    :visible.sync="visible"
    title="微信绑定管理"
    width="50%"
    :close-on-click-modal="false"
  >
    <div class="tg-table__box">
      <div class="tg-box--border"></div>
      <el-table
        ref="table"
        class="tg-table"
        tooltip-effect="dark"
        :data="list"
        :cell-style="{ borderRightColor: '#e0e6ed75' }"
        :header-cell-style="{ borderRightColor: '#e0e6ed75' }"
        border
      >
        <template v-for="(item, index) in table_title">
          <el-table-column
            v-if="item.show"
            :key="index"
            :prop="item.props"
            :width="item.width"
            :label="item.label"
          >
            <template slot-scope="scope">
              <div v-if="item.props === 'updated_at'">
                {{
                  moment(scope.row[scope.column.property]).format(
                    "YYYY-MM-DD HH:mm:ss"
                  )
                }}
              </div>
              <div v-else-if="item.props === 'mobile'">
                <mobileHyposensitization
                  :mobileTemInfo="{
                    row: scope.row,
                    has_eye_limit: scope.row?.has_eye_limit,
                    mobile: scope.row.mobile
                  }"
                ></mobileHyposensitization>
              </div>
              <span v-else>{{ scope.row[scope.column.property] }}</span>
            </template>
          </el-table-column>
        </template>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button type="text" @click="handleUnbind(scope.row)"
              >解除绑定</el-button
            >
            <el-button type="text" @click="modifyBindRelation(scope.row)"
              >修改绑定关系</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog
      width="30%"
      title="修改绑定关系"
      :visible.sync="modifyBindRelationVisible"
      append-to-body
    >
      <el-form :model="form" label-width="120px">
        <el-form-item label="绑定关系">
          <el-select v-model="form.role_id" placeholder="请选择绑定关系">
            <el-option
              v-for="item in roleList"
              :key="item.role_id"
              :label="item.role_name"
              :value="item.role_id"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button
          class="tg-button--plain"
          type="plain"
          @click="dialogBeforeClose"
          >取消</el-button
        >
        <el-button
          class="tg-button--primary"
          type="primary"
          @click="saveModifyBindRelation"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <div slot="footer">
      <el-button
        class="tg-button--primary"
        type="primary"
        @click="dialogBeforeClose"
        >关闭</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import studentInforApi from "@/api/studentInfor";
import intentionApi from "@/api/intentionStudent";
export default {
  props: {
    dialogType: {
      type: String,
      default: "student"
    },
    visible: {
      type: Boolean,
      default: false
    },
    student_id: {
      type: String,
      default: ""
    },
    customer_id: {
      type: String,
      default: ""
    },
    student_name: {
      type: String,
      default: ""
    },
    row_id: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      table_title: [
        {
          label: "关联关系",
          show: true,
          props: "role_str"
        },
        {
          label: "关联账号",
          show: true,
          props: "visitor_mobile"
        }
      ],
      rowInfo: {},
      form: {
        role_id: ""
      },
      modifyBindRelationVisible: false,
      list: [],
      roleList: []
    };
  },
  created() {
    this.getRoleList({ from: "mini", token: "" });
    if (this.dialogType === "customer") {
      this.getCustomerList();
    } else {
      this.getStudentList();
    }
  },
  methods: {
    getCustomerList() {
      intentionApi
        .cusBindList({ customer_id: this.customer_id })
        .then((res) => {
          const { code, data, message } = res;
          if (code === 0) {
            this.list = data;
          } else {
            this.$message.error(message);
          }
        });
    },
    getRoleList(data) {
      studentInforApi.getRoleList(data).then(({ data }) => {
        console.log(data, "res.data");
        this.roleList = data.data;
      });
    },
    dialogBeforeClose() {
      this.$emit("close");
      this.$emit("uploadStatus");
    },
    getStudentList() {
      studentInforApi
        .getStudentBindList({ student_id: this.row_id })
        .then((res) => {
          const { code, data, message } = res.data;
          if (code === 0) {
            this.list = data;
          } else {
            this.$message.error(message);
          }
        });
    },
    modifyBindRelation(row) {
      console.log(row);
      this.rowInfo = row;
      this.modifyBindRelationVisible = true;
    },
    handleUnbind(row) {
      this.$confirm(
        `确定解除${this.student_name} 与 ${row.role_str}的关联关系？`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      ).then(() => {
        if (this.dialogType === "student") {
          this.unbindStudent(row);
        } else {
          this.unbindCustomer(row);
        }
      });
    },
    unbindCustomer(row) {
      intentionApi
        .unbindCustomer({
          student_name: this.student_name,
          customer_id: this.customer_id,
          bind_openid: row.open_id
        })
        .then(({ data }) => {
          if (data.code === 0) {
            this.$message.success("解除绑定成功!");
            this.getCustomerList();
          } else {
            this.$message.error(data.message);
          }
        });
    },
    unbindStudent(row) {
      studentInforApi
        .unbindWechat({
          student_id: row.student_id,
          bind_openid: row.open_id
        })
        .then(({ data }) => {
          if (data.code === 0) {
            this.$message.success("解除绑定成功!");
            this.getStudentList();
          } else {
            this.$message.error(data.message);
          }
        });
    },
    saveStudentBind() {
      studentInforApi
        .modifyStudentBind({
          student_id: this.row_id,
          bind_openid: this.rowInfo.open_id,
          role_id: this.form.role_id,
          student_name: this.student_name
        })
        .then(({ data }) => {
          if (data.code === 0) {
            this.$message.success("修改绑定关系成功!");
            this.getStudentList();
            this.modifyBindRelationVisible = false;
          } else {
            this.$message.error(data.message);
          }
        });
    },
    saveCustomerBind() {
      intentionApi
        .modifyCustomerRole({
          customer_id: this.customer_id,
          bind_openid: this.rowInfo.open_id,
          role_id: this.form.role_id,
          student_name: this.student_name
        })
        .then((res) => {
          const { code, message } = res.data;
          console.log(res, "customer res");
          if (code === 0) {
            this.$message.success("修改绑定关系成功!");
            this.getCustomerList();
            this.modifyBindRelationVisible = false;
          } else {
            this.$message.error(message);
          }
        });
    },
    saveModifyBindRelation() {
      if (this.dialogType === "student") {
        this.saveStudentBind();
      } else {
        this.saveCustomerBind();
      }
    }
  }
};
</script>

<style></style>
