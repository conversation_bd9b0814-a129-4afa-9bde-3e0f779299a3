<!--添加物品类别-->
<template>
  <el-dialog
    :visible="true"
    width="460px"
    class="add-categroy-dialog"
    :title="title"
    :before-close="handleClose"
  >
    <div class="tg-box--margin">
      <div class="tg-header__sub-title">
        <img src="../../assets/图片/icon_permission.png" alt="" />
        <span>要添加的板块名称不能重复</span>
      </div>
      <el-form class="tg-box--margin">
        <el-form-item label="板块名称" prop="name">
          <el-input
            v-model="name"
            placeholder="请输入板块名称"
            :maxlength="10"
          />
        </el-form-item>
        <el-form-item
          label="按行政区域筛选"
          prop="parent_id"
          class="tg-box--margin"
        >
          <el-select
            v-model="display"
            placeholder="请选择"
            class="tg-select--width"
          >
            <el-option
              v-for="(item, index) in display_list"
              :value="item.id"
              :label="item.name"
              :key="index"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose" class="tg-button--plain">取消</el-button>
      <el-button type="primary" @click="handleClick" class="tg-button--primary"
        >确定</el-button
      >
    </span>
  </el-dialog>
</template>
<script>
export default {
  props: ["infoData"],
  data() {
    return {
      name: "",
      display: "",
      display_list: [
        { name: "是", id: 1 },
        { name: "否", id: 2 }
      ]
    };
  },
  computed: {
    title() {
      return this.infoData ? "编辑板块" : "新增板块";
    }
  },
  mounted() {
    console.log(this.infoData);
    this.name = this.infoData ? this.infoData.name : "";
    this.display = this.infoData ? this.infoData.display : "";
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
    handleClick() {
      if (this.name === "") {
        this.$message.error("请输入板块名称");
        return false;
      } else if (this.display === "") {
        this.$message.error("请选择是否按行政区域筛选");
        return false;
      }
      console.log(this.infoData);
      this.infoData
        ? this.$emit("editInfo", {
            name: this.name,
            display: this.display,
            id: this.infoData.id
          })
        : this.$emit("confirm", { name: this.name, display: this.display });
    }
  }
};
</script>
<style lang="less" scoped>
.add-categroy-dialog {
  ::v-deep .el-dialog__body {
    padding: 0 16px 16px 16px;
  }
  .tg-header__sub-title {
    background: #f5f8fc;
    margin-left: 0;
    margin-right: 0;
    height: 32px;
    span {
      font-size: @text-size_special;
      color: #8492a6;
    }
  }
}
::v-deep .el-input {
  width: 315px !important;
}
::v-deep .el-input.is-focus::after {
  border: none !important;
}
::v-deep .el-form-item__label {
  width: 110px;
  text-align: left;
}
</style>
