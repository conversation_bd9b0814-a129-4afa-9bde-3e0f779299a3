<!--新增物品-->
<template>
  <el-dialog
    :visible="true"
    width="800px"
    class="add-goods"
    :title="type == 'add' ? '新增' : '编辑'"
    :before-close="handleClose"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      class="tg-box--margin"
      label-width="80px"
    >
      <el-form-item label="视频名称" prop="name">
        <el-input
          type="input"
          placeholder="请输入视频名称"
          v-model="form.name"
          :maxlength="20"
        />
      </el-form-item>
      <el-form-item label="板块" prop="parent_id" class="tg-box--margin">
        <el-select
          v-model="form.parent_id"
          :popper-append-to-body="false"
          placeholder="请选择板块"
          class="tg-select--width"
        >
          <el-option
            v-for="(item, index) in new_video_parent_list"
            :value="item.id"
            :label="item.name"
            :key="index"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item required label="选择校区" style="margin-top: 16px">
        <el-input
          placeholder="请选择校区"
          readonly
          show-word-limit
          :disabled="isDetail"
          :validate-event="false"
          @click.native="isDetail ? '' : (school_tree_visible = true)"
          v-model="form.department_name"
          class="tg-select tg-select--dialog custom-input"
          @mouseenter.native="school_flag = true"
          @mouseleave.native="school_flag = false"
        >
          <img
            slot="suffix"
            :src="
              !school_flag
                ? require('@/assets/图片/icon_more.png')
                : require('@/assets/图片/icon_more_ac.png')
            "
            alt=""
            class="btn__img--dotted"
          />
        </el-input>
        <school-tree
          :flag.sync="school_tree_visible"
          v-if="school_tree_visible"
          :id.sync="form.department_id"
          :name.sync="form.department_name"
          :type="'radio'"
          :use_store_options="false"
        >
        </school-tree>
      </el-form-item>

      <el-form-item
        label="上传视频"
        prop="video_url"
        required
        style="margin-top: 16px"
      >
        <div style="color: #999; font-size: 12px; margin-bottom: 10px">
          仅支持上传mp4/mpeg/webm/mov格式视频
        </div>
        <el-upload
          class="upload-demo"
          action="#"
          :disabled="isDetail"
          accept=".mp4, .webm, .mov"
          :show-file-list="false"
          ref="uploadImgs"
          :before-upload="(file) => beforeUpload(file, 'video')"
          :http-request="handleUpload"
        >
          <el-button
            type="primary"
            style="margin-bottom: 10px"
            :loading="uploadLoading"
            :disabled="isDetail"
            size="medium"
            >上传</el-button
          >
          <span class="el-upload__tip">注意：视频文件不能大于500M</span>
        </el-upload>
        <p style="line-height: 20px; font-size: 12px; margin-top: 0">
          {{ form.video_url }}
          <el-button
            v-if="form.video_url"
            type="text"
            @click="handleRemoveVideo"
            >删除</el-button
          >
        </p>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose" class="tg-button--plain">取消</el-button>
      <el-button
        type="primary"
        v-throttle="handleClick"
        class="tg-button--primary"
        >确定</el-button
      >
    </span>
  </el-dialog>
</template>
<script>
import { v4 as uuidv4 } from "uuid";
import videoApi from "@/api/wonderfulVideo";
export default {
  components: {},
  props: {
    type: String,
    id: String,
    video_parent_list: Array,
    parent_id: String
  },
  data() {
    return {
      school_flag: false,
      school_tree_visible: false,
      uploadLoading: false,
      form: {
        parent_id: "",
        video_url: "",
        name: "",
        department_id: [],
        department_name: []
      },
      isDetail: false,
      rules: {
        name: [{ required: true, trigger: "blur" }],
        parent_id: [{ required: true, trigger: "blur" }]
      },
      categroy_list: [],
      course_flag: false,

      new_video_parent_list: []
    };
  },
  mounted() {
    this.new_video_parent_list = this.video_parent_list.slice(1);
    if (this.type === "edit") {
      this.getGoodsInfo({ id: this.id });
    } else {
      if (this.parent_id !== "") {
        this.form.parent_id = this.parent_id;
      }
    }
  },
  methods: {
    async getVideoDimensions(file) {
      return new Promise((resolve) => {
        const video = document.createElement("video");
        video.src = URL.createObjectURL(file);

        video.onloadedmetadata = () => {
          resolve({
            width: video.videoWidth,
            height: video.videoHeight
          });
          URL.revokeObjectURL(video.src); // 释放内存
        };
      });
    },
    readFile(file) {
      return new Promise((resolve) => {
        const reader = new FileReader();
        reader.readAsBinaryString(file);
        reader.onload = (ev) => {
          resolve(ev.target.result);
        };
      });
    },
    handleRemoveVideo() {
      this.form.video_url = "";
      this.mediaType = "";
    },

    async handleUpload(upload) {
      console.log(1);
      this.btnLoading = true;
      this.uploadLoading = true;
      this.btnLoadingType = "upload";
      const f = upload.file;
      const { width, height } = await this.getVideoDimensions(f);
      console.log(width, height, "width, height");
      const orginName = f.name.substring(0, f.name.lastIndexOf("."));
      const suffix = f.name.match(/[^.]+$/)[0];
      const name = `${orginName}_${uuidv4()}.${suffix}`;
      const copyFile = new File([f], name);
      this.Oss.uploadFile(copyFile)
        .then((res) => {
          if (res.code === 0) {
            // this.$message.success("上传成功");
            this.mediaType = "video";
            this.form.video_urls = [
              {
                name: res.objectKey,
                url: res.url,
                size: f.size,
                width,
                height
              }
            ];
            this.form.video_url = res.url;
            this.btnLoading = false;
            this.uploadLoading = false;
          } else {
            this.btnLoading = false;
            this.uploadLoading = false;
            this.$message.error(res.msg);
          }
        })
        .catch(() => {
          this.btnLoading = false;
          this.uploadLoading = false;
        });
    },

    beforeUpload(file, type) {
      const fileSuffix = file.name.substring(file.name.lastIndexOf(".") + 1);
      const whiteList = ["mp4", "mpeg", "webm", "mov"];
      if (whiteList.indexOf(fileSuffix) === -1) {
        this.$message.info("上传视频格式仅支持：" + whiteList.join(","));
        return false;
      }
      const isLt500M = file.size / 1024 / 1024 > 500;
      if (isLt500M) {
        this.$message.info("上传视频大小不能超过500MB!");
        return false;
      }
    },
    handleClick() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const params = {
            ...this.form
          };
          // params.sales_cycle =
          //   this.form.sales_method === 2 ? Number(params.sales_cycle) : 0;
          this.$emit("confirm", params);
        } else {
          this.$message.error("请填写必填项");
          return false;
        }
      });
    },
    handleClose() {
      this.$emit("close");
    },
    async getGoodsInfo(d) {
      const res = await videoApi.videoDetail(d);
      this.form = res.data.data;
    }
  },
  created() {
    this.Oss.getAliyun();
  }
};
</script>
<style lang="less" scoped>
.add-goods {
  ::v-deep .el-dialog__body {
    padding: 0 16px 16px 16px;
  }
  ::v-deep .el-form-item__content,
  ::v-deep .el-form-item__label {
    line-height: 32px;
  }
  ::v-deep .el-input,
  .msg {
    width: 400px;
  }
  .status,
  .el-checkbox {
    margin-right: 16px;
  }
  ::v-deep .el-input-number {
    line-height: 32px;
    .el-input__inner {
      text-align: left;
    }
  }
  .tg-select--width {
    ::v-deep .el-select-dropdown {
      width: 400px;
    }
  }
}
.school-tree {
  padding: 8px 0;
  border-top: 1px solid #ecedf2;
}
</style>
