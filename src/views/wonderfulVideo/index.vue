<!--物品管理-->
<template>
  <div class="videos container">
    <div class="goods-content tg-box--margin">
      <div class="goods-content--left tg-box--shadow">
        <div class="title">
          <span>板块</span>
          <el-button
            type="primary"
            class="tg-button--primary tg-button__icon"
            @click="addCategroy"
            v-has="{ m: 'wonderfulVideo', o: 'parentCreate' }"
          >
            <img
              src="../../assets/图片/jh.png"
              alt=""
              class="tg-button__icon--normal"
            />新增</el-button
          >
        </div>
        <div class="side-wrap">
          <div
            v-for="(item, index) in video_parent_list"
            :key="index"
            class="side"
          >
            <div
              class="side-select"
              :class="
                categroy_hover_index === index || categroy_index === index
                  ? 'side-select-active'
                  : ''
              "
              @mouseenter="categroy_hover_index = index"
              @mouseleave="categroy_hover_index = -1"
              @click="changeList(index)"
            >
              <el-tooltip
                class="item"
                effect="dark"
                placement="bottom"
                v-if="item.name.length > show_limit"
                :content="item.name"
              >
                <span class="side-select__label">{{ item.name }}</span>
              </el-tooltip>
              <span class="side-select__label" v-else>{{ item.name }}</span>
              <img
                v-has="{ m: 'wonderfulVideo', o: 'parentUpdate' }"
                v-if="categroy_hover_index === index"
                src="../../assets/图片/icon_submenu_edit_ac.png"
                alt=""
                class="side-setting__icon"
                @click.stop="editCategroy(item)"
              />
              <img
                v-has="{ m: 'wonderfulVideo', o: 'parentDel' }"
                v-if="categroy_hover_index === index"
                src="../../assets/图片/ClassTimeSC.png"
                alt=""
                class="side-setting__icon"
                @click.stop="confirmDelCategroy(index)"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="goods-content--right container">
        <tg-search
          :searchTitle.sync="searchTitle"
          :form.sync="search"
          @reset="reset"
          @search="searchVal"
          :showNum="3"
        ></tg-search>
        <el-row class="tg-box--margin tg-shadow--margin tg-row--height">
          <el-button
            type="plain"
            class="tg-button--plain"
            @click="openDialog(1)"
            v-has="{ m: 'wonderfulVideo', o: 'create' }"
            >新增</el-button
          >
          <el-button
            type="plain"
            class="tg-button--plain"
            @click="openDialog(2, '', {}, 'many')"
            v-has="{ m: 'wonderfulVideo', o: 'status' }"
            :disabled="canPut"
            >批量上架</el-button
          >
          <el-button
            type="plain"
            class="tg-button--plain"
            @click="openDialog(2, '', {}, 'many')"
            v-has="{ m: 'wonderfulVideo', o: 'status' }"
            :disabled="takeOff"
            >批量下架</el-button
          >
        </el-row>
        <div class="tg-table__box tg-table-margin">
          <div class="tg-box--border"></div>
          <el-table
            ref="table"
            :data="list"
            tooltip-effect="dark"
            class="tg-table"
            @selection-change="handleSelectionChange"
            :row-key="getRowKeys"
            border
            :cell-style="{ borderRightColor: '#e0e6ed75' }"
            :header-cell-style="{ borderRightColor: '#e0e6ed75' }"
          >
            <el-table-column
              type="selection"
              width="50"
              :reserve-selection="true"
            >
            </el-table-column>
            <template v-for="(item, index) in tableTitle">
              <el-table-column
                v-if="item.show"
                :key="index"
                :prop="item.props"
                :label="item.label"
                :min-width="item.width"
                show-overflow-tooltip
                :fixed="item.fixed ? true : false"
              >
                <template slot-scope="scope">
                  <!-- <el-button
                      v-if="item.props === 'student_name'"
                      class="tg-text--blue"
                      :class="{ 'tg-text--black': !can_info }"
                      type="text"
                      >{{ scope.row.student_name }}</el-button
                    > -->
                  <div v-if="item.props === 'status'">
                    {{
                      scope.row.status === 1
                        ? "未上架"
                        : scope.row.status === 2
                        ? "已上架"
                        : "已下架"
                    }}
                  </div>
                  <span v-else>{{ scope.row[scope.column.property] }}</span>
                </template>
              </el-table-column>
            </template>
            <el-table-column label="操作" width="220" fixed="right">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  size="small"
                  v-has="{ m: 'wonderfulVideo', o: 'status' }"
                  class="tg-text--blue tg-span__divide-line"
                  @click="openDialog(2, scope.row.id, scope.row, 'single')"
                  v-if="scope.row.status === 1 || scope.row.status === 3"
                  >上架</el-button
                >
                <el-button
                  type="text"
                  size="small"
                  v-has="{ m: 'wonderfulVideo', o: 'status' }"
                  class="tg-text--blue tg-span__divide-line"
                  @click="openDialog(2, scope.row.id, scope.row, 'single')"
                  v-if="scope.row.status === 2"
                  >下架</el-button
                >
                <el-button
                  type="text"
                  size="small"
                  v-has="{ m: 'wonderfulVideo', o: 'update' }"
                  class="tg-text--blue tg-span__divide-line"
                  @click="openDialog(6, scope.row.id, scope.row)"
                  v-if="scope.row.status === 1 || scope.row.status === 3"
                  >编辑</el-button
                >
                <el-button
                  type="text"
                  size="small"
                  class="tg-text--blue tg-span__divide-line"
                  v-has="{ m: 'wonderfulVideo', o: 'delete' }"
                  @click="openDialog(7, scope.row.id)"
                  v-if="scope.row.status === 1 || scope.row.status === 3"
                  >删除</el-button
                >
                <el-button
                  type="text"
                  size="small"
                  class="tg-text--blue tg-span__divide-line"
                  v-has="{ m: 'wonderfulVideo', o: 'list' }"
                  @click="openDialog(9, scope.row.id, scope.row)"
                  >预览</el-button
                >
              </template>
            </el-table-column>
            <template slot="empty">
              <div style="margin-top: 15%">
                <TgLoading v-if="loading"></TgLoading>
                <div class="empty-container" v-else>暂无数据～</div>
              </div>
            </template>
          </el-table>
          <!-- 分页 -->
          <div class="tg-pagination">
            <span class="el-pagination__total">共 {{ total }} 条</span>
            <el-pagination
              background
              layout="sizes,prev,pager,next,jumper"
              :total="total"
              :page-size="pageSize"
              :current-page="page"
              @current-change="currentChange"
              @size-change="sizeChange"
              :page-sizes="[10, 20, 50, 100]"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
    <add-video-categroy
      v-if="add_categroy_visible"
      @close="add_categroy_visible = false"
      @confirm="confirmCreateCategroy"
      @editInfo="editCreateCategroy"
      :infoData="infoData"
    ></add-video-categroy>
    <add-video
      :type="videos_type"
      v-if="add_videos_visible"
      @close="add_videos_visible = false"
      @confirm="confirmCreateGoods"
      :id="videos_id"
      :video_parent_list="video_parent_list"
      :parent_id="search.parent_id"
    ></add-video>
    <el-dialog
      v-if="dialogPosterVisible"
      title="预览"
      :append-to-body="true"
      :visible.sync="dialogPosterVisible"
      @close="closeVideo"
    >
      <video
        style="width: 100%; max-height: 100%; object-fit: contain"
        controls
      >
        <source
          v-for="item in ['video/mp4', 'video/mpeg', 'video/webm', 'video/ogg']"
          :key="item"
          :src="preview_video_url"
          :type="item"
        />
        您的浏览器不支持 video 标签。
      </video>
    </el-dialog>
  </div>
</template>
<script>
import tgSearch from "@/components/search/search.vue";
import AddVideoCategroy from "./addVideoCategroy.vue";
import AddVideo from "./addVideo.vue";
import videoApi from "@/api/wonderfulVideo";

export default {
  data() {
    return {
      dialogPosterVisible: false,
      checked_videos: [],
      list: [],
      page: 1,
      total: 0,
      searchTitle: [
        { props: "name", label: "视频名称", type: "input", show: true },
        {
          props: "parent_id",
          label: "板块",
          type: "select",
          show: true,
          selectOptions: []
        },
        {
          props: "status",
          label: "状态",
          type: "mutipleSelect",
          placeholder: "请选择状态",
          width: 200,
          selectOptions: [
            { name: "未上架", id: 1 },
            { name: "已上架", id: 2 },
            { name: "已下架", id: 3 }
          ],
          show: true
        },
        {
          props: "creator_id",
          label: "创建人",
          type: "mark_staff",
          show: false
        },
        {
          props: "created_at",
          label: "创建时间",
          type: "date",
          width: 220,
          show: false,
          selectOptions: [],
          has_options: true
        }
      ],
      search: {
        id: "",
        name: "",
        parent_id: "",
        status: [1, 2, 3],
        creator_id: "",
        created_at: []
      },
      loading: false,
      height: window.innerHeight - 336,
      pageSize: 10,
      tableTitle: [
        {
          props: "name",
          label: "视频名称",
          show: true,
          width: 150,
          fixed: true
        },
        {
          props: "parent_name",
          label: "板块",
          show: true,
          width: 180
        },
        { props: "department_name", label: "校区", show: true, width: 150 },
        { props: "status", label: "状态", show: true, width: 100 },
        { props: "creator_name", label: "创建人", show: true, width: 100 },
        {
          props: "created_at",
          label: "创建时间",
          type: "date",
          width: 180,
          show: true,
          selectOptions: [],
          has_options: true
        }
      ],
      video_parent_list: [],
      add_categroy_visible: false,
      add_videos_visible: false,
      categroy_index: 0,
      categroy_hover_index: 0,
      videos_type: "",
      videos_id: "",
      videos_ids: [],
      many_video_status: 1,
      show_limit: 0,
      has_operate: false,
      infoData: "",
      preview_video_url: "",
      canPut: false,
      takeOff: false
    };
  },

  computed: {
    school_id() {
      return this.$store.getters.doneGetSchoolId;
    }
  },
  created() {
    // this.reset();
    this.resetDate();
    this.video_parent_list = [{ id: "", name: "全部" }];
  },
  async mounted() {
    this.show_limit = window.innerWidth < 1500 ? 8 : 15;
    await this.videoParentList();
    await this.getVideosList();
  },
  methods: {
    closeVideo() {
      this.dialogPosterVisible = false;
      this.preview_video_url = "";
    },
    editCategroy(row) {
      this.infoData = row;
      this.add_categroy_visible = true;
    },
    async editCreateCategroy(row) {
      //   const names = this.video_parent_list.map((item) => item.name);
      //   if (names.includes(row.name)) {
      //     this.$message.error("该类别已存在，不允许重复添加!");
      //     return false;
      //   }
      const res = await videoApi.parentVideoUpdate(row);
      if (res.data.message === "success") {
        this.add_categroy_visible = false;
        this.videoParentList();
      }
    },
    handleSelectionChange(val) {
      const firstValue = val[0].status;
      const bool = val.every((item) => item.status === firstValue);
      this.videos_ids = val.map((item) => item.id);
      if ((firstValue === 1 || firstValue === 3) && bool) {
        this.canPut = false;
        this.takeOff = true;
        this.many_video_status = 2;
      } else if (firstValue === 2 && bool) {
        this.takeOff = false;
        this.canPut = true;
        this.many_video_status = 3;
      } else {
        this.canPut = this.takeOff = true;
      }
    },
    currentChange(val) {
      this.loading = true;
      this.page = val;
      this.getVideosList();
    },
    sizeChange(val) {
      this.page = 1;
      this.pageSize = val;
      this.getVideosList();
    },
    getRowKeys(row) {
      // this.$refs.form.clearSelection();
      return row.id;
    },
    searchVal() {
      this.page = 1;
      this.clearSelection();
      this.getVideosList();
    },
    resetDate() {
      this.$nextTick(async () => {
        const formatDate = (date) => moment(date).format("YYYY-MM-DD");
        const end = new Date();

        // 创建时间和修改时间默认7天
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setTime(sevenDaysAgo.getTime() - 3600 * 1000 * 24 * 6);

        // 设置时间范围
        this.search.created_at = [formatDate(sevenDaysAgo), formatDate(end)];
      });
    },
    reset() {
      this.search = {
        id: "",
        name: "",
        parent_id: "",
        status: [1, 2, 3],
        creator_id: "",
        created_at: []
      };
      this.page = 1;
      this.pageSize = 10;
      this.resetDate();
      this.clearSelection();
      this.searchVal();
    },
    clearSelection() {
      this.videos_ids = [];
      this.$nextTick(() => {
        this.$refs.table.clearSelection();
      });
    },
    changeList(index) {
      this.categroy_index = index;
      const { id } = this.video_parent_list[index];
      this.$set(this.search, "parent_id", id);
      this.page = 1;
      this.clearSelection();
      this.getVideosList();
    },
    confirmDelCategroy(index) {
      this.$confirm(
        "此操作将永久删除此板块和此板块下所有视频, 是否继续?",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(() => {
          console.log(index);
          const { id } = this.video_parent_list[index];
          this.categroy_index =
            index === this.video_parent_list.length - 1 ? index - 1 : index;
          if (this.categroy_index > -1) {
            const { id: new_id } = this.video_parent_list[this.categroy_index];
            this.$set(this.search, "parent_id", new_id);
          } else {
            this.$set(this.search, "parent_id", "");
          }
          this.delCategroy({ id });
        })
        .catch(() => {});
    },

    async changeVideoStatus(type, id, row) {
      if (type === "single") {
        const data = {
          ids: [id],
          status: row.status === 1 || row.status === 3 ? 2 : 3
        };
        const res = await videoApi.updateStatus(data);
        if (res.data.message === "success") {
          this.$message.success("操作成功");
          this.clearSelection();
          this.getVideosList();
        } else {
          this.$message.error(res.data.message);
        }
      } else if (type === "many") {
        if (this.videos_ids.length === 0) {
          this.$message.error("选项不能为空");
          return false;
        }
        const data = {
          ids: this.videos_ids,
          status: this.many_video_status
        };
        const res = await videoApi.updateStatus(data);
        if (res.data.message === "success") {
          this.$message.success("操作成功");
          this.clearSelection();
          this.getVideosList();
        } else {
          this.$message.error(res.data.message);
        }
      }
    },
    openDialog(type, id, row, form) {
      if (type === 1) {
        this.add_videos_visible = true;
        this.videos_type = "add";
      } else if (type === 2) {
        this.changeVideoStatus(form, id, row);
      } else if (type === 6) {
        // 编辑
        this.add_videos_visible = true;
        this.videos_type = "edit";
        this.videos_id = id;
      } else if (type === 7) {
        // 删除
        this.$confirm("此操作将永久删除, 是否继续?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            this.delVideos({ id });
          })
          .catch(() => {});
      } else if (type === 9) {
        console.log(row);
        this.dialogPosterVisible = true;
        this.preview_video_url = row.video_url;
        console.log("preview_video_url", this.preview_video_url);
      }
    },
    addCategroy() {
      this.infoData = "";
      this.add_categroy_visible = true;
    },
    confirmCreateCategroy(data) {
      const names = this.video_parent_list.map((item) => item.name);
      if (names.includes(data.name)) {
        this.$message.error("该板块已存在，不允许重复添加!");
        return false;
      }
      this.createCategroy(data);
    },
    confirmCreateGoods(form) {
      if (this.videos_type === "add") {
        this.page = 1;
        this.createGoods(form);
      } else if (this.videos_type === "edit") {
        this.updateGoods({ ...form, id: this.videos_id });
      }
    },
    async createCategroy(d) {
      const { data } = await videoApi.parentVideoCreate(d);
      if (data.message === "success") {
        this.add_categroy_visible = false;
        this.videoParentList();
      }
    },
    async delVideos(d) {
      const { data } = await videoApi.videoDel(d);
      if (data.message === "success") {
        this.clearSelection();
        this.getVideosList();
      }
    },
    async delCategroy(d) {
      const { data } = await videoApi.parentVideoDel(d);
      if (data.message === "success") {
        this.videoParentList();
        this.clearSelection();
        this.getVideosList();
      }
    },
    // created_at
    async getVideosList() {
      console.log(this.search);
      const d = {
        page: this.page,
        page_size: this.pageSize,
        start_time:
          this.search.created_at.length > 0
            ? this.search.created_at[0]
            : undefined,
        end_time:
          this.search.created_at.length > 0
            ? this.search.created_at[1]
            : undefined,
        department_ids: this.school_id,
        ...this.search
      };
      this.loading = true;
      this.list = [];
      const res = await videoApi.videoList(d);
      console.log("res", res);
      const { count, results } = res.data.data;
      this.list = results == null ? [] : results;
      this.total = count;
      this.loading = false;
    },
    async videoParentList(d) {
      const { data } = await videoApi.videoParentList(d);
      this.video_parent_list.push(...(data.data ?? []));
      this.searchTitle[1].selectOptions = this.video_parent_list;
      this.search.parent_id = this.video_parent_list[0].id;
    },
    async createGoods(d) {
      const { data } = await videoApi.videoCreate(d);
      if (data.message === "success") {
        this.$message.success("操作成功");
        this.add_videos_visible = false;
        this.clearSelection();
        this.getVideosList();
      } else {
        this.$message.error(data.message);
      }
    },
    async updateGoods(d) {
      const { data } = await videoApi.videoUpdate(d, "修改成功");
      if (data.message === "success") {
        this.$message.success("操作成功");
        this.add_videos_visible = false;
        this.clearSelection();
        this.getVideosList();
      } else {
        this.$message.error(data.message);
      }
    },
    selectChange(val) {
      if (this.search.sales_method === 2) {
        this.searchTitle.push({
          props: "sales_cycle",
          label: "租赁周期",
          type: "input",
          append: "月",
          show: true
        });
      } else {
        this.searchTitle = this.searchTitle.filter(
          (item) => item.props !== "sales_cycle"
        );
      }
    }
  },
  components: {
    tgSearch,
    AddVideoCategroy,
    AddVideo
  }
};
</script>
<style lang="less" scoped>
.videos {
  ::v-deep .el-table td:nth-last-child(2) {
    border-right-color: rgba(224, 230, 237, 0.46) !important;
  }
  ::v-deep .el-table__header th:nth-last-child(2) {
    border-right-color: rgba(224, 230, 237, 0.46) !important;
  }
  .goods-content--left {
    width: 230px;
    background: #fff;
    border-radius: 4px;
    height: 100%;
  }
  .goods-content--right {
    width: calc(100% - 240px);
    margin-left: 16px;
    height: 100%;
  }
  @media screen and (max-width: 1500px) {
    .goods-content--left {
      width: 180px;
    }
    .goods-content--right {
      width: calc(100% - 180px);
    }
  }
  .goods-content {
    display: flex;
    flex-direction: row;
    // margin-left: 6px;
    width: 100%;
  }
  .title {
    padding: 0 16px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    height: 64px;
    border-bottom: 1px solid #e0e6ed;
    span {
      font-size: 14px;
      font-family: @text-famliy_medium;
      color: @text-color_second;
    }
  }
  .side-select {
    height: 48px;
    line-height: 48px;
    border-left: 2px solid transparent;
    padding-left: 14px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
  }
  .side-select__label {
    font-size: @text-size_small;
    font-family: @text-famliy_medium;
    color: @text-color_second;
    display: inline-block;
    white-space: nowrap;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .side-select-active {
    .side-select__label {
      color: @base-color;
    }
    background-color: @light-color;
    border-left: 2px solid @base-color;
  }
  .side-setting__icon {
    width: 14px;
    height: 14px;
    margin-right: 16px;
    cursor: pointer;
  }
  .circle {
    width: 5px;
    height: 5px;
    border-radius: 50%;
    background: @text-color_third;
    display: inline-block;
    vertical-align: middle;
    margin-right: 5px;
  }
  .is-enabled {
    background: @base-color;
  }
  .tg-row--height {
    width: 100%;
  }
  .enabled-wrap {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  .side-wrap {
    overflow: scroll;
    height: calc(100vh - 210px);
  }
  .tg-table-margin {
    margin-bottom: 0;
  }
  ::v-deep .el-table__body-wrapper {
    height: calc(100% - 46px);
    .empty-container {
      position: absolute;
      top: 50%;
      left: 50%;
    }
    .loading-container {
      position: absolute;
      top: 30%;
      left: 1%;
      background: transparent;
      .box {
        height: 100%;
      }
    }
  }
  ::v-deep .el-table tr {
    background-color: transparent !important;
  }
  ::v-deep .el-table__fixed {
    background: #fff;
    z-index: 9;
  }
  ::v-deep .el-table__fixed-right {
    box-sizing: border-box;
    background-color: #fff;
    z-index: 8;
    right: -17px;
  }
}
</style>
