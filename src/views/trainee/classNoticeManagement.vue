<template>
  <div class="course-statistics">
    <tg-search
      class="connect-search"
      :searchTitle.sync="search_title"
      :form.sync="searchForm"
      :showNum="3"
      :isExport="isExport"
      @reset="reset"
      @educe="exportExcel"
      @search="search"
      :loadingState="exportLoading"
      :defaultSpread="true"
    ></tg-search>
    <!-- :summary-method="getSummaries" -->
    <template>
      <div class="tg-table__box course-statistics-table">
        <el-table
          ref="table"
          :data="list"
          tooltip-effect="dark"
          class="tg-table"
          v-loading="false"
          border
        >
          <template v-for="(item, index) in table_title">
            <el-table-column
              :key="index"
              :prop="item.props"
              :label="item.label"
              show-overflow-tooltip
              :min-width="item.width"
              :sortable="item.sort"
            >
              <template slot-scope="scope">
                <span v-if="item.type === 'date'">
                  {{ scope.row[scope.column.property] | getTime }}
                </span>
                <span v-else-if="item.props === 'read_status'">
                  <el-tag v-if="scope.row.read_status === 1" type="success"
                    >已读</el-tag
                  >
                  <el-tag v-if="scope.row.read_status === 2" type="info"
                    >未读</el-tag
                  >
                </span>
                <span v-else-if="item.props === 'is_cancel'">
                  <el-tag v-if="scope.row.is_cancel === 1" type="info"
                    >已撤销</el-tag
                  >
                  <el-tag v-if="scope.row.is_cancel === 2" type="success"
                    >未撤销</el-tag
                  >
                </span>
                <span v-else-if="item.props === 'scheduling_start_time'">
                  {{
                    scope.row[item.props]
                      ? scope.row[item.props]?.indexOf("0001-01-01") > -1
                        ? ""
                        : getTime(scope.row[item.props])
                      : ""
                  }}
                </span>
                <span v-else>{{ scope.row[scope.column.property] }}</span>
              </template>
            </el-table-column>
          </template>
          <el-table-column label="操作" width="150" fixed="right">
            <template slot-scope="scope">
              <el-button
                v-if="$_has({ m: 'traineeReport', o: 'info' })"
                @click="toDetail(scope.row)"
                type="text"
                class="tg-text--blue tg-span__divide-line"
                >查看</el-button
              >
              <el-button
                v-if="
                  $_has({ m: 'traineeReport', o: 'revoke' }) &&
                  scope.row.is_cancel === 2
                "
                type="text"
                @click="revoke(scope.row)"
                class="tg-text--blue tg-span__divide-line"
                >撤销</el-button
              >
            </template>
          </el-table-column>
          <template slot="empty">
            <div style="margin-top: 4%">
              <div style="width: 100%; min-height: 300px" v-if="loading">
                <loading></loading>
              </div>
              <div class="empty-container" v-else>暂无数据～</div>
            </div>
          </template>
        </el-table>
        <!-- 分页 -->
        <div class="tg-pagination">
          <span class="el-pagination__total">共 {{ total }} 条</span>
          <el-pagination
            background
            layout="sizes,prev,pager,next,jumper"
            :total="total"
            :page-size="page_size"
            :current-page="page"
            @current-change="currentChange"
            @size-change="sizeChange"
            :page-sizes="[10, 20, 50, 100]"
          ></el-pagination>
        </div>
      </div>
    </template>
  </div>
</template>
<script>
import traineeReportApi from "@/api/traineeReport.js";
import loading from "../loading";
import { export_excel_sync_new } from "@/public/asyncExport";
import timeFormat from "@/public/timeFormat";

export default {
  name: "classNoticeManagement",
  components: { loading },
  data() {
    return {
      exportLoading: false,
      searchForm: {
        header_teacher_id: "",
        course_id: [],
        name: "",
        class_teacher_id: "",
        department_id: "",
        classroom_id: "",
        alias_name: "",
        education_id: "",
        tutor_id: "",
        send_time: [],
        read_status: undefined,
        is_cancel: undefined
      },
      isExport: true,
      search_title: [
        {
          props: "name",
          label: "学员信息",
          type: "input",
          show: true,
          width: 400,
          placeholder: "请输入学员姓名/学号/电话"
        },
        // {
        //   props: "department_id",
        //   label: "所属校区",
        //   type: "school",
        //   show: true,
        //   selectOptions: [],
        //   school_choose_type: "radio",
        //   use_store_options: true
        // },
        {
          props: "classroom_id",
          label: "在读班级",
          type: "choose_class",
          show: true,
          placeholder: "请选择班级"
        },
        {
          props: "alias_name",
          label: "班级别名",
          type: "input",
          show: false
        },
        {
          props: "course_id",
          label: "课程名称",
          type: "choose_course",
          show: false,
          placeholder: "请选择课程名称"
        },
        {
          props: "class_teacher_id",
          label: "任课老师",
          type: "course_staff",
          show: false,
          is_leave: true,
          selectOptions: [],
          placeholder: "请选择任课老师"
        },
        {
          props: "header_teacher_id",
          label: "班主任",
          type: "course_staff",
          is_leave: true,
          selectOptions: [],
          placeholder: "请选择班主任"
        },
        {
          props: "education_id",
          label: "教务",
          type: "course_staff",
          show: false,
          is_leave: true,
          placeholder: "请选择教务",
          selectOptions: []
        },
        {
          props: "tutor_id",
          label: "辅导老师",
          type: "course_staff",
          show: false,
          is_leave: true,
          placeholder: "请选择辅导老师"
        },
        {
          props: "send_time",
          label: "发送时间",
          type: "date",
          show: false,
          selectOptions: [],
          has_options: true
        },
        {
          props: "read_status",
          label: "是否已读",
          type: "select",
          selectOptions: [
            {
              id: "",
              name: "不限"
            },
            {
              id: 1,
              name: "已读"
            },
            {
              id: 2,
              name: "未读"
            }
          ],
          show: false
        },
        {
          props: "is_cancel",
          label: "消息状态",
          type: "select",
          selectOptions: [
            {
              id: "",
              name: "不限"
            },
            {
              id: 1,
              name: "已撤销"
            },
            {
              id: 2,
              name: "未撤销"
            }
          ],
          show: false
        }
      ],

      table_title: [
        {
          props: "student_name",
          label: "姓名",
          width: 100
        },
        {
          props: "student_gender_ch",
          label: "性别",
          width: 100
        },
        {
          props: "student_number",
          label: "学号",
          width: 100
        },
        {
          props: "department_name",
          label: "所属校区",
          width: 200
          // sort: "custom"
        },
        {
          props: "classroom_name",
          label: "在读班级",
          width: 140,
          type: "name"
          // sort: "custom"
        },
        {
          props: "alias_name",
          label: "班级别名",
          width: 100
        },
        {
          props: "course_name",
          label: "课程名称",
          width: 100
        },
        {
          props: "course_level",
          label: "课程种类",
          width: 100
        },
        {
          props: "scheduling_start_time",
          label: "上课时间",
          width: 100
        },
        {
          props: "class_teacher_name",
          label: "任课老师",
          width: 100
        },
        {
          props: "header_teacher_name",
          label: "班主任",
          width: 100
        },
        {
          props: "education_name",
          label: "教务",
          width: 100
        },
        {
          props: "tutor_name",
          label: "辅导老师",
          width: 100
        },
        {
          props: "feedback_create_date",
          label: "发送时间",
          width: 100,
          type: "date"
        },
        {
          props: "teacher_name",
          label: "发送人",
          width: 100
        },
        {
          props: "read_status",
          label: "是否已读",
          width: 100
        },
        {
          props: "is_cancel",
          label: "消息状态",
          width: 100
        },
        {
          props: "is_cancel_date",
          label: "撤销时间",
          width: 100
        },
        {
          props: "employee_name",
          label: "撤销人",
          width: 100
        }
      ],
      //   summary_table_title: [
      //     {
      //       props: "course_num",
      //       label: "在读课程数量",
      //       width: 300
      //     },
      //     {
      //       props: "people_num",
      //       label: "统计人数",
      //       width: 300
      //     }
      //   ],
      total: 0,
      page_size: 10,
      page: 1,
      list: [],
      average_num: 0,
      lessonInfoVisible: false,
      loading: false,
      height: 0,
      student: {}
    };
  },
  beforeUpdate() {
    this.$nextTick(() => {
      this.$refs.table.doLayout();
    });
  },
  created() {
    this.height = window.innerHeight - 380;
    this.isExport = this.$_has({ m: "traineeReport", o: "export" });
  },
  computed: {
    school_id() {
      return this.$store.getters.doneGetSchoolId;
    }
  },
  watch: {
    school_id: {
      handler(val) {
        this.search();
      },
      immediate: true
    }
  },
  methods: {
    search() {
      this.page = 1;
      const search = this.getSearch();
      this.getTraineeList(search);
    },
    getTime(time) {
      return timeFormat.GetTime(time);
    },
    getSearch() {
      const newForm = JSON.parse(JSON.stringify(this.searchForm));
      const query = {
        page: this.page,
        page_size: this.page_size,
        type: "class_notice",
        ...newForm
      };
      query.department_id = this.school_id;
      if (this.searchForm.course_id.length === 0) {
        delete query.course_id;
      } else {
        query.course_id = this.searchForm.course_id.split(",");
      }
      if (this.searchForm.classroom_id.length === 0) {
        delete query.classroom_id;
      } else {
        query.classroom_id = this.searchForm.classroom_id.split(",");
      }
      if (this.searchForm.send_time.length) {
        query.send_start = this.searchForm.send_time[0];
        query.send_end = this.searchForm.send_time[1];
        delete query.send_time;
      }
      return query;
    },
    async getTraineeList(search) {
      this.loading = true;
      const res = await traineeReportApi.getList(search);
      if (res.data.data === null) return;
      const { count, results, average_num } = res.data.data;
      this.total = count;
      this.list = results === null ? [] : results;
      this.average_num = average_num ?? 0;
      this.loading = false;
    },
    // sortChange(val) {
    //   const { prop, order } = val;
    //   let _oreder = "";
    //   if (order === "ascending") {
    //     _oreder = "asc";
    //   } else if (order === "descending") {
    //     _oreder = "desc";
    //   }
    //   this.searchForm.sort = `${prop} ${_oreder}`;
    //   this.search();
    // },
    // getSummaries(param) {
    //   const sums = [];
    //   const { columns, data } = param;
    //   columns.forEach((val, index) => {
    //     const values = data.map((item) => {
    //       return item.course_num;
    //     });
    //     let sum = 0;
    //     values.forEach((item) => {
    //       if (typeof item === "number") {
    //         sum = sum + item;
    //       } else {
    //         sum = "";
    //       }
    //     });

    //     if (index === 0) {
    //       sums[index] = "合计";
    //       return;
    //     }
    //     if (index === 3) {
    //       sums[index] = "人均 " + this.average_num.toFixed(2) + " 个课程";
    //       return;
    //     }
    //     sums[index] = "";
    //   });
    //   return sums;
    // },

    async exportExcel() {
      const q = this.getSearch();
      // delete q.page;
      // delete q.page_size;
      // const res = await traineeReportApi.exportData(q);
      // downLoadFile(
      //   res,
      //   "学员报告管理" + moment(new Date()).format("YYYY-MM-DD HH-mm-ss")
      // );
      const opt = {
        vm: this, // vue组件实例，
        api_url: "/api/school-service/admin/feedback/export", // 接口地址
        file_name: "学员报告管理", // 文件名
        success_msg: "学员报告管理导出成功！", // 导出成功的提示语
        error_msg: "学员报告管理导出失败！", // 导出失败的提示语,
        query: q
      };
      export_excel_sync_new(opt);
    },
    // 撤销
    revoke(row) {
      // 撤销前弹出确认询问
      this.$confirm("确认撤销该条学员报告？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(async () => {
        const res = await traineeReportApi.revoke({
          is_cancel: 1,
          feedback_id: row.feedback_id
        });
        const { code, message } = res.data;
        if (code === 0) {
          this.$message.success("撤销成功!");
          this.search();
        } else {
          this.$message.error(message);
        }
      });
    },
    reset() {
      this.searchForm = {
        header_teacher_id: "",
        course_id: [],
        name: "",
        teacher_id: "",
        department_id: "",
        classroom_id: "",
        alias_name: "",
        education_id: "",
        tutor_id: "",
        send_time: [],
        read_status: undefined,
        is_cancel: undefined
      };
      this.search();
      this.$refs.table.clearSort();
    },
    currentChange(val) {
      this.page = val;
      const search = this.getSearch();
      this.getTraineeList(search);
    },
    sizeChange(val) {
      this.page = 1;
      this.page_size = val;
      this.reset();
      const search = this.getSearch();
      this.getTraineeList(search);
    },
    toDetail(row) {
      const { feedback_id, student_id } = row;
      //   this.student = {
      //     student_id,
      //     student_name
      //   };
      // 路由到学员报告详情
      this.$router.push({
        name: "traineeReportManagementDetail",
        query: {
          feedback_id,
          student_id
        }
      });
    }
  }
};
</script>
<style lang="less" scoped>
.connect-search {
  margin: 0 6px;
  width: calc(100% - 12px);
}
.course-statistics {
  margin-top: 16px;
}
::v-deep .course-statistics-table {
  width: calc(100% - 12px);
  border: 1px solid @base-color;
  min-height: 100px;

  .el-table__footer td {
    border: none;
  }
  .el-table {
    padding: 0;
  }
  th {
    background: @light-color;
  }
  .el-table th:first-child > .cell {
    padding-left: 26px;
  }
  .el-table td:first-child > .cell {
    padding-left: 26px;
  }
  .el-table__footer-wrapper tbody td {
    background: @light-color;
  }
  .el-table__footer-wrapper {
    td {
      border-top: 1px solid @base-color;
    }
  }
  .el-table::before {
    background-color: @base-color;
  }
}
::v-deep .summary-table {
  .el-table::before {
    width: 0;
  }
  .el-table {
    border-radius: 4px;
  }
}
::v-deep .el-table__fixed-right {
  .el-table__fixed-header-wrapper,
  .el-table__fixed-body-wrapper {
    right: 0;
  }
}
::v-deep button.tg-span__divide-line {
  &::after {
    top: 12px;
  }
}
::v-deep .el-table__body-wrapper {
  height: calc(100% - 46px);
  table {
    min-height: 100px;
  }
  .empty-container {
    position: absolute;
    top: 50%;
    left: 50%;
  }
  .loading-container {
    position: absolute;
    top: 15%;
    left: 1%;
    background: transparent;
    .box {
      height: 100%;
    }
  }
}
::v-deep .tg-search {
  div.tg-search__box:first-child .el-input {
    width: 200px;
  }
}
</style>
