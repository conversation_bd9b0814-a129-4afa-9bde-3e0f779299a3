<template>
  <div class="competition-create">
    <div class="tg-header__subtitle tg-info__back">
      <img src="@/assets/图片/icon_menu_down_ac.png" alt @click="onBack" />
      <span @click="onBack">返回</span>
    </div>
    <el-form :model="form" ref="form" label-width="140px">
      <div class="plan base-config">
        <div class="title">基础信息</div>
        <base-config
          :data.sync="baseData"
          :key="dataKey"
          ref="baseConfig"
        ></base-config>
      </div>
      <div class="plan form-config">
        <div class="title">表单配置</div>
        <form-config
          :data.sync="formData"
          :key="dataKey"
          ref="formConfig"
        ></form-config>
      </div>
      <div class="plan authority-control">
        <div class="title">权限控制</div>
        <!-- 
          :key="dataKey" -->
        <authority-control
          :data.sync="authorityData"
          ref="authorityControl"
        ></authority-control>
      </div>
    </el-form>
    <div class="competition-create__footer">
      <el-button size="small" v-throttle="cancel">取消</el-button>
      <template v-if="$route.query.type !== 'detail'">
        <el-button size="small" v-throttle="save">保存</el-button>
        <el-button type="primary" size="small" v-throttle="publish"
          >发布</el-button
        >
      </template>
    </div>
  </div>
</template>

<script>
import competitionApi from "@/api/competition";
import baseConfig from "../components/base/baseConfig.vue";
import FormConfig from "../components/base/formConfig.vue";
import authorityControl from "../components/base/authorityControl.vue";
import { eventBus } from "../eventBus";
export default {
  components: {
    baseConfig,
    FormConfig,
    authorityControl
  },
  data() {
    return {
      dataKey: 0,
      form: {},
      baseData: {},
      formData: {},
      authorityData: {}
    };
  },
  created() {
    if (this.$route.query.id) {
      this.getMatchDetail();
    }
  },
  methods: {
    onBack() {
      this.$router.go(-1);
    },
    getMatchDetail() {
      competitionApi.matchDetail({ id: this.$route.query.id }).then((res) => {
        const { code, data, message } = res;
        if (code === 0) {
          const isAbnormalTime = (time) => {
            return time === "0001-01-01" ? "" : time;
          };
          this.baseData = {
            remark: data.remark, // 赛事描述
            cover_url: data.cover_url, // 封面图片
            title_url: data.title_url, // 头图
            back_url: data.back_url, // 背景图
            name: data.name, // 赛事名称
            area_ids: data.area_ids, // 所属校区
            area_names: data.area_names, // 所属校区
            publish_type: data.publish_type, // 发布方式
            auto_publish_time: isAbnormalTime(data.auto_publish_time), // 自动发布时间
            match_type: data.match_type, // 赛事类型
            cancel_sign_up: data.cancel_sign_up, // 是否允许取消报名
            cancel_sign_up_time: [
              isAbnormalTime(data.cancel_sign_up_start_time),
              isAbnormalTime(data.cancel_sign_up_end_time)
            ], // 允许取消报名时间
            cancel_sign_up_start_time: isAbnormalTime(
              data.cancel_sign_up_start_time
            ), // 允许取消报名开始时间
            cancel_sign_up_end_time: isAbnormalTime(
              data.cancel_sign_up_end_time
            ), // 允许取消报名结束时间
            sign_up_time: [data.sign_up_start_time, data.sign_up_end_time], // 报名时间
            sign_up_start_time: data.sign_up_start_time, // 报名开始时间
            sign_up_end_time: data.sign_up_end_time, // 报名结束时间
            hold_time: [data.hold_start_time, data.hold_end_time], // 比赛时间
            hold_start_time: data.hold_start_time, // 比赛开始时间
            hold_end_time: data.hold_end_time, // 比赛结束时间
            open_is: data.open_is, // 赛事是否公开
            auto_create_class: data.auto_create_class, // 是否自动创建班级
            course_id: data.course_id, // 班级课程
            course_name: data.course_name, // 班级课程
            department_ids: data.department_ids, // 所属校区
            department_names: data.department_names // 所属校区
          };
          const baseFormFields = [
            {
              id: "",
              isShow: data.f_rank_level,
              must_is: data.f_rank_level_must,
              source: "base",
              label: "聂道棋力",
              key: "f_rank_level_must",
              disabled: false
            },
            {
              id: "",
              isShow: data.f_department_ids,
              must_is: data.f_department_must,
              source: "base",
              label: "所属校区",
              key: "f_department_must",
              disabled: false
            }
          ];
          const customizeFormFields = data.customize_result.map((item) => ({
            id: item.id,
            must_is: item.must_is,
            source: "custom",
            label: item.name,
            key: item.id,
            disabled: true
          }));
          this.formData = {
            checkList: [...baseFormFields, ...customizeFormFields],
            checkeds: [
              ...baseFormFields.filter((i) => i.isShow === 1).map((i) => i.key),
              ...customizeFormFields.map((i) => i.key)
            ]
          };
          this.authorityData = {
            user_types: data.user_types, // 参赛用户类型
            rank_level:
              data.rank_level_limit === 2
                ? data.rank_level
                : data?.rank_level?.join(","), // 聂道棋力
            rank_level_limit: data.rank_level_limit, // 聂道棋力限制
            sign_up_num: data.sign_up_num, // 报名人数
            sign_up_num_limit: data.sign_up_num_limit // 报名人数限制
          };
          const filterFields = [
            "match_type",
            "open_is",
            "rank_level",
            "rank_level_limit",
            "department_info"
          ];
          filterFields.forEach((item) => {
            console.log(data[item], item, "data[item]");
            if (item === "department_info") {
              eventBus.$emit(
                "toCompetitionPage",
                {
                  department_names: data?.department_names?.join(","),
                  areaName: data?.area_names
                },
                item
              );
            } else {
              eventBus.$emit("toCompetitionPage", data[item], item);
            }
          });
          this.dataKey++;
        } else {
          this.$message.error(message);
        }
      });
    },
    // 获取子组件FormData
    getFormData() {
      const baseConfig = this.$refs.baseConfig;
      const formConfig = this.$refs.formConfig;
      const authorityControl = this.$refs.authorityControl;
      this.form = {
        ...baseConfig.form,
        ...formConfig.form,
        ...authorityControl.form
      };
      console.log(this.form);
    },
    verifyFormData() {
      this.getFormData();
      const {
        name,
        match_type,
        rank_level_limit,
        rank_level,
        sign_up_time,
        hold_time,
        department_ids,
        sign_up_num,
        sign_up_num_limit,
        publish_type,
        auto_publish_time,
        user_types,
        cancel_sign_up,
        cancel_sign_up_time,
        title_url,
        back_url,
        remark,
        auto_create_class,
        course_id
      } = this.form;
      console.log(this.form);
      if (!name) {
        this.$message.error("请填写赛事名称！");
        return false;
      } else if (!match_type) {
        this.$message.error("请选择赛事类型！");
        return false;
      } else if (!rank_level_limit) {
        this.$message.error("请选择聂道棋力运算语！");
        return false;
      } else if (rank_level_limit !== 4 && !rank_level) {
        this.$message.error("请选择聂道棋力！");
        return false;
      } else if (!sign_up_time?.length) {
        this.$message.error("请选择赛事报名时间！");
        return false;
      } else if (!hold_time?.length) {
        this.$message.error("请选择赛事举办时间！");
        return false;
      } else if (!department_ids?.length) {
        this.$message.error("请选择授权校区！");
        return false;
      } else if (auto_create_class === 1 && !course_id) {
        this.$message.error("请选择班级课程！");
        return false;
      } else if (sign_up_num_limit !== 4 && !sign_up_num) {
        this.$message.error("请填写赛事报名人数限制！");
        return false;
      } else if (publish_type === 2 && !auto_publish_time?.length) {
        this.$message.error("请选择自动发布时间！");
        return false;
      } else if (!user_types.length) {
        this.$message.error("请选择参赛用户类型！");
        return false;
      } else if (!cancel_sign_up) {
        this.$message.error("请选择取消报名！");
        return false;
      } else if (cancel_sign_up === 1 && !cancel_sign_up_time?.length) {
        this.$message.error("请选择允许取消报名时间！");
        return false;
      } else if (back_url && !title_url) {
        this.$message.error("请上传头图！");
        return false;
      } else if (title_url && !back_url) {
        this.$message.error("请上传背景图！");
        return false;
      } else if (!remark) {
        this.$message.error("请填写赛事描述！");
        return false;
      } else {
        return true;
      }
    },
    submitForm(status) {
      const valid = this.verifyFormData();
      if (!valid) {
        return false;
      }
      const params = this.$lodash.cloneDeep(this.form);
      if (params.publish_type === 2 && params.auto_publish_time) {
        this.$confirm(
          `当前赛事已设置于${moment(params.auto_publish_time).format(
            "YYYY年MM月DD日"
          )}自动发布，确定立即发布？`,
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          }
        ).then(() => {
          this.fn(params, status);
        });
      } else {
        this.fn(params, status);
      }
    },
    fn(params, status) {
      const checkeds = this.$refs.formConfig.checkeds;
      const checkList = this.$refs.formConfig.checkList;
      params.customize_result = checkList
        .filter((i) => i.source === "custom")
        .map((i) => ({
          id: i.id,
          must_is: i.must_is,
          name: i.label
        }));
      checkList.map((i) => {
        if (i.source === "base") {
          params[i.key] = i.must_is;
        }
      });
      params.sign_up_num = Number(params.sign_up_num);
      params.f_department_id = checkeds.includes("f_department_must") ? 1 : 2;
      params.f_rank_level = checkeds.includes("f_rank_level_must") ? 1 : 2;
      console.log(params, this.form, checkList, checkeds);
      params.status = status;
      params.rank_level =
        params.rank_level_limit === 2 ? params.rank_level : [params.rank_level];
      params.department_ids =
        typeof params.department_ids === "string"
          ? params.department_ids?.split(",")
          : params.department_ids;
      delete params.hold_time;
      delete params.sign_up_time;
      delete params.cancel_sign_up_time;
      if (this.$route.query.id) {
        params.id = this.$route.query.id;
        competitionApi.matchUpdate(params).then((res) => {
          if (res.code === 0) {
            this.$message.success("操作成功");
            this.$router.go(-1);
          } else {
            this.$message.error(res.message);
          }
        });
      } else {
        competitionApi.matchCreate(params).then((res) => {
          if (res.code === 0) {
            this.$message.success("操作成功");
            this.$router.go(-1);
          } else {
            this.$message.error(res.message);
          }
        });
      }
    },
    cancel() {
      this.$router.go(-1);
    },
    save() {
      // this.getFormData();
      this.submitForm(1);
    },
    publish() {
      console.log("publish");
      console.log(this.form);
      // this.getFormData();
      this.submitForm(3);
    }
  }
};
</script>

<style scoped lang="scss">
.competition-create {
  padding: 20px;
  background-color: #fff;
  .tg-info__back {
    margin-bottom: 16px;
    img {
      width: 10px;
      height: 6px;
      margin-right: 10px;
      transform: rotate(90deg);
      -moz-transform: rotate(90deg);
      -ms-transform: rotate(90deg);
      -o-transform: rotate(90deg);
      -webkit-transform: rotate(90deg);
      cursor: pointer;
    }
    span {
      cursor: pointer;
    }
  }
  .plan {
    padding: 20px 8px;
    background-color: #fff;
    border-bottom: 1px solid #ebeef5;
    .title {
      color: #000;
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 20px;
    }
  }
  .competition-create__footer {
    height: 100px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    background: #fff;
  }
}
</style>
