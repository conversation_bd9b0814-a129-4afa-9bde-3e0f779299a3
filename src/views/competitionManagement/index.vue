<template>
  <div class="competition-management">
    <tg-search
      :searchTitle.sync="searchTitle"
      :form.sync="tableParams"
      @reset="reset"
      :isExport="isExport"
      :loadingState="exportLoading"
      @search="searchList"
      :hasDefaultDate="true"
      @educe="exportExcel"
      class="tg-box--margin"
    ></tg-search>
    <el-row style="margin: 16px 0 0 6px; width: 100%">
      <el-col :span="24">
        <!-- <el-slider v-model="value" range show-stops :max="1000"></el-slider> -->
        <el-button
          @click="createCompetition"
          v-has="{ m: 'competition', o: 'create' }"
          type="plain"
          class="tg-button--plain"
          >新建赛事</el-button
        >
      </el-col>
    </el-row>
    <div class="tg-table__box">
      <div class="tg-box--border"></div>
      <el-table
        :data="results || []"
        class="tg-table"
        :row-key="getRowKeys"
        ref="table"
        :cell-style="{ borderRightColor: '#e0e6ed75' }"
        :header-cell-style="{ borderRightColor: '#e0e6ed75' }"
        border
      >
        <template v-for="(item, index) in table_title">
          <el-table-column
            v-if="item.show"
            :key="index"
            :prop="item.props"
            :label="item.hasCustomHeader ? '' : item.label"
            :min-width="item.width"
            :fixed="item.fixed ? true : false"
            :sortable="item.sort"
            show-overflow-tooltip
          >
            <template
              v-if="item.hasCustomHeader && item.props === 'real_sign_up_num'"
              slot="header"
            >
              报名人数
              <el-tooltip
                popper-class="tg-tooltip"
                effect="light"
                content="聂道用户/小程序游客/全部人数"
                placement="top"
              >
                <img
                  class="tg-tooltip__icon"
                  src="../../assets/图片/icon_question.png"
                  alt=""
                />
              </el-tooltip>
            </template>
            <template slot-scope="scope">
              <template v-if="item.props === 'created_at'">
                {{
                  moment(scope.row[scope.column.property]).format(
                    "YYYY-MM-DD HH:mm"
                  )
                }}
              </template>
              <template v-else-if="item.props === 'name'">
                <div class="copy_name">
                  <el-button
                    :disabled="!$_has({ m: 'competition', o: 'info' })"
                    @click="toCompetitionPage(scope.row)"
                    type="text"
                  >
                    {{ scope.row[scope.column.property] }}
                  </el-button>
                  <div v-copy="scope.row[scope.column.property]"></div>
                </div>
              </template>
              <template v-else-if="item.type === 'img'">
                <el-image
                  :src="scope.row[scope.column.property]"
                  :preview-src-list="[scope.row[scope.column.property]]"
                  style="width: 50px; height: 50px"
                ></el-image>
              </template>
              <template v-else-if="item.props === 'publish_type'">
                {{
                  scope.row[scope.column.property] === 1
                    ? "手动发布"
                    : "自动发布"
                }}
              </template>
              <template v-else-if="item.props === 'status'">
                {{
                  statusOptions.find((item) => item.id === scope.row["status"])
                    .name
                }}
              </template>
              <template v-else-if="item.props === 'department_ids'">
                <el-button type="text" @click="showDepartment(scope.row)"
                  >查看</el-button
                >
              </template>
              <template v-else-if="item.props === 'real_sign_up_num'">
                <el-button
                  :disabled="!$_has({ m: 'competition', o: 'studentList' })"
                  type="text"
                  @click="showSignUpNum(scope.row)"
                  >{{
                    scope.row.sign_up_student_num +
                    scope.row.sign_up_customer_num
                  }}/{{ scope.row.sign_up_tourist_num }}/{{
                    scope.row.sign_up_student_num +
                    scope.row.sign_up_customer_num +
                    scope.row.sign_up_tourist_num
                  }}</el-button
                >
              </template>
              <template v-else-if="item.props === 'hold_time'">
                <span>{{
                  moment(scope.row["hold_start_time"]).format("YYYY-MM-DD")
                }}</span>
                <span>&nbsp;至&nbsp;</span>
                <span>{{
                  moment(scope.row["hold_end_time"]).format("YYYY-MM-DD")
                }}</span>
              </template>
              <template v-else-if="item.props === 'sign_up_time'">
                <span>{{
                  moment(scope.row["sign_up_start_time"]).format("YYYY-MM-DD")
                }}</span>
                <span>&nbsp;至&nbsp;</span>
                <span>{{
                  moment(scope.row["sign_up_end_time"]).format("YYYY-MM-DD")
                }}</span>
              </template>
              <template v-else-if="item.props === 'match_type_name'">
                {{
                  matchTypeOptions.find(
                    (item) => item.id === scope.row["match_type"]
                  ).name
                }}
              </template>
              <span v-else>{{ scope.row[scope.column.property] }}</span>
            </template>
          </el-table-column>
        </template>
        <el-table-column fixed="right" width="200" label="操作">
          <template slot-scope="{ row }">
            <el-button
              v-has="{ m: 'competition', o: 'update' }"
              type="text"
              v-if="![2, 3, 5].includes(row.status)"
              @click="editTemplate(row)"
              >编辑</el-button
            >
            <template v-if="[2, 3].includes(row.status)">
              <el-button
                v-has="{ m: 'competition', o: 'updateStatus' }"
                type="text"
                @click="changeStatus(row, 4)"
                >下架</el-button
              >
            </template>
            <template>
              <el-button
                v-if="[1, 4].includes(row.status)"
                v-has="{ m: 'competition', o: 'updateStatus' }"
                type="text"
                @click="changeStatus(row, 3)"
                >发布</el-button
              >
              <el-button
                v-if="[1].includes(row.status)"
                v-has="{ m: 'competition', o: 'delete' }"
                type="text"
                @click="deleteCompetition(row)"
                >删除</el-button
              >
              <!-- <el-button
                v-if="[2].includes(row.status)"
                v-has="{ m: 'competition', o: 'studentExport' }"
                type="text"
                @click="exportCompetition(row)"
                >导出</el-button
              > -->
              <!-- <el-button
                type="text"
                v-if="![1].includes(row.status)"
                @click="statisticsCompetition(row)"
                >统计</el-button
              > -->
            </template>
            <!-- <el-button type="text" @click="statisticsCompetition(row)"
              >统计</el-button
            > -->
            <!-- <el-button type="text" @click="exportCompetition(row)"
              >导出</el-button
            > -->
          </template>
        </el-table-column>
        <template slot="empty">
          <div style="margin-top: 15%">
            <TgLoading v-if="loading"></TgLoading>
            <div class="empty-container" v-else>暂无数据～</div>
          </div>
        </template>
      </el-table>
      <div class="tg-pagination">
        <span class="el-pagination__total">共 {{ count }} 条</span>
        <el-pagination
          background
          layout="sizes,prev,pager,next,jumper"
          :total="count"
          :page-size="tableParams.page_size"
          :current-page.sync="tableParams.page"
          @current-change="currentChange"
          @size-change="sizeChange"
          :page-sizes="[10, 20, 50, 100]"
        ></el-pagination>
      </div>
    </div>
    <number-applicants-dialog
      :rowInfo="rowInfo"
      @refresh="getList"
      v-if="numberApplicantsDialogVisible"
      :visible.sync="numberApplicantsDialogVisible"
      :cancelReasonList="cancelReasonList"
    ></number-applicants-dialog>
    <!-- <school-tree
      :flag.sync="school_tree_visible"
      v-if="school_tree_visible"
      :id.sync="department_ids"
      :controlSchool="true"
      :checkedControlSchool="true"
      :type="'chooseSchool'"
      :use_store_options="true"
    >
    </school-tree> -->
    <saveSchoolTree
      :checkedControlSchool="true"
      v-if="school_tree_visible"
      :flag.sync="school_tree_visible"
      :id.sync="department_ids"
      :name.sync="department_name"
      :required="true"
      :has_modal="true"
      :type="'isLook'"
      :use_store_options="false"
      :is_center="true"
    >
    </saveSchoolTree>
  </div>
</template>

<script>
import competitionApi from "@/api/competition";
import saveSchoolTree from "@/components/schoolTree/saveSchoolTree.vue";
import numberApplicantsDialog from "./components/dialog/numberApplicantsDialog.vue";
import { statusOptions, matchTypeOptions } from "./config/idnex";
import { export_excel_sync_new } from "@/public/asyncExport";
import quickTime from "@/public/quickTime";
export default {
  components: {
    numberApplicantsDialog,
    saveSchoolTree
  },
  data() {
    return {
      value: [0, 1000],
      department_ids: [],
      school_tree_visible: false,
      exportLoading: false,
      numberApplicantsDialogVisible: false,
      rowInfo: {},
      statusOptions,
      matchTypeOptions,
      searchTitle: [
        {
          props: "name",
          label: "赛事名称",
          type: "input",
          show: true
        },
        {
          props: "match_index",
          label: "赛事ID",
          type: "input",
          show: true
        },
        {
          props: "match_type",
          label: "赛事类型",
          type: "mutipleSelect_filterable",
          show: true,
          selectOptions: [{ id: undefined, name: "不限" }, ...matchTypeOptions]
        },
        // {
        //   props: "department_ids",
        //   label: "授权校区",
        //   type: "school",
        //   show: true,
        //   selectOptions: [],
        //   school_choose_type: "chooseSchool",
        //   use_store_options: true
        // },
        {
          props: "status",
          label: "赛事状态",
          type: "mutipleSelect_filterable",
          show: true,
          selectOptions: [{ id: undefined, name: "不限" }, ...statusOptions]
        },
        {
          props: "sign_up_time",
          label: "报名时间",
          type: "date",
          show: true,
          width: 200,
          has_options: true
        },
        {
          props: "hold_time",
          label: "举办时间",
          type: "date",
          show: true,
          width: 200,
          has_options: true
        },
        {
          props: "sign_up_num",
          label: "报名人数范围",
          type: "num_range",
          show: true,
          selectOptions: []
        },
        {
          props: "created_time",
          label: "创建时间",
          type: "date",
          show: true,
          width: 200,
          has_options: true
        },
        {
          props: "operate_id",
          label: "创建人",
          type: "course_staff",
          show: true,
          is_leave: true,
          selectOptions: []
        }
      ],
      isExport: false,
      tableParams: {
        page: 1,
        page_size: 10,
        identity: [2, 3],
        name: "",
        match_index: "",
        operate_id: undefined,
        sign_up_time: [],
        sign_up_end_time: undefined,
        sign_up_start_time: undefined,
        created_time: [],
        created_end_time: "",
        created_start_time: "",
        hold_time: [],
        hold_end_time: "",
        hold_start_time: "",
        status: undefined,
        match_type: undefined,
        department_ids: undefined,
        sign_up_num: [],
        sign_up_num_max: "",
        sign_up_num_min: "",
        sign_up_num_max_str: "",
        sign_up_num_min_str: ""
      },
      results: [],
      count: 0,
      loading: false,
      table_title: [
        {
          props: "match_index",
          label: "赛事ID",
          show: true,
          width: 80
        },
        {
          props: "name",
          label: "赛事名称",
          show: true,
          width: 200
        },
        {
          props: "match_type_name",
          label: "赛事类型",
          show: true,
          width: 80
        },
        {
          props: "department_ids",
          label: "授权校区",
          show: true,
          width: 80
        },
        {
          props: "status",
          label: "赛事状态",
          show: true,
          width: 80
        },
        {
          props: "publish_type",
          label: "上架方式",
          show: true,
          width: 80
        },
        {
          props: "sign_up_time",
          label: "报名时间",
          show: true,
          width: 230
        },
        {
          props: "hold_time",
          label: "举办时间",
          show: true,
          width: 230
        },
        {
          props: "real_sign_up_num",
          label: "报名人数",
          show: true,
          width: 120,
          hasCustomHeader: true
        },
        {
          props: "cover_url",
          type: "img",
          label: "封面图",
          show: true,
          width: 80
        },
        {
          props: "title_url",
          type: "img",
          label: "头图",
          show: true,
          width: 80
        },
        {
          props: "back_url",
          type: "img",
          label: "背景图",
          show: true,
          width: 80
        },
        {
          props: "created_at",
          label: "创建时间",
          show: true,
          width: 160
        },
        {
          props: "operator_name",
          label: "创建人",
          show: true,
          width: 160
        }
      ],
      cancelReasonList: []
    };
  },
  computed: {
    school_id() {
      return this.$store.getters.doneGetSchoolId;
    }
  },
  watch: {
    "tableParams.match_type": {
      handler(newVal) {
        console.log(newVal, "newVal");
      }
    },
    school_id: {
      handler(newVal) {
        this.tableParams.department_ids = newVal;
        this.tableParams.page = 1;
        this.tableParams.page_size = 10;
        this.setSearchDefault();
        this.getList();
      },
      immediate: true
    }
  },
  created() {
    this.setSearchDefault();
    if (this.$_has({ m: "competition", o: "export" })) {
      this.isExport = true;
    }
    this.getCancelReason();
  },
  methods: {
    getRowKeys(row) {
      return row.match_index;
    },
    async getList() {
      const res = await competitionApi.matchList(this.tableParams);
      if (res.code === 0) {
        this.results = res.data.results;
        this.count = res.data.count;
      } else {
        this.$message.error(res.message);
      }
    },
    createCompetition() {
      this.$router.push({
        name: "competitionCreate"
      });
    },
    reset() {
      this.tableParams = {
        page: 1,
        page_size: 10,
        identity: [2, 3],
        name: "",
        match_index: "",
        operate_id: undefined,
        created_end_time: "",
        created_start_time: "",
        hold_end_time: "",
        hold_start_time: "",
        status: undefined,
        match_type: undefined,
        department_ids: this.school_id,
        sign_up_num: [],
        sign_up_num_max: "",
        sign_up_num_min: "",
        sign_up_num_max_str: "",
        sign_up_num_min_str: ""
      };
      this.getList();
    },
    setSearchDefault() {
      const pastThirty = quickTime.GetDate("pastThirty");
      this.$set(this.tableParams, "created_time", pastThirty);
      this.getParams();
    },
    getParams() {
      console.log(this.tableParams, "tableParams");
      if (this.tableParams.created_time?.length) {
        this.tableParams.created_start_time = this.tableParams.created_time[0];
        this.tableParams.created_end_time = this.tableParams.created_time[1];
      } else {
        this.tableParams.created_start_time = "";
        this.tableParams.created_end_time = "";
      }
      if (this.tableParams.sign_up_time?.length) {
        this.tableParams.sign_up_start_time = this.tableParams.sign_up_time[0];
        this.tableParams.sign_up_end_time = this.tableParams.sign_up_time[1];
      } else {
        this.tableParams.sign_up_start_time = "";
        this.tableParams.sign_up_end_time = "";
      }
      if (this.tableParams.hold_time?.length) {
        this.tableParams.hold_start_time = this.tableParams.hold_time[0];
        this.tableParams.hold_end_time = this.tableParams.hold_time[1];
      } else {
        this.tableParams.hold_start_time = "";
        this.tableParams.hold_end_time = "";
      }
      if (this.tableParams.sign_up_num?.length) {
        this.tableParams.sign_up_num_min = this.tableParams.sign_up_num[0];
        this.tableParams.sign_up_num_max = this.tableParams.sign_up_num[1];
      } else {
        this.tableParams.sign_up_num_min = "";
        this.tableParams.sign_up_num_max = "";
      }
      console.log(this.tableParams?.sign_up_num_min, "tableParams");
      this.tableParams.sign_up_num_min_str = String(
        this.tableParams?.sign_up_num_min || ""
      );
      this.tableParams.sign_up_num_max_str = String(
        this.tableParams?.sign_up_num_max || ""
      );
      if (typeof this.tableParams.department_ids === "string") {
        this.tableParams.department_ids =
          this.tableParams.department_ids.split(",");
      }
      delete this.tableParams.department_ids_name;
      return this.tableParams;
    },
    searchList() {
      this.tableParams.page = 1;
      this.getParams();
      console.log(this.tableParams, "tableParams");
      this.getList();
    },
    showDepartment(row) {
      console.log(row, "row");
      this.department_ids = row.department_ids;
      this.school_tree_visible = true;
    },
    createTemplate() {
      this.$router.push({
        name: "competitionCreate",
        query: { type: "create" }
      });
    },
    currentChange(page) {
      this.tableParams.page = page;
      this.getList();
    },
    sizeChange(size) {
      this.page = 1;
      this.tableParams.page_size = size;
      this.getList();
    },
    toCompetitionPage(row) {
      this.$router.push({
        name: "competitionDetail",
        query: { id: row.id, type: "detail" }
      });
    },
    showSignUpNum(row) {
      console.log(row, "row");
      this.rowInfo = row;
      this.numberApplicantsDialogVisible = true;
    },
    editTemplate(row) {
      this.$router.push({
        name: "competitionEdit",
        query: { id: row.id, type: "edit" }
      });
    },
    // 赛事状态更新
    changeStatus(row, status) {
      console.log(row, status, "row, status");
      const tipsTxt = {
        3: "确定发布该赛事吗？发布后不可修改基本信息！",
        4: "确定下架该赛事吗？下架后用户将不可见！"
      };
      this.$confirm(tipsTxt[status], "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        competitionApi
          .matchUpdateStatus({
            id: row.id,
            status
          })
          .then((res) => {
            if (res.code === 0) {
              this.$message.success("操作成功");
              this.getList();
            } else {
              this.$message.error(res.message);
            }
          });
      });
    },
    exportExcel() {
      const opt = {
        vm: this, // vue组件实例，
        api_url: "/api/questionnaire-service/admin/match/export", // 接口地址
        file_name: "赛事列表", // 文件名
        success_msg: "赛事列表导出成功！", // 导出成功的提示语
        error_msg: "赛事列表导出失败！", // 导出失败的提示语,
        query: {
          page: 1,
          ...this.getParams()
        }
      };
      export_excel_sync_new(opt);
    },
    // 赛事删除
    deleteCompetition(row) {
      console.log(row, "row");
      this.$confirm("确定要删除该赛事吗？删除后将无法恢复！", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        competitionApi
          .matchDelete({
            id: row.id
          })
          .then((res) => {
            if (res.code === 0) {
              this.$message.success("操作成功");
              this.getList();
            } else {
              this.$message.error(res.message);
            }
          });
      });
    },
    statisticsCompetition(row) {
      console.log(row, "row");
    },
    // 报名人数导出
    exportCompetition(row) {
      console.log(row, "row");
      const opt = {
        vm: this, // vue组件实例，
        api_url: "/api/questionnaire-service/admin/match/student/export", // 接口地址
        file_name: "报名人数列表", // 文件名
        success_msg: "报名人数列表导出成功！", // 导出成功的提示语
        error_msg: "报名人数列表导出失败！", // 导出失败的提示语,
        query: {
          page: 1,
          page_size: 10,
          name: "",
          mobile: "",
          match_index: row.match_index,
          status: undefined,
          createdAt: "",
          start_time: "",
          end_time: ""
        }
      };
      export_excel_sync_new(opt);
    },
    // 获取取消原因
    getCancelReason() {
      competitionApi.getCancelReason().then((res) => {
        if (res.code === 0) {
          this.cancelReasonList = res.data;
        } else {
          this.$message.error(res.message);
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.tg-tooltip__icon {
  width: 14px;
  vertical-align: initial;
}
</style>
