<template>
  <div class="authority-control">
    <div style="position: relative">
      <!-- <div v-if="$route.query.type === 'detail'" class="is-detail"></div> -->

      <el-form-item label="参赛用户类型" required prop="user_types">
        <el-checkbox
          label="全选"
          :disabled="isDetail"
          v-model="allSelect"
          @change="handleAllSelect"
        ></el-checkbox>
        <el-checkbox-group
          v-model="form.user_types"
          @change="handleCheckboxChange"
        >
          <el-checkbox
            :disabled="isDetail"
            :label="item.value"
            v-for="item in userTypes"
            :key="item.value"
            >{{ item.label }}</el-checkbox
          >
        </el-checkbox-group>
      </el-form-item>
      <el-form-item required label="聂道棋力限制">
        <el-select
          class="comparison-select"
          :disabled="isDetail"
          @change="
            handleFormItemChange(form.rank_level_limit, 'rank_level_limit')
          "
          v-model="form.rank_level_limit"
          placeholder="请选择聂道棋力限制"
        >
          <el-option
            v-for="item in comparisonOperator"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
        <el-select
          :key="form.rank_level_limit"
          :multiple="form.rank_level_limit === 2"
          v-if="form.rank_level_limit !== 4"
          :disabled="isDetail"
          @change="handleFormItemChange(form.rank_level, 'rank_level')"
          v-model="form.rank_level"
          placeholder="请选择聂道棋力"
          :style="{ minWidth: form.rank_level.length * 100 + 'px' }"
          class="rank-level-select"
        >
          <el-option
            v-for="item in nie_dao_level_list"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item required label="赛事报名人数限制">
        <el-select
          class="comparison-select"
          :disabled="isDetail"
          v-model="form.sign_up_num_limit"
          placeholder="请选择聂道棋力限制"
        >
          <el-option
            v-for="item in comparisonOperator.filter((item) =>
              [2, 4].includes(item.id)
            )"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
        <el-input
          v-if="form.sign_up_num_limit !== 4"
          @blur="handleSignUpNumBlur"
          :disabled="isDetail"
          v-model="form.sign_up_num"
          placeholder="请输入赛事报名人数限制"
        />
      </el-form-item>
    </div>
    <div>
      <div class="label">赛事可见范围</div>
      <div class="tg-table__box">
        <div class="tg-box--border"></div>
        <el-table
          :data="results"
          class="tg-table"
          :key="tableKey"
          ref="table"
          :cell-style="{ borderRightColor: '#e0e6ed75' }"
          :header-cell-style="{ borderRightColor: '#e0e6ed75' }"
          border
        >
          <template v-for="(item, index) in table_title">
            <el-table-column
              v-if="item.show"
              :key="index"
              :prop="item.props"
              :label="item.label"
              :min-width="item.width"
              :fixed="item.fixed ? true : false"
              :sortable="item.sort"
              :show-overflow-tooltip="item.tooltip"
            >
              <template slot-scope="{ row }">
                <template v-if="item.props === 'match_type'">
                  {{
                    matchTypeOptions.find((item) => item.id === row.match_type)
                      ?.name
                  }}
                </template>
                <template v-else-if="item.props === 'rank_level_limit'">
                  {{
                    comparisonOperator.find(
                      (item) => item.id === row.rank_level_limit
                    )?.name
                  }}
                  <template v-if="row.rank_level_limit !== 4">
                    {{
                      typeof row.rank_level !== "string"
                        ? row.rank_level.join("、")
                        : row.rank_level
                    }}
                  </template>
                </template>
                <template v-else-if="item.props === 'department_info'">
                  <!-- <div>
                    <div v-if="row.department_info?.areaName?.length">
                      所选校区区域：
                    </div>
                    <div>
                      {{ row.department_info?.areaName?.join("、") }}
                    </div>
                  </div> -->
                  <div style="margin-top: 10px">
                    <div v-if="row.department_info?.department_names?.length">
                      所选校区：
                    </div>
                    <div>
                      {{
                        row.department_info?.department_names?.replace(
                          /,/g,
                          "、"
                        )
                      }}
                    </div>
                  </div>
                </template>
                <template v-else-if="item.props === 'open_is'">
                  <div>
                    <div v-if="row.open_is === 1">是</div>
                    <div v-if="row.open_is === 2">否</div>
                  </div>
                </template>
                <template v-else-if="item.props === 'user_types'">
                  <div>
                    {{
                      form.user_types
                        .map(
                          (i) =>
                            userTypes.find((item) => item.value === i)?.label
                        )
                        .join("、")
                    }}
                  </div>
                </template>
              </template>
            </el-table-column>
          </template>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { eventBus } from "@/views/competitionManagement/eventBus";
import { matchTypeOptions, comparisonOperator } from "../../config/idnex";
import { nie_dao_level_list } from "@/public/dict";
export default {
  props: {
    data: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    isDetail() {
      return this.$route.query.type === "detail";
    }
  },
  data() {
    return {
      matchTypeOptions,
      comparisonOperator,
      nie_dao_level_list,
      tableKey: 0,
      allSelect: false,
      form: {
        user_types: [],
        sign_up_num_limit: 2,
        sign_up_num: "",
        rank_level_limit: 2,
        rank_level: []
      },
      visible: false,
      userTypes: [
        {
          label: "在读",
          value: "in_school"
        },
        {
          label: "试听",
          value: "audition"
        },
        {
          label: "休学",
          value: "out_school"
        },
        {
          label: "临时",
          value: "temp"
        },
        {
          label: "意向",
          value: "customer"
        },
        {
          label: "游客",
          value: "tourist"
        },
        {
          label: "退学",
          value: "drop_school"
        }
      ],
      results: [
        {
          match_type: "",
          department_info: "",
          rank_level: [],
          rank_level_limit: 2,
          open_is: 2,
          user_types: ""
        }
      ],
      table_title: [
        {
          props: "match_type",
          label: "赛事类型",
          show: true,
          width: 160
        },
        {
          props: "department_info",
          label: "区域",
          show: true,
          width: 160
        },
        {
          props: "rank_level_limit",
          label: "等级限制",
          show: true,
          width: 160
        },
        {
          props: "open_is",
          label: "是否公开",
          show: true,
          width: 120
        },
        {
          props: "user_types",
          label: "用户类型",
          show: true,
          width: 200
        }
      ]
    };
  },
  created() {
    setTimeout(() => {
      if (this.data.user_types) {
        this.form.user_types = this.data.user_types;
        this.results[0].user_types = this.data.user_types;
        if (this.data.user_types.length === this.userTypes.length) {
          this.allSelect = true;
        } else {
          this.allSelect = false;
        }
      }
      if (this.$route.query.id) {
        this.form = this.data;
      }
    }, 500);
    eventBus.$on("toCompetitionPage", (val, key) => {
      this.$set(this.results[0], key, val);
    });
  },
  methods: {
    handleAllSelect(val) {
      if (val) {
        this.form.user_types = this.userTypes.map((item) => item.value);
      } else {
        this.form.user_types = [];
      }
    },
    handleCheckboxChange(val) {
      if (val.length === this.userTypes.length) {
        this.allSelect = true;
      } else {
        this.allSelect = false;
      }
    },
    handleFormItemChange(row, key) {
      if (key === "match_type") {
        this.form.open_is = 1;
        eventBus.$emit("toCompetitionPage", 1, "open_is");
      }
      if (key === "rank_level_limit") {
        if (row === 2) {
          this.$set(this.form, "rank_level", []);
        } else {
          this.$set(this.form, "rank_level", "");
        }
      }
      eventBus.$emit("toCompetitionPage", row, key);
    },
    handleSignUpNumBlur() {
      function isPositiveInteger(value) {
        return Number.isInteger(Number(value)) && Number(value) > 0;
      }
      if (!isPositiveInteger(this.form.sign_up_num)) {
        this.form.sign_up_num = "";
      }
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .comparison-select {
  width: 95px;
  .el-input {
    width: 100%;
  }
}
.is-detail {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  cursor: not-allowed;
  z-index: 1000;
}
::v-deep .rank-level-select {
  .el-input {
    width: 100% !important;
  }
  .el-select__tags {
    max-width: unset !important;
  }
}
</style>
