<template>
  <div class="base-config">
    <div>
      <div style="position: relative">
        <!-- <div class="is-detail" v-if="$route.query.type === 'detail'"></div> -->
        <div style="display: flex">
          <div>
            <el-form-item required label="赛事名称">
              <el-input
                :disabled="isDetail"
                v-model="form.name"
                class="custom-input"
                maxlength="30"
                placeholder="请输入赛事名称"
              />
              <span style="font-size: 14px; color: #999; margin-left: 10px"
                >{{ form.name.length }}/30</span
              >
            </el-form-item>
            <div style="display: flex">
              <el-form-item required label="赛事类型">
                <el-select
                  class="custom-input"
                  :disabled="isDetail"
                  v-model="form.match_type"
                  @change="handleFormItemChange(form.match_type, 'match_type')"
                  placeholder="请选择赛事类型"
                >
                  <el-option
                    v-for="item in matchTypeOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </div>
            <el-form-item required label="赛事报名时间">
              <el-date-picker
                :disabled="isDetail"
                v-model="form.sign_up_time"
                type="daterange"
                @change="changeSignUpTime"
                range-separator="至"
                value-format="yyyy-MM-dd"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item required label="赛事举办时间">
              <el-date-picker
                :disabled="isDetail"
                v-model="form.hold_time"
                @change="changeHoldTime"
                type="daterange"
                range-separator="至"
                value-format="yyyy-MM-dd"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item required label="授权校区">
              <el-input
                :disabled="isDetail"
                placeholder="请选择所属校区"
                readonly
                show-word-limit
                :validate-event="false"
                @click.native="openSchoolTree"
                v-model="form.department_names"
                class="tg-select tg-select--dialog custom-input"
                @mouseenter.native="school_flag = true"
                @mouseleave.native="school_flag = false"
              >
                <img
                  slot="suffix"
                  :src="
                    !school_flag
                      ? require('@/assets/图片/icon_more.png')
                      : require('@/assets/图片/icon_more_ac.png')
                  "
                  alt=""
                  class="btn__img--dotted"
                />
              </el-input>
              <school-tree
                :flag.sync="school_tree_visible"
                v-if="school_tree_visible"
                :id.sync="form.department_ids"
                :name.sync="form.department_names"
                :areaName.sync="form.area_names"
                :areaId.sync="form.area_ids"
                @confirm="confirmDepartment"
                :type="'chooseSchool'"
                :use_store_options="false"
              >
              </school-tree>
            </el-form-item>
            <div style="display: flex">
              <el-form-item class="w-270" required label="赛事是否公开">
                <el-radio
                  :disabled="isDetail"
                  v-model="form.open_is"
                  @change="handleFormItemChange(form.open_is, 'open_is')"
                  :label="1"
                  v-has="{ m: 'competition', o: 'isShowCompetitionOpen' }"
                  >是</el-radio
                >
                <el-radio
                  v-model="form.open_is"
                  @change="handleFormItemChange(form.open_is, 'open_is')"
                  :label="2"
                  >否</el-radio
                >
              </el-form-item>
            </div>
            <div style="display: flex">
              <el-form-item required label="是否自动创建班级">
                <el-radio
                  :disabled="isDetail"
                  v-model="form.auto_create_class"
                  :label="1"
                  >是</el-radio
                >
                <el-radio
                  :disabled="isDetail"
                  v-model="form.auto_create_class"
                  :label="2"
                  >否</el-radio
                >
              </el-form-item>
              <el-form-item
                v-if="form.auto_create_class === 1"
                label="班级课程"
                required
              >
                <div class="custom--select">
                  <el-input
                    :disabled="isDetail"
                    v-model="form.course_name"
                    readonly
                    :placeholder="`请输入课程名称`"
                    @click.native="clickCourseName"
                    class="class-course"
                  >
                    <img
                      src="@/assets/图片/icon_more.png"
                      slot="suffix"
                      alt=""
                      class="more"
                    />
                  </el-input>
                  <choose-course
                    v-if="choose_course_visible"
                    type="radio"
                    :isChild="true"
                    attribute_type="class"
                    :check_id.sync="form.course_id"
                    :check_name.sync="form.course_name"
                    :check_arr.sync="course_check_arr"
                    :choose_course_visible="choose_course_visible"
                    @close="handleCourseClose"
                    :status="true"
                  ></choose-course>
                </div>
              </el-form-item>
            </div>
            <div style="display: flex">
              <el-form-item class="w-270" required label="赛事上架方式">
                <el-radio
                  :disabled="isDetail"
                  v-model="form.publish_type"
                  :label="1"
                  >手动发布</el-radio
                >
                <el-radio
                  :disabled="isDetail"
                  v-model="form.publish_type"
                  :label="2"
                  >自动发布</el-radio
                >
              </el-form-item>
              <el-form-item
                required
                v-if="form.publish_type === 2"
                label="自动发布时间"
              >
                <el-date-picker
                  :disabled="isDetail"
                  v-model="form.auto_publish_time"
                  :picker-options="autoPublishPickerOptions"
                  value-format="yyyy-MM-dd"
                  type="date"
                  placeholder="选择日期"
                >
                </el-date-picker>
              </el-form-item>
            </div>
          </div>
          <div>
            <el-form-item required label="赛事描述">
              <el-input
                type="textarea"
                :disabled="isDetail"
                maxlength="500"
                v-model="form.remark"
              ></el-input>
              <p
                style="
                  font-size: 12px;
                  color: #999;
                  margin-top: 5px;
                  text-align: right;
                "
              >
                {{ form.remark.length }}/500
              </p>
            </el-form-item>
          </div>
        </div>
        <div style="display: flex">
          <el-form-item class="w-270" required label="取消报名">
            <el-radio
              :disabled="isDetail"
              v-model="form.cancel_sign_up"
              :label="1"
              >允许</el-radio
            >
            <el-radio
              :disabled="isDetail"
              v-model="form.cancel_sign_up"
              :label="2"
              >不允许</el-radio
            >
          </el-form-item>
          <el-form-item
            required
            v-if="form.cancel_sign_up === 1"
            label="允许取消报名时间"
          >
            <el-date-picker
              v-model="form.cancel_sign_up_time"
              @change="changeCancelSignUpTime"
              :disabled="isDetail"
              type="daterange"
              range-separator="至"
              value-format="yyyy-MM-dd"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </div>
      </div>
      <div style="display: flex">
        <el-form-item label="上传封面图" class="tg-box--margin">
          <el-upload
            :disabled="$route.query.type === 'detail'"
            class="avatar-uploader"
            action="#"
            :show-file-list="false"
            accept="image/png, image/jpeg"
            :http-request="(upload) => uploadImg(upload, 'cover_url')"
          >
            <img v-if="form.cover_url" :src="form.cover_url" class="avatar" />
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
          <div style="font-size: 12px; text-align: center; color: #999">
            限制尺寸：160*160 像素
          </div>
          <div
            v-if="form.cover_url"
            style="width: 150px; display: flex; justify-content: center"
          >
            <el-button
              type="text"
              v-if="$route.query.type !== 'detail'"
              @click="handleRemove('cover_url')"
              >删除</el-button
            >
            <el-button
              type="text"
              @click="handlePictureCardPreview(form.cover_url)"
              >查看</el-button
            >
          </div>
        </el-form-item>
        <el-form-item
          :required="!!form.back_url"
          label="上传头图"
          class="tg-box--margin"
        >
          <el-upload
            :disabled="$route.query.type === 'detail'"
            class="avatar-uploader"
            action="#"
            :show-file-list="false"
            accept="image/png, image/jpeg"
            :http-request="(upload) => uploadImg(upload, 'title_url')"
          >
            <img v-if="form.title_url" :src="form.title_url" class="avatar" />
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
          <div style="font-size: 12px; text-align: center; color: #999">
            限制尺寸：750*650 像素
          </div>
          <div
            v-if="form.title_url"
            style="width: 150px; display: flex; justify-content: center"
          >
            <el-button
              v-if="$route.query.type !== 'detail'"
              type="text"
              @click="handleRemove('title_url')"
              >删除</el-button
            >
            <el-button
              type="text"
              @click="handlePictureCardPreview(form.title_url)"
              >查看</el-button
            >
          </div>
        </el-form-item>
        <el-form-item
          :required="!!form.title_url"
          label="上传背景图"
          class="tg-box--margin"
        >
          <el-upload
            :disabled="$route.query.type === 'detail'"
            class="avatar-uploader"
            action="#"
            :show-file-list="false"
            accept="image/png, image/jpeg"
            :http-request="(upload) => uploadImg(upload, 'back_url')"
          >
            <img v-if="form.back_url" :src="form.back_url" class="avatar" />
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
          <div
            v-if="form.back_url"
            style="width: 150px; display: flex; justify-content: center"
          >
            <el-button
              v-if="$route.query.type !== 'detail'"
              type="text"
              @click="handleRemove('back_url')"
              >删除</el-button
            >
            <el-button
              type="text"
              @click="handlePictureCardPreview(form.back_url)"
              >查看</el-button
            >
          </div>
        </el-form-item>
      </div>
    </div>
    <el-dialog title="预览" :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt="" />
    </el-dialog>
  </div>
</template>

<script>
import { nie_dao_level_list } from "@/public/dict";
import { matchTypeOptions, comparisonOperator } from "../../config/idnex";
import schoolTree from "@/components/schoolTree/schoolTree";
import { v4 as uuidv4 } from "uuid";
import { eventBus } from "@/views/competitionManagement/eventBus";
export default {
  components: {
    schoolTree
  },
  props: {
    data: {
      type: Object,
      default: () => {}
    }
  },
  created() {
    this.Oss.getAliyun();
  },
  mounted() {
    if (this.$route.query.id) {
      this.form = this.data;
    }
  },
  computed: {
    isDetail() {
      return this.$route.query.type === "detail";
    }
  },
  data() {
    return {
      autoPublishPickerOptions: {
        disabledDate(time) {
          // 禁用当天之前的日期
          return time.getTime() < Date.now();
        }
      },
      form: {
        area_ids: [],
        area_names: [],
        cover_url: "", // 封面图片
        title_url: "", // 头图
        back_url: "", // 背景图
        name: "", // 赛事名称
        remark: "", // 赛事描述
        department_ids: [], // 所属校区
        department_names: [], // 所属校区
        match_type: "", // 赛事类型
        rank_level_limit: 2, // 聂道棋力限制
        rank_level: "", // 聂道棋力
        sign_up_time: [], // 赛事报名时间
        sign_up_start_time: "", // 赛事报名开始时间
        sign_up_end_time: "", // 赛事报名结束时间
        hold_time: [], // 赛事举办时间
        hold_start_time: "", // 赛事举办开始时间
        hold_end_time: "", // 赛事举办结束时间
        open_is: 2, // 赛事是否公开
        auto_create_class: 1, // 是否自动创建班级
        course_id: "", // 班级课程
        course_name: "", // 班级课程
        sign_up_num_limit: 2, // 赛事报名人数限制
        sign_up_num: "", // 赛事报名人数
        publish_type: 1, // 赛事上架方式
        cancel_sign_up: 1, // 取消报名
        cancel_sign_up_time: [], // 允许取消报名时间
        cancel_sign_up_end_time: "", // 允许取消报名时间
        cancel_sign_up_start_time: "", // 允许取消报名开始时间
        auto_publish_time: "" // 自动发布时间
      },
      matchTypeOptions, // 赛事类型
      nie_dao_level_list, // 聂道棋力
      comparisonOperator, // 聂道棋力限制
      school_tree_visible: false, // 所属校区
      school_flag: false, // 所属校区
      dialogType: "add", // 图片类型
      dialogVisible: false, // 图片预览
      dialogImageUrl: "", // 图片预览地址
      choose_course_visible: false, // 选择课程弹窗
      course_check_arr: [] // 选择课程
    };
  },
  methods: {
    openSchoolTree() {
      if (this.isDetail) {
        return;
      }
      this.school_tree_visible = true;
    },
    handleFormItemChange(row, key) {
      if (key === "rank_level_limit") {
        if (row === 2) {
          this.$set(this.form, "rank_level", []);
        } else {
          this.$set(this.form, "rank_level", "");
        }
      }
      eventBus.$emit("toCompetitionPage", row, key);
    },
    // 赛事报名时间
    changeSignUpTime(val) {
      if (val) {
        this.form.sign_up_start_time = val[0];
        this.form.sign_up_end_time = val[1];
      } else {
        this.form.sign_up_start_time = "";
        this.form.sign_up_end_time = "";
      }
    },
    // 赛事举办时间
    changeHoldTime(val) {
      if (val) {
        this.form.hold_start_time = val[0];
        this.form.hold_end_time = val[1];
      } else {
        this.form.hold_start_time = "";
        this.form.hold_end_time = "";
      }
    },
    // 允许取消报名时间
    changeCancelSignUpTime(val) {
      if (val) {
        this.form.cancel_sign_up_start_time = val[0];
        this.form.cancel_sign_up_end_time = val[1];
      } else {
        this.form.cancel_sign_up_start_time = "";
        this.form.cancel_sign_up_end_time = "";
      }
    },
    // 获取图片宽高
    getImageDimensions(file) {
      return new Promise((resolve, reject) => {
        const img = new Image();
        const reader = new FileReader();

        reader.onload = (e) => {
          img.src = e.target.result;
        };

        img.onload = () => {
          resolve({
            width: img.width,
            height: img.height
          });
        };

        img.onerror = (error) => {
          reject(error);
        };

        reader.readAsDataURL(file);
      });
    },
    confirmDepartment(val) {
      const departmentInfo = {
        areaName: this.form.area_names,
        department_names: this.form.department_names
      };
      this.handleFormItemChange(departmentInfo, "department_info");
    },
    async uploadImg(upload, key) {
      const f = upload.file;
      const suffix = f.name.match(/[^.]+$/)[0];
      const name = `${uuidv4()}.${suffix}`;
      const copyFile = new File([f], name);

      const { width, height } = await this.getImageDimensions(upload.file);
      if (key === "cover_url") {
        if (!(width === 160 && height === 160)) {
          this.$message.info("请上传封面图宽高等于 160*160 的图片!");
          return false;
        }
      } else if (key === "title_url") {
        if (!(width === 750 && height === 650)) {
          this.$message.info("请上传头图宽高等于 750*650 的图片!");
          return false;
        }
      }
      this.Oss.uploadFile(copyFile).then((res) => {
        if (res.code === 0) {
          this.$set(this.form, key, res.url);
        }
      });
    },
    handleRemove(key) {
      this.form[key] = "";
    },
    handlePictureCardPreview(url) {
      this.dialogImageUrl = url;
      this.dialogVisible = true;
    },
    clickCourseName() {
      this.choose_course_visible = true;
    },
    handleCourseClose() {
      this.choose_course_visible = false;
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .el-textarea__inner {
  width: 300px;
  height: 325px;
}
.is-detail {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  cursor: not-allowed;
  z-index: 1000;
}
.avatar-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  text-align: center;
  width: 150px;
  height: 150px;
  /deep/ .el-upload {
    width: 100%;
    height: 100%;
  }
  img {
    width: 100%;
    height: 100%;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100%;
    display: block;
    height: 100%;
    line-height: 150px;
    text-align: center;
  }
}
/deep/ .el-form-item {
  margin-bottom: 8px;
}
.w-270 {
  /deep/ .el-form-item__content {
    width: 270px !important;
  }
}
.custom-input {
  width: 270px !important;
  /deep/ .el-input {
    width: 100%;
  }
}

/deep/ .comparison-select {
  width: 95px;
  .el-input {
    width: 100%;
  }
}
.custom--select {
  .more {
    width: 16px;
    vertical-align: middle;
  }
}
</style>
