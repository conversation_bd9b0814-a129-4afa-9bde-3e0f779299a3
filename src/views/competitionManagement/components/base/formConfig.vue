<template>
  <div class="form-config">
    <!-- <div class="is-detail" v-if="$route.query.type === 'detail'"></div> -->
    <div class="form-item">
      <el-form-item label="报名表单字段">
        <div style="display: flex">
          <div style="margin-right: 30px">
            <el-checkbox-group
              :disabled="!isNotDetail"
              v-model="fixedCheckeds"
              size="small"
            >
              <el-checkbox border disabled label="姓名" size="small">
              </el-checkbox>
              <el-checkbox border disabled label="手机号" size="small">
              </el-checkbox>
            </el-checkbox-group>
          </div>
          <div>
            <el-checkbox-group
              v-model="checkeds"
              :disabled="!isNotDetail"
              size="small"
              @change="changeCheckeds"
            >
              <el-checkbox
                border
                v-for="(item, index) in checkList"
                :key="item.key"
                :label="item.key"
                :disabled="item.disabled"
              >
                {{ item.label }}
                <div
                  v-if="item.source === 'custom' && isNotDetail"
                  class="el-checkbox__close"
                  @click.stop="deleteCustomField(item, index)"
                >
                  <i class="el-icon-circle-close"></i>
                </div>
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
        <el-popover placement="top" width="200" v-model="isShowAdd">
          <el-input
            v-model.trim="addName"
            :disabled="!isNotDetail"
            maxlength="10"
            show-word-limit
            size="mini"
            placeholder="请输入报名表单字段名称"
          ></el-input>
          <div style="text-align: center; margin-top: 10px">
            <el-button size="mini" type="text" @click="cancelAddCustomField"
              >取消</el-button
            >
            <el-button
              type="primary"
              size="mini"
              @keydown.enter="addCustomField"
              @click="addCustomField"
              >确定</el-button
            >
          </div>
          <el-button
            v-if="isNotDetail"
            slot="reference"
            type="text"
            icon="el-icon-circle-plus"
            >增加自定义字段</el-button
          >
        </el-popover>
      </el-form-item>
      <div v-for="item in checkList" :key="item.key">
        <el-form-item
          v-if="checkeds.includes(item.key)"
          :label="item.label + '是否必填'"
          :prop="item.key"
        >
          <el-radio :disabled="!isNotDetail" v-model="item.must_is" :label="1"
            >是</el-radio
          >
          <el-radio :disabled="!isNotDetail" v-model="item.must_is" :label="2"
            >否</el-radio
          >
        </el-form-item>
      </div>
    </div>
  </div>
</template>

<script>
import competitionApi from "@/api/competition";
export default {
  props: {
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      addCustomFieldIndex: 0,
      fixedCheckeds: ["姓名", "手机号"],
      form: {
        f_mobile: 1,
        f_name: 1
      },
      addName: "",
      isShowAdd: false,
      checkList: [
        {
          id: "",
          must_is: 1,
          source: "base",
          label: "聂道棋力",
          key: "f_rank_level_must",
          disabled: false
        },
        {
          id: "",
          must_is: 1,
          source: "base",
          label: "所属校区",
          key: "f_department_must",
          disabled: false
        }
      ],
      checkeds: []
    };
  },
  created() {
    if (JSON.stringify(this.data) !== "{}") {
      this.checkeds = this.data.checkeds;
      this.checkList = this.data.checkList;
    }
  },
  computed: {
    isNotDetail() {
      return this.$route.query.type !== "detail";
    }
  },
  methods: {
    changeCheckeds(val) {},
    addCustomField() {
      const fixedFieldNames = ["姓名", "手机号", "聂道棋力", "所属校区"];
      if (!this.addName) {
        this.$message.error("请填写自定义字段名称！");
        return;
      }
      if (fixedFieldNames.includes(this.addName)) {
        this.$message.error("字段重复，禁止添加！");
        return;
      }
      if (this.checkList.some((i) => i.label === this.addName)) {
        this.$message.error("字段重复，禁止添加！");
        return;
      }
      if (this.checkList.filter((i) => i.source === "custom").length >= 5) {
        this.$message.error("自定义字段最多只能添加5个！");
        return;
      }
      this.addCustomFieldIndex++;
      this.checkList.push({
        id: "",
        must_is: 1,
        source: "custom",
        key: String(this.addCustomFieldIndex),
        label: this.addName,
        disabled: true
      });
      this.checkeds.push(String(this.addCustomFieldIndex));
      this.cancelAddCustomField();
    },
    deleteCustomField(item, index) {
      // this.checkList = this.checkList?.filter((i) => i.key !== item.key);
      if (item.id) {
        competitionApi
          .matchCustomizeDel({
            id: item.id
          })
          .then((res) => {
            if (res.code === 0) {
              const checkedIndex = this.checkeds.indexOf(item.id);
              this.checkeds.splice(checkedIndex, 1);
              this.checkList.splice(index, 1);
            } else {
              this.$message.error(res.message);
            }
          });
      } else {
        this.checkeds.splice(index, 1);
        this.checkList.splice(index, 1);
      }
    },
    cancelAddCustomField() {
      this.isShowAdd = false;
      this.addName = "";
    }
  }
};
</script>

<style lang="less" scoped>
.form-config {
  position: relative;
}
.is-detail {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  cursor: not-allowed;
  z-index: 1000;
}
/deep/ .el-form-item__label {
  max-width: 210px !important;
  width: auto !important;
}
/deep/ .el-checkbox {
  position: relative;
  .el-checkbox__close {
    position: absolute;
    right: -8px;
    top: -8px;
    font-size: 16px;
    cursor: pointer;
  }
}
</style>
