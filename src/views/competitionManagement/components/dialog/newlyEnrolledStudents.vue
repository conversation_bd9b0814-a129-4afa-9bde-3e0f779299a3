<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :title="rowId ? '编辑报名学生' : '新增报名学生'"
      :before-close="beforeClose"
      :append-to-body="true"
    >
      <el-form :model="form" ref="form">
        <el-form-item label="添加学员" required>
          <el-input
            placeholder="请选择学员"
            readonly
            show-word-limit
            :validate-event="false"
            :disabled="rowId ? true : false"
            @click.native="openStudentDialog"
            v-model="form.student_name"
            class="tg-select tg-select--dialog"
            @mouseenter.native="school_flag = true"
            @mouseleave.native="school_flag = false"
          >
            <img
              slot="suffix"
              :src="
                !school_flag
                  ? require('@/assets/图片/icon_more.png')
                  : require('@/assets/图片/icon_more_ac.png')
              "
              alt=""
              class="btn__img--dotted"
            />
          </el-input>
        </el-form-item>
        <el-form-item label="手机号码" required>
          <el-input v-model="form.student_mobile" disabled></el-input>
        </el-form-item>
        <el-form-item
          label="所属校区"
          :required="rules.f_department_must === 1"
          v-if="rules.f_department_ids === 1"
        >
          <el-input v-model="form.department_name" disabled></el-input>
        </el-form-item>
        <el-form-item
          label="聂道棋力"
          :required="rules.f_rank_level_must === 1"
          v-if="rules.f_rank_level === 1"
        >
          <el-select
            v-model="form.rank_level"
            :disabled="nie_dao_disabled"
            placeholder="请选择聂道棋力"
          >
            <el-option
              v-for="item in nie_dao_level_list"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          :required="item.must_is === 1 ? true : false"
          v-for="item in customize_result"
          :key="item.id"
          :label="item.name"
        >
          <el-input
            v-model.trim="item.value"
            maxlength="20"
            :placeholder="`请输入${item.name}`"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button class="tg-button--plain" type="plain" @click="cancel"
          >取 消</el-button
        >
        <el-button class="tg-button--primary" type="primary" v-throttle="save"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <choose-student
      type="radio"
      v-if="student_visible"
      :check_arr.sync="student_check_arr"
      @close="student_visible = false"
      :search_val="search_val"
      :rowInfo="rowInfo"
      @confirm="studentConfirm"
      :department_id="rowInfo.department_ids"
    ></choose-student>
  </div>
</template>

<script>
import ChooseStudent from "@/components/student/chooseStudent.vue";
import competitionApi from "@/api/competition";
import { nie_dao_level_list } from "@/public/dict";
export default {
  components: {
    ChooseStudent
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    rowInfo: {
      type: Object,
      default: () => {}
    },
    rowId: {
      type: String,
      default: ""
    },
    rules: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      nie_dao_disabled: false,
      form: {
        rank_level: ""
      },
      nie_dao_level_list,
      school_tree_visible: false,
      school_flag: false,
      student_visible: false,
      student_check_arr: [],
      search_val: "",
      customize_result: []
    };
  },
  computed: {
    departmentName() {
      return this.$store.getters.doneGetSchoolName;
    },
    departmentId() {
      return this.$store.getters.doneGetSchoolId;
    }
  },
  created() {
    this.getCustomize();
  },
  methods: {
    openStudentDialog() {
      if (!this.rowId) {
        this.student_visible = true;
      }
    },
    getCustomize() {
      competitionApi
        .matchStudentCustomize({ match_index: this.rowInfo.match_index })
        .then((res) => {
          this.customize_result = res.data.map((item) => {
            return {
              id: item.id,
              name: item.name,
              must_is: item.must_is
            };
          });
          if (this.rowId) {
            this.getStudentInfo();
          }
        });
    },
    getStudentInfo() {
      if (!this.rowId) {
        return false;
      }
      competitionApi.studentDetail({ id: this.rowId }).then((res) => {
        this.form = res.data;
        this.form.student_mobile = res.data.mobile;
        this.form.department_name = res.data.department_name;
        this.form.department_id = res.data.department_id;
        this.form.student_name = res.data.name;
        this.form.rank_level = res.data.rank_level;
        this.form.student_id = res.data.student_id;
        // this.customize_result = res.data.customize_result;
        this.$set(this, "customize_result", res.data.customize_result);
        console.log(this.customize_result, "this.customize_result");
      });
    },
    beforeClose() {
      this.$emit("update:visible", false);
      this.form = {};
      this.customize_result = [];
    },
    studentConfirm() {
      const student_info = this.student_check_arr[0];
      this.form.student_name = student_info.student_base.student_name;
      this.form.student_mobile = student_info.student_base.student_mobile;
      this.form.department_name = student_info.department_name;
      this.form.rank_level = student_info.nie_dao_level;
      if (student_info.nie_dao_level) {
        this.nie_dao_disabled = true;
      }
      console.log(this.student_check_arr, "this.student_check_arr");
    },
    cancel() {
      this.customize_result = [];
      this.$emit("update:visible", false);
    },
    save() {
      const student_info = this.student_check_arr[0];
      console.log(student_info, "student_info");
      if (student_info?.student_type === "drop_school") {
        this.$message.error("该学员已退学，无法进行报名！");
        return;
      }
      const params = {
        rank_level: student_info?.nie_dao_level || this.form.rank_level,
        name:
          student_info?.student_base?.student_name || this.form.student_name,
        mobile:
          student_info?.student_base?.student_mobile ||
          this.form.student_mobile,
        department_id: student_info?.department_id || this.form.department_id,
        match_index: this.rowInfo.match_index,
        student_id: student_info?.student_id || this.form.student_id,
        customize_result: this.customize_result.map((item) => {
          return {
            id: item.id,
            value: item.value
          };
        })
      };
      if (this.rowId) {
        params.id = this.rowId;
        competitionApi.matchStudentUpdate(params).then((res) => {
          if (res.code === 0) {
            this.$message.success("操作成功");
            this.$emit("update:visible", false);
            this.$emit("refresh");
          } else {
            this.$message.error(res.message);
          }
        });
      } else {
        competitionApi.matchStudentAdd(params).then((res) => {
          if (res.code === 0) {
            this.$message.success("操作成功");
            this.$emit("update:visible", false);
            this.$emit("refresh");
          } else {
            this.$message.error(res.message);
          }
        });
      }
    }
  }
};
</script>

<style></style>
