<template>
  <el-dialog
    title="查看报名人数"
    :visible.sync="visible"
    width="80%"
    :before-close="beforeClose"
  >
    <div class="student-tab">
      <span
        :class="{ 'student-tab--active': tab === 1 }"
        @click="changeTab(1)"
        v-show="$_has({ m: 'competition', o: 'studentList' })"
        >聂道学员列表</span
      >
      <span
        :class="{ 'student-tab--active': tab === 2 }"
        @click="changeTab(2)"
        v-show="$_has({ m: 'competition', o: 'touristList' })"
        >游客列表</span
      >
    </div>
    <student-list
      v-show="tab === 1 && $_has({ m: 'competition', o: 'studentList' })"
      :rowInfo="rowInfo"
      :cancelReasonList="cancelReasonList"
    />
    <tourist-list
      v-show="tab === 2 && $_has({ m: 'competition', o: 'touristList' })"
      :rowInfo="rowInfo"
      :cancelReasonList="cancelReasonList"
    />
  </el-dialog>
</template>

<script>
import studentList from "./studentList.vue";
import touristList from "./touristList.vue";
export default {
  components: {
    studentList,
    touristList
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    rowInfo: {
      type: Object,
      default: () => {}
    },
    // 取消报名原因
    cancelReasonList: {
      type: Array,
      default: () => []
    }
  },
  computed: {},
  data() {
    return {
      tab: 1
    };
  },
  created() {
    this.tab = this.$_has({ m: "competition", o: "studentList" }) ? 1 : 2;
  },
  methods: {
    changeTab(tab) {
      this.tab = tab;
    },
    beforeClose() {
      this.$emit("update:visible", false);
      this.$emit("refresh");
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .el-dialog__body {
  padding: 16px;
}
.student-tab {
  width: 100%;
  height: 46px;
  background: #fff;
  border-radius: 4px;
  padding: 0 16px;
  // border-top: 1px solid #e0e6ed;
  // box-sizing: border-box;
  box-shadow: 0 2px 6px 0 rgba(204, 208, 217, 0.35);
  span {
    font-family: @text-famliy_semibold;
    color: @text-color_second;
    font-size: @text-size_normal;
    display: inline-block;
    height: 44px;
    border-bottom: 2px solid transparent;
    line-height: 44px;
    cursor: pointer;
    font-weight: bold;
  }
  span + span {
    margin-left: 32px;
  }
  span.student-tab--active {
    color: #2d80ed;
    border-color: #2d80ed;
  }
}
</style>
