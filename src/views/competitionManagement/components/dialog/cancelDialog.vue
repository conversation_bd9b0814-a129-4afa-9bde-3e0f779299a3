<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      title="取消报名"
      :before-close="beforeClose"
      :append-to-body="true"
    >
      <el-form :model="form" ref="form" :rules="rules">
        <el-form-item label="用户姓名" required>
          <el-input
            placeholder="请选择学员"
            readonly
            show-word-limit
            :validate-event="false"
            disabled
            v-model="form.student_name"
            class="tg-select tg-select--dialog"
            @mouseenter.native="school_flag = true"
            @mouseleave.native="school_flag = false"
          >
            <img
              slot="suffix"
              :src="
                !school_flag
                  ? require('@/assets/图片/icon_more.png')
                  : require('@/assets/图片/icon_more_ac.png')
              "
              alt=""
              class="btn__img--dotted"
            />
          </el-input>
        </el-form-item>
        <el-form-item label="手机号码" required>
          <el-input v-model="form.student_mobile" disabled></el-input>
        </el-form-item>
        <el-form-item label="取消原因" prop="cancel_reason_id" required>
          <el-select
            v-model="form.cancel_reason_id"
            placeholder="请选择取消原因"
          >
            <el-option
              v-for="item in cancelReasonList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="取消说明"
          prop="cancel_explanation"
          :required="cancelInfo.must === 1"
        >
          <el-input
            v-model="form.cancel_explanation"
            placeholder="请输入取消说明"
            type="textarea"
            :rows="3"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button class="tg-button--plain" type="plain" @click="cancel"
          >取 消</el-button
        >
        <el-button
          class="tg-button--primary"
          type="primary"
          :loading="loading"
          v-throttle="save"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import competitionApi from "@/api/competition";
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    rowId: {
      type: String,
      default: ""
    },
    cancelReasonList: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      nie_dao_disabled: false,
      form: {
        rank_level: "",
        cancel_reason_id: "",
        cancel_explanation: ""
      },
      school_tree_visible: false,
      school_flag: false,
      student_check_arr: [],
      search_val: "",
      rules: {
        cancel_reason_id: [
          { required: true, message: "请选择取消原因", trigger: "change" }
        ],
        cancel_explanation: [
          {
            required: false,
            message: "请输入取消说明",
            trigger: "blur"
          }
        ]
      }
    };
  },
  computed: {
    departmentName() {
      return this.$store.getters.doneGetSchoolName;
    },
    departmentId() {
      return this.$store.getters.doneGetSchoolId;
    },
    cancelInfo() {
      return (
        this.cancelReasonList.find(
          (item) => item.id === this.form.cancel_reason_id
        ) || {
          must: 0
        }
      );
    }
  },
  mounted() {
    this.getStudentInfo();
  },
  methods: {
    getStudentInfo() {
      competitionApi.studentDetail({ id: this.rowId }).then((res) => {
        const { data } = res;
        this.form = {
          ...this.form,
          student_mobile: data.mobile,
          department_id: data.department_id,
          student_name: data.name,
          rank_level: data.rank_level,
          student_id: data.student_id,
          cancel_reason_id: data.cancel_reason_id || "",
          cancel_explanation: data.cancel_explanation || ""
        };
      });
    },
    beforeClose() {
      this.$emit("update:visible", false);
      this.form = {};
    },
    cancel() {
      this.$emit("update:visible", false);
    },
    save() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit("cancel", this.rowId, this.form);
        }
      });
    }
  }
};
</script>

<style></style>
