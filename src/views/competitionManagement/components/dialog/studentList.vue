<template>
  <div>
    <div>
      <tg-search
        :searchTitle.sync="searchTitle"
        :form.sync="tableParams"
        :showNum="4"
        @reset="reset"
        @educe="exportExcel"
        :isExport="isExport"
        @search="searchList"
        class="tg-box--margin"
      ></tg-search>
      <el-row style="margin: 16px 0 0 6px; width: 100%">
        <el-col :span="24">
          <template v-if="rowInfo.status === 2">
            <el-button
              v-has="{ m: 'competition', o: 'studentAdd' }"
              @click="createCompetition"
              type="plain"
              class="tg-button--plain"
              >新增报名学员</el-button
            >
          </template>
        </el-col>
      </el-row>
      <div class="tg-table__box">
        <div class="tg-box--border"></div>
        <el-table
          :data="results || []"
          class="tg-table"
          :row-key="getRowKeys"
          ref="table"
          :cell-style="{ borderRightColor: '#e0e6ed75' }"
          :header-cell-style="{ borderRightColor: '#e0e6ed75' }"
          border
        >
          <template v-for="(item, index) in table_title">
            <el-table-column
              v-if="item.show"
              :key="index"
              :prop="item.props"
              :label="item.label"
              :min-width="item.width"
              :fixed="item.fixed ? true : false"
              :sortable="item.sort"
              :show-overflow-tooltip="item.tooltip"
            >
              <template slot-scope="scope">
                <template v-if="item.props === 'sign_up_time'">
                  {{
                    moment(scope.row[scope.column.property]).format(
                      "YYYY-MM-DD HH:mm:ss"
                    )
                  }}
                </template>
                <template v-else-if="item.props === 'name'">
                  <div class="copy_name">
                    <el-button @click="toStudentPage(scope.row)" type="text">
                      {{ scope.row[scope.column.property] }}
                    </el-button>
                    <div v-copy="scope.row[scope.column.property]"></div>
                  </div>
                </template>
                <template v-else-if="item.props === 'student_type'">
                  {{ scope.row.student_type | student_type_filter }}
                </template>
                <template v-else-if="item.props === 'source'">
                  {{
                    scope.row[scope.column.property] === 1 ? "天工" : "小程序"
                  }}
                </template>
                <template v-else-if="item.props === 'identity'">
                  {{
                    scope.row["identity"] === 1
                      ? "游客"
                      : scope.row["identity"] === 2
                      ? "意向客户"
                      : "学员"
                  }}
                </template>
                <template v-else-if="item.props === 'status'">
                  {{
                    scope.row[scope.column.property] === 1 ? "已报名" : "已取消"
                  }}
                </template>
                <template v-else-if="item.props === 'cancel_reason_id'">
                  {{
                    cancelReasonList.find(
                      (item) => item.id === scope.row[scope.column.property]
                    )?.name
                  }}
                </template>
                <span v-else>{{ scope.row[scope.column.property] }}</span>
              </template>
            </el-table-column>
          </template>
          <el-table-column fixed="right" width="200" label="操作">
            <template slot-scope="{ row }">
              <template v-if="rowInfo.status === 2">
                <el-button
                  type="text"
                  v-has="{ m: 'competition', o: 'studentUpdate' }"
                  @click="edit(row)"
                  >{{ row.status === 2 ? "报名" : "编辑" }}</el-button
                >
                <template v-if="row.status !== 2">
                  <el-button
                    type="text"
                    v-has="{ m: 'competition', o: 'studentUpdateStatus' }"
                    v-if="rowInfo.cancel_sign_up === 1 && isInCancelSignUpTime"
                    @click="cancle(row)"
                    >取消报名</el-button
                  >
                </template>
              </template>
            </template>
          </el-table-column>
          <template slot="empty">
            <div style="margin-top: 15%">
              <TgLoading v-if="loading"></TgLoading>
              <div class="empty-container" v-else>暂无数据～</div>
            </div>
          </template>
        </el-table>
        <div class="tg-pagination">
          <span class="el-pagination__total">共 {{ count }} 条</span>
          <el-pagination
            background
            layout="sizes,prev,pager,next,jumper"
            :total="count"
            :page-size="tableParams.page_size"
            :current-page.sync="tableParams.page"
            @current-change="currentChange"
            @size-change="sizeChange"
            :page-sizes="[10, 20, 50, 100]"
          ></el-pagination>
        </div>
      </div>
    </div>
    <newly-enrolled-students
      :visible.sync="student_visible"
      v-if="student_visible"
      :rowInfo="rowInfo"
      @refresh="getList"
      :rules="rules"
      :rowId="rowId"
    ></newly-enrolled-students>
    <cancel-dialog
      v-if="rowId"
      :visible.sync="cancel_visible"
      :rowId="rowId"
      :cancelReasonList="cancelReasonList"
      @refresh="getList"
      @cancel="cancelStuatus"
      :loading="cancel_loading"
    ></cancel-dialog>
  </div>
</template>

<script>
import competitionApi from "@/api/competition";
import { export_excel_sync_new } from "@/public/asyncExport";
import newlyEnrolledStudents from "./newlyEnrolledStudents.vue";
import cancelDialog from "./cancelDialog.vue";
const student_type = [
  { id: "audition", name: "试听" },
  { id: "drop_school", name: "退学" },
  { id: "in_school", name: "在读" },
  { id: "out_school", name: "休学" },
  { id: "temp", name: "临时" }
];
export default {
  components: {
    newlyEnrolledStudents,
    cancelDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    rowInfo: {
      type: Object,
      default: () => {}
    },
    // 取消报名原因
    cancelReasonList: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    // 判断是否在允许取消报名的时间范围内
    isInCancelSignUpTime() {
      const startDateStr = this.rowInfo.cancel_sign_up_start_time;
      const endDateStr = this.rowInfo.cancel_sign_up_end_time;
      const targetDateStr = moment().format("YYYY-MM-DD");

      // 将日期字符串转换为 Date 对象
      const startDate = new Date(startDateStr);
      const endDate = new Date(endDateStr);
      const targetDate = new Date(targetDateStr);

      // 判断目标日期是否在范围内
      if (targetDate >= startDate && targetDate <= endDate) {
        return true;
      } else {
        return false;
      }
    }
  },
  data() {
    return {
      tab: 1,
      rules: {},
      customHeader: [],
      isExport: false,
      searchTitle: [
        {
          props: "name",
          label: "用户姓名",
          type: "input",
          show: true
        },
        {
          props: "student_number",
          label: "学号",
          type: "input",
          show: true
        },
        {
          props: "mobile",
          label: "手机号",
          type: "input",
          show: true
        },
        // 学员状态
        {
          props: "student_type",
          label: "学员状态",
          type: "select",
          show: false,
          selectOptions: [
            { id: "", name: "全部" },
            { id: "audition", name: "试听" },
            { id: "drop_school", name: "退学" },
            { id: "in_school", name: "在读" },
            { id: "out_school", name: "休学" },
            { id: "temp", name: "临时" }
          ]
        },
        {
          props: "status",
          label: "报名状态",
          type: "mutipleSelect_filterable",
          show: false,
          selectOptions: [
            { id: 1, name: "已报名" },
            { id: 2, name: "已取消" }
          ]
        },
        {
          props: "identity",
          label: "报名身份",
          type: "select",
          show: false,
          selectOptions: [
            { id: "", name: "全部" },
            { id: 3, name: "学员" },
            { id: 2, name: "意向客户" }
          ]
        },
        {
          props: "createdAt",
          label: "报名时间",
          type: "date",
          show: false,
          width: 200,
          has_options: true
        }
      ],
      student_visible: false,
      student_check_arr: [],
      tableParams: {
        page: 1,
        page_size: 10,
        identity: undefined,
        name: "",
        mobile: "",
        status: undefined,
        createdAt: [],
        start_time: "",
        end_time: "",
        student_type: ""
      },
      table_title: [
        {
          props: "name",
          label: "用户姓名",
          show: true,
          tooltip: true,
          width: 160
        },
        {
          props: "student_number",
          label: "学号",
          show: true,
          tooltip: true,
          width: 100
        },
        {
          props: "mobile",
          label: "手机号",
          show: true,
          tooltip: true,
          width: 140
        },
        {
          props: "student_type",
          label: "学员状态",
          show: true,
          tooltip: true,
          width: 100
        },
        {
          props: "rank_level",
          label: "聂道棋力",
          show: true,
          tooltip: true,
          width: 80
        },
        {
          props: "department_name",
          label: "所属校区",
          show: true,
          tooltip: true,
          width: 160
        },
        {
          props: "status",
          label: "报名状态",
          show: true,
          tooltip: true,
          width: 80
        },
        {
          props: "sign_up_time",
          label: "报名时间",
          show: true,
          tooltip: true,
          width: 165
        },
        {
          props: "identity",
          label: "报名身份",
          show: true,
          tooltip: true,
          width: 80
        },
        {
          props: "source",
          label: "报名来源",
          show: true,
          tooltip: true,
          width: 80
        },
        {
          props: "operator_name",
          label: "操作人",
          show: true,
          tooltip: true,
          width: 100
        },
        // 取消报名原因
        {
          props: "cancel_reason_id",
          label: "取消原因",
          show: true,
          tooltip: true,
          width: 100
        },
        // 取消报名说明
        {
          props: "cancel_explanation",
          label: "取消说明",
          show: true,
          tooltip: true,
          width: 100
        }
      ],
      results: [],
      count: 0,
      loading: false,
      cancel_visible: false,
      rowId: "",
      cancel_loading: false
    };
  },
  filters: {
    student_type_filter(value) {
      return student_type.find((item) => item.id === value)?.name || "";
    }
  },
  created() {
    if (this.$_has({ m: "competition", o: "studentExport" })) {
      this.isExport = true;
    }
    if (this.$_has({ m: "competition", o: "studentList" })) {
      this.getList();
    }
  },
  methods: {
    changeTab(tab) {
      this.tab = tab;
    },
    beforeClose() {
      this.$emit("update:visible", false);
      this.$emit("refresh");
    },
    reset() {
      this.tableParams = {
        page: 1,
        page_size: 10,
        name: "",
        mobile: "",
        identity: undefined,
        match_index: this.rowInfo.match_index,
        status: undefined,
        createdAt: [],
        start_time: "",
        end_time: "",
        student_type: "",
        student_number: ""
      };
      this.getList();
    },
    searchList() {
      this.tableParams.page = 1;
      if (this.tableParams.createdAt?.length) {
        this.tableParams.start_time = this.tableParams.createdAt[0];
        this.tableParams.end_time = this.tableParams.createdAt[1];
      } else {
        this.tableParams.start_time = "";
        this.tableParams.end_time = "";
      }
      this.getList();
    },
    getList() {
      this.tableParams.match_index = this.rowInfo.match_index;
      competitionApi.matchStudentList(this.tableParams).then((res) => {
        if (res.code === 0) {
          if (res.data.results.list.length) {
            this.customHeader = res.data.results.list[0]?.customize_result.map(
              (i) => ({
                props: i.id,
                label: i.name,
                show: true,
                tooltip: true,
                width: 190
              })
            );
          }
          this.rules = {
            f_department_ids: res.data.results.f_department_ids,
            f_department_must: res.data.results.f_department_must,
            f_rank_level: res.data.results.f_rank_level,
            f_rank_level_must: res.data.results.f_rank_level_must
          };
          for (let i = 0; i < this.table_title.length; i++) {
            if (
              this.table_title[i].props === "rank_level" &&
              res.data.results.f_rank_level === 2
            ) {
              this.table_title[i].show = false;
            }
            if (
              this.table_title[i].props === "department_name" &&
              res.data.results.f_department_ids === 2
            ) {
              this.table_title[i].show = false;
            }
          }
          console.log(this.table_title, this.customHeader, "this.table_title");
          const isRepeat = this.table_title.some((i) =>
            this.customHeader.some((j) => j.props === i.props)
          );
          if (isRepeat) {
            this.table_title = [...this.table_title];
          } else {
            this.table_title = [...this.table_title, ...this.customHeader];
          }
          this.results = res.data.results.list.map((i) => {
            for (const item of i.customize_result) {
              i[item.id] = item.value;
            }
            return {
              ...i
            };
          });
          this.count = res.data.count;
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    exportExcel() {
      const opt = {
        vm: this, // vue组件实例，
        api_url: "/api/questionnaire-service/admin/match/student/export", // 接口地址
        file_name: "报名人数列表", // 文件名
        success_msg: "报名人数列表导出成功！", // 导出成功的提示语
        error_msg: "报名人数列表导出失败！", // 导出失败的提示语,
        query: {
          page: 1,
          match_index: this.rowInfo.match_index,
          ...this.tableParams
        }
      };
      export_excel_sync_new(opt);
    },
    currentChange(page) {
      this.tableParams.page = page;
      this.getList();
    },
    sizeChange(size) {
      this.tableParams.page_size = size;
      this.getList();
    },
    createCompetition() {
      this.rowId = "";
      this.student_visible = true;
    },
    edit(row) {
      this.student_visible = true;
      this.rowId = row.id;
    },
    cancle(row) {
      this.rowId = row.id;
      this.cancel_visible = true;
    },
    cancelStuatus(id, form) {
      this.cancel_loading = true;
      competitionApi
        .matchStudentUpdateStatus({
          id,
          status: 2,
          ...form
        })
        .then((res) => {
          if (res.code === 0) {
            this.cancel_loading = false;
            this.$message.success("取消报名成功");
            this.cancel_visible = false;
            this.rowId = "";
            this.getList();
          } else {
            this.cancel_loading = false;
            this.$message.error(res.msg);
          }
        })
        .finally(() => {
          this.cancel_loading = false;
        });
    },
    getRowKeys(row) {
      return row.id;
    },
    toStudentPage(row) {
      const id = row.student_id;
      const department_id = row.department_id ?? "";
      if (row.identity === 3) {
        this.$router.push({
          name: `studentInforDetails`,
          query: { id, department_id }
        });
      } else {
        this.$router.push({
          name: `marketStudent`,
          query: { id, department_id }
        });
      }
    }
  }
};
</script>

<style lang="less" scoped></style>
