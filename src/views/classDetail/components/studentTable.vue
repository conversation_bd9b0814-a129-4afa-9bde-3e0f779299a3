<template>
  <div class="container">
    <tg-search
      :searchTitle.sync="searchTitle"
      :form.sync="searchInfo"
      :showNum="4"
      @reset="reset"
      @search="search"
      class="tg-box--margin"
    ></tg-search>
    <div class="search_right_box d-if tg-box--margin tg-box--width">
      <el-button
        v-has="{ m: 'classroom', o: 'divid' }"
        type="plain"
        class="tg-button--plain"
        @click="joinClassBatch"
        >入班</el-button
      >
      <el-button
        type="plain"
        v-has="{ m: 'classroom', o: 'shift' }"
        :disabled="checked_student.length > 0 ? false : true"
        :class="{ disabled: checked_student.length > 0 ? false : true }"
        class="tg-button--plain"
        @click="turnClassBatch"
        >转班</el-button
      >
      <el-button
        v-has="{ m: 'classroom', o: 'shift' }"
        type="plain"
        :disabled="checked_student.length > 0 ? false : true"
        :class="{ disabled: checked_student.length > 0 ? false : true }"
        class="tg-button--plain"
        @click="litreClassBatch"
        >升班</el-button
      >
      <el-button
        v-if="visible && searchInfo.status === 'is_end'"
        type="plain"
        :disabled="checked_student.length > 0 ? false : true"
        :class="{ disabled: checked_student.length > 0 ? false : true }"
        class="tg-button--plain"
        @click="sendSurvey"
        >推送问卷调查</el-button
      >

      <el-button
        type="plain"
        v-has="{ m: 'class', o: 'class_notice' }"
        :disabled="checked_student.length > 0 ? false : true"
        :class="{ disabled: checked_student.length > 0 ? false : true }"
        class="tg-button--plain"
        @click="batchSendContent('class_notice')"
        >批量发送班级通知</el-button
      >
    </div>
    <div class="tg-table__box">
      <div class="tg-box--border"></div>
      <el-table
        ref="table"
        :data="tableData"
        tooltip-effect="dark"
        :row-key="getRowKeys"
        class="tg-table"
        @selection-change="handleSelectionChange"
        v-loading="is_loading"
      >
        <el-table-column
          type="selection"
          width="50"
          :reserve-selection="true"
        ></el-table-column>
        <el-table-column
          v-for="(item, index) in table_title"
          :key="index"
          :prop="item.props"
          :label="item.label"
          :min-width="item.width"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span
              v-if="item.props === 'student_name'"
              @click="getStuDetail(scope.row)"
              style="color: #2d80ed; cursor: pointer"
            >
              {{ scope.row.student_name }}</span
            >
            <div v-else-if="item.props === 'ylb_status'">
              <ylb-tag :ylb_status="scope.row.ylb_status"></ylb-tag>
            </div>
            <div v-else-if="item.props === 'student_mobile'">
              <mobileHyposensitization
                :mobileTemInfo="{
                  row: scope.row,
                  has_eye_limit: scope.row?.has_eye_limit,
                  mobile: scope.row.student_mobile
                }"
              ></mobileHyposensitization>
            </div>
            <span v-else>{{ scope.row[item.props] }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right">
          <template slot-scope="scope">
            <el-dropdown v-if="searchInfo.status !== 'wait_in_classroom'">
              <span
                class="el-dropdown-link tg-table--operate"
                style="cursor: pointer"
              ></span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  v-has="{ m: 'classroom', o: 'shift' }"
                  @click.native="outClass(scope.row)"
                >
                  <span>出班</span>
                </el-dropdown-item>
                <el-dropdown-item
                  v-has="{ m: 'classroom', o: 'shift' }"
                  @click.native="turnClass(scope.row)"
                >
                  <span>转班</span>
                </el-dropdown-item>
                <el-dropdown-item
                  v-has="{ m: 'class', o: 'class_notice' }"
                  @click.native="sendContent(scope.row, 'class_notice')"
                  type="text"
                  size="small"
                  ><span>发送班级通知</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="tg-pagination">
        <span class="el-pagination__total">共 {{ total }} 条</span>
        <el-pagination
          background
          layout="prev, pager, next,jumper"
          :total="total"
          :page-size="page_size"
          :current-page="page"
          @current-change="currentChange"
        ></el-pagination>
      </div>
    </div>
    <!-- <transferClasses
      @closeTransferDialog="closeTransferDialog"
      v-if="transferDialog"
      :notFind="true"
      :rowData="classRoomInfo"
      :testData="testData"
    ></transferClasses> -->
    <choose-class
      check_id
      check_name
      :type="operation_type"
      :course_up_id="classRoomInfo.course_id"
      :department_id="classRoomInfo.department_id"
      :department_name="classRoomInfo.department_name"
      :classroom_id="classRoomInfo.id"
      :excludeData="excludeData"
      :student_id="students_id"
      :choose_class_visible="showChooseClass"
      @close="closeChooseClassDio"
      v-if="showChooseClass"
    ></choose-class>
    <editorDialog
      v-if="editorDialogVisible"
      :department_id="editorDialogDepartmentId"
      @close="editorDialogVisible = false"
      @confirm="editorDialogConfirm"
    ></editorDialog>
  </div>
</template>

<script>
import classManagementApi from "@/api/classManagement";
// import transferClasses from "@/views/classManagement/transferClasses.vue";
import timeFormat from "@/public/timeFormat";
import classroomManagement from "@/api/classroomManagement";
import chooseClass from "@/views/classManagement/chooseClass";
import tgSearch from "@/components/search/search.vue";
import discountApi from "@/api/discount";
import appletResource from "@/api/appletResource"; // 资料库
import editorDialog from "@/views/classManagement/editorDialog.vue";

export default {
  components: {
    // transferClasses,
    chooseClass,
    tgSearch,
    editorDialog
  },
  data() {
    return {
      searchInfo: {
        date: "", // 入班日期
        studentName: "", // 学员姓名
        status: "in_classroom", // 学员状态
        student_mobile: ""
      },
      visible: false,
      is_loading: false,
      editorDialogVisible: false,
      operation_type: "",
      tableData: [],
      checked_student: [],
      table_title: [
        {
          props: "student_name",
          label: "学员姓名",
          width: 140
        },
        {
          props: "ylb_status",
          label: "元萝卜用户",
          width: 120
        },
        {
          props: "app_bind_type",
          label: "学生端",
          width: 120
        },
        {
          props: "student_number",
          label: "学号",
          width: 120
        },
        {
          props: "student_mobile",
          label: "手机号",
          width: 120
        },
        {
          props: "student_gender",
          label: "性别",
          width: 100
        },
        {
          props: "student_cash",
          label: "剩余课(课时)",
          width: 140
        },
        {
          props: "enter_time",
          label: "入班日期",
          width: 140
        },
        {
          props: "out_time",
          label: "出班日期",
          width: 140
        },
        {
          props: "memo",
          label: "入班原因",
          width: 200
        }
      ],
      total: 0,
      page_size: 10,
      page: 1,
      transferDialog: false,
      showChooseClass: false,
      testData: [],
      searchTitle: [
        {
          props: "studentName",
          label: "学员姓名",
          maxlength: 10,
          type: "input",
          show: true
        },
        {
          props: "student_mobile",
          label: "手机号",
          type: "input",
          maxlength: 11,
          show: true,
          placeholder: "请输入学员手机号"
        },
        { props: "date", label: "入班日期", type: "date", show: true },
        {
          props: "status",
          label: "学员状态",
          type: "select",
          show: true,
          selectOptions: []
        }
      ],
      students_id: [],
      excludeData: {},
      isBatchSend: false,
      send_type: "",
      editorDialogDepartmentId: "",
      rowData: {},
      send_types: {
        course_summary: "课程总结",
        class_notice: "班级通知",
        parent_class: "家长课堂"
      }
    };
  },
  computed: {
    classRoomInfo() {
      return this.$store.getters.doneGetSchoolServiceClassroomInfo;
    },
    studentRefresh() {
      return this.$store.getters.doneGetChangeOfClassRefresh;
    }
  },
  watch: {
    studentRefresh(val) {
      if (val) {
        this.search();
        this.$refs.table.clearSelection();
        this.$store.commit("setChangeOfClassRefresh", false);
      }
    },
    "searchInfo.status"() {
      this.search();
    }
  },
  methods: {
    sendContent(row, type) {
      this.isBatchSend = false;
      this.send_type = type;
      this.editorDialogDepartmentId = row.department_id;
      this.rowData = row;
      this.editorDialogVisible = true;
    },
    batchSendContent(type) {
      this.isBatchSend = true;
      this.send_type = type;
      const oneStu = this.checked_student[0];
      this.editorDialogDepartmentId = oneStu.department_id;
      this.editorDialogVisible = true;
    },
    // 发送学员报告
    editorDialogConfirm(content) {
      console.log("content :>> ", content);
      const { send_type, send_types } = this;
      // 询问框提示
      this.$confirm(`确定发送${send_types[send_type]}吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        const data = {
          classroom_id: "",
          classroom_name: "",
          student_id: "",
          student_gender: "",
          student_name: ""
        };
        let students = [];

        if (this.checked_student.length > 0 && this.isBatchSend) {
          // 批量发送
          students = this.checked_student.map((item) => {
            return {
              student_gender: item.student_gender,
              student_id: item.student_id,
              student_name: item.student_name
            };
          });
          data.classroom_id = this.checked_student[0].classroom_id;
          data.classroom_name = this.checked_student[0].classroom_name;
        } else {
          const {
            classroom_id,
            classroom_name,
            student_id,
            student_gender,
            student_name
          } = this.rowData;
          data.classroom_id = classroom_id;
          data.classroom_name = classroom_name;
          students.push({
            student_gender,
            student_id,
            student_name
          });
        }
        console.log(this.checked_student[0]);
        const params = {
          classroom: {
            classroom_id: data.classroom_id,
            classroom_name: data.classroom_name
          },
          content,
          type: send_type,
          students
        };
        appletResource
          .feedbackSend(params)
          .then((res) => {
            if (res.data.code === 0) {
              this.$message.success("发送成功！");
              this.editorDialogVisible = false;
              // 清除选中
              this.$refs.table.clearSelection();
            } else {
              this.$message.error(res.data.message);
            }
          })
          .catch((err) => {
            console.error(err);
            this.$message.error("发送失败！");
          });
      });
    },
    getSurveyUsable() {
      const { department_id } = this.classRoomInfo;
      discountApi
        .getSurveyUsable({
          survey_type: "graduation",
          campus_id: department_id
        })
        .then((res) => {
          const { code, data } = res.data;
          if (code === 0) {
            if (data.survey_id) {
              this.visible = true;
            }
          }
        });
    },
    sendSurvey() {
      /// 询问框
      this.$confirm(`您确认要给选中的学员推送问卷调查?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        const { department_id } = this.classRoomInfo;
        const stu_ids = this.checked_student.map((item) => item.student_id);
        discountApi
          .surveySend({
            survey_type: "graduation",
            campus_id: department_id,
            student_id: stu_ids
          })
          .then((res) => {
            const { code, message } = res.data;
            if (code === 0) {
              this.$refs.table.clearSelection();
              this.$message.success("推送成功！");
            } else {
              this.$message.error(message);
            }
          });
      });
    },
    getStuDetail(row) {
      const id = row.student_id;
      const customer_id = row?.student_base?.customer_id ?? "";
      const department_id = row.department_id ?? "";
      this.$router.push({
        name: `studentInforDetails`,
        query: { id, customer_id, department_id }
      });
    },
    // 获取学生状态
    getStudentStatus() {
      classManagementApi.getSchoolStudentStatus().then((res) => {
        if (+res.status === 200 && res.data) {
          const statusOptions = [];
          for (const k in res.data) {
            const obj = {
              name: res.data[k],
              id: k
            };
            statusOptions.push(obj);
          }
          this.searchTitle[3].selectOptions = statusOptions;
        }
      });
    },
    searchVal() {
      this.page = 1;
      this.search();
    },
    // 查询
    search() {
      const query = {
        page: this.page,
        page_size: this.page_size,
        classroom_id: this.$route.query.id,
        show_leave_student: true,
        student_mobile: this.searchInfo.student_mobile,
        status:
          +this.searchInfo.status.length === 0
            ? ["in_classroom", "wait_in_classroom", "out_classroom", "is_end"]
            : this.searchInfo.status
      };
      if (this.searchInfo.date) {
        query.enter_begin_time = this.searchInfo.date[0];
        query.enter_over_time = this.searchInfo.date[1];
      }
      if (this.searchInfo.studentName) {
        query.student_name = this.searchInfo.studentName;
      }
      this.is_loading = true;
      classManagementApi
        .GetSchoolServiceClassroomStudentList(query)
        .then((res) => {
          // console.log(res);
          this.is_loading = false;
          if (+res.status === 200 && res.data.results) {
            this.tableData = res.data.results.map((item) => {
              return {
                ...item,
                enter_time: timeFormat.GetTime(item.enter_time),
                out_time: timeFormat.GetTime(item.out_time),
                student_gender: item.student_gender === "female" ? "女" : "男"
              };
            });
          } else {
            this.tableData = [];
          }
          this.total = res.data.count;

          this.getSurveyUsable(); // 获取推送问卷可用状态
        })
        .catch(() => {
          this.is_loading = false;
        });
    },
    // 重置
    reset() {
      for (const k in this.searchInfo) {
        if (k !== "status") {
          this.searchInfo[k] = "";
        }
      }
      // this.searchInfo.status = "in_classroom";
      this.page = 1;
      this.search();
    },
    getRowKeys() {
      return this.$uuid.v1();
    },
    // 获取选中项
    handleSelectionChange(val) {
      this.checked_student = val;
    },
    // 分页器切换
    currentChange(page) {
      this.page = page;
      this.search();
    },
    closeTransferDialog() {
      this.transferDialog = false;
    },
    closeChooseClassDio() {
      this.showChooseClass = false;
    },
    // 单个出班
    outClass(row) {
      this.$confirm(`确认要移除学员${row.student_name}吗?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        const params = {
          classroom_id: this.classRoomInfo.id,
          handler_type: "out",
          memo: "",
          student_ids: [row.student_id]
        };
        classroomManagement.removeStudents(params).then((res) => {
          if (+res.status === 200) {
            this.$message.success("出班成功!");
            this.search();
          }
        });
      });
    },
    // 单个转班
    turnClass(row) {
      console.log(row);
      this.testData = [row];
      this.students_id = [row.student_id];
      this.excludeData = {
        exclude_id: row.classroom_id,
        department_id: row.department_id,
        department_name: row.department_name
      };
      this.operation_type = "转班";
      this.showChooseClass = true;
    },
    // 入班
    joinClassBatch() {
      const {
        course_id,
        id,
        name,
        department_id,
        department_name,
        match_index
      } = this.classRoomInfo;
      this.$router.push({
        name:
          match_index > 0
            ? "classManagementDividMatch"
            : "classManagementDivid",
        query: {
          course_id,
          classroom_id: id,
          classroom_name: name,
          department_id,
          department_name,
          page: this.page,
          match_index
        }
      });
    },
    // 批量转班
    turnClassBatch() {
      if (this.checked_student.length > 0) {
        this.testData = this.checked_student;
        this.students_id = this.checked_student.map((i) => i.student_id);
        console.log(this.checked_student, "checked_student");
        this.operation_type = "转班";
        this.showChooseClass = true;
      }
    },
    // 批量升班
    litreClassBatch() {
      if (this.checked_student.length > 0) {
        this.testData = this.checked_student;
        this.operation_type = "升班";
        this.showChooseClass = true;
      }
    }
  },
  mounted() {
    this.search();
    this.getStudentStatus();
  }
};
</script>

<style lang="less" scoped>
.search_right_box {
  // margin-left: 6px;
}
/deep/ button.disabled {
  background-color: #ccc;
  border: 1px solid #ccc;
  color: #fff;
}
.tg-table__box {
  margin: 0;
  margin-top: 16px;
}
.container {
  margin: 0 !important;
}
</style>
