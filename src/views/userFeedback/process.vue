<template>
  <div class="user-feedback-process">
    <el-dialog
      title="处理进度"
      :visible="true"
      width="800px"
      class="custom_edit_dialogs"
      :show-close="true"
      :before-close="back"
    >
      <div class="dialogs-content">
        <div class="block">
          <el-timeline>
            <el-timeline-item
              color="#409EFF"
              :hide-timestamp="true"
              placement="top"
            >
              <h4 class="title">提交反馈</h4>
              <p class="time"></p>
              <el-card>
                <div class="row-content">
                  <span>反馈校区：</span>
                  <span>{{ feedbackData.department_name }}</span>
                </div>
                <div class="row-content">
                  <span>反馈类型：</span>
                  <span>{{ feedbackData.category_name }}</span>
                </div>
                <div class="row-content">
                  <span>反馈内容：</span>
                  <span>{{ feedbackData.feedback }}</span>
                </div>
                <div class="row-content">
                  <span>反馈图片：</span>
                  <span>
                    <el-image
                      v-for="item in feedbackData.feedback_attachment"
                      :src="item"
                      :key="item"
                      fit="cover"
                      :preview-src-list="feedbackData.feedback_attachment"
                      style="
                        width: 60px;
                        height: 60px;
                        margin-right: 10px;
                        border-radius: 10px;
                      "
                    />
                  </span>
                </div>
              </el-card>
            </el-timeline-item>
            <el-timeline-item
              v-if="feedbackData.process_status === 2"
              color="#409EFF"
              :hide-timestamp="true"
              placement="top"
            >
              <h4 class="title">受理中</h4>
            </el-timeline-item>
            <el-timeline-item
              v-if="feedbackData.process_status === 1"
              color="#409EFF"
              :hide-timestamp="true"
              placement="top"
            >
              <h4 class="title">答复内容</h4>
              <el-card>
                <div class="row-content">
                  <span>操作人：</span>
                  <span>{{ feedbackData.operator_name }}</span>
                </div>
                <div class="row-content">
                  <span>答复时间：</span>
                  <span>{{ feedbackData.process_time | getTime }}</span>
                </div>
                <div class="row-content">
                  <span>答复内容：</span>
                  <span>{{ feedbackData.reply }}</span>
                </div>
                <div class="row-content">
                  <span>备注：</span>
                  <span>{{ feedbackData.remark }}</span>
                </div>
                <div class="row-content">
                  <span>备注图片：</span>
                  <span>
                    <el-image
                      v-for="item in feedbackData.remark_attachment"
                      :src="item"
                      :key="item"
                      fit="cover"
                      :preview-src-list="feedbackData.remark_attachment"
                      style="
                        width: 60px;
                        height: 60px;
                        margin-right: 10px;
                        border-radius: 10px;
                      "
                    />
                  </span>
                </div>
              </el-card>
            </el-timeline-item>
            <el-timeline-item
              v-if="feedbackData.process_status === 1"
              color="#67C23A"
              :hide-timestamp="true"
              placement="top"
            >
              <h4 class="title">处理完成</h4>
            </el-timeline-item>
          </el-timeline>
          <div class="btn-box">
            <el-button
              v-if="feedbackData.process_status === 2"
              type="primary"
              size="small"
              @click="handleProcess"
              >立即处理</el-button
            >
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="tg-button--plain" type="plain" @click="back"
          >关闭</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "userFeedbackProcess",
  props: {
    feedbackData: {
      type: Object,
      required: true,
      default: () => ({})
    }
  },
  data() {
    return {
      feedbackInfo: {}
    };
  },
  methods: {
    initData() {
      // TODO: 初始化数据，接口获取
      //   if (this.feedbackData) {
      //     this.feedbackInfo = this.feedbackData;
      //   }
    },
    back() {
      this.$emit("close");
    },
    handleProcess() {
      this.$emit("handleProcess", this.feedbackData);
      this.$emit("close");
    }
  }
};
</script>

<style lang="less" scoped>
.user-feedback-process {
  /deep/ .el-timeline-item__content {
    .time {
      font-size: 13px;
      color: #999;
      margin: 10px 0;
    }
    .row-content {
      display: flex;
      align-items: center;
      padding: 8px 0;
      span:nth-child(1) {
        width: 80px;
      }
      span:nth-child(2) {
        flex: 1;
      }
    }
  }
}
</style>
