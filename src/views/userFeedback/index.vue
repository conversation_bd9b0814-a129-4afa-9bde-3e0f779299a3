<template>
  <div class="container vessel">
    <tg-search
      :searchTitle.sync="searchTitle"
      :form.sync="search"
      @reset="reset"
      @search="searchStaff"
      :showNum="3"
      :isExport="isExport"
      :loadingState="exportLoading"
      @educe="exportExcel"
      class="tg-box--margin"
    ></tg-search>

    <div class="tg-table__box">
      <div class="tg-box--border"></div>
      <el-table
        ref="table"
        :data="list"
        class="tg-table"
        :row-key="getRowKeys"
        :cell-style="{ borderRightColor: '#e0e6ed75' }"
        :header-cell-style="{ borderRightColor: '#e0e6ed75' }"
        border
      >
        <el-table-column
          show-overflow-tooltip
          prop="feedback_id"
          label="反馈编号"
          width="200"
          fixed="left"
        >
          <template slot-scope="scope">
            <div class="copy_name">
              <span @click="toInfo(scope.row)">{{ scope.row.order_no }}</span>
              <div v-copy="scope.row.order_no"></div>
            </div>
          </template>
        </el-table-column>

        <el-table-column
          label="反馈时间"
          prop="minpro_course_no"
          min-width="170"
        >
          <template slot-scope="scope">
            {{ scope.row.created_at | getTime }}
          </template>
        </el-table-column>

        <el-table-column label="学号" width="140" prop="student_number">
          <template slot-scope="scope">
            <div
              v-if="
                $_has({ m: 'userFeedback', o: 'visitorShow' }) ||
                scope.row.is_anonymous === 2
              "
            >
              {{ scope.row.student_number }}
            </div>
            <div v-else>****</div>
          </template>
        </el-table-column>
        <el-table-column label="姓名" prop="student_name" width="140">
          <template slot-scope="scope">
            <div
              v-if="
                $_has({ m: 'userFeedback', o: 'visitorShow' }) ||
                scope.row.is_anonymous === 2
              "
            >
              {{ scope.row.student_name }}
            </div>
            <div v-else>****</div>
          </template>
        </el-table-column>
        <el-table-column label="手机号" prop="student_mobile" width="140">
          <template slot-scope="scope">
            <div
              v-if="
                $_has({ m: 'userFeedback', o: 'visitorShow' }) ||
                scope.row.is_anonymous === 2
              "
            >
              <mobileHyposensitization
                :mobileTemInfo="{
                  row: scope.row,
                  has_eye_limit: scope.row?.has_eye_limit,
                  mobile: scope.row.student_mobile
                }"
              ></mobileHyposensitization>
            </div>
            <div v-else>****</div>
          </template>
        </el-table-column>

        <el-table-column label="客户状态" prop="customer_status" width="140">
          <template slot-scope="scope">
            {{ scope.row.customer_status }}
          </template>
        </el-table-column>
        <el-table-column
          label="反馈校区"
          prop="department_name"
          min-width="140"
        >
        </el-table-column>
        <el-table-column label="反馈类型" prop="category_name" width="140">
        </el-table-column>
        <el-table-column label="提交方式" prop="is_anonymous" width="140">
          <template slot-scope="scope">
            {{ scope.row.is_anonymous === 1 ? "匿名" : "非匿名" }}
          </template>
        </el-table-column>
        <el-table-column label="联系方式" prop="contact_phone" min-width="140">
          <template slot-scope="scope">
            <div>
              {{ scope.row.contact_phone }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="反馈图片" prop="feedback_image" min-width="140">
          <template slot-scope="scope">
            <el-image
              v-if="scope.row.feedback_attachment.length"
              :src="scope.row.feedback_attachment[0]"
              :preview-src-list="scope.row.feedback_attachment"
              fit="cover"
              style="width: 60px; height: 60px"
            ></el-image>
          </template>
        </el-table-column>
        <el-table-column
          show-overflow-tooltip
          label="反馈内容"
          prop="feedback"
          width="140"
        >
        </el-table-column>
        <el-table-column label="处理进度" prop="status" width="140">
          <template slot-scope="scope">
            <el-tag
              v-if="scope.row.process_status === 1"
              @click="handleView(scope.row)"
              type="success"
              >已处理</el-tag
            >
            <el-tag v-else @click="handleView(scope.row)" type="warning"
              >待处理</el-tag
            >
          </template>
        </el-table-column>

        <el-table-column label="操作人" prop="operator_name" width="140">
        </el-table-column>
        <el-table-column label="处理时间" prop="process_time" width="170">
          <template slot-scope="scope">
            {{ scope.row.process_time | getTime }}
          </template>
        </el-table-column>
        <el-table-column
          show-overflow-tooltip
          label="备注"
          prop="remark"
          width="140"
        >
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
          <template slot-scope="scope">
            <el-button
              v-has="{ m: 'userFeedback', o: 'reply' }"
              v-if="scope.row.process_status !== 1"
              type="text"
              size="small"
              class="tg-text--blue tg-span__divide-line"
              @click="handleOperation(scope.row)"
              >处理</el-button
            >
          </template>
        </el-table-column>

        <template slot="empty">
          <div style="margin-top: 15%">
            <TgLoading v-if="loading"></TgLoading>
            <div class="empty-container" v-else>暂无数据～</div>
          </div>
        </template>
      </el-table>
      <div class="tg-pagination">
        <span class="el-pagination__total">共 {{ total }} 条</span>
        <el-pagination
          background
          layout="sizes,prev,pager,next,jumper"
          :total="total"
          :page-size="pageSize"
          :current-page="page"
          @size-change="sizeChange"
          @current-change="currentChange"
          :page-sizes="[10, 20, 50, 100]"
        >
        </el-pagination>
      </div>
    </div>

    <create
      @close="dialogCreate = false"
      @getList="getList"
      :feedbackData="rowData"
      v-if="dialogCreate"
      ref="createComponent"
    ></create>
    <process
      :feedbackData="rowData"
      @close="dialogProcess = false"
      @handleProcess="handleProcess"
      v-if="dialogProcess"
    ></process>
  </div>
</template>
<script>
import userFeedbackApi from "@/api/userFeedback.js";
import Create from "./create.vue";
import Process from "./process.vue";
import { downLoadFile } from "@/public/downLoadFile";
export default {
  name: "userFeedback",
  components: {
    Create,
    Process
  },
  data() {
    return {
      dialogCreate: false,
      dialogProcess: false,
      searchTitle: [
        {
          props: "student_info",
          label: "学员信息",
          type: "input",
          show: true,
          placeholder: "请输入学员姓名/学号/电话"
        },
        {
          props: "category_id",
          label: "反馈类型",
          type: "select",
          width: 200,
          show: true,
          placeholder: "请输入反馈类型",
          selectOptions: []
        },
        {
          props: "feedback_time",
          label: "反馈时间",
          type: "date",
          width: 400,
          show: true,
          placeholder: "请选择反馈时间"
        },

        {
          props: "order_no",
          label: "反馈编号",
          type: "input",
          width: 200,
          show: false,
          placeholder: "请输入反馈编号"
        },
        {
          props: "is_anonymous",
          label: "提交方式",
          type: "select",
          width: 200,
          show: false,
          placeholder: "请选择",
          selectOptions: [
            {
              id: "",
              name: "不限"
            },
            {
              id: 1,
              name: "匿名"
            },
            {
              id: 2,
              name: "非匿名"
            }
          ]
        },
        {
          props: "operator_id",
          label: "操作人",
          type: "course_staff",
          show: false,
          is_leave: true
        },
        {
          props: "process_status",
          label: "处理状态",
          type: "select",
          width: 200,
          show: false,
          placeholder: "请选择",
          selectOptions: [
            {
              id: "",
              name: "不限"
            },
            {
              id: 2,
              name: "待处理"
            },
            {
              id: 1,
              name: "已处理"
            }
          ]
        }
      ],
      search: {
        student_name: "",
        category_id: "",
        feedback_time: "",
        feedback_id: "",
        submit_type: "",
        operator_id: "",
        process_status: ""
      },
      height: window.innerHeight - 370,
      loading: false,
      list: [],
      page: 1,
      total: 0,
      pageSize: 10,
      rowData: {},
      rowId: "",
      isExport: false,
      feedbackTypeList: [],
      exportLoading: false
      // stop_use: false,
      // start_use: false
    };
  },

  computed: {
    school_id() {
      return this.$store.getters.doneGetSchoolId;
    }
  },

  created() {
    if (this.$_has({ m: "userFeedback", o: "export" })) {
      this.isExport = true;
    }
  },

  filters: {
    statusFilter: function (value) {
      return value === 1 ? "待处理" : "已处理";
    }
  },
  mounted() {
    this.getFeedbackType();
    this.getList();
  },
  methods: {
    // 获取反馈类型
    getFeedbackType() {
      userFeedbackApi.getFeedbackType().then((res) => {
        if (res.data.code === 0) {
          const arr = res.data.data.map((item) => ({
            id: item.id,
            name: item.name
          }));
          this.feedbackTypeList = arr;
          this.searchTitle[1].selectOptions = [
            {
              id: "",
              name: "不限"
            },
            ...arr
          ];
        }
      });
    },

    // 导出
    exportExcel() {
      this.exportLoading = true;
      const { feedback_time } = this.search;
      if (feedback_time) {
        this.search.start_date = feedback_time[0];
        this.search.end_date = feedback_time[1];
      } else {
        this.search.start_date = "";
        this.search.end_date = "";
      }
      userFeedbackApi
        .exportDetailExcel({
          ...this.search,
          department_id: this.school_id
        })
        .then((res) => {
          downLoadFile(res, "反馈记录");
          this.exportLoading = false;
        })
        .catch(() => {
          this.exportLoading = false;
        });
    },
    handleOperation(row) {
      this.dialogCreate = true;
      this.rowData = row;
    },
    checkSelectable(row) {
      return row.status !== 1;
    },

    handleView(row) {
      if (this.$_has({ m: "userFeedback", o: "detail" })) {
        this.dialogProcess = true;
        this.rowData = row;
      } else {
        this.$message.warning("您没有权限操作,请联系管理员");
      }
    },

    // 关闭弹窗
    closeFrontPriorityDialog() {
      this.frontPriorityDialogVisible = false;
      // 恢复开关状态
      if (this.currentCourse.is_front === 1) {
        this.currentCourse.is_front = 2;
      }
    },

    // 提交推荐到首页的API请求
    submitToFront(row) {
      const currentIsFront = row.is_front; // 保存当前的状态值
      userFeedbackApi
        .toFront({
          is_front: row.is_front,
          minpro_course_id: row.minpro_course_id,
          front_index: row.front_index
        })
        .then((res) => {
          if (res.data.code === 0) {
            this.$message.success("操作成功");
            row.is_front = currentIsFront;
          } else {
            this.$message.error(res.data?.message || "操作失败");
            // 操作失败，恢复开关状态
            row.is_front = currentIsFront === 1 ? 2 : 1;
          }
        })
        .catch(() => {
          // 请求异常，恢复开关状态
          row.is_front = row.is_front === 1 ? 2 : 1;
          this.$message.error("操作失败");
        });
    },

    getList() {
      this.loading = true;
      // this.list = [];
      // this.total = 0;
      const { feedback_time } = this.search;
      if (feedback_time) {
        this.search.start_date = feedback_time[0];
        this.search.end_date = feedback_time[1];
      } else {
        this.search.start_date = "";
        this.search.end_date = "";
      }
      userFeedbackApi
        .getFeedbackList({
          page: this.page,
          page_size: this.pageSize,
          ...this.search,
          department_id: this.school_id
        })
        .then((res) => {
          if (res.data.code === 0) {
            this.list = res.data?.data?.results || [];
            this.total = res.data?.data?.count || 0;
          } else {
            this.$message.error(res.data?.message || "获取列表失败");
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 分页
    currentChange(val) {
      this.page = val;
      this.getList();
    },
    sizeChange(val) {
      this.page = 1;
      this.pageSize = val;
      this.getList();
    },

    reset() {
      this.search = {
        student_info: "",
        category_id: "",
        feedback_time: "",
        feedback_id: "",
        is_anonymous: "",
        handler_employee_id: "",
        process_status: ""
      };
      this.page = 1;
      this.pageSize = 10;
      this.getList();
    },
    searchStaff() {
      this.page = 1;
      this.getList();
    },

    getRowKeys(row) {
      return row.minpro_course_id;
    },
    handleProcess(row) {
      this.rowData = row;
      this.dialogCreate = true;
    }
  }
};
</script>
<style lang="less" scoped>
/deep/ .el-message-box__message {
  padding: 40px;
}
/deep/ .el-message-box {
  height: 100px;
  width: 400px !important;
}
.vessel {
  ::v-deep .el-table td:last-child {
    border-right-color: transparent !important;
  }
  ::v-deep .el-table__header th:last-child {
    border-right-color: transparent !important;
  }
}
.attribute {
  width: 29px;
  height: 23px;
  cursor: pointer;
  vertical-align: middle;
}
.tg-row--height {
  width: 100%;
}
.on_type {
  display: flex;
  align-items: center;
  .green_pie {
    width: 6px;
    height: 6px;
    background: #2d80ed;
    border-radius: 3px;
    margin-right: 7px;
  }
  .gray_pie {
    width: 6px;
    height: 6px;
    background: #96a7bd;
    border-radius: 3px;
    margin-right: 7px;
  }
}

/deep/ button.disabled {
  background-color: #ccc;
  border: 1px solid #ccc;
  color: #fff;
}
::v-deep .el-table__body-wrapper {
  height: calc(100% - 46px);
  .empty-container {
    position: absolute;
    top: 50%;
    left: 50%;
  }
  .loading-container {
    position: absolute;
    top: 30%;
    left: 1%;
    background: transparent;
    .box {
      height: 100%;
    }
  }
  .el-tag {
    cursor: pointer;
  }
}
::v-deep .copy_name {
  span {
    // color: rgb(21, 125, 240);
    cursor: pointer;
    width: 100%;
    overflow: hidden;
    word-wrap: break-word;
    text-overflow: ellipsis;
  }
}
</style>
