<template>
  <div id="schoolServiceScheduling" class="container">
    <!-- 头部 -->

    <!-- <div> -->
    <tg-search
      :searchTitle.sync="searchTitle"
      :form.sync="search"
      :showNum="3"
      @reset="reset"
      @change="getSubChannel"
      @search="searchStaff"
      class="tg-box--margin"
    ></tg-search>
    <!-- </div> -->
    <el-row style="margin: 16px 0 0 6px; width: 100%" v-if="has_btn">
      <el-col :span="24">
        <el-button
          @click="createDialog = true"
          type="plain"
          class="tg-button--plain"
          v-has="{ m: 'scheduling', o: 'create' }"
          >新增</el-button
        >
        <el-button
          type="plain"
          class="tg-button--plain"
          @click="del(2)"
          v-has="{ m: 'scheduling', o: 'delete' }"
          >批量删除</el-button
        >
        <el-button
          type="plain"
          class="tg-button--plain"
          @click="batchCancel"
          v-has="{ m: 'scheduling', o: 'cancel' }"
          >批量取消</el-button
        >
        <el-button
          type="plain"
          class="tg-button--plain"
          @click="exportExcel"
          :loading="exportLoading"
          v-has="{ m: 'scheduling', o: 'export' }"
          >导出</el-button
        >
        <el-button
          type="plain"
          class="tg-button--plain"
          @click="moveSchedulingClick"
          v-has="{ m: 'scheduling', o: 'move' }"
          >移动排课</el-button
        >
        <el-button
          type="plain"
          class="tg-button--plain"
          @click="copySchedulingClick"
          v-has="{ m: 'scheduling', o: 'copy' }"
          >复制排课</el-button
        >
        <select-field
          :allFields.sync="table_title"
          :btnType="'button'"
        ></select-field>
      </el-col>
    </el-row>
    <div class="tg-table__box">
      <div class="tg-box--border"></div>
      <el-table
        @selection-change="handleSelectionChange"
        :data="list.results || []"
        class="tg-table"
        :row-key="getRowKeys"
        :summary-method="getSummaries"
        show-summary
        ref="table"
        :cell-style="{ borderRightColor: '#e0e6ed75' }"
        :header-cell-style="{ borderRightColor: '#e0e6ed75' }"
        border
        @sort-change="sortChange"
      >
        <el-table-column type="selection" width="50" :reserve-selection="true">
        </el-table-column>
        <template v-for="(item, index) in table_title">
          <el-table-column
            v-if="item.show"
            :key="index"
            :prop="item.props"
            :label="item.label"
            :min-width="item.width"
            :fixed="item.fixed ? true : false"
            :sortable="item.sort"
            :show-overflow-tooltip="item.tooltip"
          >
            <template #header>
              <TableTitleDesc
                v-if="item.props === 'finish_lesson_numb'"
                title="实到计费人数"
                desc="实际出勤 / 实际计费 / 应出勤计费"
              ></TableTitleDesc>
              <TableTitleDesc
                v-else-if="item.props === 'course_summary'"
                title="课程总结发送状态"
                desc="已发人数 / 实际出勤人数 / 应出勤计费"
              ></TableTitleDesc>
              <TableTitleDesc
                v-else-if="item.props === 'parent_class'"
                title="家长课堂发送状态"
                desc="已发人数 / 实际出勤人数 / 应出勤计费"
              ></TableTitleDesc>
              <span v-else>{{ item.label }}</span>
            </template>
            <template slot-scope="scope">
              <template v-if="item.props === 'classroom_name'">
                <div class="copy_name">
                  <el-button
                    :disabled="
                      scope.row.status === 'is_cancelled' ||
                      scope.row.status === 'is_started'
                    "
                    class="tg-text--blue tg-table__name--ellipsis"
                    type="text"
                    @click="openEdit(scope.row)"
                    >{{ scope.row.classroom_name }}</el-button
                  >
                  <div v-copy="scope.row.classroom_name"></div>
                </div>
              </template>
              <template v-else-if="item.type === 'date'">
                {{
                  scope.row[scope.column.property]
                    ? scope.row[scope.column.property].indexOf("0001-01-01") >
                      -1
                      ? ""
                      : getDate(scope.row[scope.column.property])
                    : ""
                }}
              </template>
              <template v-else-if="item.props === 'start_time'">
                {{ moment(scope.row.start_time).format("YYYY-MM-DD HH:mm") }} -
                {{ moment(scope.row.end_time).format("HH:mm") }}
                {{ scope.row.week_day_chn }}
              </template>
              <template v-else-if="item.props === 'class_duration'">
                {{
                  moment.duration(scope.row.class_duration, "seconds").hours() +
                  "时" +
                  moment
                    .duration(scope.row.class_duration, "seconds")
                    .minutes() +
                  "分"
                }}
              </template>
              <div v-else-if="item.props === 'finish_lesson_numb'">
                <el-button
                  @click="show_actual_attendance(scope.row)"
                  type="text"
                  class="tg-text--blue"
                >
                  {{ scope.row.roll_called_student_numb }}/{{
                    scope.row.billable_student_numb
                  }}/{{ scope.row.total_student_numb }}</el-button
                >
              </div>
              <div v-else-if="item.props === 'course_summary'">
                <el-button
                  :disabled="scope.row.status === 'not_start'"
                  @click="show_course_summary(scope.row, item.props)"
                  type="text"
                  class="tg-text--blue"
                >
                  {{ scope.row.course_summary_send }}/{{
                    scope.row.roll_called_student_numb
                  }}/{{ scope.row.total_student_numb }}</el-button
                >
              </div>
              <div v-else-if="item.props === 'parent_class'">
                <el-button
                  :disabled="scope.row.status === 'not_start'"
                  @click="show_course_summary(scope.row, item.props)"
                  type="text"
                  class="tg-text--blue"
                >
                  {{ scope.row.parent_class_send }}/{{
                    scope.row.roll_called_student_numb
                  }}/{{ scope.row.total_student_numb }}</el-button
                >
              </div>
              <span v-else>{{ scope.row[scope.column.property] }}</span>
            </template>
          </el-table-column>
        </template>
        <el-table-column fixed="right" label="操作">
          <template slot-scope="scope">
            <el-dropdown>
              <operate-more :index="scope.$index"></operate-more>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  v-if="scope.row.status !== 'is_cancelled'"
                  type="text"
                  size="small"
                  @click.native="openDialog(2, scope.row)"
                  v-has="{ m: 'scheduling', o: 'begin_class' }"
                  ><span style="margin-left: 10px">点名上课</span>
                </el-dropdown-item>
                <el-dropdown-item
                  v-if="scope.row.status === 'not_start'"
                  type="text"
                  size="small"
                  v-has="{ m: 'scheduling', o: 'add_student' }"
                  @click.native="openDialog(4, scope.row)"
                  ><span style="margin-left: 10px">添加学员</span>
                </el-dropdown-item>
                <el-dropdown-item
                  v-if="
                    (scope.row.status === 'not_start' &&
                      $_has({ m: 'scheduling', o: 'remove_trash' })) ||
                    $_has({
                      m: 'redo',
                      o: 'remove_makeup'
                    }) ||
                    $_has({
                      m: 'scheduling',
                      o: 'remove_temp'
                    })
                  "
                  type="text"
                  size="small"
                  @click.native="openDialog(5, scope.row)"
                  ><span style="margin-left: 10px">移除学员</span>
                </el-dropdown-item>
                <el-dropdown-item
                  v-if="scope.row.status !== 'is_cancelled'"
                  type="text"
                  size="small"
                  @click.native="modificationContent(scope.row)"
                  v-has="{ m: 'scheduling', o: 'batch_update_context' }"
                  ><span style="margin-left: 10px">修改上课内容</span>
                </el-dropdown-item>
                <el-dropdown-item
                  v-if="scope.row.status === 'not_start'"
                  type="text"
                  size="small"
                  @click.native="openDialog(1, scope.row)"
                  v-has="{ m: 'scheduling', o: 'temp_list' }"
                  ><span style="margin-left: 10px">临时调课</span>
                </el-dropdown-item>
                <el-dropdown-item
                  type="text"
                  size="small"
                  v-has="{ m: 'scheduling', o: 'batch_update' }"
                  @click.native="openDialog(7, scope.row)"
                  ><span style="margin-left: 10px">批量修改</span>
                </el-dropdown-item>
                <el-dropdown-item
                  v-if="scope.row.status === 'not_start'"
                  type="text"
                  size="small"
                  @click.native="openDialog(3, scope.row)"
                  v-has="{ m: 'scheduling', o: 'cancel' }"
                  ><span style="margin-left: 10px">取消</span>
                </el-dropdown-item>
                <el-dropdown-item
                  v-if="
                    scope.row.status === 'not_start' ||
                    scope.row.status === 'is_cancelled'
                  "
                  type="text"
                  size="small"
                  @click.native="del(1, scope.row)"
                  v-has="{ m: 'scheduling', o: 'delete' }"
                  ><span style="margin-left: 10px">删除</span>
                </el-dropdown-item>
                <el-dropdown-item
                  v-if="scope.row.status == 'is_started'"
                  type="text"
                  size="small"
                  v-has="{ m: 'scheduling', o: 'revocation' }"
                  @click.native="openDialogFinished(scope.row)"
                  ><span style="margin-left: 10px">撤销上课</span>
                </el-dropdown-item>
                <!-- <template v-if="!scope.row.is_send_parent_class">
                  <el-dropdown-item
                    v-has="{ m: 'class', o: 'parent_class' }"
                    @click.native="sendContent(scope.row, 'parent_class')"
                    type="text"
                    size="small"
                    :disabled="scope.row.status === 'not_start'"
                    ><span>发送家长课堂</span>
                  </el-dropdown-item>
                </template> -->
                <!-- <el-dropdown-item
                  v-has="{ m: 'class', o: 'class_notice' }"
                  @click.native="sendContent(scope.row, 'class_notice')"
                  type="text"
                  size="small"
                  :disabled="scope.row.status === 'not_start'"
                  ><span>发送班级通知</span>
                </el-dropdown-item> -->
                <!-- <template v-if="!scope.row.is_send_course_summary">
                  <el-dropdown-item
                    v-has="{ m: 'class', o: 'course_summary' }"
                    @click.native="sendContent(scope.row, 'course_summary')"
                    type="text"
                    size="small"
                    :disabled="scope.row.status === 'not_start'"
                    ><span>发送课程总结</span>
                  </el-dropdown-item>
                </template> -->
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
        <template slot="empty">
          <div style="margin-top: 15%">
            <TgLoading v-if="loading"></TgLoading>
            <div class="empty-container" v-else>暂无数据～</div>
          </div>
        </template>
      </el-table>
      <div class="tg-pagination">
        <span class="el-pagination__total">共 {{ list.count }} 条</span>
        <el-pagination
          background
          layout="sizes,prev,pager,next,jumper"
          :total="list.count"
          :page-size="page_size"
          :current-page.sync="page"
          @current-change="currentChange"
          @size-change="sizeChange"
          :page-sizes="[10, 20, 50, 100]"
        ></el-pagination>
      </div>
      <!-- 批量取消上课 -->
      <el-dialog
        title="取消上课"
        :visible="cancellation"
        width="800px"
        class="cancelDialog"
        v-if="cancellation"
        :before-close="cancel"
      >
        <div class="cancellation-top tg-box--margin">
          <img class="tips__img" src="../../assets/图片/icon_info.png" alt="" />
          <span class="tips">已取消的上课记录将无法恢复</span>
        </div>
        <div v-if="cancel_type == 'only'">
          <div style="display: flex" class="tab">
            <table class="mail-table" cellspacing="0" cellpadding="0">
              <tr>
                <td class="column" style="height: 48px">班级</td>
                <td>{{ info.classroom_name }}</td>
                <td class="column" style="height: 48px">上课时间</td>
                <td>
                  {{ info.format_class_time }}
                </td>
              </tr>
            </table>
          </div>
        </div>
        <el-form
          :model="form"
          ref="form"
          label-width="80px"
          class="tg-box--margin"
        >
          <el-form-item label="取消原因" required prop="memo">
            <el-input
              v-model="form.memo"
              type="textarea"
              :rows="6"
              resize="none"
              style="border-radius: 4px; height: 180px"
            ></el-input>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button class="tg-button--plain" @click="cancel">取消</el-button>
          <el-button
            class="tg-button--primary"
            type="primary"
            @click="cancellationConfirm"
            >确定</el-button
          >
        </span>
      </el-dialog>
      <!-- 撤销上课 -->
      <el-dialog title="提示" :visible.sync="dialogFinished" width="460px">
        <div style="margin: 30px 20px" class="dialog_content">
          <img src="../../assets/图片/tips_info.png" alt style="height: 80px" />
          <div>撤销上课后,该堂课将恢复成未上课的状态,确定撤销上课吗？</div>
        </div>

        <span slot="footer" class="dialog-footer">
          <el-button class="tg-button--plain" @click="dialogFinished = false"
            >取消</el-button
          >
          <el-button
            class="tg-button--primary"
            type="primary"
            @click="cancelClass"
            >确定</el-button
          >
        </span>
      </el-dialog>
      <!-- 修改上课内容 -->
      <el-dialog
        title="修改上课内容"
        :visible.sync="dialogFinished_Content"
        width="800px"
      >
        <div>
          <el-form
            :model="formContent"
            ref="form"
            label-width="80px"
            class="tg-box--margin"
          >
            <el-form-item label="上课内容" required>
              <el-input
                v-model="formContent.class_context"
                type="textarea"
                resize="none"
                style="border-radius: 4px; height: 180px"
              ></el-input>
            </el-form-item>

            <el-form-item label="上课课件" required>
              <el-input
                v-model="formContent.class_ware"
                style="border-radius: 4px; height: 60px"
              ></el-input>
            </el-form-item>

            <el-form-item label="备注" required>
              <el-input
                v-model="formContent.memo"
                type="textarea"
                resize="none"
                style="border-radius: 4px; height: 180px"
              ></el-input>
            </el-form-item>
          </el-form>
        </div>

        <span slot="footer" class="dialog-footer">
          <el-button
            class="tg-button--plain"
            @click="dialogFinished_Content = false"
            >取消</el-button
          >
          <el-button
            class="tg-button--primary"
            type="primary"
            @click="classroomContent"
            >确定</el-button
          >
        </span>
      </el-dialog>
    </div>
    <create @closeCreateDialog="closeCreateDialog" v-if="createDialog"></create>
    <specify-by-name
      :lock_obj="lock_obj"
      :id="scheduling_id"
      v-if="specify_visible"
      from="serviceScheduling"
      @close="specifyNameClose"
      @really="getList"
      @showPush="showPushDialog"
    ></specify-by-name>
    <add-student
      :type="add_type"
      :id="scheduling_id"
      v-if="add_student_visible"
      @close="add_student_visible = false"
      @really="getList"
    ></add-student>
    <update
      @closeUpdateDialog="closeUpdateDialog"
      v-if="updateDialog"
      :id="rowData.id"
    ></update>
    <temporary-transfer
      :id="scheduling_id"
      v-if="temporary_transfer_visible"
      @close="temporary_transfer_visible = false"
    ></temporary-transfer>
    <batch-update
      @close="batch_update_visible = false"
      :id="scheduling_id"
      v-if="batch_update_visible"
      :row="choose_row"
      @really="getList"
    ></batch-update>
    <cancel-the-class
      v-if="cancel_class_visible"
      :scheduling_id="rowData.id"
      :submitLoading="classroomFinishedLoading"
      @close="cancelClassClose"
    >
    </cancel-the-class>
    <actual-attendance-student
      v-if="actual_attendance_visible"
      @close="actual_attendance_visible = false"
      :id="scheduling_id"
    ></actual-attendance-student>
    <courseSummarDialog
      v-if="course_summar_dialog_visible"
      @close="course_summar_dialog_visible = false"
      :type="send_type"
      :rowData="rowData"
      :id="scheduling_id"
    ></courseSummarDialog>
    <PushActionStudents
      v-if="pushActionVisible"
      :data="pushActionData.data"
      survey_type="audition"
      :rowInfo="rowInfo"
      :campus_id="pushActionData.department_id"
      @cancel="pushActionCancel"
      @confirm="pushActionConfirm"
    ></PushActionStudents>
    <copyMoveSchedule
      v-if="copy_move_schedule_visible"
      @close="copyMoveClose"
      @confirm="copyMoveConfirm"
      :copyMoveType="copyMoveType"
      :department_id="single_department_id"
    ></copyMoveSchedule>
    <school-tree
      :flag.sync="school_tree_visible"
      :id.sync="single_department_id"
      :name.sync="single_department_name"
      :required="true"
      :has_modal="true"
      type="radio"
      @confirm="confirmSchool"
      :use_store_options="true"
    >
    </school-tree>
    <editorDialog
      v-if="editorDialogVisible"
      :department_id="editorDialogDepartmentId"
      @close="editorDialogVisible = false"
      @confirm="editorDialogConfirm"
    ></editorDialog>
  </div>
</template>

<script>
import SelectField from "@/components/selectField/selectField.vue";
import timeFormat from "@/public/timeFormat"; // 日期转化
import classManagementApi from "@/api/classManagement";
import schoolServiceSchedulingExcelApi from "@/api/schoolServiceScheduling";
import tgSearch from "@/components/search/search.vue";
import operateMore from "@/components/operateMore/index.vue";
import Create from "./create.vue";
import Update from "./update.vue";
import TemporaryTransfer from "@/components/schoolServiceScheduling/temporaryTransfer.vue";
import specifyByName from "@/components/schoolServiceScheduling/specifyByName.vue";
import actualAttendanceStudent from "@/components/schoolServiceScheduling/actualAttendanceStudent.vue";
import courseSummarDialog from "@/components/schoolServiceScheduling/courseSummarDialog.vue";
import AddStudent from "@/components/schoolServiceScheduling/addStudent.vue";
import CancelTheClass from "@/components/schoolServiceScheduling/cancelTheClass.vue";
import specifyByNameApi from "@/api/specifyByName.js";
import BatchUpdate from "@/views/schoolServiceScheduling/batchUpdate.vue";
import { getCourseConfigByType } from "@/api/courseManagement.js";
import { export_excel_sync_new } from "@/public/asyncExport";
import { course_years } from "@/public/dict.js";
import { tableSummaries } from "@/mixins/tableSummaries";
import copyMoveSchedule from "@/views/schoolServiceScheduling/copyMoveSchedule.vue";
import editorDialog from "@/views/classManagement/editorDialog.vue";
import appletResource from "@/api/appletResource"; // 资料库
export default {
  components: {
    tgSearch,
    operateMore,
    Create,
    SelectField,
    Update,
    TemporaryTransfer,
    specifyByName,
    AddStudent,
    BatchUpdate,
    CancelTheClass,
    actualAttendanceStudent,
    courseSummarDialog,
    copyMoveSchedule,
    editorDialog
  },
  mixins: [tableSummaries],
  data() {
    return {
      editorDialogVisible: false,
      editorDialogDepartmentId: "",
      table_title: [
        {
          props: "classroom_name",
          label: "班级名称",
          show: true,
          width: 160,
          fixed: true,
          tooltip: true,
          sort: "custom"
        },
        {
          props: "classroom_alias_name",
          label: "班级别名",
          show: true,
          width: 140,
          fixed: true,
          tooltip: true,
          sort: "custom"
        },
        {
          props: "department_name",
          label: "所属校区",
          show: true,
          width: 140,
          tooltip: true,
          sort: "custom"
        },
        {
          props: "course_name",
          label: "课程名称",
          show: true,
          width: 160,
          tooltip: true,
          sort: "custom"
        },
        {
          props: "scheduling_form_chn",
          label: "上课形式",
          show: true,
          width: 120,
          tooltip: true,
          sort: "custom"
        },
        {
          props: "teacher_name",
          label: "任课老师",
          show: true,
          width: 120,
          tooltip: true,
          sort: "custom"
        },
        {
          props: "header_teacher_name",
          label: "班主任",
          show: true,
          width: 100,
          tooltip: true,
          sort: "custom"
        },
        {
          props: "school_room_name",
          label: "上课教室",
          show: true,
          width: 130,
          tooltip: true,
          sort: "custom"
        },
        {
          props: "class_ware",
          label: "章节内容",
          show: true,
          width: 110,
          tooltip: true
        },
        {
          props: "class_context",
          label: "上课内容",
          show: true,
          width: 110,
          tooltip: true
        },
        {
          props: "class_num",
          label: "课次",
          show: true,
          width: 110,
          tooltip: true
        },
        {
          props: "start_time",
          label: "上课时间",
          show: true,
          width: 200,
          tooltip: true,
          sort: "custom"
        },
        {
          props: "class_duration",
          label: "上课时长",
          show: true,
          width: 110,
          tooltip: true,
          sort: "custom"
        },
        {
          props: "status_chn",
          label: "上课状态",
          show: true,
          width: 110,
          tooltip: true,
          sort: "custom"
        },
        {
          props: "finish_lesson_numb",
          label: "实到计费人数",
          show: true,
          width: 130,
          tooltip: true
        },
        {
          props: "course_summary",
          label: "课程总结发送状态",
          show: true,
          width: 160,
          tooltip: true
        },
        // {
        //   props: "class_notice",
        //   label: "班级通知发送状态",
        //   show: true,
        //   width: 160,
        //   tooltip: true
        // },
        {
          props: "parent_class",
          label: "家长课堂发送状态",
          show: true,
          width: 160,
          tooltip: true
        },
        {
          props: "scheduling_employee_name",
          label: "点名人",
          show: true,
          width: 120,
          tooltip: true,
          sort: "custom"
        },
        {
          props: "scheduling_employee_is_teacher",
          label: "是否为老师",
          show: true,
          width: 120,
          tooltip: true
        }
      ],
      single_department_id: "",
      single_department_name: "",
      school_tree_visible: false,
      copy_move_schedule_visible: false,
      actual_attendance_visible: false,
      cancel_class_visible: false,
      classroomFinishedLoading: false,
      course_summar_dialog_visible: false,
      exportLoading: false,
      rowInfo: {},
      pushActionVisible: false,
      pushActionData: {
        data: [],
        department_id: ""
      },
      formContent: {
        memo: "", // 备注
        class_context: "", // 上课内容
        class_ware: "" //	上课课件
      },
      form: {
        memo: ""
      },
      page_size: 10,
      totalData: {
        roll_called_student_numb: "",
        billable_student_numb: "",
        total_student_numb: ""
      },
      temporary_trasfer_visible: false,
      dialogFinished: false,
      dialogFinished_Content: false,
      cancellation: false,
      cancellation_one: false,
      createDialog: false,
      updateDialog: false,
      tab: 1,
      type: "",
      name: "", // 名字
      memo: "", // 备注
      is_enabled: "", // 是否开启
      id: "",
      page: 1,
      // 筛选字段
      searchTitle: [
        { props: "name", label: "班级名称", type: "input", show: true },
        {
          props: "classroom_alias_name",
          label: "班级别名",
          type: "input",
          show: true
        },
        {
          props: "created_at",
          label: "上课时间",
          type: "custom_date",
          show: true
          // width: 200,
          // has_options: true
        },
        {
          props: "classroom_status",
          label: "结业状态",
          type: "select",
          show: false,
          selectOptions: [{ id: "", name: "不限" }]
        },
        {
          props: "course_property",
          label: "课程属性",
          type: "select",
          show: false,
          selectOptions: [{ id: "", name: "不限" }]
        },
        {
          props: "course_type",
          label: "课程类型",
          type: "select",
          show: false,
          selectOptions: [{ id: "", name: "不限" }]
        },
        {
          props: "classroom_type",
          label: "班级类型",
          type: "select",
          show: false,
          selectOptions: [{ id: "", name: "不限" }]
        },
        {
          props: "status",
          label: "上课状态",
          type: "select",
          show: false,
          selectOptions: [{ id: "", name: "不限" }]
        },
        {
          props: "header_teacher_id",
          label: "班主任",
          type: "course_staff",
          show: false,
          is_leave: true,
          selectOptions: []
        },
        {
          props: "department_id",
          label: "所属校区",
          show: false,
          selectOptions: [],
          type: "school", // 表单类型
          school_choose_type: "chooseSchool", // 校区是否单选
          use_store_options: true
        },
        {
          props: "teacher_id",
          label: "任课老师",
          type: "course_staff",
          show: false,
          is_leave: true,
          selectOptions: []
        },

        // { props: "student_id", label: "学生姓名", type: "input", show: false },
        {
          props: "year",
          label: "年份",
          type: "select",
          show: false,
          selectOptions: [{ id: "", name: "不限" }]
        },
        {
          props: "scheduling_form",
          label: "上课形式",
          type: "select",
          show: false,
          selectOptions: [{ id: "", name: "不限" }]
        },
        {
          props: "course_id",
          label: "课程名称",
          type: "choose_course",
          show: false
        },
        {
          props: "student_id",
          label: "学员",
          type: "student",
          show: true,
          placeholder: "请输入学员姓名/学号/手机号"
        },
        {
          props: "school_room_id",
          label: "上课教室",
          type: "choose_room",
          show: true,
          placeholder: "请选择上课教室"
        },
        {
          props: "scheduling_employee_id",
          label: "点名人",
          type: "course_staff",
          show: false,
          is_leave: true,
          selectOptions: []
        }
        //   label: "计划结业日期",
        //   type: "date",
        //   show: false,
        //   width: 200,
        // },
      ],
      // 筛选
      search: {
        course_id: "",
        student_id: "",
        school_room_id: "",
        year: "",
        status: "",
        course_property: "",
        course_type: "",
        name: "",
        classroom_alias_name: "",
        teacher_id: "",
        header_teacher_id: "",
        class_time: "",
        classroom_type: "",
        classroom_status: "",
        created_at: this.getDate(),
        department_id: "",
        department_id_name: "",
        sort: "",
        scheduling_employee_id: ""
      },
      height: window.innerHeight - 370,
      status_list: [],
      pickerReleaseDateOptions: {
        disabledDate: (time) => {
          const curDate = new Date().getTime();
          return time.getTime() > curDate;
        }
      },
      temporary_transfer_visible: false,
      scheduling_id: "",
      specify_visible: false,
      info: {},
      cancel_type: "",
      customer_data: [],
      add_student_visible: false,
      lock_obj: "",
      batch_update_visible: false,
      choose_row: {},
      has_btn: false,
      loading: false,
      list: {},
      copyMoveType: "",
      editorDialogClassroomId: "",
      rowData: {},
      send_type: "",
      send_types: {
        course_summary: "课程总结",
        class_notice: "班级通知",
        parent_class: "家长课堂"
      }
    };
  },
  computed: {
    // 班级列表
    schoolServicesChedulingList() {
      return this.$store.getters.doneGetSchoolServicesChedulingList;
    },
    schoolIds() {
      return this.$store.getters.doneGetSchoolId;
    }
  },
  watch: {
    schoolServicesChedulingList(new_obj) {
      this.list = new_obj;
      this.list.results = new_obj?.results || [];
      this.loading = false;
      this.totalLoaded = false;
    },
    // 转
    success(new_bool) {
      if (new_bool) {
        this.clearSelection();
        this.$store.dispatch("getInvitationList", {
          page: 1
        });
      }
    },
    schoolIds: {
      handler() {
        this.search.department_id = "";
        this.search.department_id_name = "";
        this.initList();
        // this.getTotalData();
        this.clearSelection();
      },
      immediate: true
    }
  },
  created() {
    if (
      this.$_has({ m: "scheduling", o: "create" }) ||
      this.$_has({ m: "scheduling", o: "delete" }) ||
      this.$_has({ m: "scheduling", o: "cancel" }) ||
      this.$_has({ m: "scheduling", o: "export" })
    ) {
      this.has_btn = true;
    } else {
      this.height = window.innerHeight - 322;
    }
  },
  methods: {
    sendContent(row, type) {
      console.log(row);
      this.isBatchSend = false;
      this.send_type = type;
      this.editorDialogDepartmentId = row.department_id;
      this.id = row.id;
      this.rowData = row;
      this.editorDialogVisible = true;
    },
    // 发送学员报告
    editorDialogConfirm(content) {
      console.log("content :>> ", content);
      const { send_type, send_types } = this;
      // 询问框提示
      this.$confirm(`确定发送${send_types[send_type]}吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        const data = {
          classroom_id: "",
          classroom_name: "",
          student_id: "",
          student_gender: "",
          student_name: ""
        };
        // let students = [];

        // if (this.checked_student.length > 0 && this.isBatchSend) {
        //   // 批量发送
        //   students = this.checked_student.map((item) => {
        //     return {
        //       student_gender: item.student_gender,
        //       student_id: item.student_id,
        //       student_name: item.student_name
        //     };
        //   });
        //   data.classroom_id = this.checked_student[0].classroom_id;
        //   data.classroom_name = this.checked_student[0].classroom_name;
        // } else {
        //   const {
        //     classroom_id,
        //     classroom_name,
        //     student_id,
        //     student_gender,
        //     student_name
        //   } = this.rowData;
        //   data.classroom_id = classroom_id;
        //   data.classroom_name = classroom_name;
        //   students.push({
        //     student_gender,
        //     student_id,
        //     student_name
        //   });
        // }
        const {
          classroom_id,
          classroom_name
          // student_id,
          // student_gender,
          // student_name
        } = this.rowData;
        console.log(this.rowData, "this.rowData");
        data.classroom_id = classroom_id;
        data.classroom_name = classroom_name;
        // students.push({
        //   student_gender,
        //   student_id,
        //   student_name
        // });
        // console.log(this.checked_student[0]);
        const params = {
          classroom: {
            classroom_id: data.classroom_id,
            classroom_name: data.classroom_name
          },
          content,
          scheduling_id: this.id,
          type: send_type,
          students: []
        };
        console.log(params, "params");
        appletResource
          .feedbackSend(params)
          .then((res) => {
            if (res.data.code === 0) {
              this.$message.success("发送成功！");
              // 清除选中
              this.editorDialogVisible = false;
              this.$refs.table.clearSelection();
              this.initList();
            } else {
              this.$message.error(res.data.message);
            }
          })
          .catch((err) => {
            console.error(err);
            this.$message.error("发送失败！");
          });
      });
    },
    copyMoveClose() {
      this.copy_move_schedule_visible = false;
    },
    copyMoveConfirm() {
      this.copy_move_schedule_visible = false;
      this.initList();
    },
    moveSchedulingClick() {
      this.copyMoveType = "move";
      if (this.schoolIds.length > 1) {
        this.school_tree_visible = true;
      } else {
        this.single_department_id = this.schoolIds[0];
        this.copy_move_schedule_visible = true;
      }
    },

    copySchedulingClick() {
      this.copyMoveType = "copy";
      if (this.schoolIds.length > 1) {
        this.school_tree_visible = true;
      } else {
        this.single_department_id = this.schoolIds[0];
        this.copy_move_schedule_visible = true;
      }
    },
    confirmSchool() {
      this.copy_move_schedule_visible = true;
    },
    showPushDialog(data) {
      this.pushActionVisible = false;
      if (data.data.length > 0) {
        this.pushActionData = data;
        this.pushActionVisible = true;
      }
    },
    pushActionCancel() {
      this.pushActionVisible = false;
    },
    pushActionConfirm() {
      this.pushActionVisible = false;
    },
    getSummariesCellJsx(slot1, slot2, slot3) {
      let jsx = null;
      const { totalLoading, totalLoaded } = this;
      // const empty =
      //   roll_called_student_numb === "" && total_student_numb === "";
      jsx = (
        <span>
          {totalLoading ? (
            <i class="el-icon-loading"></i>
          ) : (
            <span>{totalLoaded ? `${slot1}/${slot2}/${slot3}` : "*"}</span>
          )}
        </span>
      );
      return jsx;
    },
    getSummaries(param) {
      const sums = [];
      if (this.totalData) {
        const { columns } = param;
        console.log(columns);
        columns.forEach(({ property }, index) => {
          // console.log(property);
          if (property === "classroom_name") {
            sums[index] = this.getSummariesJsx();
            return;
          }
          if (property === "finish_lesson_numb") {
            // sums[index] = `${roll_called_student_numb}/${total_student_numb}`;
            sums[index] = this.getSummariesCellJsx(
              this.totalData.roll_called_student_numb,
              this.totalData.billable_student_numb,
              this.totalData.total_student_numb
            );
            return;
          }
          if (property === "course_summary") {
            sums[index] = this.getSummariesCellJsx(
              this.totalData.course_summary_numb,
              this.totalData.roll_called_student_numb,
              this.totalData.total_student_numb
            );
            return;
          }
          if (property === "parent_class") {
            sums[index] = this.getSummariesCellJsx(
              this.totalData.parent_class_numb,
              this.totalData.roll_called_student_numb,
              this.totalData.total_student_numb
            );
            return;
          }
          sums[index] = "";
        });
        return sums;
      } else {
        return [];
      }
    },
    specifyNameClose() {
      this.searchStaff();
      this.specify_visible = false;
    },
    show_actual_attendance(row) {
      this.scheduling_id = row.id;
      this.actual_attendance_visible = true;
    },
    show_course_summary(row, key) {
      this.course_summar_dialog_visible = true;
      this.send_type = key;
      this.rowData = row;
      this.scheduling_id = row.id;
    },
    closeCreateDialog() {
      this.createDialog = false;
    },
    closeUpdateDialog() {
      this.updateDialog = false;
    },
    cancel() {
      this.cancellation = false;
      this.$set(this.form, "memo", "");
    },
    openEdit(row) {
      this.rowData = row;
      this.updateDialog = true;
    },
    // 批量取消
    batchCancel() {
      if (+this.customer_data.length === 0) return;
      this.cancellation = true;
      this.cancel_type = "more";
      // this.back();
    },
    // 取消，批量取消确认
    cancellationConfirm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const data = {
            memo: this.form.memo,
            scheduling_id:
              this.cancel_type === "only"
                ? [this.scheduling_id]
                : this.customer_data.map((item) => item.id)
          };
          schoolServiceSchedulingExcelApi.cancelScheduling(data).then((res) => {
            if (typeof res.err === "undefined") {
              this.$message.success("取消成功");
              this.cancel();
              this.initList();
              // this.getTotalData();
            }
          });
        } else {
          this.$message.error("请输入必填信息");
          return false;
        }
      });
    },
    // 批量删除
    del(type, row) {
      if (type === 2 && +this.customer_data.length === 0) return;
      this.$confirm("此操作将删除排课, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          const id =
            type === 1 ? row.id : this.customer_data.map((item) => item.id);
          schoolServiceSchedulingExcelApi
            .schoolServiceSchedulingDelete({ scheduling_id: id })
            .then(() => {
              this.initList();
              // this.getTotalData();
              this.clearSelection();
            });
        })
        .catch(() => {});
    },
    initList() {
      this.loading = true;
      this.list.results = [];
      const search = this.changeSearch(this.search);
      this.clearSelection();
      const query = {
        page: this.page,
        page_size: this.page_size,
        ...search
      };
      if (query.department_id) {
        query.department_id = query.department_id.split(",");
      } else {
        query.department_id = this.schoolIds;
      }
      this.$store.dispatch("getSchoolServicesChedulingList", query);
    },
    cancelClass() {
      this.dialogFinished = false;
      this.cancel_class_visible = true;
    },
    cancelClassClose() {
      this.cancel_class_visible = false;
      this.initList();
    },

    classroomContent() {
      schoolServiceSchedulingExcelApi
        .updateContext({
          scheduling_id: this.rowdata.id,
          memo: this.formContent.memo, // 备注
          class_context: this.formContent.class_context, // 上课内容
          class_ware: this.formContent.class_ware //	上课课件
        })
        .then((res) => {
          if (!res.err) {
            this.$message.success("提交成功!");
            this.dialogFinished_Content = false;
          }
          for (const k in this.formContent) {
            this.formContent[k] = "";
          }
          this.initList();
          // this.getTotalData();
        });
    },

    openDialogFinished(row) {
      const data = {
        date_time: this.getTime(row.start_time),
        department_id: row.department_id
      };
      schoolServiceSchedulingExcelApi.lockRange(data).then((result) => {
        if (result.data.code === 0) {
          if (!result.data.data) {
            this.dialogFinished = true;
            this.rowData = row;
          } else {
            this.$message.warning("处于锁账周期内，暂不支持撤销上课!");
          }
        }
      });
    },
    modificationContent(row) {
      this.dialogFinished_Content = true;
      this.rowdata = row;
    },

    // 多选框
    handleSelectionChange(val) {
      this.customer_data = val;
    },
    // 时间格式
    getTime(time) {
      return timeFormat.GetTime(time);
    },
    getList(dialog) {
      if (dialog !== "specifyByName") {
        this.specify_visible = false;
        this.add_student_visible = false;
        this.batch_update_visible = false;
      }
      this.clearSelection();
      const search = this.changeSearch();
      const data = {
        page: this.page,
        page_size: this.page_size,
        ...search
      };
      data.department_id = this.schoolIds;
      // this.getTotalData();
      this.initList();
    },
    // 操作
    openDialog(type, row) {
      const data = {
        date_time: this.getTime(row.start_time),
        department_id: row.department_id
      };
      this.rowInfo = row;
      this.scheduling_id = row.id;
      if (+type === 1) {
        this.temporary_transfer_visible = true;
      } else if (+type === 2) {
        // 判断是否处在锁账周期内
        schoolServiceSchedulingExcelApi.lockRange(data).then((result) => {
          if (+result.status === 200 && +result.data.code === 0) {
            // false 则正常操作
            if (!result.data.data) {
              // 提交点名上锁
              specifyByNameApi
                .specifyLock({ scheduling_id: this.scheduling_id })
                .then((res) => {
                  if (+res.status === 200 && +res.data.code === 0) {
                    this.lock_obj = res.data.data.code_no;
                    this.specify_visible = true;
                  } else {
                    const { data, message } = res.data;
                    if (data) {
                      this.$message.error(
                        `该排课【${data}】正在点名中，请稍后再试`
                      );
                    } else {
                      this.$message.error(message);
                    }
                  }
                });
            } else {
              this.$message.warning("处于锁账周期内，暂不支持点名上课!");
            }
          }
        });
      } else if (+type === 3) {
        this.cancel_type = "only";
        this.cancellation = true;
        this.getInfo({ scheduling_id: this.scheduling_id });
      } else if (+type === 4) {
        schoolServiceSchedulingExcelApi.lockRange(data).then((result) => {
          if (+result.status === 200 && +result.data.code === 0) {
            if (!result.data.data) {
              this.add_student_visible = true;
              this.add_type = "add";
            } else {
              this.$message.warning("处于锁账周期内，暂不支持添加学员!");
            }
          }
        });
      } else if (+type === 5) {
        schoolServiceSchedulingExcelApi.lockRange(data).then((result) => {
          if (+result.status === 200 && +result.data.code === 0) {
            if (!result.data.data) {
              this.add_student_visible = true;
              this.add_type = "remove";
            } else {
              this.$message.warning("处于锁账周期内，暂不支持移除学员!");
            }
          }
        });
      } else if (+type === 6) {
        this.add_student_visible = true;
        this.add_type = "modify_memo";
      } else if (+type === 7) {
        this.batch_update_visible = true;
        this.choose_row = row;
      }
    },
    // 导出
    exportExcel() {
      const search = this.changeSearch();
      const opt = {
        vm: this, // vue组件实例，
        api_url: "/api/report-center-service/admin/school/schedulingExport", // 导出接口地址
        file_name: "排课管理", // 文件名
        success_msg: "排课管理数据导出成功！", // 导出成功的提示语
        error_msg: "排课管理数据导出失败！", // 导出失败的提示语,
        query: {
          ...search,
          status_type: "intention",
          department_id: this.schoolIds
        } // 列表的筛选值
      };
      export_excel_sync_new(opt);
    },
    getSubChannel(id) {
      const params = {
        parentid: id,
        page: 1,
        pageSize: 1000,
        _t: new Date().getTime(),
        order: "desc",
        is_enabled: true
      };
      this.$store.dispatch("getSubChannelByChannelId", { params });
    },
    clearSelection() {
      this.$nextTick(() => {
        this.$refs.table.clearSelection();
      });
    },
    // 查询接口
    searchStaff() {
      this.clearSelection();
      const search = this.changeSearch(this.search);
      if (!search.department_id) {
        //
        search.department_id = this.schoolIds;
      }
      this.page = 1;
      console.log(search);
      this.initList();
      // this.getTotalData();
    },
    sortChange(val) {
      const { prop, order } = val;
      let _oreder = "";
      if (order === "ascending") {
        _oreder = "asc";
      } else if (order === "descending") {
        _oreder = "desc";
      }
      this.search.sort = `${prop} ${_oreder}`;
      this.searchStaff();
    },
    changeSearch() {
      const search = JSON.parse(JSON.stringify(this.search));
      if (
        search.created_at &&
        search.created_at !== "" &&
        typeof search.created_at !== "undefined"
      ) {
        search.class_time_begin = search.created_at[0];
        search.class_time_over = search.created_at[1];
      }
      if (search.course_id) {
        search.course_id = search.course_id.split(",");
      }
      if (
        search.pre_begin_end_time &&
        search.sign_up_time !== "" &&
        typeof search.sign_up_time !== "undefined"
      ) {
        search.pre_begin_end_time = search.sign_up_time[0];
        search.pre_begin_start_time = search.sign_up_time[1];
      }
      delete search.time;
      delete search.sign_up_time;
      delete search.created_at;
      return search;
    },

    reset() {
      this.search = {
        scheduling_form: "",
        course_id: "",
        student_id: "",
        school_room_id: "",
        status: "",
        year: "",
        course_property: "",
        course_type: "",
        name: "",
        classroom_alias_name: "",
        teacher_id: "",
        header_teacher_id: "",
        department_id: "",
        class_time: "",
        classroom_type: "",
        classroom_status: "",
        created_at: "",
        alias_name: "",
        sort: "",
        scheduling_employee_id: ""
      };
      this.clearSelection();
      this.page = 1;
      this.page_size = 10;
      this.$refs.table.clearSort();
      this.initList();
    },

    updateReset() {
      // this.getTotalData();
      this.initList();
    },
    // 获取合计数据
    obtainSumData() {
      const search = this.changeSearch(this.search);
      const query = {
        ...search
      };
      if (query.department_id) {
        query.department_id = query.department_id.split(",");
      } else {
        query.department_id = this.schoolIds;
      }
      this.totalLoading = true;
      schoolServiceSchedulingExcelApi
        .GetSchoolServicesChedulingTotalList(query)
        .then((res) => {
          this.totalLoading = false;
          if (+res.status === 200 && res.data) {
            this.totalData = res.data;
            this.totalLoaded = true;
          } else {
            this.$message.error("获取合计数据失败！");
          }
        })
        .catch(() => {
          this.totalLoading = false;
        });
    },
    // 分页
    currentChange(val) {
      this.page = val;
      this.initList();
    },
    sizeChange(val) {
      this.page = 1;
      this.page_size = val;
      this.initList();
      // this.getTotalData();
    },
    getRowKeys(row) {
      return row.id;
    },

    getSchoolServiceClassroomMapStatus() {
      classManagementApi.GetSchoolServiceClassroomMapStatus().then((res) => {
        for (const key in res.data) {
          this.searchTitle[3].selectOptions.push({
            id: key,
            name: res.data[key]
          });
        }
      });
    },
    getSchoolServiceClassroomMapType() {
      classManagementApi.GetSchoolServiceClassroomMapType().then((res) => {
        for (const key in res.data) {
          this.searchTitle[6].selectOptions.push({
            id: key,
            name: res.data[key]
          });
        }
      });
    },

    getCourseServiceCourseMapStatus() {
      schoolServiceSchedulingExcelApi
        .GetCourseServiceCourseMapStatus()
        .then((res) => {
          for (const key in res.data) {
            this.searchTitle[7].selectOptions.push({
              id: key,
              name: res.data[key]
            });
          }
        });
    },
    getCourseServiceSchedulingMapform() {
      schoolServiceSchedulingExcelApi
        .GetCourseServiceSchedulingMapform()
        .then((res) => {
          for (const key in res.data) {
            this.searchTitle[12].selectOptions.push({
              id: key,
              name: res.data[key]
            });
          }
        });
    },
    getInfo(data) {
      schoolServiceSchedulingExcelApi.infoScheduling(data).then((res) => {
        this.info = res.data;
        const format_class_time = `${this.moment(this.info.start_time).format(
          "YYYY-MM-DD HH:mm"
        )}-${this.moment(this.info.end_time).format("HH:mm")} (${
          this.info.week_day_chn
        })`;
        this.$set(this.info, "format_class_time", format_class_time);
      });
    },

    getSelect(str) {
      getCourseConfigByType({}, str).then((res) => {
        if (res.data) {
          const arr = [];
          res.data.map((item) => {
            arr.push({
              id: item.config_value,
              name: item.config_name
            });
          });
          if (str === "course_genre") {
            this.searchTitle[5].selectOptions.push(...arr);
          }
          if (str === "course_attribute") {
            this.searchTitle[4].selectOptions.push(...arr);
          }
        }
      });
    },
    getDate() {
      const start = new Date();
      const end = new Date();
      const nows = start.getDay() || 7; // 注意周日算第一天，如果周日查询本周的话，天数是0，所有如     果是0，默认设置为7
      start.setTime(start.getTime() - 3600 * 1000 * 24 * (nows - 1));
      end.setTime(start.getTime() + 3600 * 1000 * 24 * 6);
      return [start, end];
    }
  },
  mounted() {
    this.getCourseServiceSchedulingMapform(); // 获取上课形式
    this.getCourseServiceCourseMapStatus(); // 获取上课状态
    this.getSchoolServiceClassroomMapStatus(); // 班级状态
    this.getSchoolServiceClassroomMapType(); // 班级类型
    this.getSelect("course_genre"); // 获取课程类型
    this.getSelect("course_attribute"); // 获取课程属性
    const years = [
      {
        id: "",
        name: "不限"
      }
    ];
    course_years.map((item) => {
      years.push({
        id: item,
        name: item
      });
    });
    this.searchTitle[11].selectOptions = years;
  }
};
</script>
<style lang="less" scoped>
::v-deep .el-table {
  height: auto;
}
#schoolServiceScheduling {
  // height: calc(100vh - 76px);
  ::v-deep .el-table td:last-child {
    border-right-color: transparent !important;
  }
  ::v-deep .el-table__header th:last-child {
    border-right-color: transparent !important;
  }
  ::v-deep .el-table__fixed-footer-wrapper td {
    left: 16px;
    border-right-color: transparent !important;
  }
  .search {
    background: #ffffff;
    box-shadow: 0 2px 6px 0 #ccd0d9;
    border-radius: 4px;
    height: 112px;
    margin: 0 6px;
  }
  .tg-button__icon--normal {
    width: 10px;
    height: 11px;
    margin-right: 9px;
  }
  .el-tabs__nav-wrap::after {
    background-color: #ffffff !important;
  }
  .el-tabs__nav-scroll {
    background: #f5f8fc !important;
  }
  .el-table__expanded-cell[class*="cell"] {
    border: 1px solid #2d80ed;
  }
  .search .el-form-item__content {
    line-height: 40px;
  }
  .el-dialog__footer {
    margin-top: -45px;
    /* padding: 11px 24px; */
  }
  .el-textarea__inner {
    height: 195px !important;
  }
  .tips {
    color: #b3b7c6;
    font-size: @text-size_special;
    font-family: @text-famliy_medium;
    margin-left: 8px;
    line-height: 32px;
  }
  .tips__img {
    width: 10px;
    height: 10px;
    margin-left: 16px;
  }
}
/deep/ .modify-teacher {
  .tg_select {
    width: 300px;
  }
}
.tip-box {
  height: 68px;
  padding: 10px;
  background-color: #f5f8fc;
  .text {
    display: flex;
    align-items: center;
    img {
      width: 16px;
      margin-right: 10px;
    }
  }
  .txt {
    margin-left: 26px;
    color: #fd6865;
    margin-top: 10px;
  }
}
/deep/ .el-dialog__body {
  // border-bottom: 1px solid #f1f1f1;
  border-bottom: none;
}
::v-deep .el-dialog__body {
  padding: 0px 16px;
}
.cancelDialog {
  /deep/ .el-dialog__body {
    display: flex;
    flex-direction: column;
    padding: 0 16px;
    border-bottom: none;
  }
}
.cancellation-top {
  background: #f5f8fc;
  border: 1px solid #2d80ed;
  border-radius: 4px;
  width: 100%;
  height: 32px;
  display: flex;
  align-items: center;
  .cancellation__img {
    width: 10px;
    height: 10px;
  }
}
.tab {
  border: 1px solid #2d80ed;
  border-radius: 4px;
  height: 48px;
  margin-top: 16px;
  background: #ffffff;
  // box-shadow: 0 6px 12px 0 rgba(0, 0, 0, 0.04);
  width: 100%;
  overflow: hidden;
}
.mail-table {
  width: 100%;
}
.mail-table tr td {
  // line-height: 35px;
  box-sizing: border-box;
  padding: 0 10px;
  width: 35%;
}
.mail-table tr td.column {
  text-align: center;
  background-color: #eff3f6;
  color: #393c3e;
  width: 80px;
}
::v-deep .el-textarea__inner {
  height: 160px;
}
.dialog_content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
::v-deep .el-table__body-wrapper {
  height: calc(100% - 46px);
  // .el-table__empty-block {
  //   display: flex;
  //   align-items: normal;
  //   justify-content: normal;
  // }
  .empty-container {
    position: absolute;
    top: 50%;
    left: 50%;
  }
  .loading-container {
    position: absolute;
    top: 30%;
    left: 1%;
    background: transparent;
    .box {
      height: 100%;
    }
  }
}
</style>
