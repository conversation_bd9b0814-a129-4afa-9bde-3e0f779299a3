<!--选择学员-->
<template>
  <div>
    <el-dialog
      :visible="true"
      width="1016px"
      :before-close="handleClose"
      class="create-sched"
    >
      <div slot="title">
        <span class="dialogTip">新增排课</span>
      </div>
      <div class="tg-dialog__content">
        <div class="search tg-box--margin">
          <el-form
            label-width="80px"
            label-position="right"
            @submit.native.prevent
            :model="form"
            ref="schedulingForm"
          >
            <div class="custom--select">
              <el-form-item label="班级" prop="classroom_id" required>
                <el-input
                  v-model="classroom_name"
                  readonly
                  :disabled="match_index > 0"
                  placeholder="请选择班级"
                  @click.native="showChooseClassDio"
                  @mouseenter.native="class_flag = true"
                  @mouseleave.native="class_flag = false"
                  :class="{ 'border--active': class_flag }"
                  style="width: 320px"
                >
                  <img
                    :src="
                      !class_flag
                        ? require('../../assets/图片/icon_more.png')
                        : require('../../assets/图片/icon_more_ac.png')
                    "
                    slot="suffix"
                    class="more"
                  />
                </el-input>
              </el-form-item>
            </div>
            <el-form-item
              label="任课老师"
              required
              prop="teacher_id"
              class="tg-form-item tg-box--margin"
              :popper-append-to-body="false"
            >
              <course-staff
                staff_placeholder="请选择任课老师"
                :check_id.sync="form.teacher_id"
                :check_name.sync="form.teacher_name"
                style="display: inline-block; width: 320px"
              ></course-staff>
              <el-button
                class="tg-button--primary"
                type="primary"
                style="width: auto; margin-left: 20px"
                @click="openTeacherDialog"
              >
                查看老师空闲时间
              </el-button>
            </el-form-item>
            <el-form-item
              label="助教"
              prop="assistant_teacher_id"
              class="tg-form-item tg-box--margin"
              :popper-append-to-body="false"
            >
              <market-staff
                :check_ids.sync="form.assistant_teacher_id"
                :check_names.sync="form.assistant_teacher_name"
                :obj="form.assistant_teacher_map"
                :staff_placeholder="'请选择助教老师'"
                v-if="reload"
                :img_type="'square'"
              ></market-staff>
            </el-form-item>
            <el-form-item
              label="排课日期"
              required
              prop="end_date"
              class="tg-form-item tg-box--margin"
            >
              <el-date-picker
                style="width: 320px"
                v-model="date"
                type="daterange"
                range-separator="至"
                value-format="yyyy-MM-dd"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                popper-class="tg-date-picker tg-date--range"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item
              label="最多排课"
              required
              prop="max_scheduling_numb"
              class="tg-form-item tg-box--margin"
            >
              <el-input-number
                style="width: 320px"
                v-model="form.max_scheduling_numb"
                :precision="0"
                :min="0"
                size="small"
                :max="max_lesson_numb"
                placeholder="请输入最多排课次数"
              ></el-input-number>
              <span style="margin-left: 10px">次</span>
            </el-form-item>
            <el-form-item
              label="上课时间"
              required
              class="tg-form-item tg-box--margin"
            >
              <div class="tg-table__box">
                <!-- <div class="tg-box--border"></div> -->
                <el-table
                  ref="table"
                  :data="class_time_data"
                  class="tg-table"
                  size="small"
                  highlight-current-row
                  style="width: 100%"
                  @selection-change="handleSelectionChange"
                  :row-key="getRowKeys"
                  cell-class-name="cell-style"
                  header-cell-class-name="header-cell-style"
                >
                  <el-table-column
                    type="selection"
                    width="50"
                    :reserve-selection="true"
                  ></el-table-column>
                  <el-table-column label="星期" prop="week_day">
                    <template slot-scope="scope">
                      {{ scope.row.week_day | formatWeek }}
                    </template>
                  </el-table-column>
                  <el-table-column label="开始时间" prop="start_time">
                    <template slot-scope="scope">
                      <span
                        v-if="
                          weekTimeOverlappingArr[scope.row.week_day].indexOf(
                            scope.row.start_time
                          ) > -1
                        "
                        style="color: red"
                        >{{ scope.row.start_time }}</span
                      >
                      <span v-else>{{ scope.row.start_time }}</span>
                    </template></el-table-column
                  >
                  <el-table-column label="结束时间" prop="end_time">
                    <template slot-scope="scope">
                      <span
                        v-if="
                          weekTimeOverlappingArr[scope.row.week_day].indexOf(
                            scope.row.end_time
                          ) > -1
                        "
                        style="color: red"
                        >{{ scope.row.end_time }}</span
                      >
                      <span v-else>{{ scope.row.end_time }}</span>
                    </template></el-table-column
                  >
                  <el-table-column
                    label="教室"
                    prop="school_room_name"
                  ></el-table-column>
                  <el-table-column
                    v-if="
                      form.classroom_type === 'reschedule' ||
                      form.classroom_type === 'audition'
                    "
                    label="操作"
                    prop=""
                  >
                    <template slot-scope="scope">
                      <el-button
                        type="text"
                        size="small"
                        @click="handleDelete(scope.$index, scope.row)"
                      >
                        移除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div
                v-if="
                  form.classroom_type === 'reschedule' ||
                  form.classroom_type === 'audition'
                "
                class="tg-box--margin tg-button__wrap"
                @click="addToVisible = true"
              >
                <img
                  class="tg-button--pointer"
                  src="../../assets/图片/icon_add.png"
                  alt=""
                />
                <span type="text" class="tg-button--pointer">编辑上课时间</span>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <span class="footer-left">
          <el-checkbox
            :disabled="match_index > 0 || class_match_index > 0"
            v-model="form.check_audit"
            >检查上课冲突</el-checkbox
          >
        </span>
        <span class="footer-right">
          <el-button class="tg-button--plain" type="plain" @click="back"
            >取消</el-button
          >
          <el-button class="tg-button--primary" type="primary" @click="preview"
            >预览排课</el-button
          >
          <el-button
            class="tg-button--primary"
            type="primary"
            @click="submitData"
            >确定</el-button
          >
        </span>
      </span>
    </el-dialog>
    <choose-class
      :check_id.sync="form.classroom_id"
      :check_name.sync="classroom_name"
      :course_id.sync="course_id"
      :choose_class_visible="showChooseClass"
      @close="closeChooseClassDio"
      v-if="showChooseClass"
    ></choose-class>
    <preview
      @closePriview="closePriview"
      :data="form"
      v-if="priview_class_visible"
    ></preview>
    <!-- <teacher-free-time v-if="false" :id="[form.teacher_id]"></teacher-free-time> -->
    <teacher-free-time
      :id="[form.teacher_id]"
      :name="[form.teacher_name]"
      :department_id="departmentIds"
      :check_map="{ [form.teacher_id]: form.teacher_name }"
      v-if="teacher_free_time_visible"
      @close="teacher_free_time_visible = false"
    ></teacher-free-time>
    <AddTo
      @close="addToVisible = false"
      v-if="addToVisible"
      :courseId="course_id"
      :classroom_type="form.classroom_type"
      @add="addList"
      :list="[]"
      :departmentId="departmentIds"
    >
    </AddTo>
  </div>
</template>
<script>
import apiScheduling from "@/api/schoolServiceScheduling";
import classManagement from "@/api/classManagement";
import ChooseClass from "./chooseClass.vue";
import Preview from "./preview.vue";
import CourseStaff from "@/components/staff/courseStaff.vue";
import MarketStaff from "@/components/staff/marketStaff.vue";
import TeacherFreeTime from "@/components/freeTime/teacherFreeTime.vue";
import AddTo from "@/components/classManagement/addTo.vue"; // 新增班级
import { timeIsOverlapping } from "@/public/utils.js";
import { v4 as uuidv4 } from "uuid";
export default {
  data() {
    return {
      teacher_free_time_visible: false,
      addToVisible: false,
      class_flag: false,
      course_id: "",
      classroom_name: "",
      teacher_name: "",
      assistant_teacher_name: [],
      responsible_map: {},
      date: null,
      form: {
        classroom_id: "",
        assistant_teacher_id: [],
        assistant_teacher_name: [],
        assistant_teacher_map: {},
        check_audit: true,
        class_time_data: [],
        end_date: "",
        max_scheduling_numb: undefined,
        start_date: "",
        teacher_id: "",
        teacher_name: "",
        classroom_type: ""
      },
      showChooseClass: false,
      priview_class_visible: false,
      list: [],
      class_time_data: [],
      class_match_index: 0,
      // pickerReleaseDateOptions: {
      //   disabledDate: (time) => {
      //     time.setHours(23, 59, 59, 0);
      //     let curDate = new Date().getTime();
      //     return time.getTime() < curDate;
      //   },
      // },
      reload: true,
      max_lesson_numb: 0
    };
  },
  components: {
    // schoolTree
    ChooseClass,
    CourseStaff,
    MarketStaff,
    Preview,
    TeacherFreeTime,
    AddTo
  },
  props: {
    classroom_id: {
      type: String,
      default: ""
    },
    classroom_name_match: {
      type: String,
      default: ""
    },
    match_index: {
      type: Number,
      default: 0
    }
  },
  computed: {
    departmentIds() {
      return this.$store.getters.doneGetSchoolId;
    },
    // 按星期分组出重复时间段
    weekTimeOverlappingArr() {
      const { class_time_data } = this;
      const obj = this.$lodash.groupBy(class_time_data, function (item) {
        return item.week_day;
      });
      const obj1 = {};
      for (const key in obj) {
        obj1[key] = timeIsOverlapping(obj[key], "start_time", "end_time");
      }
      return obj1;
    },
    isoverlapping() {
      let bool = false;
      for (const key in this.weekTimeOverlappingArr) {
        if (this.weekTimeOverlappingArr[key].length > 0) {
          bool = true;
        }
      }
      return bool;
    }
  },
  created() {
    this.$store.dispatch("getSchoolroomList", {
      page: 1,
      department_id: this.departmentIds
    });
    // apiScheduling.createScheduling({});
    this.select_class_time_data = [];
    // 赛事入班排课
    if (this.match_index) {
      this.form.classroom_id = this.classroom_id;
      this.classroom_name = this.classroom_name_match;
      this.form.check_audit = false;
      this.getClassInfo();
    }
  },
  mounted() {},
  // updated() {
  //   this.toggleSelection(this.class_time_data);
  // },
  watch: {
    date: {
      handler(newObj) {
        if (newObj) {
          this.form.start_date = newObj[0];
          this.form.end_date = newObj[1];
        } else {
          this.form.start_date = "";
          this.form.end_date = "";
        }
      },
      immediate: false
    }
  },
  methods: {
    addList(data) {
      data.forEach((item1) => {
        item1.classroom_id = this.form.classroom_id;
      });
      const arr = this.mergeSchedules(this.class_time_data, data);
      this.class_time_data = arr.sort((a, b) => {
        return a.week_day - b.week_day;
      });
      console.log(this.class_time_data);
    },

    mergeSchedules(arr1, arr2) {
      for (let i = 0; i < arr2.length; i++) {
        const obj2 = arr2[i];
        let found = false;

        for (let j = 0; j < arr1.length; j++) {
          const obj1 = arr1[j];

          if (
            obj2.week_day === obj1.week_day &&
            obj2.end_time === obj1.end_time &&
            obj2.start_time === obj1.start_time
          ) {
            // 如果存在相同的 week_day, end_time 和 start_time，覆盖 arr1 中对应 week_day 的数据
            obj2.id = obj1.id;
            arr1[j] = Object.assign(obj1, obj2);
            found = true;
            break;
          }
        }

        if (!found) {
          // 如果不存在相同的 week_day, end_time 和 start_time，将 obj2 插入到 arr1 中
          obj2.id = "";
          arr1.push(obj2);
        }
      }

      return arr1;
    },
    handleDelete(index, row) {
      this.class_time_data.splice(index, 1);
    },
    openTeacherDialog() {
      this.teacher_free_time_visible = true;
    },
    handleClose() {
      this.back();
    },
    closePriview(val) {
      this.priview_class_visible = false;
      if (val === true) {
        this.submitData();
        this.back();
      }
    },
    back() {
      this.$emit("closeCreateDialog");
    },

    really() {
      if (!this.right_stu_list.length) {
        this.$message.info("请先添加学员!");
        return;
      }
      this.showChooseClassDio();
    },
    // 获取当前班级信息
    getClassInfo() {
      this.reload = false;
      const { classroom_id } = this.form;
      if (classroom_id) {
        classManagement
          .GetSchoolServiceClassroomInfo({
            id: classroom_id
          })
          .then((res) => {
            if (!res.err) {
              this.reload = true;
              const {
                left_lesson_numb,
                class_time_data,
                teacher_name,
                teacher_id,
                assistant_teacher_name,
                assistant_teacher_id,
                assistant_teacher_map,
                classroom_type,
                match_index
              } = res.data;
              this.class_time_data = class_time_data || [];
              this.form.classroom_type = classroom_type;
              this.form.max_scheduling_numb = left_lesson_numb;
              this.max_lesson_numb = left_lesson_numb;
              this.class_match_index = match_index;
              this.form.check_audit = !(match_index > 0);
              this.$set(this.form, "teacher_name", teacher_name);
              this.$set(this.form, "teacher_id", teacher_id);
              this.$set(
                this.form,
                "assistant_teacher_name",
                assistant_teacher_name
              );
              this.$set(
                this.form,
                "assistant_teacher_id",
                assistant_teacher_id
              );
              this.$set(
                this.form,
                "assistant_teacher_map",
                assistant_teacher_map
              );
              console.log(this.form);
              this.toggleSelection(this.class_time_data);
            }
          });
      }
    },
    // getList(d) {
    //   d.status = ["in_classroom", "wait_in_classroom"];
    //   classManagementApi
    //     .GetSchoolServiceClassroomStudentList(d)
    //     .then(res => {
    //       this.list = res.data.results == null ? [] : res.data.results;
    //       this.is_loading = false;
    //       this.total = res.data.count;
    //     })
    //     .catch(err => {
    //       this.is_loading = false;
    //     });
    // },
    showChooseClassDio() {
      if (this.match_index) {
        return;
      }
      this.showChooseClass = true;
    },
    closeChooseClassDio() {
      this.showChooseClass = false;
    },
    handleSelectionChange(val) {
      // console.log(val);
      // this.form.class_time_data = val;
      this.$set(this.form, "class_time_data", val);
      // console.log(this.form)
    },
    getRowKeys(row) {
      return uuidv4();
    },
    preview() {
      this.$refs.schedulingForm.validate((valid) => {
        if (valid) {
          if (this.isoverlapping) {
            this.$message.info("相同星期时间段有重叠部分，请检查后重新提交！");
            return false;
          }
          if (!this.form.class_time_data.length) {
            this.$message.info("请选择上课时间");
            return;
          }
          this.priview_class_visible = true;
        }
      });
    },
    toggleSelection(rows) {
      rows.forEach((row) => {
        this.$refs.table.toggleRowSelection(row, true);
      });
    },

    submitData() {
      this.$refs.schedulingForm.validate((valid) => {
        if (valid) {
          if (this.isoverlapping) {
            this.$message.info("相同星期时间段有重叠部分，请检查后重新提交！");
            return false;
          }
          if (!this.form.class_time_data.length) {
            this.$message.info("上课时间不能为空,请选择上课时间!");
            return;
          }
          // this.$store.dispatch("getClassroomPreview", this.form);
          const { classroom_type } = this.form;
          if (
            classroom_type === "reschedule" ||
            classroom_type === "audition"
          ) {
            this.form.class_time_data = this.class_time_data;
          }
          apiScheduling.createScheduling(this.form).then((res) => {
            if (Array.isArray(res.data)) {
              this.priview_class_visible = true;
              return;
            }
            if (!res.err) {
              this.$message.success("提交成功!");
              // this.tempTable.data = [];
              this.back();
              if (this.match_index) {
                this.$emit("confirmScheduling");
              } else {
                this.$parent.reset();
              }
            }
          });
        }
      });
    }
  }
};
</script>
<style lang="less" scoped>
.create-sched {
  /deep/ .el-dialog__body {
    border-bottom: none;
  }
  .dialogTip {
    font-family: "PingFangSC-Semibold, sans-serif,Arial";
    font-size: 16px;
    font-weight: 600;
  }
  .search {
    width: 100%;
    ::v-deep .el-form-item__content,
    ::v-deep .el-form-item__label {
      line-height: 32px;
    }
    .tg-table {
      padding: 0;
      border: 1px solid @base-color;
    }
    .el-button {
      width: 72px;
      img {
        margin-left: 0;
        margin-right: 7px;
      }
    }
    // .search_teacher {
    //   ::v-deep .el-input {
    //     width: 170px;
    //     .el-input__inner {
    //       padding-left: 16px;
    //     }
    //   }
    // }
    ::v-deep .el-form-item {
      margin-right: 10px;
    }
    ::v-deep .el-form-item:nth-child(2) {
      margin-right: 0;
    }
    ::v-deep .el-form-item.tg_btn_margin {
      margin-right: 10px;
    }
    ::v-deep .tg-form-item {
      margin-right: 20px;
      .el-input {
        width: 320px;
      }
    }
    /deep/ .market-staff {
      .permission-select {
        width: 320px;
      }
    }
    .more {
      width: 16px;
      height: 4px;
      vertical-align: middle;
      margin-right: 8px;
    }
    .border--active {
      position: relative;
      &::after {
        content: "";
        position: absolute;
        width: 320px;
        height: 32px;
        left: -2px;
        top: -2px;
        border: none;
        border-radius: 4px;
        z-index: 10;
      }
      ::v-deep .el-input__inner {
        border-color: @base-color;
      }
    }
    .tg-table__box {
      margin-top: 8px;
      margin-left: 0;
    }
    /deep/ td.cell-style {
      padding: 6px 0;
      text-align: center;
    }
    /deep/ th.header-cell-style {
      text-align: center;
      background-color: #f5f8fc;
      border-right: 1px solid #d3dce6;
      color: #606d7d;
    }
    /deep/ th.header-cell-style {
      &:last-child {
        border-right: none;
      }
    }
  }
  ::v-deep .el-dialog__header {
    border-bottom: 1px solid #e9f0f7;
    .class-title {
      color: @base-color;
    }
  }

  ::v-deep .el-dialog__body {
    padding: 0 16px 0 16px;
  }
  .tg-dialog__content {
    display: flex;
    flex-direction: row;
    height: 589px;
    overflow-y: auto;
    padding-bottom: 20px;
    box-sizing: border-box;
  }
  .dialog-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .required {
    &::before {
      content: "*";
      margin-right: 5px;
      color: #ff0317;
    }
  }
  ::v-deep .market-staff .border--active::after {
    width: 320px;
  }
  .tg-button__wrap {
    padding-bottom: 30px;
    display: inline-flex;
    flex-direction: row;
    align-items: center;
    cursor: pointer;
    img {
      height: 14px;
      width: 14px;
      margin-right: 8px;
    }
    span {
      color: @base-color;
    }
  }
}
</style>
