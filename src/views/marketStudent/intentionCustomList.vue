<!--意向客户-->
<template>
  <div id="intention-custom" class="intention-custom container">
    <tg-search
      :searchTitle.sync="searchTitle"
      :form.sync="search"
      @reset="reset"
      @search="searchVal"
      :showNum="4"
    ></tg-search>
    <el-row class="tg-box--margin tg-shadow--margin tg-row--height">
      <div class="btn__wrap">
        <el-button
          type="plain"
          @click="openAdd('add')"
          class="tg-button--plain"
          v-has="{ m: 'intention', o: 'create' }"
          >新增</el-button
        >
        <el-button
          type="plain"
          class="tg-button--plain"
          @click="del('many')"
          v-has="{ m: 'intention', o: 'batch_delete' }"
          >批量删除</el-button
        >
        <el-button
          type="plain"
          class="tg-button--plain"
          @click="del('one')"
          v-has="{ m: 'intention', o: 'delete' }"
          :class="{ 'tg-button--disabled': checked_clue.length !== 1 }"
          :disabled="checked_clue.length === 1 ? false : true"
          >删除</el-button
        >
        <el-button
          type="plain"
          class="tg-button--plain"
          v-has="{ m: 'intention', o: 'to_audition' }"
          @click="toAudition"
          :class="{ 'tg-button--disabled': !can_listen }"
          :disabled="can_listen ? false : true"
          >转为试听学员</el-button
        >
        <el-button
          type="plain"
          class="tg-button--plain"
          v-has="{ m: 'intention', o: 'to_student' }"
          @click="toStudent"
          :class="{ 'tg-button--disabled': !can_student }"
          :disabled="can_student ? false : true"
          >转为正式学员</el-button
        >
        <el-button
          type="primary"
          @click="transferDepartmentBtn"
          class="tg-button--plain"
          :class="{ 'tg-button--disabled': !isDisabledtransferDepartmentBtn }"
          :disabled="!isDisabledtransferDepartmentBtn"
          v-has="{ m: 'intention', o: 'transferSchoolApprove' }"
          >变更校区</el-button
        >
        <el-button
          type="plain"
          class="tg-button--plain"
          @click="checked_clue.length > 0 ? (batch_edit_flag = true) : ''"
          v-has="{ m: 'intention', o: 'batch_update' }"
          >批量修改</el-button
        >
        <el-button
          type="plain"
          class="tg-button--plain"
          @click="batch_import_flag = true"
          v-has="{ m: 'intention', o: 'import' }"
          >批量导入</el-button
        >
        <el-button
          type="primary"
          icon="el-icon-download"
          @click="downloadExcel"
          class="tg-button--plain"
          v-has="{ m: 'intention', o: 'import' }"
          >下载模板</el-button
        >
        <el-button
          type="plain"
          class="tg-button--plain"
          @click="exportExcel"
          :loading="exportLoading"
          v-has="{ m: 'intention', o: 'export' }"
          >导出</el-button
        >
        <el-button
          type="primary"
          class="tg-button--plain"
          v-has="{ m: 'yiKeOpenCard', o: 'openCardCustomer' }"
          @click="openCard"
          :disabled="!canOpenCard"
          :class="{ 'tg-button--disabled': !canOpenCard }"
          >开卡</el-button
        >
        <!-- <el-button
          type="plain"
          class="tg-button--plain"
          @click="syncXiaogj"
          v-if="is_admin"
          >同步校管家</el-button
        > -->
      </div>
      <select-field
        :allFields.sync="tableTitle"
        :btnType="'button'"
      ></select-field>
    </el-row>
    <div class="tg-table__box tg-box--margin tg-table-container">
      <div class="tg-box--border"></div>
      <el-table
        ref="tableData"
        :data="list"
        tooltip-effect="dark"
        class="tg-table customer-table"
        @selection-change="handleSelectionChange"
        :row-key="getRowKeys"
        border
        v-loading="is_loading"
        :cell-style="{ borderRightColor: '#e0e6ed75' }"
        :header-cell-style="{ borderRightColor: '#e0e6ed75' }"
        @sort-change="sortChange"
      >
        <el-table-column
          type="selection"
          width="50"
          :reserve-selection="true"
          resizable
        ></el-table-column>
        <!-- <el-table-column
          min-width="60"
          label="序号"
          prop="index"
          fixed
        ></el-table-column> -->
        <template v-for="(item, index) in tableTitle">
          <el-table-column
            v-if="item.show"
            :key="index"
            :prop="item.props"
            :label="item.label"
            :min-width="item.width"
            :fixed="item.fixed ? true : false"
            :sortable="item.sort"
            show-overflow-tooltip
            resizable
          >
            <template slot-scope="scope">
              <div v-if="item.props === 'student_name'" class="copy_name">
                <el-button
                  class="tg-text--blue tg-table__name--ellipsis"
                  :class="{ 'tg-text--black': !can_info }"
                  type="text"
                  @click="openEdit(scope.row)"
                  >{{ scope.row.student_name }}
                </el-button>
                <div v-copy="scope.row.student_name"></div>
              </div>
              <template v-else-if="item.props === 'student_mobile'">
                <mobileHyposensitization
                  :mobileTemInfo="{
                    row: scope.row,
                    isShowWeihu: true,
                    isShowCallLog: true,
                    has_eye_limit: scope.row?.has_eye_limit,
                    mobile: scope.row.student_mobile
                  }"
                  @createVoip="createVoip"
                  @openCallLog="openCallLog"
                ></mobileHyposensitization>
              </template>
              <div class="el-rate" v-else-if="item.type === 'rate'">
                <span class="el-rate__item">
                  <i
                    class="el-rate__icon"
                    :class="
                      Number(scope.row[scope.column.property]) >= 1
                        ? 'el-icon-star-on'
                        : 'el-icon-star-off'
                    "
                  ></i>
                </span>
                <span class="el-rate__item">
                  <i
                    class="el-rate__icon"
                    :class="
                      Number(scope.row[scope.column.property]) >= 2
                        ? 'el-icon-star-on'
                        : 'el-icon-star-off'
                    "
                  ></i>
                </span>
                <span class="el-rate__item">
                  <i
                    class="el-rate__icon"
                    :class="
                      Number(scope.row[scope.column.property]) >= 3
                        ? 'el-icon-star-on'
                        : 'el-icon-star-off'
                    "
                  ></i>
                </span>
                <span class="el-rate__item">
                  <i
                    class="el-rate__icon"
                    :class="
                      Number(scope.row[scope.column.property]) >= 4
                        ? 'el-icon-star-on'
                        : 'el-icon-star-off'
                    "
                  ></i>
                </span>
                <span class="el-rate__item">
                  <i
                    class="el-rate__icon"
                    :class="
                      Number(scope.row[scope.column.property]) >= 5
                        ? 'el-icon-star-on'
                        : 'el-icon-star-off'
                    "
                  ></i>
                </span>
              </div>
              <!-- 出生日期单独的，因为出生日期不用准确到时分秒 -->
              <div v-else-if="item.type === 'date.date'">
                {{
                  scope.row[scope.column.property].indexOf("0001-01-01") > -1
                    ? ""
                    : moment(scope.row[scope.column.property]).format(
                        "YYYY-MM-DD"
                      )
                }}
              </div>
              <div v-else-if="item.type === 'date'">
                {{
                  scope.row[scope.column.property].indexOf("0001-01-01") > -1
                    ? ""
                    : getTime(scope.row[scope.column.property])
                }}
              </div>
              <div v-else-if="item.type === 'comp_date'">
                {{ diffTime(scope.row[scope.column.property]) }}
              </div>
              <div
                v-else-if="item.type === 'tag'"
                style="display: flex; align-items: center"
              >
                <div style="max-width: 200px; overflow: hidden">
                  <Tag
                    v-for="(item, index) in scope.row.label_list"
                    :key="index"
                    :msg="item.name"
                    :color="item.color"
                  ></Tag>
                  <!-- <Tag :msg="scope.row.student_name" ></Tag> -->
                </div>
                <el-button
                  type="text"
                  v-if="updateTag"
                  class="tg-text--blue"
                  @click="labelDialogShow(scope.row)"
                  ><img src="../../assets/edit.png" class="edit_icon"
                /></el-button>
                <el-button type="text" v-else class="tg-text--blue"
                  ><img
                    src="../../assets/图片/icon_edit_ac.png"
                    class="edit_icon"
                /></el-button>
              </div>
              <div v-else-if="item.props === 'try_listen_status'">
                {{
                  scope.row[scope.column.property] == "1"
                    ? "未转试听"
                    : "已转试听"
                }}
              </div>
              <div
                v-else-if="
                  item.props === 'yi_ke_password' && scope.row.yi_ke_password
                "
                :style="{
                  display: 'flex',
                  'justify-content': !$_has({ m: 'all_phone', o: 'has_limit' })
                    ? 'left'
                    : 'space-between',
                  'align-items': 'center'
                }"
              >
                <mobileHyposensitization
                  :mobileTemInfo="{
                    row: scope.row,
                    has_eye_limit: scope.row?.has_eye_limit,
                    mobile: scope.row.yi_ke_password
                  }"
                ></mobileHyposensitization>
                <div v-copy="scope.row.yi_ke_password"></div>
              </div>
              <div v-else-if="item.props === 'visit_status'">
                <span v-if="scope.row.visit_status === 1">已到访</span>
                <span v-if="scope.row.visit_status === 2">未到访</span>
              </div>
              <div v-else-if="item.props === 'appStatus'">
                <el-select
                  v-model="scope.row.app_binding_status"
                  class="additionalInput"
                  popper-class="tg-select-dropdown"
                  :disabled="!updatebindState"
                  @change="updateInfo(scope.row)"
                >
                  <el-option
                    v-for="(item, index) in appStatusList"
                    :value="item.value"
                    :label="item.label"
                    :key="index"
                  ></el-option>
                </el-select>
              </div>
              <div v-else-if="item.props === 'vxStatus'">
                <el-select
                  v-model="scope.row.add_wechat_status"
                  class="additionalInput"
                  popper-class="tg-select-dropdown"
                  :disabled="!updatebindState"
                  @change="updateInfo(scope.row)"
                >
                  <el-option
                    v-for="(item, index) in isAddStatus"
                    :value="item.value"
                    :label="item.label"
                    :key="index"
                  ></el-option>
                </el-select>
              </div>
              <div v-else-if="item.props === 'next_follow_day'">
                <!-- <span v-if="['昨天']"></span> -->
                <span
                  v-if="!['无', '今天'].includes(scope.row.next_follow_day)"
                >
                  {{
                    new Date().getTime() >
                    new Date(scope.row.next_follow_time).getTime()
                      ? moment(
                          new Date(scope.row.next_follow_time).getTime()
                        ).format("YYYY-MM-DD")
                      : scope.row.next_follow_day
                  }}
                </span>
                <span v-else>{{ scope.row.next_follow_day }}</span>
              </div>
              <div v-else-if="item.type === 'select'">
                {{
                  scope.row[scope.column.property] == null
                    ? ""
                    : scope.row[scope.column.property].toString()
                }}
              </div>
              <div v-else-if="item.props === 'open_id'">
                <el-button
                  :disabled="scope.row.open_id === ''"
                  type="text"
                  v-has="{ m: 'student_infor', o: 'cusBindList' }"
                  @click="openWechatManagement(scope.row)"
                  >管理</el-button
                >
              </div>
              <div v-else-if="item.props === 'student_gender'">
                {{
                  scope.row.student_gender === "male"
                    ? "男"
                    : scope.row.student_gender === "female"
                    ? "女"
                    : ""
                }}
              </div>
              <span v-else-if="item.props.indexOf('.') > -1">
                {{
                  scope.row[item.props.split(".")[0]] == null
                    ? ""
                    : scope.row[item.props.split(".")[0]][
                        item.props.split(".")[1]
                      ]
                }}
              </span>
              <span
                v-else-if="
                  item.props === 'intention_course_name' ||
                  item.props === 'tw_periods'
                "
                >{{
                  scope.row[scope.column.property] == null
                    ? ""
                    : scope.row[scope.column.property].toString()
                }}</span
              >
              <span v-else-if="['province', 'city'].includes(item.props)">
                <template v-if="scope.row.province">
                  {{ scope.row["province"] }} - {{ scope.row["city"] }}
                </template>
              </span>
              <span
                class="content-tab"
                @click="contentTabClick(scope.row)"
                v-else-if="item.props === 'last_communication_data_content'"
              >
                {{ scope.row?.last_communication_data?.content }}
              </span>

              <span v-else>{{ scope.row[scope.column.property] }}</span>
            </template>
          </el-table-column>
        </template>
        <template slot="empty">
          <div style="margin-top: 15%">
            <loading v-if="loading"></loading>
            <div class="empty-container" v-else>暂无数据～</div>
          </div>
        </template>
      </el-table>
      <!-- 分页 -->
      <div class="tg-pagination">
        <span class="el-pagination__total">共 {{ total }} 条</span>
        <el-pagination
          background
          layout="sizes,prev,pager,next,jumper"
          :total="total"
          :page-size="pageSize"
          :current-page="page"
          @current-change="currentChange"
          @size-change="sizeChange"
          :page-sizes="[10, 20, 50, 100]"
        ></el-pagination>
      </div>
    </div>
    <el-dialog
      :title="`${type === 'add' ? '新增' : '修改'}客户信息`"
      :visible="true"
      v-if="visible"
      width="1016px"
      class="custom-edit-dialog"
      :before-close="back"
    >
      <div class="custom-border">
        <div class="custom-tab">
          <span
            :class="{ 'custom-tab--active': tab === 1 }"
            @click="changeTab(1)"
            v-has="{ m: 'intention', o: 'info' }"
            >基本信息</span
          >
          <span
            :class="{ 'custom-tab--active': tab === 2 }"
            v-has="{ m: 'communication', o: 'communication_list' }"
            @click="changeTab(2)"
            v-if="type === 'edit'"
            >沟通记录</span
          >
          <span
            :class="{ 'custom-tab--active': tab === 3 }"
            v-has="{ m: 'communication', o: 'customer_used_list' }"
            @click="changeTab(3)"
            v-if="type === 'edit'"
            >优惠券明细</span
          >
          <span
            :class="{ 'custom-tab--active': tab === 4 }"
            v-has="{ m: 'yiKeOpenCard', o: 'customerOpenList' }"
            @click="changeTab(4)"
            v-if="type === 'edit'"
            >开卡记录</span
          >
        </div>
        <edit-basic
          v-if="tab === 1"
          :id="rowIds"
          :type="type"
          :visible="true"
          ref="basicInfo"
        ></edit-basic>
        <connect-record
          v-if="tab === 2 && type === 'edit'"
          :id="rowIds"
          :type="type"
          :visible="true"
        ></connect-record>

        <couponDetails
          :visible="true"
          :customerId="rowIds"
          v-if="tab === 3 && type !== 'add'"
        ></couponDetails>
        <openCardRecordCustomer
          v-if="tab === 4 && type === 'edit'"
          :id="rowIds"
        ></openCardRecordCustomer>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="tg-button--plain" @click="back" v-if="tab === 1"
          >返回</el-button
        >
        <!-- v-loading.fullscreen.lock="sub_loading" -->

        <el-button
          class="tg-button--primary"
          type="primary"
          @click="type === 'add' ? add() : edit()"
          v-if="tab === 1 && really_permission"
          >确定</el-button
        >
        <el-button
          class="tg-button--primary"
          type="primary"
          @click="visible = false"
          v-else
          >关闭</el-button
        >
      </span>
      <el-dialog
        v-if="student_repeat_visible"
        :title="errCode === 2 ? '提示(成功)' : '提示(失败)'"
        class="student_repeat_dialog"
        :visible="true"
        :show-close="true"
        :append-to-body="true"
        :close-on-press-escape="false"
        :before-close="closeRepeat"
      >
        <div class="student-repeat-table">
          <p v-if="errCode === 2" class="student-repeat-txt">
            该意向客户手机号已存在于以下校区
          </p>
          <p v-if="errCode === 3" class="student-repeat-txt">
            该意向客户已存在于以下校区
          </p>
          <table border="1">
            <thead>
              <tr>
                <th>姓名</th>
                <th>手机号</th>
                <th>所属校区</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(repeatData, index) in sameMobileStuData" :key="index">
                <td>{{ repeatData.student_name }}</td>
                <td>{{ repeatData.student_mobile }}</td>
                <td>{{ repeatData.department_name }}</td>
              </tr>
            </tbody>
          </table>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="closeRepeat" class="tg-button--plain"
            >关闭</el-button
          >
        </span>
      </el-dialog>
    </el-dialog>
    <el-dialog
      title="存在重复学员信息"
      :visible.sync="isShowRepetitionStudentDialog"
    >
      <el-table :data="repetitionStudentData">
        <el-table-column
          property="student_name"
          label="姓名"
          width="150"
        ></el-table-column>
        <el-table-column
          property="student_mobile"
          label="联系手机号"
          width="200"
        ></el-table-column>
        <el-table-column
          property="department_name"
          label="所属校区"
        ></el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button
          class="tg-button--primary"
          type="primary"
          @click="type === 'add' ? add(false) : edit(false)"
          >仍要确定</el-button
        >
        <el-button
          class="tg-button--primary"
          type="primary"
          @click="isShowRepetitionStudentDialog = false"
          >关闭</el-button
        >
      </span>
    </el-dialog>
    <batch-import
      v-if="batch_import_flag"
      @close="closeBatch"
      :type="'intention'"
    ></batch-import>
    <batch-edit
      v-if="batch_edit_flag && checked_clue.length > 0"
      @cancel="batch_edit_flag = false"
      :check_intention_list="checked_clue"
      @really="batchEditReally"
      :type="'intention'"
      :has_student_ids="has_student_ids"
    ></batch-edit>
    <LabelDialog
      :label_flag_visible="label_flag_visible"
      :label_arr.sync="student_info_select.label_list"
      v-if="label_flag_visible"
      @close="label_flag_visible = false"
      @confirm="confirm"
    ></LabelDialog>
    <el-dialog
      title="请选择试听班级"
      :visible.sync="dialogTableVisible"
      width="500px"
    >
      <el-radio v-model="radio" label="1">新开班试听</el-radio>
      <el-radio v-model="radio" label="2">跟班试听</el-radio>
      <span slot="footer" class="dialog-footer">
        <el-button
          class="tg-button--plain"
          type="primary"
          @click="toAuditionReally"
          >关闭</el-button
        >
        <el-button
          class="tg-button--primary"
          type="primary"
          @click="confirmRadio"
          v-loading.fullscreen.lock="sub_loading"
          >确定</el-button
        >
      </span>
    </el-dialog>
    <new-class-audition
      v-if="new_class_visible"
      @close="new_class_visible = false"
      :department_name="checked_clue[0]?.department_name"
      :department_id="checked_clue[0]?.department_id"
      :student_list="student_id"
      @really="
        dialogTableVisible = false;
        new_class_visible = false;
        $router.push({ path: '/audition' });
      "
    ></new-class-audition>
    <attendant-audition
      :department_name="checked_clue[0]?.department_name"
      :department_id="checked_clue[0]?.department_id"
      :studentList="student_id"
      v-if="attendant_audition_visible"
      @close="attendant_audition_visible = false"
      @really="
        dialogTableVisible = false;
        attendant_audition_visible = false;
        $router.push({ path: '/audition' });
      "
    ></attendant-audition>
    <callLog
      v-if="record_flag_visible"
      :phone="stuMobile"
      :stuName="stuName"
      @close="record_flag_visible = false"
    ></callLog>
    <!-- 发送问卷调查 -->
    <PushActionStudents
      v-if="pushActionVisible"
      :data="pushActionData.data"
      survey_type="sign_up"
      :is_multiple="true"
      @close="pushActionCancel"
      @cancel="pushActionCancel"
      @confirm="pushActionConfirm"
    ></PushActionStudents>
    <el-dialog
      title="意向客户变更校区"
      :visible.sync="transferDepartmentDialog"
    >
      <el-form
        :model="transferDepartmentForm"
        label-width="95px"
        :rules="transferDepartmentRules"
        ref="transferDepartmentRef"
        label-position="right"
      >
        <!-- <el-form-item label="变更校区：" prop="dst_department_name">
          <el-input
            placeholder="请选择所属校区"
            readonly
            show-word-limit
            :validate-event="false"
            @click.native="school_tree_visible = true"
            v-model="transferDepartmentForm.dst_department_name"
            class="tg-select tg-select--dialog"
          >
            <img
              slot="suffix"
              :src="
                !school_flag
                  ? require('../../assets/图片/icon_more.png')
                  : require('../../assets/图片/icon_more_ac.png')
              "
              alt=""
              class="btn__img--dotted"
            />
          </el-input>
          <school-tree
            :flag.sync="school_tree_visible"
            v-if="school_tree_visible"
            :interconnection="false"
            :id.sync="transferDepartmentForm.dst_department_id"
            :controlSchool="true"
            :useData="checked_clue.map((i) => i.department_id)"
            :name.sync="transferDepartmentForm.dst_department_name"
            :type="'radio'"
          >
          </school-tree>
        </el-form-item> -->
        <el-form-item label="变更校区：" required>
          <el-select
            v-model="transferDepartmentForm.dst_department_id"
            placeholder="请选择变更校区"
            @change="handleTransferDepartmentChange"
            filterable
            style="width: 400px"
          >
            <el-option
              v-for="item in schoolList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注：" prop="remark">
          <el-input
            type="textarea"
            placeholder="请输入备注"
            v-model="transferDepartmentForm.remark"
            maxlength="200"
            show-word-limit
          >
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="transferDepartmentCancel">取消</el-button>
        <el-button type="primary" @click="transferDepartmentSubmit"
          >提交</el-button
        >
      </div>
    </el-dialog>
    <openCardDialog
      v-if="openCardVisible"
      student_type="intention"
      @close="closeCard"
      @confirm="confirmCard"
      :loading="openCardLoading"
    ></openCardDialog>
    <wechatBindManagement
      v-if="wechatBindManagement_visible"
      :visible.sync="wechatBindManagement_visible"
      dialogType="customer"
      :row_id="row_id"
      :student_name="customer_name"
      :customer_id="customer_id"
      @uploadStatus="uploadStatus"
      @close="wechatBindManagement_visible = false"
    ></wechatBindManagement>
  </div>
</template>
<script>
import Vue from "vue";
import timeFormat from "@/public/timeFormat";
import tgSearch from "@/components/search/search.vue";
// import schoolTree from "@/components/schoolTree/schoolTree";
import editBasic from "@/components/marketStudent/editBasic.vue";
import connectRecord from "../../components/marketStudent/connectRecord.vue";
import SelectField from "../../components/selectField/selectField.vue";
import couponDetails from "@/components/studentInfo/couponDetails.vue";
import intentionApi from "@/api/intentionStudent";
import schoolTransferFeeApi from "@/api/schoolTransferFee";
import seatApi from "@/api/seatAdministration";
import { next_follow_type, follow_status, valid_status } from "@/public/dict";
import moment from "moment";
import loading from "../loading";
import batchImport from "@/components/marketStudent/batchImport.vue";
import batchEdit from "@/components/marketStudent/batchEdit.vue";
import labelDialog from "@/components/marketStudent/labelDialog.vue";
import studentInforApi from "@/api/studentInfor";
import NewClassAudition from "@/components/audition/newClassAudition.vue";
import attendantAudition from "@/components/audition/attendantAudition.vue";
import callLog from "@/components/weihu/callLog.vue";
import discountApi from "@/api/discount";
import yikeCardApi from "@/api/yikeCard";
import { Base64 } from "js-base64";
import openCardDialog from "./openCardDialog.vue";
import openCardRecordCustomer from "./openCardRecordCustomer.vue";
import wechatBindManagement from "@/views/studentInfor/common/wechatBindManagement.vue";

export default {
  data() {
    return {
      openCardLoading: false,
      customer_name: "",
      customer_id: "",
      wechatBindManagement_visible: false,
      row_id: "",
      school_flag: false,
      school_tree_visible: false,
      transferDepartmentDialog: false,
      transferDepartmentForm: {
        remark: "",
        customer_id: "",
        src_department_id: "",
        src_department_name: "",
        dst_department_id: "",
        dst_department_name: ""
      },
      isShowRepetitionStudentDialog: false,
      repetitionStudentData: [],
      pushActionVisible: false,
      student_repeat_visible: false,
      sameMobileStuData: [],
      errCode: null,
      student_id: [],
      attendant_audition_visible: false,
      new_class_visible: false,
      radio: "",
      dialogTableVisible: false,
      exportLoading: false,
      extension_id: "", // 坐席号
      record_flag_visible: false,
      stuName: "",
      stuMobile: "",
      list: [],
      total: 0,
      loading: true,
      pushActionData: {
        data: []
      },
      schoolList: [],
      searchTitle: [
        {
          props: "name",
          label: "客户信息",
          type: "input",
          show: true,
          style: {
            width: "200px"
          },
          placeholder: "请输入客户姓名/手机号"
        },
        // { props: "channel_id", label: "一级渠道", type: "input", show: true,selectOptions:[] },
        // { props: "sub_channel_id", label: "二级渠道", type: "input", show: false,selectOptions:[] },
        {
          props: "status",
          label: "到店状态",
          type: "select",
          show: true,
          selectOptions: []
        },
        {
          props: "try_listen_status",
          label: "是否转试听",
          type: "select",
          show: true,
          selectOptions: [
            {
              id: "",
              name: "不限"
            },
            {
              id: 1,
              name: "未转试听"
            },
            {
              id: 2,
              name: "已转试听"
            },
            {
              id: 3,
              name: "试听成功"
            }
          ]
        },
        {
          props: "real_visit",
          label: "是否到访",
          type: "select",
          show: true,
          selectOptions: [
            {
              name: "不限",
              id: ""
            },
            {
              name: "未到访",
              id: 2
            },
            {
              name: "已到访",
              id: 1
            }
          ]
        },
        {
          props: "label_id",
          label: "用户标签",
          type: "label_flag",
          show: false,
          selectOptions: []
        },
        {
          props: ["channel_id", "sub_channel_id", "channel_ids"],
          label: "渠道",
          type: "choose_channel",
          show: false
        },
        {
          props: "follow_type",
          label: "跟进类型",
          type: "select",
          show: false,
          selectOptions: next_follow_type
        },
        {
          props: "follow_status",
          label: "跟进状态",
          type: "select",
          show: false,
          selectOptions: follow_status
        },
        {
          props: "valid_status",
          label: "有效性",
          type: "select",
          show: false,
          selectOptions: valid_status
        },
        {
          props: "created_at",
          label: "生成日期",
          type: "date",
          show: false,
          has_options: true
        },
        {
          props: "follow_time",
          label: "下次跟进时间",
          type: "date",
          show: false,
          has_options: true
        },
        {
          props: "to_student_time",
          label: "转化日期",
          type: "date",
          show: false,
          has_options: true
        },
        {
          props: "promise_visit_time",
          label: "诺到访日",
          type: "date",
          show: false,
          has_options: true
        },
        {
          props: "real_visit_time",
          label: "实到访日",
          type: "date",
          show: false,
          has_options: true
        },
        {
          props: "last_follow_time",
          label: "沟通日期",
          type: "date",
          show: false,
          has_options: true
        },
        {
          props: "re_employee_id",
          label: "录单人",
          type: "course_staff",
          show: false,
          is_leave: true,
          selectOptions: []
        },
        {
          props: "intention_level_name",
          label: "意向级别",
          type: "rate",
          show: false
        },
        {
          props: "advisor_id",
          label: "课程顾问",
          type: "course_staff",
          show: true,
          is_leave: true,
          selectOptions: []
        },
        // {
        //   props: "advisor_id2",
        //   label: "课程顾问2",
        //   type: "mark_staff",
        //   show: true,
        //   is_leave: true,
        //   selectOptions: []
        // },
        {
          props: "responsible_id",
          label: "市场专员",
          type: "course_staff",
          show: false,
          is_leave: true,
          selectOptions: []
        },
        {
          props: "class_id",
          label: "意向班级",
          type: "choose_class",
          show: false,
          selectOptions: []
        },
        {
          props: "course_id",
          label: "意向课程",
          type: "choose_course",
          show: false,
          selectOptions: []
        },
        {
          props: "status_type",
          label: "客户状态",
          type: "select",
          show: false,
          selectOptions: [
            {
              id: "",
              name: "不限"
            },
            {
              name: "意向客户",
              id: "intention"
            },
            {
              name: "临时学员",
              id: "temp"
            },
            {
              name: "试听学员",
              id: "tryListen"
            },
            {
              name: "在读学员",
              id: "student"
            },
            {
              name: "休学学员",
              id: "out"
            },
            {
              name: "流失学员",
              id: "churn"
            }
          ]
        },
        {
          props: "communicate_count",
          label: "沟通次数",
          type: "input",
          show: true
        },
        {
          props: "tw_period",
          label: "报名期次",
          type: "input",
          show: false
        },
        {
          props: "add_wechat_status",
          label: "微信状态",
          type: "select",
          show: false,
          selectOptions: [
            {
              id: "",
              name: "不限"
            },
            {
              id: 1,
              name: "已加微"
            },
            {
              id: 2,
              name: "未加微"
            }
          ]
        },
        {
          props: "citys",
          label: "省份/市区",
          placeholder: "请选择省市",
          type: "area"
        },
        {
          props: "birth_day",
          label: "出生日期",
          type: "date",
          show: false,
          has_options: true
        }
      ],
      height: window.innerHeight - 430,
      visible: false,
      tab: 1,
      tableTitle: [
        {
          props: "student_name",
          label: "客户姓名",
          show: true,
          width: 150,
          fixed: true,
          sort: "custom"
        },
        {
          props: "student_gender",
          label: "性别",
          show: true,
          width: 80,
          sort: "custom"
        },
        { props: "student_mobile", label: "手机号", show: true, width: 200 },
        {
          props: "department_name",
          label: "所属校区",
          show: true,
          sort: "custom",
          width: 150
        },
        {
          props: "initial_department_name",
          label: "初始校区",
          show: true,
          width: 150
        },
        {
          props: "status_type_chn",
          label: "客户状态",
          show: true,
          sort: "custom",
          width: 150
        },
        {
          props: "status_name",
          label: "到店状态",
          sort: "custom",
          show: true,
          width: 150
        },
        {
          props: "try_listen_status",
          label: "是否转试听",
          show: true,
          sort: "custom",
          width: 150
        },
        {
          props: "yi_ke_account",
          label: "APP账号",
          show: true,
          width: 100
        },
        {
          props: "yi_ke_password",
          label: "APP初始密码",
          show: true,
          width: 140
        },
        {
          props: "yi_ke_vip_valid_date_str",
          label: "过期时间(剩余天数)",
          show: true,
          width: 150
        },
        {
          props: "visit_status",
          label: "是否到访",
          show: true,
          sort: "custom",
          width: 150
        },
        {
          props: "appStatus",
          label: "APP绑定状态",
          show: true,
          width: 150
        },
        {
          props: "vxStatus",
          label: "微信状态",
          show: true,
          width: 150
        },
        {
          props: "",
          label: "用户标签",
          show: true,
          width: 230,
          type: "tag"
        },
        {
          props: "intention_level_name",
          label: "意向级别",
          show: true,
          sort: "custom",
          width: 150,
          type: "rate"
        },
        {
          props: "intention_course_name",
          label: "意向课程",
          show: true,
          width: 200
        },
        {
          props: "valid_status_reason_chn",
          label: "有效性",
          show: true,
          sort: "custom",
          width: 200
        },
        {
          props: "birth_day",
          label: "出生日期",
          show: true,
          width: 200,
          sort: "custom",
          type: "date.date"
        },
        {
          props: "age",
          label: "年龄",
          show: true,
          sort: "custom",
          width: 100
        },
        // { props: "department_name", label: "所属校区", show: true, width: 150 },
        {
          props: "province",
          label: "省份/市区",
          show: true,
          sort: "custom",
          width: 150
        },
        {
          props: "channel_name",
          label: "一级渠道",
          show: true,
          sort: "custom",
          width: 150
        },
        {
          props: "sub_channel_name",
          label: "二级渠道",
          show: true,
          sort: "custom",
          width: 200
        },
        {
          props: "father_name",
          label: "父亲姓名",
          show: false,
          sort: "custom",
          width: 200
        },
        {
          props: "father_mobile",
          label: "父亲手机号",
          show: false,
          width: 200
        },
        {
          props: "father_job",
          label: "父亲职业",
          show: false,
          width: 200
        },
        {
          props: "mother_name",
          label: "母亲姓名",
          show: false,
          width: 200
        },
        {
          props: "mother_mobile",
          label: "母亲手机号",
          show: false,
          width: 200
        },
        {
          props: "mother_job",
          label: "母亲职业",
          show: false,
          width: 200
        },
        {
          props: "other_mobile",
          label: "其他联系方式",
          show: false,
          width: 200
        },
        {
          props: "home_address",
          label: "家庭地址",
          show: false,
          width: 200
        },
        {
          props: "advisor_name",
          label: "课程顾问",
          show: true,
          sort: "custom",
          width: 150
        },
        {
          props: "responsible_name",
          label: "市场专员",
          type: "select",
          show: true,
          width: 150
        },
        { props: "tw_periods", label: "报名期次", show: true, width: 150 },
        {
          props: "communicate_count",
          label: "沟通次数",
          show: true,
          sort: "custom",
          width: 150
        },
        {
          props: "last_follow_day",
          label: "上次沟通",
          show: true,
          width: 150
        },
        {
          props: "next_follow_day",
          label: "下次跟进",
          show: true,
          width: 150
        },
        {
          props: "created_at",
          label: "生成日期",
          show: true,
          width: 160,
          sort: "custom",
          type: "date"
        },
        {
          props: "re_employee_name",
          label: "录单人",
          show: true,
          sort: "custom",
          width: 150
        },
        { props: "memo", label: "备注", show: true, width: 150 },
        {
          props: "open_id",
          label: "小程序绑定状态",
          show: true,
          sort: "custom",
          width: 150
        },
        {
          props: "promise_visit_time",
          label: "诺到访日",
          show: true,
          sort: "custom",
          width: 150,
          type: "date"
        },
        {
          props: "real_visit_time",
          label: "实到访日",
          show: true,
          sort: "custom",
          width: 150,
          type: "date"
        },
        {
          props: "to_student_time",
          label: "转化日期",
          show: true,
          sort: "custom",
          width: 150,
          type: "date"
        },
        {
          props: "generation_time",
          label: "录入时间",
          show: true,
          width: 150,
          type: "date"
        },
        {
          props: "last_follow_time",
          label: "沟通日期",
          show: true,
          width: 150,
          type: "date"
        },
        {
          props: "introducer_name",
          label: "介绍人",
          show: true,
          // sort: "custom",
          width: 150
        },
        {
          props: "last_communication_data.communication_type_chn",
          label: "跟进类型",
          show: true,
          width: 150
        },
        {
          props: "last_communication_data_content",
          label: "沟通内容",
          show: true,
          width: 150
        },
        {
          props: "nwp_level_name",
          label: "证书棋力",
          show: true,
          width: 150
        }
      ],
      rowIds: "",
      level_list: [],
      search: {
        name: "",
        mobile: "",
        channel_id: "",
        sub_channel_id: "",
        status: "",
        follow_type: "",
        time: "",
        follow_time: "",
        real_visit_time: "",
        promise_visit_time: "",
        last_follow_time: "",
        to_student_time: "",
        intention_level_name: 0,
        class_id: "",
        course_id: "",
        re_employee_id: "",
        responsible_id: "",
        advisor_id: "",
        re_employee_id_name: "",
        advisor_id_name: "",
        responsible_id_name: "",
        try_listen_status: "",
        real_visit: "",
        tw_period: "",
        status_type: "",
        add_wechat_status: "",
        valid_status: "",
        follow_status: undefined,
        sort: "",
        citys: [],
        province: "",
        city: ""
      },
      pageSize: 10,
      page: 1,
      checked_clue: [],
      status_list: [],
      type: "",
      batch_import_flag: false,
      batch_edit_flag: false,
      intention_custom_list: [],
      can_info: false,
      really_permission: false,
      is_loading: false,
      can_listen: false,
      can_student: false,
      sub_loading: false,
      has_student_ids: false,
      is_admin: false,
      label_flag_visible: false,
      student_info_select: "",
      appStatusList: [
        { label: "绑定", value: 1 },
        { label: "未绑定", value: 2 }
      ],
      isAddStatus: [
        { label: "添加", value: 1 },
        { label: "未添加", value: 2 }
      ],
      transferDepartmentRules: {
        dst_department_name: [
          { required: true, message: "请选择校区", trigger: "change" }
        ],
        remark: [{ required: true, message: "请输入备注", trigger: "blur" }]
      },
      birth_day: [],
      birthday_start: "",
      birthday_end: "",
      openCardVisible: false
    };
  },
  watch: {
    "$route.query.id": {
      handler() {
        console.log(this.$route, "this.$route.query.id");
        if (this.$route.query.id && this.$route.name === "marketStudent") {
          if (this.$_has({ m: "intention", o: "list" })) {
            if (
              this.$_has({ m: "intention", o: "info" }) ||
              this.$_has({ m: "communication", o: "info" })
            ) {
              this.can_info = true;
            }
          }
          this.openEdit({ id: this.$route.query.id });
        }
      },
      immediate: true
    },
    intention_custom_list(new_obj) {
      this.list = new_obj.results == null ? [] : new_obj.results;
      this.total = new_obj.count;
    },
    tableTitle: {
      handler() {
        this.$nextTick(() => {
          // 在数据加载完，重新渲染表格
          this.$refs.tableData.doLayout();
        });
      },
      immediate: true,
      deep: true
    },
    visible: {
      handler(bool) {
        if (!bool) {
          const search = this.changeSearch();
          this.getIntentionList(search);
          this.tab = 1;
        }
        if (bool) {
          if (
            this.type === "add" &&
            this.$_has({ m: "intention", o: "create" })
          ) {
            this.really_permission = true;
          } else if (
            this.type === "edit" &&
            this.$_has({ m: "intention", o: "update" })
          ) {
            this.really_permission = true;
          }
        }
      },
      deep: true
    },
    school_id(val) {
      if (val?.length) {
        this.page = 1;
        this.clearSelection();
        this.search.channel_id = "";
        this.search.sub_channel_id = "";
        this.search.channel_ids = [];
        this.search.channel_ids_name = [];
        this.getList();
      }
    },
    checked_clue: {
      handler() {
        // 在读，休学，退学，为正式学员，不可转正式和试听 ，临时，意向可转试听和正式， 试听可转正式
        if (this.checked_clue) {
          const arr = ["temp", "tryListen", "intention"];
          const arr2 = ["temp", "intention"];
          this.has_student_ids = this.checked_clue.some(
            (item) => item.student_id !== ""
          );
          this.can_student = this.checked_clue.every((item) =>
            arr.includes(item.status_type)
          );
          this.can_listen = this.checked_clue.every((item) =>
            arr2.includes(item.status_type)
          );
        }

        // if (this.checked_clue.length === 0) {
        //   this.can_listen = true;
        //   this.can_student = true;
        // } else {
        //   this.has_student_ids = false;
        //   this.checked_clue.forEach((item) => {
        //     if (item.student_id != "") {
        //       this.can_listen = false;
        //       this.can_student = false;
        //       this.has_student_ids = true;
        //     }
        //   });
        // }
      },
      deep: true,
      immediate: true
    },
    all_permission: {
      handler(val) {
        this.is_admin = val != null && val.indexOf("is_admin") > -1;
      },
      immediate: true
    },
    $route(to, from) {
      // this.reset();
      this.$refs?.table?.clearSelection();
    },
    "search.citys": {
      handler(newVal) {
        this.search.province = newVal[0];
        this.search.city = newVal[1];
      },
      deep: true
    }
  },
  computed: {
    school_id() {
      return this.$store.getters.doneGetSchoolId;
    },
    all_permission() {
      return this.$store.getters.doneGetPermissionInfo;
    },
    updateTag() {
      return this.$_has({ m: "intention", o: "updateTag" });
    },
    updatebindState() {
      return this.$_has({ m: "intention", o: "updatebindState" });
    },
    isNetworkSchool() {
      return ["网校测试校区C", "聂卫平围棋网校-新"].includes(
        this.$refs.basicInfo.form.department_name
      );
    },
    isDisabledtransferDepartmentBtn() {
      return (
        this.checked_clue.length === 1 &&
        this.checked_clue[0].status_type === "intention"
      );
    },
    canOpenCard() {
      if (this.checked_clue.length === 1) {
        const { yi_ke_account } = this.checked_clue[0];
        return !yi_ke_account;
      }
      return false;
    }
  },
  created() {
    if (this.$_has({ m: "intention", o: "list" })) {
      if (
        this.$_has({ m: "intention", o: "info" }) ||
        this.$_has({ m: "communication", o: "info" })
      ) {
        this.can_info = true;
      }
    }
    console.log(this.$route.query);
  },
  mounted() {
    if (this.$_has({ m: "intention", o: "list" })) {
      this.searchTitle[3].selectOptionsTwo = [];
      this.searchTitle[4].selectOptionsTwo = [{ name: "不限", id: "" }];
      this.is_loading = true;
      this.getIntentionList();
      this.getLevelList();
      this.getStatusList();
      this.voip = null;
    }
    // this.getEmployeeList();
  },
  beforeRouteEnter(to, from, next) {
    // 不是从详情进入的到该页面的路由，需要重置页面
    if (from.name) {
      next((vm) => {
        vm.reset();
        vm.$refs.table.clearSelection();
      });
    } else {
      next((vm) => {
        vm.$refs.table.clearSelection();
      });
    }
  },
  deactivated() {
    if (this.voip) {
      this.voip.webTcpSocket.WS.close();
      this.voip.webTcpSocket.closeSocketHeart();
      this.voip = null;
    }
  },

  methods: {
    openCard() {
      if (this.checked_clue.length !== 1) {
        this.$message.error("请选择一条数据");
        return;
      }
      this.openCardVisible = true;
    },
    confirmCard(data) {
      console.log(data);
      const { id } = this.checked_clue[0];
      this.openCardLoading = true;
      yikeCardApi
        .sendCustomerMembership({
          p_type: 2,
          card_type: +data.card_type,
          validity_type: +data.validity_type,
          student_id: id
        })
        .then((res) => {
          const { code, message } = res.data;
          if (code === 0) {
            this.$message.success("开卡成功");
            this.openCardVisible = false;
            this.getIntentionList();
          } else {
            this.$message.error(message);
          }
        })
        .finally(() => {
          this.openCardLoading = false;
        });
    },
    closeCard() {
      this.openCardVisible = false;
    },
    openWechatManagement(row) {
      console.log(row, "row");
      this.customer_name = row.student_name;
      this.customer_id = row.id;
      this.wechatBindManagement_visible = true;
    },
    uploadStatus() {
      this.getIntentionList();
    },
    // 是否展示可发送问卷的学员
    showPushDialog(data) {
      const customer_id = this.checked_clue.map((item) => item.id);
      if (customer_id.length) {
        discountApi
          .surveySendMultiple({
            customer_id,
            survey_type: "sign_up"
          })
          .then((res) => {
            const data = res.data;
            if (data.data.length > 0) {
              this.pushActionData = data;
              this.pushActionVisible = true;
            }
          });
      }
    },
    pushActionCancel() {
      this.pushActionVisible = false;
    },
    pushActionConfirm() {
      this.pushActionVisible = false;
    },
    contentTabClick(row) {
      this.tab = 2;
      this.visible = true;
      this.type = "edit";
      this.rowIds = row.id;
    },
    closeRepeat() {
      if (this.errCode === 2) {
        this.visible = false;
      }
      this.student_repeat_visible = false;
    },
    sortChange(val) {
      const { prop, order } = val;
      let _oreder = "";
      if (order === "ascending") {
        _oreder = "asc";
      } else if (order === "descending") {
        _oreder = "desc";
      }
      this.search.sort = `${prop} ${_oreder}`;
      this.searchVal();
    },
    callPhone(row) {
      // 拨号
      this.$set(row, "isCalled", true);
      this.voip.CallPhone(row.student_mobile, (data) => {
        console.log(data);
        if (+data.code === 0) {
          this.$message.success("呼叫" + row.student_mobile + "成功！");
        } else {
          this.$message.error("拔号失败：" + data.message);
          this.$set(row, "isCalled", false);
        }
      });
    },
    createVoip(row) {
      const { employee_id } = JSON.parse(localStorage.getItem("user_info"));
      if (employee_id) {
        seatApi
          .getSeatInfo({
            employee_id
          })
          .then((res) => {
            const { data, code } = res.data;
            if (code === 0) {
              const { student_mobile } = row;
              if (student_mobile) {
                const serviceUrl = "https://voip.800ing.com/";
                if (this.voip) {
                  this.callPhone(row);
                } else {
                  // eslint-disable-next-line no-undef, new-cap
                  this.voip = new voipCall();
                  this.voip.init(serviceUrl);
                  const username = data.extension_id;
                  this.extension_id = data.extension_id;
                  const password = Base64.decode(data.password);
                  // const username = "101832800";
                  // const password = "41153943";

                  console.log("object :>>", password);

                  this.voip.userlogin(username, password);
                  // 登录返回，code =0登录成功，其它为失败
                  this.voip.CallBack_login = (code, message) => {
                    if (+code === 0) {
                      this.callPhone(row);
                    } else {
                      this.$message.error("拔号失败：" + message);
                      this.$set(row, "isCalled", false);
                    }
                  };
                }
                // 挂机回调
                this.voip.CallBack_HangUp = (kind, phone, obj) => {
                  this.$set(row, "isCalled", false);
                  // this.voip.webTcpSocket.WS.close();
                  this.$message.info("通话结束！");
                };
              }
            } else {
              this.$message.error("获取当前账号坐席信息失败！");
            }
          })
          .catch(() => {
            this.$message.error("获取当前账号坐席信息失败！");
          });
      } else {
        this.$message.error("获取员工信息失败，请重新登录！");
      }
    },
    openCallLog(row) {
      this.record_flag_visible = true;
      this.stuName = row.student_name;
      this.stuMobile = row.student_mobile;
    },
    confirmRadio() {
      if (this.radio === "2") {
        this.attendant_audition_visible = true;
      } else if (this.radio === "1") {
        this.new_class_visible = true;
      }
    },
    // 年月日
    getTime(time) {
      return timeFormat.GetTime(time);
    },
    back() {
      this.visible = false;
    },
    reset() {
      // this.search = {};
      this.search = {
        name: "",
        mobile: "",
        channel_id: "",
        sub_channel_id: "",
        status: "",
        follow_type: "",
        time: "",
        follow_time: "",
        real_visit_time: "",
        promise_visit_time: "",
        last_follow_time: "",
        to_student_time: "",
        intention_level_name: 0,
        class_id: "",
        course_id: "",
        re_employee_id: "",
        responsible_id: "",
        advisor_id: "",
        re_employee_id_name: "",
        advisor_id_name: "",
        responsible_id_name: "",
        label_id: "",
        real_visit: "",
        try_listen_status: "",
        tw_period: "",
        status_type: "",
        add_wechat_status: "",
        valid_status: "",
        sort: "",
        citys: [],
        province: "",
        city: "",
        birth_day: [],
        birthday_start: "",
        birthday_end: ""
      };
      this.clearSelection();
      this.$refs.tableData.clearSort();
      this.searchTitle[3].selectOptionsTwo = [];
      this.searchTitle[4].selectOptionsTwo = [{ name: "不限", id: "" }];

      this.page = 1;
      this.pageSize = 10;
      this.getIntentionList();
    },
    updateInfo(row) {
      const data = {
        customer_id: row.id,
        add_wechat_status: row.add_wechat_status,
        app_binding_status: row.app_binding_status
      };
      intentionApi.UpdateAppendData(data).then((res) => {
        if (+res.status === 200) {
          this.$message.success("数据更新成功!");
        }
      });
    },
    changeSearch() {
      const search = JSON.parse(JSON.stringify(this.search));
      console.log(search);
      if (
        search.created_at &&
        search.created_at !== "" &&
        typeof search.created_at !== "undefined"
      ) {
        search.start_date = search.created_at[0];
        search.end_date = search.created_at[1];
      }
      if (
        search.follow_time &&
        search.follow_time !== "" &&
        typeof search.follow_time !== "undefined"
      ) {
        search.follow_time_start = search.follow_time[0];
        search.follow_time_end = search.follow_time[1];
      }
      if (
        search.real_visit_time &&
        search.real_visit_time !== "" &&
        typeof search.real_visit_time !== "undefined"
      ) {
        search.real_visit_start_time = search.real_visit_time[0];
        search.real_visit_end_time = search.real_visit_time[1];
      }
      if (
        search.promise_visit_time &&
        search.promise_visit_time !== "" &&
        typeof search.promise_visit_time !== "undefined"
      ) {
        search.promise_visit_start_time = search.promise_visit_time[0];
        search.promise_visit_end_time = search.promise_visit_time[1];
      }
      if (
        search.last_follow_time &&
        search.last_follow_time !== "" &&
        typeof search.last_follow_time !== "undefined"
      ) {
        search.last_follow_start_time = search.last_follow_time[0];
        search.last_follow_end_time = search.last_follow_time[1];
      }
      if (
        search.birth_day &&
        search.birth_day !== "" &&
        typeof search.birth_day !== "undefined"
      ) {
        search.birthday_start = search.birth_day[0];
        search.birthday_end = search.birth_day[1];
      }
      if (
        search.to_student_time &&
        search.to_student_time !== "" &&
        typeof search.to_student_time !== "undefined"
      ) {
        search.to_student_start_time = search.to_student_time[0];
        search.to_student_end_time = search.to_student_time[1];
      }
      const item = this.level_list.find(
        (item) => +item.name === +search.intention_level_name
      );
      if (search.class_id !== "") search.class_id = search.class_id.split(",");
      if (search.course_id !== "")
        search.course_id = search.course_id.split(",");
      if (+search.intention_level_name !== 0)
        search.intention_level_id = item?.id;
      delete search.intention_level_name;
      delete search.time;
      delete search.follow_time;
      delete search.created_at;
      delete search?.channel_ids;
      return search;
    },
    searchVal() {
      this.page = 1;
      this.clearSelection();
      this.getList();
    },
    clearSelection() {
      this.$nextTick(() => {
        this.$refs.tableData.clearSelection();
      });
    },
    getList() {
      const search = this.changeSearch();
      Object.keys(search).forEach((key) => {
        if (
          search[key] === "" ||
          search[key] === null ||
          search[key] === undefined
        ) {
          Vue.delete(search, key);
        }
      });
      this.getIntentionList(search);
    },
    getDepartment() {
      schoolTransferFeeApi
        .getDepartmentList({
          no_department_id: this.checked_clue[0].department_id
        })
        .then((res) => {
          if (+res.status === 200 && +res.data.code === 0) {
            this.schoolList = res.data.data;
          } else {
            this.schoolList = [];
          }
        });
    },
    transferDepartmentBtn() {
      const { id, department_name, department_id } = this.checked_clue[0];
      this.transferDepartmentForm.customer_id = id;
      this.transferDepartmentForm.src_department_id = department_id;
      this.transferDepartmentForm.src_department_name = department_name;
      // console.log(department_id, this.checked_clue);
      this.getDepartment();
      this.transferDepartmentDialog = true;
    },
    handleTransferDepartmentChange(val) {
      const curSchoolInfo = this.schoolList.find((i) => i.id === val);
      this.transferDepartmentForm.dst_department_name = curSchoolInfo.name;
    },
    transferDepartmentSubmit() {
      this.$refs.transferDepartmentRef.validate((valid) => {
        if (valid) {
          intentionApi
            .transferSchoolApprove(this.transferDepartmentForm)
            .then((res) => {
              console.log(res);
              if (res.data.code === 0) {
                this.$message.success("变成校区成功！");
                this.transferDepartmentCancel();
                if (this.$_has({ m: "intention", o: "transferSchoolList" })) {
                  setTimeout(() => {
                    this.$emit("changeTab");
                  }, 1000);
                }
              } else {
                this.$message.error(res.data.err);
              }
            });
        }
      });
      console.log(this.transferDepartmentForm);
    },
    transferDepartmentCancel() {
      this.transferDepartmentDialog = false;
      this.transferDepartmentForm = {
        remark: "",
        customer_id: "",
        src_department_id: "",
        src_department_name: "",
        dst_department_id: "",
        dst_department_name: ""
      };
    },
    // 模板下载
    downloadExcel() {
      const downloadElement = document.createElement("a");
      downloadElement.href =
        "./intention_excel.xlsx?v=" + window.__APP_VERSION__;
      downloadElement.download = `意向客户模板.xlsx`; // 下载后文件名
      document.body.appendChild(downloadElement);
      downloadElement.click(); // 点击下载
      document.body.removeChild(downloadElement);
    },
    changeTab(value) {
      this.tab = value;
    },
    handleSelectionChange(val) {
      this.checked_clue = val;
      console.log(this.checked_clue);
    },
    openAdd() {
      this.type = "add";
      this.visible = true;
    },
    currentChange(val) {
      this.page = val;
      const search = this.changeSearch();
      this.getIntentionList(search);
    },
    sizeChange(val) {
      this.pageSize = val;
      this.page = 1;
      const search = this.changeSearch();
      this.getIntentionList(search);
    },
    openEdit(row) {
      console.log(row, "row");
      if (!this.can_info) return;
      this.tab = 1;
      this.visible = true;
      this.type = "edit";

      this.rowIds = row.id;
    },
    exportExcel() {
      // 如果有勾选，走部分导出
      if (this.checked_clue.length) {
        if (this.checked_clue.length > 500) {
          this.$message.info("最多支持500个选中的数据导出，请减少勾选！");
        } else {
          this.selected_export();
        }
      } else {
        this.stu_exp_timer = null;
        this.$confirm("是否导出意向客户信息", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          this.exportLoading = true;
          this.$message.info("正在导出数据,请稍等...");
          const search = this.changeSearch();
          const data = {
            department_id: this.school_id,
            ...search
          };
          intentionApi
            .ExportExcel(data)
            .then((res) => {
              const { data: resp } = res;
              const that = this;
              if (resp.file_id) {
                that.stu_exp_timer = setInterval(() => {
                  that.exportDownloadLoop(resp.file_id);
                }, 5000);
              }
            })
            .catch(() => {
              this.exportLoading = false;
            });
        });
      }
    },
    exportDownloadLoop(file_id) {
      intentionApi
        .exportLoop({
          file_id
        })
        .then((res) => {
          const { data } = res;
          if (+data.status === 2) {
            this.exportLoading = false;
            this.exportDownloadFile(file_id);
            clearInterval(this.stu_exp_timer);
          } else if (+data.status === 3) {
            this.exportLoading = false;
            this.$message.error("导出意向客户失败！");
            clearInterval(this.stu_exp_timer);
          }
        })
        .catch(() => {
          clearInterval(this.stu_exp_timer);
          this.$message.error("导出意向客户失败！");
          this.exportLoading = false;
        });
    },
    // 选中部分导出
    selected_export() {
      this.$confirm("是否导出意向客户信息", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        const selectIds = this.$refs.tableData.selection.map((item) => item.id);
        this.exportLoading = true;
        intentionApi
          .selectedExportFile({
            ids: selectIds
          })
          .then((res) => {
            if (res.status === 200) {
              const blob = new Blob([res.data], {
                type: "application/vnd.ms-excel"
              }); // type这里表示xlsx类型
              const downloadElement = document.createElement("a");
              const href = window.URL.createObjectURL(blob); // 创建下载的链接
              downloadElement.href = href;
              downloadElement.download = `${moment(new Date()).format(
                "意向客户列表YYYY年MM月DD日HH时mm分"
              )}.xlsx`; // 下载后文件名
              document.body.appendChild(downloadElement);
              downloadElement.click(); // 点击下载
              this.$message.success("导出意向客户成功！");
            } else {
              this.$message.error("导出意向客户失败！");
            }
            this.exportLoading = false;
          })
          .catch(() => {
            this.$message.error("导出意向客户失败！");
            this.exportLoading = false;
          });
      });
    },
    // 全量导出
    exportDownloadFile(file_id) {
      this.exportLoading = true;
      intentionApi
        .exportDownFile({
          file_id
        })
        .then((res) => {
          if (res.status === 200) {
            const blob = new Blob([res.data], {
              type: "application/vnd.ms-excel"
            }); // type这里表示xlsx类型
            const downloadElement = document.createElement("a");
            const href = window.URL.createObjectURL(blob); // 创建下载的链接
            downloadElement.href = href;
            downloadElement.download = `${moment(new Date()).format(
              "意向客户列表YYYY年MM月DD日HH时mm分"
            )}.xlsx`; // 下载后文件名
            document.body.appendChild(downloadElement);
            downloadElement.click(); // 点击下载
            this.$message.success("导出意向客户成功！");
          } else {
            this.$message.error("导出意向客户失败！");
          }
          this.exportLoading = false;
        })
        .catch(() => {
          this.$message.error("导出意向客户失败！");
          this.exportLoading = false;
        });
    },
    edit(isChecked = true) {
      this.sub_loading = true;
      this.$refs.basicInfo.$refs.form_refs.validate((valid) => {
        console.log(valid);
        if (valid) {
          const form = this.$refs.basicInfo.form;
          if (
            form.valid_status === "invalid" &&
            form.valid_status_reason === ""
          ) {
            this.$message.error("请检查字段");
            this.sub_loading = false;
            return;
          }
          // this.visible = false;
          const obj = JSON.parse(JSON.stringify(form));
          const item = this.level_list.find(
            (item) => +item.name === +obj.intention_level_name
          );
          obj.intention_level_id =
            +obj.intention_level_name === 0 ? "" : item?.id;
          const list = this.$refs.basicInfo.course_list;
          obj.course_list = list.map((item) => {
            return {
              classroom_id: item.class_id,
              course_id: item.course_id
            };
          });
          if (obj.department_name instanceof Array) {
            obj.department_name = obj.department_name[0];
          }
          if (isChecked) {
            const checkParams = {
              student_name: obj.student_name,
              student_mobile: obj.student_mobile,
              customer_id: this.rowIds
            };
            intentionApi
              .createCustomerCheck(checkParams)
              .then(({ code, data, message }) => {
                if (code === 0) {
                  this.updateClue(obj);
                } else if (code === 1) {
                  this.isShowRepetitionStudentDialog = true;
                  this.repetitionStudentData = data;
                } else {
                  this.$message.error(message);
                }
                this.sub_loading = false;
              });
          } else {
            this.updateClue(obj);
          }
        } else {
          const form = this.$refs.basicInfo.form;
          if (form.student_name === "") {
            this.$message.error("保存失败，需填写姓名");
          } else if (form.student_gender === "") {
            this.$message.error("保存失败，需选择性别");
          } else if (
            (!this.isNetworkSchool && form.birth_day === "") ||
            form.birth_day === null
          ) {
            this.$message.error("保存失败，需选择出生日期");
          } else if (form.student_mobile.length !== 11) {
            this.$message.error("保存失败，需填写11位手机号");
          } else if (form.channel_id === "") {
            this.$message.error("保存失败，需选择渠道");
          } else if (form.sub_channel_id === "") {
            this.$message.error("保存失败，需选择二级渠道");
          } else if (form.department_name === "") {
            this.$message.error("保存失败，需选择所属校区");
          } else if (form.intention_level_name === 0) {
            this.$message.error("保存失败，需选择意向级别");
          }

          this.sub_loading = false;
        }
      });
    },
    add(isChecked = true) {
      this.sub_loading = true;
      Promise.all([this.isValid("form_refs"), this.isValid("form2_refs")])
        .then(() => {
          const form = this.$refs.basicInfo.form;
          const appendObj = JSON.parse(
            JSON.stringify(this.$refs.basicInfo.additional_information)
          );
          appendObj.add_wechat_status = appendObj.add_wechat_status
            ? appendObj.add_wechat_status
            : 0;
          appendObj.app_binding_status = appendObj.app_binding_status
            ? appendObj.app_binding_status
            : 0;
          let obj = JSON.parse(JSON.stringify(form));
          const item = this.level_list.find(
            (item) => +item.name === +obj.intention_level_name
          );
          obj = {
            ...obj,
            intention_level_id: +obj.intention_level_name === 0 ? "" : item?.id,
            init_status: "intention",
            status: obj.status.toString(),
            additional_information: appendObj
          };
          if (obj.department_name instanceof Array) {
            obj.department_name = obj.department_name[0];
          }
          const list = this.$refs.basicInfo.course_list;
          obj.course_list = list.map((item) => {
            return {
              classroom_id: item.class_id,
              course_id: item.course_id
            };
          });
          console.log(obj);
          if (isChecked) {
            const checkParams = {
              student_name: obj.student_name,
              student_mobile: obj.student_mobile
            };
            intentionApi
              .createCustomerCheck(checkParams)
              .then(({ code, data, message }) => {
                if (code === 0) {
                  this.addClue(obj);
                } else if (code === 1) {
                  this.isShowRepetitionStudentDialog = true;
                  this.repetitionStudentData = data;
                } else {
                  this.$message.error(message);
                }
                this.sub_loading = false;
              });
          } else {
            this.addClue(obj);
          }
        })
        .catch(() => {
          const form = this.$refs.basicInfo.form;
          if (form.student_name === "") {
            this.$message.error("保存失败，需填写姓名");
          } else if (form.student_gender === "") {
            this.$message.error("保存失败，需选择性别");
          } else if (
            (!this.isNetworkSchool && form.birth_day === "") ||
            form.birth_day === null
          ) {
            this.$message.error("保存失败，需选择出生日期");
          } else if (form.student_mobile.length !== 11) {
            this.$message.error("保存失败，需填写11位手机号");
          } else if (form.channel_id === "") {
            this.$message.error("保存失败，需选择渠道");
          } else if (form.sub_channel_id === "") {
            this.$message.error("保存失败，需选择二级渠道");
          } else if (form.department_name === "") {
            this.$message.error("保存失败，需选择所属校区");
          } else if (form.intention_level_name === 0) {
            this.$message.error("保存失败，需选择意向级别");
          } else if (form.advisor_id === "") {
            this.$message.error("保存失败，需选择课程顾问");
          }
          this.sub_loading = false;
        });
    },
    isValid(ref) {
      return new Promise((resolve, reject) => {
        this.$refs.basicInfo.$refs[ref].validate((valid) => {
          if (valid) {
            resolve();
          } else {
            reject(new Error("校验未通过"));
          }
        });
      });
    },
    diffTime(val) {
      if (val !== "" && val != null) {
        const time_one = moment(moment(val).format("YYYY-MM-DD"));
        const time_two = moment(moment(new Date()).format("YYYY-MM-DD"));
        const days = time_one.diff(time_two, "days");
        return days < 0
          ? `${Math.abs(days)}天前`
          : days === 0
          ? "今天"
          : days > 0 && days < 31
          ? `${days}天后`
          : val;
      }
    },
    getRowKeys(row) {
      // this.$refs.form.clearSelection();完成后需要手动清空
      return row.id;
    },
    cancelCustom() {
      if (this.checked_clue.length === 0) return;
      this.$confirm("此操作将取消转化, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        intentionApi.cancelCustomerStatus().then((res) => {
          if (!res.data.err) {
            this.clearSelection();
            this.getList();
            this.checked_clue = [];
          }
        });
      });
    },
    del(type) {
      if (this.checked_clue.length === 0) return;
      this.$confirm("此操作将删除意向客户, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        if (type === "one") {
          const data = this.checked_clue.map((item) => item.id);
          intentionApi.deleteStudentsOne({ customer_id: data }).then((res) => {
            if (typeof res !== "undefined") {
              this.clearSelection();
              this.getList();
              this.checked_clue = [];
            }
          });
        } else {
          const data = this.checked_clue.map((item) => item.id);
          intentionApi.DeleteStudents_xs({ customer_id: data }).then((res) => {
            if (typeof res !== "undefined") {
              this.clearSelection();
              this.getList();
              this.checked_clue = [];
            }
          });
        }
      });
    },
    batchEditReally() {
      this.batch_edit_flag = false;
      this.clearSelection();
      this.getList();
      this.checked_clue = [];
    },
    toAudition() {
      if (this.checked_clue.length === 0) return;

      this.$confirm("确定要将选中的客户转为试听学员吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        const data = this.checked_clue.map((item) => item.id);
        intentionApi.toAudition({ customer_ids: data }).then((res) => {
          console.log(res);
          if (res.data.code === 0) {
            const obj = this.checked_clue.find(
              (item) =>
                item.department_id !== this.checked_clue[0].department_id
            );
            if (obj && obj.department_id) {
              this.$message.success("操作成功");
              this.checked_clue = [];
              this.clearSelection();
              this.getList();
            } else {
              this.student_id = res.data.data ?? [];
              this.dialogTableVisible = true;
            }
          } else {
            this.$message.error(res.data.message);
          }
        });
      });
    },
    toAuditionReally() {
      this.dialogTableVisible = false;
      // this.$message.success("操作成功");
      this.checked_clue = [];
      this.clearSelection();
      this.getList();
    },
    toStudent() {
      if (this.checked_clue.length === 0) return;
      this.$confirm("确定要将选中的客户转为正式学员吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        const data = this.checked_clue.map((item) => item.id);
        intentionApi.toStudent({ customer_ids: data }).then((res) => {
          if (res.data.code === 0) {
            this.$message.success("操作成功");
            // 隐藏发送问卷调查
            // this.showPushDialog();
            this.checked_clue = [];
            this.clearSelection();
            this.getList();
            // 推送问卷调查
          } else {
            this.$message.error(res.data.message);
          }
        });
      });
    },
    updateClue(data) {
      intentionApi
        .UpdateEmployee_xs(data)
        .then((res) => {
          if (res.data.code !== 0) {
            this.$message.error(res.data.message);
          } else {
            if (typeof res !== "undefined") {
              this.visible = false;
              this.tab = 1;
            }
          }
          this.isShowRepetitionStudentDialog = false;
          this.sub_loading = false;
        })
        .catch((err) => {
          const { code, data, message } = err;
          this.errCode = code;
          if (code === 1) {
            this.$message.error(message);
          } else if (code === 2) {
            this.sameMobileStuData = data ?? [];
            this.$message.success("提交成功！");
            this.student_repeat_visible = true;
          } else if (code === 3) {
            this.sameMobileStuData = data ?? [];
            this.student_repeat_visible = true;

            this.$message.error("提交失败！");
          }
          this.visible = true;
          this.sub_loading = false;
        });
    },

    addClue(data) {
      intentionApi
        .GetCreateStudents_xs(data)
        .then(() => {
          this.isShowRepetitionStudentDialog = false;
          this.visible = false;
          this.tab = 1;
          this.sub_loading = false;
        })
        .catch((err) => {
          const { code, data, message } = err;
          this.errCode = code;
          if (code === 1) {
            this.$message.error(message);
          } else if (code === 2) {
            this.sameMobileStuData = data ?? [];
            this.$message.success("提交成功！");
            this.student_repeat_visible = true;
          } else if (code === 3) {
            this.sameMobileStuData = data ?? [];
            this.student_repeat_visible = true;

            this.$message.error("提交失败！");
          }
          this.sub_loading = false;
        });
    },
    closeBatch() {
      this.batch_import_flag = false;
      this.reset();
    },
    getLevelList() {
      intentionApi.GetLevelList().then((res) => {
        this.level_list = res.data;
      });
    },
    getIntentionList(search) {
      this.loading = true;
      this.list = [];
      const params = {
        page: this.page,
        page_size: this.pageSize,
        status_type: "",
        ...search,
        department_id: this.school_id
      };
      delete params.channel_ids_name;
      intentionApi.GetStudentsList_xs(params).then((res) => {
        this.loading = false;
        this.intention_custom_list = res.data;
        this.is_loading = false;
      });
    },
    getStatusList() {
      this.status_list = [];
      intentionApi.getStatusList().then((res) => {
        for (const key in res.data) {
          this.status_list.push({ name: key, id: res.data[key] });
        }
        // console.log(this.status_list);
        this.searchTitle[1].selectOptions = JSON.parse(
          JSON.stringify(this.status_list)
        );
        this.searchTitle[1].selectOptions.unshift({
          name: "不限",
          id: ""
        });
      });
    },

    // getEmployeeList() {
    //   staffApi.GetEmployeeList().then((res) => {
    //     this.searchTitle[9].selectOptions = res.data;
    //     this.searchTitle[10].selectOptions = res.data;
    //     this.searchTitle[7].selectOptions = res.data;
    //   });
    // },
    handleClose(done) {
      this.$confirm("确认关闭？").then(() => {
        done();
      });
    },
    // 后续可能会干掉
    async syncXiaogj() {
      if (this.checked_clue.length === 0) {
        this.$message.error("请选择需要同步到校管家的意向客户");
        return false;
      }
      try {
        const data = this.checked_clue.map((item) => item.id);
        const res = await intentionApi.syncXiaogj({ customer_id: data });
        if (typeof res.error === "undefined") {
          this.$message.success("同步成功");
          this.checked_clue = [];
          this.clearSelection();
          this.getList();
        }
      } catch (e) {
        this.$message.error(e);
      }
    },
    labelDialogShow(row) {
      this.student_info_select = row;
      this.label_flag_visible = true;
    },
    confirm(val) {
      // console.log(val);
      const form = {
        id: this.student_info_select.id,
        label_ids: val || []
      };
      studentInforApi.saveCustomerLabel(form).then(() => {
        this.getIntentionList(this.search);
      });
    }
  },
  components: {
    tgSearch,
    editBasic,
    connectRecord,
    SelectField,
    batchImport,
    batchEdit,
    labelDialog,
    couponDetails,
    // schoolTree,
    loading,
    NewClassAudition,
    attendantAudition,
    callLog,
    openCardDialog,
    openCardRecordCustomer,
    wechatBindManagement
  }
};
</script>
<style lang="less">
.tg-select-dropdown.el-popper[x-placement^="bottom"] {
  margin-top: -1px;
}

.tg-select-dropdown {
  .popper__arrow {
    display: none;
  }
}
</style>
<style lang="less" scoped>
#intention-custom {
  margin: 0;
  padding: 0;
  ::v-deep .el-table td:last-child {
    border-right-color: transparent !important;
  }
  ::v-deep .el-table__header th:last-child {
    border-right-color: transparent !important;
  }
  .content-tab {
    color: @base-color;
    cursor: pointer;
    display: block;
    height: 40px;
    line-height: 40px;
  }
}
.intention-custom {
  // height: 100vh;
  // overflow-x: hidden;
  margin: 0;
  ::v-deep .custom-edit-dialog > .el-dialog > .el-dialog__body {
    padding: 0;
  }

  .custom-edit-dialog {
    ::v-deep .el-dialog__header {
      border-bottom: none;
    }
    ::v-deep .el-dialog__footer {
      border-top: none;
    }
    .custom-tab {
      height: 46px;
      background: #fff;
      padding: 0 16px;
      box-shadow: 0 2px 6px 0 rgba(204, 208, 217, 0.35);
      border-top: 1px solid #e9f0f7;
      position: relative;
      z-index: 1;
      span {
        font-family: @text-famliy_semibold;
        color: @text-color_second;
        font-size: @text-size_normal;
        font-weight: bold;
        display: inline-block;
        height: 44px;
        border-bottom: 2px solid transparent;
        line-height: 44px;
        cursor: pointer;
      }
      span + span {
        margin-left: 32px;
      }
      span.custom-tab--active {
        color: #2d80ed;
        border-color: #2d80ed;
      }
    }

    .custom-border {
      border-bottom: 1px solid #e1e7ef;
    }
  }
  ::v-deep .el-rate__icon.el-icon-star-off {
    background: url("../../assets/图片/icon_rate.png");
    background-size: 100%;
    background-repeat: no-repeat;
    width: 16px;
    height: 16px;
  }
  ::v-deep .el-icon-star-off:before {
    content: "1";
    visibility: hidden;
  }
  ::v-deep .el-rate__icon.el-icon-star-on {
    background: url("../../assets/图片/icon_rate_ac.png");
    background-size: 100%;
    background-repeat: no-repeat;
    width: 16px;
    height: 16px;
  }
  ::v-deep .el-icon-star-on:before {
    content: "1";
    visibility: hidden;
  }
  ::v-deep .el-button {
    font-weight: normal;
  }
  .tg-row--height {
    height: 32px;
    width: 100%;
  }
  .btn__wrap {
    overflow-y: scroll;
    width: calc(100% - 130px);
    display: inline-block;
    white-space: nowrap;
  }
  .tg-table-container {
    width: 100%;
    flex: 1;
    height: 100%;
    margin-bottom: 0;
  }
}
.container {
  margin: 0;
}
.additionalInput {
  width: 100px;
  ::v-deep .el-input {
    width: 100%;
  }
}
.edit_icon {
  width: 12px;
  height: 12px;
}
::v-deep .attend-class {
  padding: 0 16px;
}
::v-deep .el-table__body-wrapper {
  height: calc(100% - 46px);
  .empty-container {
    position: absolute;
    top: 50%;
    left: 50%;
  }
  .loading-container {
    position: absolute;
    top: 30%;
    left: 1%;
    background: transparent;
    .box {
      height: 100%;
    }
  }
  .weihu-custom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    span {
      display: flex;
      align-items: center;
      i {
        font-size: 20px;
        cursor: pointer;
        color: #2d80ed;
        margin: 0 6px;
      }
    }
  }
}
.student_repeat_dialog {
  ::v-deep .el-dialog__body {
    padding: 16px;
  }

  .student-repeat-table {
    .student-repeat-txt {
      margin-top: 0;
      padding-left: 10px;
      font-size: 15px;
      color: #000;
      position: relative;
      &::before {
        content: "";
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background-color: #2d80ed;
        position: absolute;
        top: 7px;
        left: 0;
      }
    }
    table {
      width: 100%;
      border-collapse: collapse;
      border-color: #dcdcdc;
      tr {
        td,
        th {
          padding: 10px 0;
          text-align: center;
        }
        th {
          background-color: #f0f0f0;
        }
      }
    }
  }
}
</style>
