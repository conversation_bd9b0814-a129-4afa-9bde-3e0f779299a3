<template>
  <div>
    <el-dialog
      title="开卡"
      :visible="true"
      width="400px"
      class="open-card-dialog"
      :before-close="closeCard"
    >
      <el-form :model="form" label-width="120px">
        <el-form-item label="卡类型">
          <el-select v-model="form.card_type" placeholder="请选择卡类型">
            <el-option
              v-for="item in card_type"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="有效期类型">
          <el-select
            v-model="form.validity_type"
            placeholder="请选择有效期类型"
          >
            <el-option
              v-for="item in card_validity_type"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="plain" class="tg-button--plain" @click="closeCard"
          >取 消</el-button
        >
        <el-button
          class="tg-button--primary"
          type="primary"
          :loading="loading"
          @click="confirmCard"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { card_type, card_validity_type } from "@/public/dict";
export default {
  props: {
    student_type: {
      type: String,
      default: ""
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        card_type: "1",
        validity_type: undefined
      }
    };
  },

  computed: {
    card_type() {
      return card_type.filter((item) => item.id === "1");
    },
    card_validity_type() {
      if (this.student_type === "intention") {
        // 意向
        return card_validity_type.filter((item) => item.id === "15"); // 周卡
      } else if (this.student_type === "student") {
        // 学生
        return card_validity_type.filter((item) => item.id === "4"); // 双周卡
      } else {
        return [];
      }
    }
  },
  created() {
    if (this.student_type === "intention") {
      this.form.validity_type = "15";
    } else if (this.student_type === "student") {
      this.form.validity_type = "4";
    }
  },
  methods: {
    openCard() {
      this.openCardVisible = true;
    },
    closeCard() {
      this.$emit("close");
    },
    confirmCard() {
      this.$emit("confirm", this.form);
    }
  }
};
</script>
<style scoped lang="scss">
.el-dialog__body {
}
</style>
