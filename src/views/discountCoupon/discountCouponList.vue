<template>
  <div class="discountCoupon container">
    <div class="goods-content tg-box--margin">
      <div class="goods-content--left tg-box--shadow">
        <div class="title">
          <span>类型</span>
          <el-button
            type="primary"
            class="tg-button--primary tg-button__icon"
            @click="editCouponType"
            v-has="{ m: 'coupon_category', o: 'create' }"
          >
            <img
              src="../../assets/图片/jh.png"
              alt
              class="tg-button__icon--normal"
            />新增
          </el-button>
        </div>
        <div class="side-wrap">
          <div v-for="(item, index) in data_list" :key="index" class="side">
            <div
              class="side-select"
              :class="
                hover_index === index || categroy_index === index
                  ? 'side-select-active'
                  : ''
              "
              @mouseenter="hover_index = index"
              @mouseleave="hover_index = -1"
              @click="categroyTypeActive(item, index)"
            >
              <span class="side-select__label">{{ item.name }}</span>
              <img
                v-has="{ m: 'coupon_category', o: 'update' }"
                v-if="index !== 0 && hover_index === index"
                src="../../assets/图片/icon_submenu_edit_ac.png"
                alt
                class="side-setting__icon"
                @click.stop="editCouponType(item)"
              />
              <img
                v-has="{ m: 'coupon_category', o: 'delete' }"
                v-if="index !== 0 && hover_index === index"
                src="../../assets/图片/ClassTimeSC.png"
                alt
                class="side-setting__icon"
                @click.stop="delCouponType(item)"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="goods-content--right container">
        <tg-search
          :searchTitle.sync="searchTitle"
          :form.sync="search"
          @reset="reset"
          @search="searchVal"
          :showNum="3"
        ></tg-search>
        <el-row
          class="tg-box--margin tg-shadow--margin tg-row--height tg-box--width"
        >
          <el-button
            type="primary"
            class="tg-button--primary tg-button__icon"
            @click="openDialog(1)"
            v-has="{ m: 'coupon_category', o: 'coupon_create' }"
          >
            <img
              src="../../assets/图片/jh.png"
              alt
              class="tg-button__icon--normal"
            />创建优惠券
          </el-button>
          <el-button
            type="plain"
            class="tg-button--plain"
            @click="openDialog(2)"
            v-has="{ m: 'coupon_category', o: 'coupon_delete' }"
            :disabled="select_ids.length == 0"
            >删除</el-button
          >
        </el-row>
        <div class="tg-table__box">
          <div class="tg-box--border"></div>
          <el-table
            ref="table"
            :data="list"
            tooltip-effect="dark"
            class="tg-table"
            @selection-change="handleSelectionChange"
            :row-key="getRowKeys"
            :cell-style="{ borderRightColor: '#e0e6ed75' }"
            :header-cell-style="{ borderRightColor: '#e0e6ed75' }"
            border
          >
            <el-table-column
              type="selection"
              width="50"
              :reserve-selection="true"
              fixed="left"
            ></el-table-column>
            <el-table-column label="名称" width="160">
              <template slot-scope="scope">
                <div class="copy_name">
                  <el-button
                    type="text"
                    size="small"
                    class="tg-text--blue"
                    @click="openDialog(7, scope.row)"
                    >{{ scope.row.name }}</el-button
                  >
                  <div v-copy="scope.row.name"></div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="面额">
              <template slot-scope="scope">{{
                scope.row.coupon_type == 1
                  ? (scope.row.quota / 100).toFixed(2)
                  : `打${(scope.row.quota / 10).toFixed(1)}折`
              }}</template>
            </el-table-column>
            <el-table-column prop="coupon_type" label="类型">
              <template slot-scope="scope">
                {{ scope.row.coupon_type == 1 ? "满减券" : "折扣券" }}
              </template>
            </el-table-column>
            <el-table-column prop="name" label="使用门槛" width="110">
              <template slot-scope="scope">
                {{
                  scope.row.used_threshold == 0
                    ? "无门槛"
                    : `订单满${scope.row.used_threshold / 100}可用`
                }}
              </template>
            </el-table-column>
            <el-table-column label="发行张数">
              <template slot-scope="scope">
                <!-- <el-button
                  type="text"
                  size="small"
                  v-has="{ m: 'coupon_category', o: 'coupon_detail' }"
                  class="tg-text--blue tg-span__divide-line"
                  @click="openDialog(4, scope.row)"
                  >{{ scope.row.offer_count }}</el-button
                > -->
                <span>{{ scope.row.offer_count }}</span>
              </template>
            </el-table-column>
            <el-table-column label="已发放">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  size="small"
                  v-has="{ m: 'coupon_category', o: 'coupon_detail' }"
                  class="tg-text--blue tg-span__divide-line"
                  @click="openDialog(5, scope.row)"
                  >{{ scope.row.assign_count }}</el-button
                >
              </template>
            </el-table-column>
            <el-table-column prop="name" label="已使用">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  size="small"
                  v-has="{ m: 'coupon_category', o: 'coupon_detail' }"
                  class="tg-text--blue tg-span__divide-line"
                  @click="openDialog(6, scope.row)"
                  >{{ scope.row.used_count }}</el-button
                >
              </template>
            </el-table-column>
            <!-- <el-table-column label="开始时间" width="100">
              <template slot-scope="scope">
                {{ moment(scope.row.valid_time_start).format("YYYY-MM-DD") }}
              </template>
            </el-table-column>
            <el-table-column label="结束时间" width="100">
              <template slot-scope="scope">
                {{ moment(scope.row.valid_time_end).format("YYYY-MM-DD") }}
              </template>
            </el-table-column> -->
            <el-table-column label="有效时间" width="220">
              <template slot-scope="scope">
                {{ scope.row.valid_time_start | getDate }}至{{
                  scope.row.valid_time_end | getDate
                }}
              </template>
            </el-table-column>
            <el-table-column label="发券时间" width="220">
              <template slot-scope="scope">
                <span
                  v-if="
                    scope.row.assign_time_start_str === '' &&
                    scope.row.assign_time_end_str === ''
                  "
                  >不限</span
                >
                <span v-else>
                  {{ scope.row.assign_time_start_str }}至{{
                    scope.row.assign_time_end_str
                  }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="发放方式">
              <template slot-scope="scope">
                {{
                  scope.row.assign_method == 1
                    ? "不限"
                    : scope.row.assign_method == 2
                    ? "小程序发放"
                    : "手动发放"
                }}
              </template>
            </el-table-column>
            <el-table-column label="允许叠加">
              <template slot-scope="scope">
                <el-switch v-model="scope.row.is_overlay" disabled></el-switch>
              </template>
            </el-table-column>
            <el-table-column label="状态">
              <template slot-scope="scope">
                {{
                  scope.row.valid_status == 1
                    ? "进行中"
                    : scope.row.valid_status == 2
                    ? "未开启"
                    : scope.row.valid_status == 3
                    ? "已结束"
                    : ""
                }}
              </template>
            </el-table-column>
            <el-table-column prop="name" label="停用/启用">
              <template slot-scope="scope">
                {{ scope.row.used_status ? "启用" : "停用" }}
              </template>
            </el-table-column>
            <el-table-column
              show-overflow-tooltip
              prop="description"
              min-width="120"
              label="说明"
            ></el-table-column>
            <el-table-column label="操作" fixed="right" width="210">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  size="small"
                  v-has="{ m: 'coupon_category', o: 'coupon_update' }"
                  class="tg-text--blue tg-span__divide-line"
                  @click="openDialog(7, scope.row)"
                  >修改</el-button
                >
                <el-button
                  v-if="scope.row.assign_status"
                  type="text"
                  size="small"
                  v-has="{ m: 'coupon_category', o: 'coupon_pool' }"
                  class="tg-text--blue tg-span__divide-line"
                  @click="openDialog(9, scope.row)"
                  >分配</el-button
                >
                <el-button
                  type="text"
                  size="small"
                  class="tg-text--blue tg-span__divide-line"
                  v-has="{ m: 'coupon_category', o: 'coupon_delete' }"
                  v-if="!scope.row.assign_status && !scope.row.used_status"
                  @click="openDialog(3, scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
            <template slot="empty">
              <div style="margin-top: 15%">
                <TgLoading v-if="loading"></TgLoading>
                <div class="empty-container" v-else>暂无数据～</div>
              </div>
            </template>
          </el-table>
          <div class="tg-pagination">
            <span class="el-pagination__total">共 {{ total }} 条</span>
            <el-pagination
              background
              layout="sizes,prev,pager,next,jumper"
              :total="total"
              :page-size="pageSize"
              :current-page="page"
              @current-change="currentChange"
              @size-change="sizeChange"
              :page-sizes="[10, 20, 50, 100]"
            ></el-pagination>
          </div>
        </div>
      </div>
    </div>
    <distribute-dialog
      v-if="distribute_dialog_visible"
      @close="distribute_dialog_visible = false"
    ></distribute-dialog>
    <addCouponType
      v-if="add_type_visible"
      :coupon_type_name="coupon_type_name"
      @close="add_type_visible = false"
      @confirm="confirmCreateType"
    ></addCouponType>
    <issueDialog
      v-if="issue_flag_visible"
      source="discountCouponList"
      :issue_flag_visible="issue_flag_visible"
      :select_row="select_row"
      :dialog_type="dialog_type"
      @close="issue_flag_visible = false"
    ></issueDialog>
    <addCouponDialog
      ref="addCouponDialog"
      :data_list="data_type"
      :year="year"
      :course_level="searchTitle[5].selectOptions"
      :course_type="searchTitle[6].selectOptions"
      :course_genre_list="course_genre_list"
      :course_attribute_list="course_attribute_list"
      @init="initCoupon"
    ></addCouponDialog>
    <editCouponDialog
      ref="editCouponDialog"
      :data_list="data_type"
      :year="year"
      :course_level="searchTitle[5].selectOptions"
      :course_type="searchTitle[6].selectOptions"
      :course_genre_list="course_genre_list"
      :course_attribute_list="course_attribute_list"
      @init="initCoupon"
    ></editCouponDialog>
    <delFail
      v-if="delfail_visible"
      @close="
        delfail_visible = false;
        initCoupon();
      "
      :list="delfail_list"
    ></delFail>
  </div>
</template>

<script>
import { getCourseConfigByType } from "@/api/courseManagement.js";
import DistributeDialog from "./dialog/distributeDialog.vue";
import issueDialog from "./dialog/issueDialog.vue";
import addCouponDialog from "./dialog/addCoupon.vue";
import editCouponDialog from "./dialog/editCoupon.vue";
import addCouponType from "./dialog/addCouponType.vue";
import couponApi from "@/api/discountCoupon";
import { course_years } from "@/public/dict.js";
import studentInforApi from "@/api/studentInfor";
import delFail from "./dialog/delFail.vue";

export default {
  data() {
    return {
      distribute_dialog_visible: false,
      data_list: [],
      data_type: [],
      hover_index: 0,
      categroy_index: 0,
      searchTitle: [
        { props: "name", label: "名称", type: "input", show: true },
        {
          props: "article_ids",
          label: "适用物品",
          type: "choose_goods",
          show: true,
          arr: []
        },
        {
          props: "course_ids",
          label: "适用课程",
          type: "choose_course",
          show: true
        },
        {
          props: "valid_status",
          label: "状态",
          type: "select",
          show: false,
          selectOptions: [
            { name: "不限", id: "" },
            { name: "进行中", id: 1 },
            { name: "未开启", id: 2 },
            { name: "已结束", id: 3 }
          ]
        },
        {
          props: "course_year",
          label: "年份",
          type: "select",
          show: false,
          selectOptions: []
        },
        {
          props: "course_level",
          label: "课程种类",
          type: "select",
          width: 400,
          show: false,
          placeholder: "请选择",
          selectOptions: []
        },
        {
          props: "course_type",
          label: "类型",
          type: "select",
          width: 400,
          show: false,
          placeholder: "请选择",
          selectOptions: []
        },
        {
          props: "course_attribute",
          label: "课程属性",
          type: "select",
          width: 400,
          show: false,
          placeholder: "请选择",
          selectOptions: []
        },
        {
          props: "student_type",
          label: "学员类别",
          type: "mutipleSelect",
          width: 400,
          show: false,
          placeholder: "请选择",
          selectOptions: []
        },
        {
          props: "is_enabled",
          label: "停用/启用",
          type: "select",
          show: false,
          selectOptions: [
            { name: "停用", id: 2 },
            { name: "启用", id: 1 }
          ]
        }
      ],
      search: {
        name: "",
        valid_status: "",
        course_year: [],
        course_level: [],
        course_type: [],
        course_attribute: [],
        course_ids: [],
        article_ids: []
      },
      page: 1,
      pageSize: 10,
      total: 0,
      list: [],
      issue_flag_visible: false,
      creat_flag_visible: false,
      select_row: {},
      dialog_type: "issue",
      loading: false,
      add_type_visible: false,
      coupon_type_name: "",
      coupon_type_id: "",
      delete_type: "",
      select_ids: [],
      category_id: "",
      year: [],
      course_genre_list: [],
      editInfo: {},
      course_attribute_list: [],
      delfail_visible: false
    };
  },
  created() {
    this.getSelect("course_level");
    this.getSelect("course_attribute");
    this.getSelect("course_type");
    this.getSelect("course_genre");
  },
  mounted() {
    this.initCouponType();
    this.getStudentStyleList();
    this.initCoupon();
    const years = [
      {
        id: "",
        name: "不限"
      }
    ];

    course_years.map((item) => {
      years.push({
        id: item,
        name: item
      });
      this.year.push({
        id: item,
        name: item + ""
      });
    });

    this.searchTitle[4].selectOptions = years;
  },
  computed: {
    school_id() {
      return this.$store.getters.doneGetSchoolId;
    }
  },
  watch: {
    school_id() {
      this.initCoupon();
    }
  },
  methods: {
    getStudentStyleList() {
      studentInforApi.getStudentType().then((res) => {
        if (+res.status === 200 && +res.data.code === 0) {
          const searchItem = this.searchTitle.filter(
            (item) => item.props === "student_type"
          )[0];
          searchItem.selectOptions = res.data.data;
        }
      });
    },
    initCouponType() {
      // 优惠券分类接口
      this.data_list = [{ name: "全部" }];
      couponApi
        .GetCouponTypeList({
          department_id: this.school_id
        })
        .then((res) => {
          if (res.data.data !== null && res.data.data.length > 0) {
            this.data_list = this.data_list.concat(res.data.data);
          }
          this.data_type = res.data.data ?? [];
        });
    },
    initCoupon() {
      this.loading = true;
      this.list = [];
      // 优惠券列表接口
      const data = {
        category_id: this.category_id,
        page: this.page,
        page_size: this.pageSize,
        name: this.search.name,
        valid_status: this.search.valid_status,
        course_year: this.search.course_year || undefined,
        course_level: this.search.course_level || undefined,
        course_type: this.search.course_type || undefined,
        course_attribute: this.search.course_attribute || undefined,
        course_ids: this.search.course_ids,
        article_ids: this.search.article_ids,
        student_type: this.search.student_type,
        is_enabled: this.search.is_enabled,
        department_id: this.school_id
      };
      couponApi.GetCouponList(data).then((res) => {
        this.list = res.data.data.results ?? [];
        this.total = res.data.data.count;
        this.loading = false;
      });
    },
    editCouponType(val) {
      // 优惠券分类修改弹窗打开
      if (val) {
        this.coupon_type_name = val.name;
        this.coupon_type_id = val.id;
      } else {
        this.coupon_type_name = "";
      }

      this.add_type_visible = true;
    },
    delCouponType(val) {
      // 优惠券删除弹窗打开
      this.delete_type = "type";
      this.coupon_type_id = val.id;
      this.delCoupon();
    },
    handleSelectionChange(val) {
      // 列表多选
      this.select_ids = val.map((t) => {
        return t.id;
      });
    },
    searchVal() {
      // 搜索
      this.page = 1;
      this.clearSelection();
      this.initCoupon();
    },
    clearSelection() {
      this.$nextTick(() => {
        this.$refs.table.clearSelection();
      });
    },
    reset() {
      // 重置
      this.search = {
        name: "",
        valid_status: "",
        course_year: [],
        course_level: [],
        course_type: [],
        course_attribute: [],
        course_ids: [],
        article_ids: [],
        student_type: [],
        is_enabled: ""
      };
      this.page = 1;
      this.pageSize = 10;
      this.searchTitle[1].arr = [];
      this.clearSelection();
      this.searchVal();
    },
    categroyTypeActive(val, index) {
      // 优惠券分类列表切换
      this.categroy_index = index;
      this.category_id = val.id;
      this.initCoupon();
    },
    openDialog(val, row) {
      this.select_row = row;
      if (val === 1) {
        this.$refs.addCouponDialog.openDialog();
      } else if (val === 2) {
        this.delete_type = "multi";
        this.delCoupon();
      } else if (val === 3) {
        this.delete_type = "";
        this.delCoupon();
      } else if (val === 4) {
        this.dialog_type = "1";
        this.issue_flag_visible = true;
      } else if (val === 5) {
        this.dialog_type = "2";
        this.issue_flag_visible = true;
      } else if (val === 9) {
        this.distribute_dialog_visible = true;
      } else if (val === 6) {
        this.dialog_type = "3";
        this.issue_flag_visible = true;
      } else if (val === 7) {
        couponApi.GetCouponInfo({ id: row.id }).then((res) => {
          this.$refs.editCouponDialog.openDialog(res.data.data);
        });
      }
    },
    getRowKeys(row) {
      return row.id;
    },
    currentChange(val) {
      this.page = val;
      this.initCoupon();
    },
    sizeChange(val) {
      this.page = 1;
      this.pageSize = val;
      this.initCoupon();
    },
    delCoupon() {
      // 删除接口
      this.$confirm("此操作将永久删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          if (this.delete_type === "type") {
            couponApi.DelCouponType({ id: this.coupon_type_id }).then((res) => {
              if (res.data.code === 0) {
                this.$message.success("优惠券类型删除成功");
                this.clearSelection();
                this.initCouponType();
              } else if (res.data.code === 1) {
                this.$message.error(res.data.message);
              }
            });
          } else if (this.delete_type === "multi") {
            couponApi.DelCoupons({ ids: this.select_ids }).then((res) => {
              if (res.data.code === 0) {
                this.$message.success("优惠券删除成功");
                this.initCoupon();
              } else if (res.data.code === 1) {
                this.delfail_visible = true;
                this.delfail_list = res.data.message.split("，");
                // this.$message.error(res.data.message);
              }
            });
          } else {
            couponApi.DelCoupon({ id: this.select_row.id }).then((res) => {
              if (res.data.code === 0) {
                this.$message.success("优惠券删除成功");
                this.initCoupon();
              } else if (res.data.code === 1) {
                // this.delfail_visible = true;
                // this.delfail_list = res.data.message.split("，");
                this.$message.error(res.data.message);
              }
            });
          }
        })
        .catch(() => {});
    },
    confirmCreateType(val) {
      // 修改接口
      if (this.coupon_type_name) {
        couponApi
          .SaveCouponType({ name: val, id: this.coupon_type_id })
          .then((res) => {
            if (res.data.code === 0) {
              this.add_type_visible = false;
              this.$message.success("优惠券分类修改成功");
              this.clearSelection();
              this.initCouponType();
            } else if (res.data.code === 1) {
              this.add_type_visible = false;
              this.$message.error(res.data.message);
            }
          });
      } else {
        couponApi.CreatecouponType({ name: val }).then((res) => {
          if (res.data.code === 0) {
            this.add_type_visible = false;
            this.$message.success("优惠券分类添加成功");
            this.clearSelection();
            this.initCouponType();
          } else if (res.data.code === 1) {
            this.add_type_visible = false;
            this.$message.error(res.data.message);
          }
        });
      }
    },
    async getSelect(str) {
      const { data } = await getCourseConfigByType({}, str);
      if (data) {
        const arr = [];
        data.map((item) => {
          arr.push({
            id: str === "course_type" ? item.config_value : item.config_name,
            name: item.config_name
          });
        });
        if (str === "course_type") {
          this.searchTitle[6].selectOptions = arr;
          this.searchTitle[6].selectOptions.unshift({ name: "不限", id: "" });

          this.course_type_list = arr;
        }
        if (str === "course_level") {
          this.searchTitle[5].selectOptions = arr;
          this.searchTitle[5].selectOptions.unshift({ name: "不限", id: "" });
        }
        // if (str === "course_class_type") {
        //   this.searchTitle[5].selectOptions = arr;
        // }
        if (str === "course_attribute") {
          this.course_attribute_list = data;
          // this.searchTitle[7].selectOptions = data;
          for (const key in data) {
            this.searchTitle[7].selectOptions.push({
              id: data[key].config_value,
              name: data[key].config_name
            });
          }
          this.searchTitle[7].selectOptions.unshift({ name: "不限", id: "" });
        }
        if (str === "course_genre") {
          this.course_genre_list = data;
        }
      }
    }
  },
  components: {
    DistributeDialog,
    issueDialog,
    addCouponDialog,
    addCouponType,
    editCouponDialog,
    delFail
  }
};
</script>

<style lang="less" scoped>
.discountCoupon {
  ::v-deep .el-table td:last-child {
    border-right-color: transparent !important;
  }
  ::v-deep .el-table__header th:last-child {
    border-right-color: transparent !important;
  }
  .goods-content--left {
    width: 230px;
    background: #fff;
    border-radius: 4px;
    height: 100%;
  }

  .goods-content--right {
    width: calc(100% - 240px);
    margin-left: 10px;
  }

  .side-wrap {
    overflow: scroll;
    height: calc(100vh - 150px);
  }

  @media screen and (max-width: 1500px) {
    .goods-content--left {
      width: 180px;
    }

    .goods-content--right {
      width: calc(100% - 180px);
    }
  }

  .goods-content {
    width: 100%;
    display: flex;
    flex-direction: row;
    margin-left: 6px;
    flex: 1;
  }

  .title {
    padding: 0 16px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    height: 64px;
    border-bottom: 1px solid #e0e6ed;

    span {
      font-size: 15px;
      font-family: @text-famliy_medium;
    }
  }

  .side-select {
    height: 48px;
    line-height: 48px;
    border-left: 2px solid transparent;
    padding-left: 14px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
  }

  .side-select__label {
    font-size: @text-size_small;
    font-family: @text-famliy_medium;
    color: @text-color_second;
    display: inline-block;
    white-space: nowrap;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .side-select-active {
    .side-select__label {
      color: @base-color;
    }

    background-color: @light-color;
    border-left: 2px solid @base-color;
  }

  .side-setting__icon {
    width: 14px;
    height: 14px;
    margin-right: 8px;
    cursor: pointer;
  }
  .dialog_content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  ::v-deep .el-table__body-wrapper {
    height: calc(100% - 46px);
    .empty-container {
      position: absolute;
      top: 50%;
      left: 50%;
    }
    .loading-container {
      position: absolute;
      top: 30%;
      left: 1%;
      background: transparent;
      .box {
        height: 100%;
      }
    }
  }
}
</style>
