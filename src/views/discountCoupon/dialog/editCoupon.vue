<template>
  <el-dialog
    title="修改优惠券"
    :visible="true"
    v-if="creat_flag_visible"
    width="70%"
    :before-close="close"
    class="edit_coupon_dialog"
  >
    <el-form
      label-width="120px"
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
    >
      <div class="dialog_box">
        <div class="dialog_title">基本信息</div>
        <div class="dialog_info">
          <el-form-item label="优惠券分类" prop="category_id" required>
            <el-select
              v-model="ruleForm.category_id"
              disabled
              :popper-append-to-body="false"
            >
              <el-option
                v-for="item in data_list"
                :value="item.id"
                :label="item.name"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="优惠券名称" prop="name" required>
            <el-input
              v-model="ruleForm.name"
              :disabled="dialog_type == 'look'"
            ></el-input>
          </el-form-item>
          <el-form-item label="优惠券类型" prop="coupon_type" required>
            <el-select v-model="ruleForm.coupon_type" disabled>
              <el-option :value="1" label="满减券"></el-option>
              <el-option :value="2" label="折扣券"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="发行总量" prop="amount" required>
            <el-input v-model.number="ruleForm.amount" disabled></el-input>
          </el-form-item>
          <el-form-item
            label="优惠券面额"
            prop="quota"
            required
            v-if="ruleForm.coupon_type == 1"
          >
            <el-input v-model.number="ruleForm.quota" disabled></el-input>
          </el-form-item>
          <el-form-item label="优惠内容" prop="quota" required v-else>
            <el-input
              class="min_input"
              v-model.number="ruleForm.quota"
              disabled
            ></el-input>
            <span style="margin-right: 30px"
              >% 打{{ (ruleForm.quota / 10).toFixed(1) }}折</span
            >
            <el-checkbox v-model="checked" disabled>
              优惠上限
              <el-input
                class="min_input"
                v-model.number="ruleForm.quota_upper"
                disabled
              ></el-input
              >元
            </el-checkbox>
          </el-form-item>
          <el-form-item label="限发数量" prop="limit_count" required>
            <el-input
              v-model.number="ruleForm.limit_count"
              :disabled="dialog_type == 'look'"
            ></el-input>
          </el-form-item>
        </div>
      </div>
      <div class="dialog_box">
        <div class="dialog_title">使用条件</div>
        <div class="dialog_info">
          <el-form-item label="使用范围" prop="used_range" required>
            <el-checkbox-group
              v-model="ruleForm.used_range"
              :disabled="dialog_type == 'look'"
            >
              <el-checkbox label="1">前台报名</el-checkbox>
              <el-checkbox label="2">线上报名</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="使用门槛" prop="used_threshold" required>
            <el-radio-group v-model="radio" :disabled="dialog_type == 'look'">
              <el-radio :label="0">无门槛</el-radio>
              <el-radio :label="1">
                订单满
                <el-input
                  class="min_input"
                  v-model.number="ruleForm.used_threshold"
                  :disabled="radio == 0 || dialog_type == 'look'"
                ></el-input
                >元可用
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="适用于" prop="used_limit" required>
            <el-checkbox-group
              v-model="ruleForm.used_limit"
              :disabled="dialog_type == 'look'"
            >
              <el-checkbox label="1" @change="checkChange('1')"
                >课程</el-checkbox
              >
              <el-checkbox label="2" @change="checkChange('2')"
                >适用课程属性</el-checkbox
              >
              <el-checkbox label="3" @change="checkChange('3')"
                >物品</el-checkbox
              >
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="发放方式" prop="assign_method" required>
            <el-radio-group v-model="ruleForm.assign_method" disabled>
              <el-radio :label="1">不限</el-radio>
              <el-radio :label="2">小程序发放</el-radio>
              <el-radio :label="3">手动发放</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="课程类型" v-if="check_fit">
            <el-select
              v-model="ruleForm.course_type"
              multiple
              placeholder="请选择"
              :disabled="check_type.course_type || dialog_type == 'look'"
              :popper-append-to-body="false"
            >
              <el-option
                v-for="item in course_type"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
            <el-checkbox
              v-model="check_type.course_type"
              @change="changeChoose()"
              :disabled="dialog_type == 'look'"
              class="margin_check"
              >不限</el-checkbox
            >
          </el-form-item>
          <el-form-item label="年份" v-if="check_fit">
            <el-select
              v-model="ruleForm.course_year"
              multiple
              placeholder="请选择"
              :disabled="check_type.course_year || dialog_type == 'look'"
              :popper-append-to-body="false"
            >
              <el-option
                v-for="item in year"
                :key="item.id"
                :label="item.name"
                :value="item.name"
              ></el-option>
            </el-select>
            <el-checkbox
              v-model="check_type.course_year"
              @change="changeChoose()"
              :disabled="dialog_type == 'look'"
              class="margin_check"
              >不限</el-checkbox
            >
          </el-form-item>
          <el-form-item label="课程种类" v-if="check_fit">
            <el-select
              v-model="ruleForm.course_level"
              multiple
              placeholder="请选择"
              :disabled="check_type.course_level || dialog_type == 'look'"
              :popper-append-to-body="false"
            >
              <el-option
                v-for="item in course_level"
                :key="item.id"
                :label="item.name"
                :value="item.name"
              ></el-option>
            </el-select>
            <el-checkbox
              v-model="check_type.course_level"
              @change="changeChoose()"
              :disabled="dialog_type == 'look'"
              class="margin_check"
              >不限</el-checkbox
            >
          </el-form-item>
          <el-form-item label="类型" v-if="check_fit">
            <el-select
              v-model="ruleForm.lesson_type"
              multiple
              placeholder="请选择"
              :disabled="check_type.lesson_type || dialog_type == 'look'"
              :popper-append-to-body="false"
            >
              <el-option
                v-for="item in course_genre_list"
                :key="item.config_value"
                :label="item.config_name"
                :value="item.config_value"
              ></el-option>
            </el-select>
            <el-checkbox
              v-model="check_type.lesson_type"
              @change="changeChoose()"
              :disabled="dialog_type == 'look'"
              class="margin_check"
              >不限</el-checkbox
            >
          </el-form-item>
          <el-form-item label="课程属性" v-if="check_fit">
            <el-select
              v-model="ruleForm.course_attribute"
              multiple
              placeholder="请选择"
              :disabled="check_type.course_attribute || dialog_type == 'look'"
              :popper-append-to-body="false"
            >
              <el-option
                v-for="item in course_attribute_list"
                :key="item.config_value"
                :label="item.config_name"
                :value="item.config_value"
              ></el-option>
            </el-select>
            <el-checkbox
              v-model="check_type.course_attribute"
              @change="changeChoose()"
              :disabled="dialog_type == 'look'"
              class="margin_check"
              >不限</el-checkbox
            >
          </el-form-item>
          <el-form-item label="课程" v-if="check_course">
            <div style="display: flex">
              <ToolTip :content="ruleForm.course_name">
                <el-input
                  v-model="ruleForm['course_name']"
                  readonly
                  placeholder="请选择课程"
                  @click.native="courseSelect"
                  @mouseenter.native="course_flag = true"
                  @mouseleave.native="course_flag = false"
                  :class="{ 'border--active': course_flag }"
                  :disabled="check_type.course_name || dialog_type == 'look'"
                >
                  <span
                    slot="suffix"
                    class="endText"
                    :style="{
                      background:
                        check_type.course_name || dialog_type == 'look'
                          ? '#cccccc'
                          : '#2d80ed'
                    }"
                    >选择</span
                  >
                </el-input>
              </ToolTip>
              <el-checkbox
                v-model="check_type.course_name"
                @change="changeChoose()"
                :disabled="dialog_type == 'look'"
                class="margin_check"
                >不限</el-checkbox
              >
            </div>
            <choose-course
              :check_id.sync="ruleForm.course_ids"
              :check_name.sync="ruleForm.course_name"
              :check_arr.sync="course_check_arr"
              :choose_course_visible="choose_course_visible"
              v-if="choose_course_visible"
              :status="true"
              @close="choose_course_visible = false"
              @confirm="courseConfirm"
            ></choose-course>
          </el-form-item>
          <el-form-item label="物品" v-if="check_goods">
            <div style="display: flex">
              <ToolTip :placement="bottom" :content="ruleForm.article_name">
                <el-input
                  v-model="ruleForm['article_name']"
                  readonly
                  placeholder="请选择物品"
                  @click.native="goodsSelect"
                  @mouseenter.native="course_flag = true"
                  @mouseleave.native="course_flag = false"
                  :class="{ 'border--active': course_flag }"
                  :disabled="check_type.article_name || dialog_type == 'look'"
                >
                  <span
                    slot="suffix"
                    class="endText"
                    :style="{
                      background:
                        check_type.article_name || dialog_type == 'look'
                          ? '#cccccc'
                          : '#2d80ed'
                    }"
                    >选择</span
                  >
                </el-input>
              </ToolTip>
              <el-checkbox
                v-model="check_type.article_name"
                @change="changeChoose()"
                :disabled="dialog_type == 'look'"
                class="margin_check"
                >不限</el-checkbox
              >
            </div>
            <choose-warehouse-goods
              v-if="goods_visible"
              :check_arr.sync="goods_check_arr"
              :check_name.sync="ruleForm.article_name"
              @close="goods_visible = false"
              @confirm="goodsConfirm"
            ></choose-warehouse-goods>
          </el-form-item>
          <el-form-item label="学员类别">
            <el-select
              v-model="ruleForm.student_type"
              multiple
              placeholder="请选择"
              :disabled="check_type.student_type || dialog_type == 'look'"
              :popper-append-to-body="false"
            >
              <el-option
                v-for="item in student_category"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
            <el-checkbox
              v-model="check_type.student_type"
              @change="changeChoose()"
              :disabled="dialog_type == 'look'"
              class="margin_check"
              >不限</el-checkbox
            >
          </el-form-item>
          <el-form-item label="有效时间" prop="valid_time_start" required>
            <el-date-picker
              v-model="validTime"
              type="daterange"
              clearable
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
              @change="validTimeChange"
              :disabled="dialog_type == 'look'"
              popper-class="tg-date-picker tg-date--range"
            ></el-date-picker>
          </el-form-item>
          <el-form-item
            label="发券时间"
            prop="assign_time_start"
            :required="assign_required"
          >
            <el-date-picker
              v-model="assignTime"
              type="daterange"
              clearable
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
              @change="assignTimeChange"
              popper-class="tg-date-picker tg-date--range"
              :disabled="check_type.assign_time_type || dialog_type == 'look'"
            ></el-date-picker>
            <el-checkbox
              v-model="check_type.assign_time_type"
              @change="changeChoose()"
              class="margin_check"
              :disabled="dialog_type == 'look'"
              >不限</el-checkbox
            >
          </el-form-item>
          <el-form-item label="是否允许发放">
            <el-switch
              v-model="ruleForm.assign_status"
              :disabled="dialog_type == 'look'"
            ></el-switch>
            <span class="text_margin" v-if="ruleForm.coupon_type == 1"
              >是否允许叠加</span
            >
            <el-switch
              v-model="ruleForm.is_overlay"
              v-if="ruleForm.coupon_type == 1"
              :disabled="dialog_type == 'look'"
            ></el-switch>
            <span class="text_margin">是否允许使用</span>
            <el-switch
              v-model="ruleForm.used_status"
              :disabled="dialog_type == 'look'"
            ></el-switch>
          </el-form-item>
          <el-form-item label="说明">
            <el-input
              type="textarea"
              v-model="ruleForm.description"
              :disabled="dialog_type == 'look'"
            ></el-input>
          </el-form-item>
        </div>
      </div>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button class="tg-button--plain" @click="close">取 消</el-button>
      <el-button
        class="tg-button--primary"
        type="primary"
        @click="really"
        v-has="{ m: 'coupon_category', o: 'coupon_update' }"
        v-if="dialog_type != 'look'"
        :loading="subnit_loading"
        >确 定</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import couponApi from "@/api/discountCoupon";
import chooseWarehouseGoods from "@/components/goods/chooseWarehouseGoods.vue";
import studentInforApi from "@/api/studentInfor";
export default {
  props: {
    data_list: {},
    year: {},
    course_level: {},
    course_type: {},
    course_genre_list: {},
    course_attribute_list: {}
  },
  data() {
    // var checkAmount = (rule, value, callback) => {
    //   if (!value || value <= 0) {
    //     this.$message.closeAll();
    //     callback(this.$message.error("发行总量必须大于0!"));
    //   }
    // };
    // var checkQuota = (rule, value, callback) => {
    //   if (!value || value <= 0) {
    //     this.$message.closeAll();
    //     callback(this.$message.error("优惠券面额必须大于0!"));
    //   }
    // };
    // var checkLimitCount = (rule, value, callback) => {
    //   if (!value || value <= 0) {
    //     this.$message.closeAll();
    //     callback(this.$message.error("限发数量必须大于0!"));
    //   }
    // };
    return {
      subnit_loading: false,
      checked: false,
      radio: 0,
      ruleForm: {
        coupon_type: 1,
        used_limit: ["2"],
        used_range: [],
        course_ids: [],
        article_ids: [],
        quota: 0,
        course_name: "",
        article_name: ""
      },
      goods_check_arr: [],
      course_flag: false,
      choose_course_visible: false,
      goods_visible: false,
      check_course: false,
      check_fit: true,
      check_goods: false,
      course_check_arr: [],
      rules: {
        name: [{ required: true, trigger: "blur" }]
      },
      check_type: {
        course_type: true,
        course_year: true,
        course_level: true,
        lesson_type: true,
        course_attribute: true,
        student_type: true,
        course_name: true,
        article_name: true,
        assign_time_type: true
      },
      student_category: [],
      creat_flag_visible: false,
      dialog_type: "",
      validTime: [],
      assignTime: []
    };
  },
  computed: {
    assign_required() {
      return !this.check_type.assign_time_type;
    }
  },
  created() {
    this.getStudentStyleList();
  },
  watch: {
    radio: {
      handler(val) {
        if (val === 0) {
          this.ruleForm.used_threshold = 0;
        }
      }
    }
  },
  methods: {
    openDialog(val, type) {
      this.dialog_type = type;
      this.creat_flag_visible = true;
      this.course_check_arr = [];
      this.goods_check_arr = [];
      this.ruleForm = JSON.parse(JSON.stringify(val));
      // this.ruleForm.course_ids.map((item, index) => {
      //   this.course_check_arr.push({
      //     id: item,
      //     course_name: this.ruleForm.course_name[index]
      //   });
      // });
      this.ruleForm.course_ids =
        this.ruleForm.course_ids && this.ruleForm.course_ids.length > 0
          ? this.ruleForm.course_ids.join(",")
          : "";
      this.ruleForm.course_name =
        this.ruleForm.course_name && this.ruleForm.course_name.length > 0
          ? this.ruleForm.course_name.join(",")
          : "";
      this.ruleForm.article_name =
        this.ruleForm.article_name && this.ruleForm.article_name.length > 0
          ? this.ruleForm.article_name.join(",")
          : "";
      this.ruleForm.quota =
        this.ruleForm.coupon_type === 1
          ? this.ruleForm.quota / 100
          : this.ruleForm.quota;
      if (this.ruleForm.used_threshold > 0) {
        this.radio = 1;
        this.ruleForm.used_threshold = this.ruleForm.used_threshold / 100;
      } else {
        this.radio = 0;
      }
      if (this.ruleForm.quota_upper > 0) {
        this.checked = true;
        this.ruleForm.quota_upper = this.ruleForm.quota_upper / 100;
      } else {
        this.checked = false;
      }
      this.validTime[0] = this.ruleForm.valid_time_start;
      this.validTime[1] = this.ruleForm.valid_time_end;
      this.assignTime[0] = this.ruleForm.assign_time_start;
      this.assignTime[1] = this.ruleForm.assign_time_end;
      this.showInput();
      this.initCheckChange();
      this.getStudentStyleList();
      if (
        this.ruleForm.assign_time_start === "" &&
        this.ruleForm.assign_time_end === ""
      ) {
        this.ruleForm.assign_time_type = true;
        this.check_type.assign_time_type = true;
      } else {
        this.ruleForm.assign_time_type = false;
        this.check_type.assign_time_type = false;
      }
    },
    validTimeChange(val) {
      if (val) {
        this.ruleForm.valid_time_start = val[0];
        this.ruleForm.valid_time_end = val[1];
      } else {
        this.validTime = [];
        this.ruleForm.valid_time_start = "";
        this.ruleForm.valid_time_end = "";
      }
    },
    assignTimeChange(val) {
      if (val) {
        this.ruleForm.assign_time_start = val[0];
        this.ruleForm.assign_time_end = val[1];
      } else {
        this.assignTime = [];
        this.ruleForm.assign_time_start = "";
        this.ruleForm.assign_time_end = "";
      }
    },
    initCheckChange() {
      if (this.ruleForm.course_type && this.ruleForm.course_type.length > 0) {
        this.check_type.course_type = false;
      }
      if (this.ruleForm.course_year && this.ruleForm.course_year.length > 0) {
        this.check_type.course_year = false;
      }
      if (this.ruleForm.course_level && this.ruleForm.course_level.length > 0) {
        this.check_type.course_level = false;
      }
      if (this.ruleForm.lesson_type && this.ruleForm.lesson_type.length > 0) {
        this.check_type.lesson_type = false;
      }
      if (
        this.ruleForm.course_attribute &&
        this.ruleForm.course_attribute.length > 0
      ) {
        this.check_type.course_attribute = false;
      }
      if (this.ruleForm.student_type && this.ruleForm.student_type.length > 0) {
        this.check_type.student_type = false;
      }
      if (this.ruleForm.course_ids && this.ruleForm.course_ids.length > 0) {
        this.check_type.course_name = false;
      }
      if (this.ruleForm.article_ids && this.ruleForm.article_ids.length > 0) {
        this.check_type.article_name = false;
      }
      if (
        this.ruleForm.assign_time_end === "" &&
        this.ruleForm.assign_time_start === ""
      ) {
        this.check_type.assign_time_type = false;
      }
    },
    showInput() {
      if (this.ruleForm.used_limit.includes("2")) {
        this.check_fit = true;
        this.check_course = false;
      }
      if (this.ruleForm.used_limit.includes("1")) {
        this.check_fit = false;
        this.check_course = true;
      }
      if (this.ruleForm.used_limit.includes("3")) {
        this.check_goods = true;
      }
    },
    initCouponType() {
      couponApi.GetCouponTypeList().then((res) => {
        this.data_list = res.data.data;
      });
    },
    goodsConfirm() {
      this.ruleForm.article_ids = this.goods_check_arr.map((t) => {
        return t.id;
      });
    },
    courseConfirm() {
      this.ruleForm.course_ids = this.course_check_arr.map((t) => {
        return t.id;
      });
    },
    checkChange(val) {
      if (val === "1" && this.ruleForm.used_limit.includes("2")) {
        this.ruleForm.used_limit.splice(
          this.ruleForm.used_limit.indexOf("2"),
          1
        );
        if (this.ruleForm.used_limit.includes("1")) {
          this.check_course = true;
          this.check_fit = false;
        } else {
          this.check_course = false;
        }
      } else if (val === "2" && this.ruleForm.used_limit.includes("1")) {
        this.ruleForm.used_limit.splice(
          this.ruleForm.used_limit.indexOf("1"),
          1
        );
        if (this.ruleForm.used_limit.includes("2")) {
          this.check_course = false;
          this.check_fit = true;
        } else {
          this.check_fit = false;
        }
      } else if (val === "3") {
        if (this.ruleForm.used_limit.includes("3")) {
          this.check_goods = true;
        } else {
          this.check_goods = false;
        }
      } else if (val === "1") {
        if (this.ruleForm.used_limit.includes("1")) {
          this.check_course = true;
          this.check_fit = false;
        } else {
          this.check_course = false;
        }
      } else if (val === "2") {
        if (this.ruleForm.used_limit.includes("2")) {
          this.check_course = false;
          this.check_fit = true;
        } else {
          this.check_fit = false;
        }
      }
    },
    close() {
      this.checked = false;
      this.radio = 0;
      this.ruleForm = {
        coupon_type: 1,
        used_limit: ["2"],
        used_range: [],
        course_ids: [],
        article_ids: [],
        quota: 0,
        course_name: "",
        article_name: ""
      };
      this.goods_check_arr = [];
      this.course_flag = false;
      this.choose_course_visible = false;
      this.goods_visible = false;
      this.check_course = false;
      this.check_fit = true;
      this.check_goods = false;
      this.course_check_arr = [];

      this.check_type = {
        course_type: true,
        course_year: true,
        course_level: true,
        lesson_type: true,
        course_attribute: true,
        student_type: true,
        course_name: true,
        article_name: true,
        assign_time_type: true
      };
      this.student_category = [];
      this.creat_flag_visible = false;
      this.dialog_type = "";
      this.validTime = [];
      this.assignTime = [];
      this.creat_flag_visible = false;
    },
    really() {
      if (this.ruleForm.amount <= 0) {
        this.$message.error("发行总量必须大于0!");
        return;
      }
      if (this.ruleForm.quota <= 0) {
        this.$message.error("优惠券面额必须大于0!");
        return;
      }
      if (this.ruleForm.used_limit.length === 0) {
        this.$message.error("请选择适用于什么!");
        return;
      }
      if (this.ruleForm.limit_count <= 0) {
        this.$message.error("限发数量必须大于0!");
        return;
      }
      const data = JSON.parse(JSON.stringify(this.ruleForm));
      if (!this.checked) {
        data.quota_upper = 0;
      } else {
        data.quota_upper = this.ruleForm.quota_upper * 100;
      }
      if (this.radio === 0) {
        this.ruleForm.used_threshold = 0;
      } else {
        data.used_threshold = this.ruleForm.used_threshold * 100;
      }
      data.quota = data.coupon_type === 1 ? data.quota * 100 : data.quota;
      if (data.course_ids) {
        if (typeof data.course_ids === "string") {
          data.course_ids = data.course_ids.split(",");
        }
      } else {
        data.course_ids = [];
      }
      data.course_name =
        data.course_name.length > 0 ? data.course_name.split(",") : [];
      data.article_name =
        data.article_name.length > 0 ? data.article_name.split(",") : [];
      this.subnit_loading = true;
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          couponApi
            .SaveCouponInfo(data)
            .then((res) => {
              this.subnit_loading = false;
              if (res.data.code === 0) {
                this.$message.success("优惠券修改成功");
                this.creat_flag_visible = false;
                this.$emit("init");
              } else {
                this.$message.error(res.data.message);
              }
            })
            .catch(() => {
              this.subnit_loading = false;
            });
        }
      });
    },
    getStudentStyleList(data) {
      studentInforApi.getStudentType(data).then((res) => {
        if (+res.status === 200 && +res.data.code === 0) {
          this.student_category = [
            { id: "unspecified", name: "未分类" },
            ...res.data.data
          ];
        }
      });
    },
    goodsSelect() {
      console.log(this.check_type.article_name, this.dialog_type);
      if (!this.check_type.article_name && this.dialog_type !== "look") {
        this.goods_visible = true;
      }
    },
    courseSelect() {
      if (!this.check_type.course_name && this.dialog_type !== "look") {
        this.choose_course_visible = true;
      }
    },
    changeChoose() {
      // 不限
      if (this.check_type.course_type) this.ruleForm.course_type = [];
      if (this.check_type.course_year) this.ruleForm.course_year = [];
      if (this.check_type.course_level) this.ruleForm.course_level = [];
      if (this.check_type.lesson_type) this.ruleForm.lesson_type = [];
      if (this.check_type.course_attribute) this.ruleForm.course_attribute = [];
      if (this.check_type.student_type) this.ruleForm.student_type = [];
      if (this.check_type.course_name) {
        this.ruleForm.course_name = "";
        this.ruleForm.course_ids = [];
        this.course_check_arr = [];
      }
      if (this.check_type.article_name) {
        this.ruleForm.article_name = "";
        this.ruleForm.article_ids = [];
        this.goods_check_arr = [];
      }
      if (this.check_type.assign_time_type) {
        this.ruleForm.assign_time_start = "";
        this.ruleForm.assign_time_end = "";
        this.assignTime = [];
      }
    }
  },
  components: { chooseWarehouseGoods }
};
</script>

<style lang="less">
.edit_coupon_dialog {
  .el-dialog__body {
    padding: 16px 20px;
    .el-select {
      line-height: 32px !important;
    }
  }
  .dialog_box {
    margin-bottom: 16px;
    .dialog_title {
      width: 100%;
      height: 48px;
      background: #f5f8fc;
      border: 1px solid #2d80ed;
      border-radius: 4px 4px 0 0;
      padding-left: 16px;
      line-height: 48px;
      box-sizing: border-box;
      font-size: 14px;
      color: #1f2d3d;
    }
    .dialog_info {
      border: 1px solid #e0e6ed;
      border-radius: 0 0 4px 4px;
      width: 100%;
      padding: 8px 24px 16px;
      box-sizing: border-box;
    }
    .el-input {
      width: 400px;
    }
    .margin_check {
      margin-left: 10px;
    }
    .min_input {
      width: 100px;
    }
  }
  .border--active {
    position: relative;
    &::after {
      content: "";
      position: absolute;
      width: 400px;
      height: 32px;
      left: -2px;
      top: 2px;
      border: 2px solid #ebf4ff;
      border-radius: 6px;
      z-index: 10;
    }
    ::v-deep .el-input__inner {
      border-color: @base-color;
    }
  }
  .el-input__icon.el-icon-date::before {
    margin-top: -1px !important;
  }
  .endText {
    width: 60px;
    height: 32px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    user-select: none;
    color: #ffffff;
    cursor: pointer;
    position: absolute;
    top: 4px;
    right: -5px;
  }
  .text_margin {
    margin-left: 20px;
    margin-right: 10px;
  }
  .el-switch__core {
    width: 30px !important;
    height: 16px;
  }
  .el-switch__core::after {
    width: 14px;
    height: 14px;
    margin-top: -1px;
  }
  .el-switch.is-checked .el-switch__core::after {
    margin-left: -15px;
  }
}
</style>
