<template>
  <el-dialog
    :visible="true"
    v-if="issue_flag_visible"
    width="1010px"
    :before-close="close"
    class="issue_dialog"
  >
    <div slot="title">
      {{
        dialog_type == "2"
          ? "已发放明细"
          : dialog_type == "1"
          ? "发行张数明细"
          : "已使用明细"
      }}-
      <span style="color: #2d80ed">{{ select_row.name }}</span>
    </div>
    <tg-search
      :searchTitle.sync="searchTitle"
      :form.sync="search"
      :isExport="true"
      @reset="reset"
      @search="searchVal"
      @educe="coupomEduce"
      :showNum="3"
    ></tg-search>
    <!-- <el-row>
      <el-input v-model="search.coupon_detail_id"></el-input>
    </el-row>-->
    <div class="tg-table__box">
      <div class="tg-box--border"></div>
      <el-table
        ref="table"
        :data="list"
        tooltip-effect="dark"
        class="tg-table"
        v-loading="loading"
        border
      >
        <el-table-column
          prop="id"
          label="优惠券编号"
          width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column prop="student_name" label="领取人"></el-table-column>
        <el-table-column prop="student_number" label="学号"></el-table-column>
        <el-table-column prop="mobile" label="手机号" width="160">
          <template slot-scope="scope">
            <mobileHyposensitization
              :mobileTemInfo="{
                row: scope.row,
                has_eye_limit: scope.row?.has_eye_limit,
                mobile: scope.row.mobile
              }"
            ></mobileHyposensitization>
          </template>
        </el-table-column>
        <el-table-column prop="school_name" label="校区"></el-table-column>
        <el-table-column prop="receive_time" label="领取时间" width="110">
          <template slot-scope="scope">
            {{ scope.row.receive_time | getTime }}
          </template>
        </el-table-column>
        <el-table-column prop="use_status_str" label="状态"></el-table-column>
        <el-table-column prop="used_time" label="使用时间" width="110">
          <template slot-scope="scope">
            {{ scope.row.used_time | getTime }}
          </template>
        </el-table-column>
        <el-table-column prop="receipt_id" label="收据号"></el-table-column>
        <el-table-column prop="assign_man" label="发放人"></el-table-column>
        <el-table-column prop="description" label="备注"></el-table-column>
        <el-table-column
          v-has="{ m: 'coupon_category', o: 'coupon_status' }"
          label="操作"
          fixed="right"
          width="80"
          v-if="dialog_type != '3'"
        >
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.available"
              active-color="#13ce66"
              v-if="
                scope.row.use_status && scope.row.school_id === department_id
              "
              :active-value="1"
              :inactive-value="2"
              @change="couponUnlock(scope.row)"
            ></el-switch>
          </template>
        </el-table-column>
      </el-table>
      <div class="tg-pagination">
        <span class="el-pagination__total">共 {{ total }} 条</span>
        <el-pagination
          background
          layout="prev, pager, next,jumper"
          :total="total"
          :page-size="pageSize"
          :current-page="page"
          @current-change="currentChange"
        ></el-pagination>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button class="tg-button--primary" type="primary" @click="close"
        >关闭</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import couponApi from "@/api/discountCoupon";
export default {
  props: {
    issue_flag_visible: {},
    select_row: {},
    dialog_type: {},
    source: {
      type: String,
      default: "campusCouponList"
    },
    department_id: {
      type: String,
      default: ""
    }
  },
  computed: {
    schoolIds() {
      return this.$store.getters.doneGetSchoolId;
    }
  },
  data() {
    return {
      page: 1,
      pageSize: 10,
      total: 0,
      list: [],
      searchTitle: [
        {
          props: "coupon_detail_id",
          label: "编号",
          type: "input",
          show: true
        },
        {
          props: "student_name",
          label: "领取人",
          type: "input",
          show: true
        },
        {
          props: "use_status",
          label: "状态",
          type: "select",
          show: true,
          selectOptions: [
            { name: "已使用", id: 3 },
            { name: "使用中", id: 2 },
            { name: "未使用", id: 1 }
          ]
        }
      ],
      search: {
        coupon_detail_id: "",
        student_name: "",
        use_status: ""
      },
      loading: false,
      form: {}
    };
  },
  created() {
    this.initCounponDetail();
  },
  methods: {
    initCounponDetail() {
      console.log(this.dialog_type, this.schoolIds);
      if (this.dialog_type === "3") {
        this.searchTitle[2].show = false;
        this.form = {
          coupon_id: this.select_row.id,
          page: this.page,
          page_size: this.pageSize,
          status: 2,
          use_status: 3,
          student_name: this.search.student_name,
          coupon_detail_id: this.search.coupon_detail_id,
          school_id:
            this.source === "campusCouponList"
              ? this.department_id
              : this.schoolIds,
          department_id:
            this.source === "campusCouponList"
              ? this.department_id
              : this.schoolIds
        };
      } else {
        this.form = {
          coupon_id: this.select_row.id,
          page: this.page,
          page_size: this.pageSize,
          status: this.dialog_type,
          use_status: this.search.use_status,
          student_name: this.search.student_name,
          coupon_detail_id: this.search.coupon_detail_id,
          department_id:
            this.source === "campusCouponList"
              ? this.department_id
              : this.schoolIds,
          school_id:
            this.source === "campusCouponList"
              ? this.department_id
              : this.schoolIds
        };
      }

      couponApi.GetCounponDetailStatus(this.form).then((res) => {
        this.list = res.data.data.results ?? [];
        this.total = res.data.data.count;
      });
    },
    close() {
      this.$emit("close");
    },
    searchVal() {
      this.page = 1;
      this.initCounponDetail();
    },
    getRowKeys(row) {
      return row.id;
    },
    currentChange(val) {
      this.page = val;
      this.initCounponDetail();
    },
    coupomEduce() {
      couponApi
        .ExportCouponDetail({ ...this.form, exportData: 1 })
        .then((res) => {
          const blob = new Blob([res.data], {
            type: "application/vnd.ms-excel"
          }); // type这里表示xlsx类型
          const downloadElement = document.createElement("a");
          const href = window.URL.createObjectURL(blob); // 创建下载的链接
          downloadElement.href = href;
          downloadElement.download = `优惠券明细.xlsx`; // 下载后文件名
          document.body.appendChild(downloadElement);
          downloadElement.click(); // 点击下载
          document.body.removeChild(downloadElement);
        });
    },
    reset() {
      this.search = {
        coupon_detail_id: "",
        student_name: "",
        use_status: ""
      };
      this.searchVal();
    },
    couponUnlock(val) {
      couponApi
        .SaveAvailable({ available: val.available, id: val.id })
        .then(() => {
          this.initCounponDetail();
        });
    }
  }
};
</script>

<style lang="less" scoped>
.issue_dialog {
  ::v-deep.el-dialog__body {
    padding: 5px 10px;
  }
  ::v-deep.tg-select .el-select-dropdown.el-popper {
    left: unset !important;
  }
}
</style>
