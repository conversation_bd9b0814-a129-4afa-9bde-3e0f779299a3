<template>
  <el-dialog
    title="新增优惠券"
    :visible="true"
    v-if="creat_flag_visible"
    width="70%"
    :before-close="close"
    class="creat_coupon_dialog"
  >
    <el-form
      label-width="120px"
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
    >
      <div class="dialog_box">
        <div class="dialog_title">基本信息</div>
        <div class="dialog_info">
          <el-form-item label="优惠券分类" prop="category_id" required>
            <el-select
              v-model="ruleForm.category_id"
              :popper-append-to-body="false"
            >
              <el-option
                v-for="item in data_list"
                :value="item.id"
                :label="item.name"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="优惠券名称" prop="name" required>
            <el-input v-model="ruleForm.name"></el-input>
          </el-form-item>
          <el-form-item label="优惠券类型" prop="coupon_type" required>
            <el-select
              v-model="ruleForm.coupon_type"
              :popper-append-to-body="false"
            >
              <el-option :value="1" label="满减券"></el-option>
              <el-option :value="2" label="折扣券"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="发行总量" prop="amount" required>
            <el-input v-model.number="ruleForm.amount"></el-input>
          </el-form-item>
          <el-form-item
            label="优惠券面额"
            prop="quota"
            required
            v-if="ruleForm.coupon_type == 1"
          >
            <el-input v-model.number="ruleForm.quota"></el-input>
          </el-form-item>
          <el-form-item label="优惠内容" prop="quota" required v-else>
            <el-input
              class="min_input"
              v-model.number="ruleForm.quota"
            ></el-input>
            <span style="margin-right: 30px"
              >% 打{{ (ruleForm.quota / 10).toFixed(1) }}折</span
            >
            <el-checkbox v-model="checked">
              优惠上限
              <el-input
                class="min_input"
                v-model.number="ruleForm.quota_upper"
                :disabled="!checked"
              ></el-input
              >元
            </el-checkbox>
          </el-form-item>
          <el-form-item label="限发数量" prop="limit_count" required>
            <el-input v-model.number="ruleForm.limit_count"></el-input>
          </el-form-item>
        </div>
      </div>
      <div class="dialog_box">
        <div class="dialog_title">使用条件</div>
        <div class="dialog_info">
          <el-form-item label="使用范围" prop="used_range" required>
            <el-checkbox-group v-model="ruleForm.used_range">
              <el-checkbox label="1">前台报名</el-checkbox>
              <el-checkbox label="2">线上报名</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="使用门槛" prop="used_threshold" required>
            <el-radio-group v-model="radio">
              <el-radio :label="0">无门槛</el-radio>
              <el-radio :label="1">
                订单满
                <el-input
                  class="min_input"
                  v-model.number="ruleForm.used_threshold"
                  :disabled="radio == 0"
                ></el-input
                >元可用
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="适用于" prop="used_limit" required>
            <el-checkbox-group v-model="ruleForm.used_limit">
              <el-checkbox label="1" @change="checkChange('1')"
                >课程</el-checkbox
              >
              <el-checkbox label="2" @change="checkChange('2')"
                >适用课程属性</el-checkbox
              >
              <el-checkbox label="3" @change="checkChange('3')"
                >物品</el-checkbox
              >
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="发放方式" prop="assign_method" required>
            <el-radio-group v-model="ruleForm.assign_method">
              <el-radio :label="1">不限</el-radio>
              <el-radio :label="2">小程序发放</el-radio>
              <el-radio :label="3">手动发放</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            label="课程类型"
            v-if="check_fit"
            prop="course_type"
            :required="assign_required_check_type"
          >
            <el-select
              v-model="ruleForm.course_type"
              multiple
              placeholder="请选择"
              :disabled="check_type.course_type"
              :popper-append-to-body="false"
            >
              <el-option
                v-for="item in course_type"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
            <el-checkbox
              v-model="check_type.course_type"
              @change="changeChoose()"
              class="margin_check"
              >不限</el-checkbox
            >
          </el-form-item>
          <el-form-item
            label="年份"
            v-if="check_fit"
            prop="course_year"
            :required="assign_required_course_year"
          >
            <el-select
              v-model="ruleForm.course_year"
              multiple
              placeholder="请选择"
              :disabled="check_type.course_year"
              :popper-append-to-body="false"
            >
              <el-option
                v-for="item in year"
                :key="item.id"
                :label="item.name"
                :value="item.name"
              ></el-option>
            </el-select>
            <el-checkbox
              v-model="check_type.course_year"
              @change="changeChoose()"
              class="margin_check"
              >不限</el-checkbox
            >
          </el-form-item>
          <el-form-item
            label="课程种类"
            v-if="check_fit"
            prop="course_level"
            :required="assign_required_course_level"
          >
            <el-select
              v-model="ruleForm.course_level"
              multiple
              placeholder="请选择"
              :disabled="check_type.course_level"
              :popper-append-to-body="false"
            >
              <el-option
                v-for="item in course_level"
                :key="item.id"
                :label="item.name"
                :value="item.name"
              ></el-option>
            </el-select>
            <el-checkbox
              v-model="check_type.course_level"
              @change="changeChoose()"
              class="margin_check"
              >不限</el-checkbox
            >
          </el-form-item>
          <el-form-item
            label="类型"
            v-if="check_fit"
            prop="lesson_type"
            :required="assign_required_lesson_type"
          >
            <el-select
              v-model="ruleForm.lesson_type"
              multiple
              placeholder="请选择"
              :disabled="check_type.lesson_type"
              :popper-append-to-body="false"
            >
              <el-option
                v-for="item in course_genre_list"
                :key="item.config_value"
                :label="item.config_name"
                :value="item.config_value"
              ></el-option>
            </el-select>
            <el-checkbox
              v-model="check_type.lesson_type"
              @change="changeChoose()"
              class="margin_check"
              >不限</el-checkbox
            >
          </el-form-item>
          <el-form-item
            label="课程属性"
            v-if="check_fit"
            prop="course_attribute"
            :required="assign_required_course_attribute"
          >
            <el-select
              v-model="ruleForm.course_attribute"
              multiple
              placeholder="请选择"
              :disabled="check_type.course_attribute"
              :popper-append-to-body="false"
            >
              <el-option
                v-for="item in course_attribute_list"
                :key="item.config_value"
                :label="item.config_name"
                :value="item.config_value"
              ></el-option>
            </el-select>
            <el-checkbox
              v-model="check_type.course_attribute"
              @change="changeChoose()"
              class="margin_check"
              >不限</el-checkbox
            >
          </el-form-item>
          <el-form-item label="课程" v-if="check_course">
            <el-input
              v-model="ruleForm['course_name']"
              readonly
              placeholder="请选择课程"
              @click.native="courseSelect"
              @mouseenter.native="course_flag = true"
              @mouseleave.native="course_flag = false"
              :class="{ 'border--active': course_flag }"
              :disabled="check_type.course_name"
            >
              <span
                slot="suffix"
                class="endText"
                :style="{
                  background: check_type.course_name ? '#cccccc' : '#2d80ed'
                }"
                >选择</span
              >
            </el-input>
            <el-checkbox
              v-model="check_type.course_name"
              @change="changeChoose()"
              class="margin_check"
              >不限</el-checkbox
            >
            <choose-course
              :check_name.sync="ruleForm.course_name"
              :check_arr.sync="course_check_arr"
              :choose_course_visible="choose_course_visible"
              v-if="choose_course_visible"
              :status="true"
              @close="choose_course_visible = false"
              @confirm="courseConfirm"
            ></choose-course>
          </el-form-item>
          <el-form-item label="物品" v-if="check_goods">
            <el-input
              v-model="ruleForm['article_name']"
              readonly
              placeholder="请选择物品"
              @click.native="goodsSelect"
              @mouseenter.native="course_flag = true"
              @mouseleave.native="course_flag = false"
              :class="{ 'border--active': course_flag }"
              :disabled="check_type.article_name"
            >
              <span
                slot="suffix"
                class="endText"
                :style="{
                  background: check_type.article_name ? '#cccccc' : '#2d80ed'
                }"
                >选择</span
              >
            </el-input>
            <el-checkbox
              v-model="check_type.article_name"
              @change="changeChoose()"
              class="margin_check"
              >不限</el-checkbox
            >
            <choose-warehouse-goods
              v-if="goods_visible"
              :check_arr.sync="goods_check_arr"
              :check_name.sync="ruleForm.article_name"
              @close="goods_visible = false"
              @confirm="goodsConfirm"
            ></choose-warehouse-goods>
          </el-form-item>
          <el-form-item
            label="学员类别"
            prop="student_type"
            :required="assign_required_student_type"
          >
            <el-select
              v-model="ruleForm.student_type"
              multiple
              placeholder="请选择"
              :disabled="check_type.student_type"
              :popper-append-to-body="false"
            >
              <el-option
                v-for="item in student_category"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
            <el-checkbox
              v-model="check_type.student_type"
              @change="changeChoose()"
              class="margin_check"
              >不限</el-checkbox
            >
          </el-form-item>
          <el-form-item label="有效时间" prop="valid_time_start" required>
            <el-date-picker
              v-model="validTime"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
              @change="validTimeChange"
              popper-class="tg-date-picker tg-date--range"
            ></el-date-picker>
          </el-form-item>
          <el-form-item
            label="发券时间"
            prop="assign_time_start"
            :required="assign_required"
          >
            <el-date-picker
              v-model="assignTime"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
              @change="assignTimeChange"
              :disabled="check_type.assign_time_type"
              popper-class="tg-date-picker tg-date--range"
            ></el-date-picker>
            <el-checkbox
              v-model="check_type.assign_time_type"
              @change="changeChoose()"
              class="margin_check"
              >不限</el-checkbox
            >
          </el-form-item>
          <el-form-item label="是否允许发放">
            <el-switch v-model="ruleForm.assign_status"></el-switch>
            <span class="text_margin" v-if="ruleForm.coupon_type == 1"
              >是否允许叠加</span
            >
            <el-switch
              v-model="ruleForm.is_overlay"
              v-if="ruleForm.coupon_type == 1"
            ></el-switch>
            <span class="text_margin">是否允许使用</span>
            <el-switch v-model="ruleForm.used_status"></el-switch>
          </el-form-item>
          <el-form-item label="说明">
            <el-input type="textarea" v-model="ruleForm.description"></el-input>
          </el-form-item>
        </div>
      </div>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button class="tg-button--plain" type="plain" @click="close"
        >取 消</el-button
      >
      <el-button
        class="tg-button--primary"
        :loading="loading"
        type="primary"
        @click="really"
        >确 定</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import couponApi from "@/api/discountCoupon";
import chooseWarehouseGoods from "@/components/goods/chooseWarehouseGoods.vue";
import studentInforApi from "@/api/studentInfor";
export default {
  props: {
    data_list: {},
    year: {},
    course_level: {},
    course_type: {},
    course_genre_list: {},
    course_attribute_list: {}
  },
  data() {
    return {
      checked: false,
      radio: 0,
      loading: false,
      ruleForm: {
        coupon_type: 1,
        assign_method: 1,
        used_limit: ["2"],
        used_range: [],
        course_ids: [],
        article_ids: [],
        quota: "",
        course_name: "",
        article_name: ""
      },
      goods_check_arr: [],
      course_flag: false,
      choose_course_visible: false,
      goods_visible: false,
      check_course: false,
      check_fit: true,
      check_goods: false,
      course_check_arr: [],
      rules: {
        name: [{ required: true, trigger: "blur" }]
      },
      check_type: {
        course_type: true,
        course_year: true,
        course_level: true,
        lesson_type: true,
        course_attribute: true,
        student_type: true,
        course_name: true,
        article_name: true,
        assign_time_type: true
      },
      student_category: [],
      creat_flag_visible: false,
      dialog_type: "",
      validTime: [],
      assignTime: []
    };
  },
  computed: {
    assign_required() {
      return !this.check_type.assign_time_type;
    },
    assign_required_check_type() {
      return !this.check_type.course_type;
    },
    assign_required_course_year() {
      return !this.check_type.course_year;
    },
    assign_required_course_level() {
      return !this.check_type.course_level;
    },
    assign_required_lesson_type() {
      return !this.check_type.lesson_type;
    },
    assign_required_course_attribute() {
      return !this.check_type.course_attribute;
    },
    assign_required_student_type() {
      return !this.check_type.student_type;
    }
  },
  watch: {
    radio: {
      handler(val) {
        if (val === 0) {
          this.ruleForm.used_threshold = 0;
        }
      }
    }
  },
  created() {
    this.getStudentStyleList();
  },
  methods: {
    validTimeChange(val) {
      if (val) {
        this.ruleForm.valid_time_start = val[0];
        this.ruleForm.valid_time_end = val[1];
      } else {
        this.validTime = [];
        this.ruleForm.valid_time_start = "";
        this.ruleForm.valid_time_end = "";
      }
    },
    assignTimeChange(val) {
      if (val) {
        this.ruleForm.assign_time_start = val[0];
        this.ruleForm.assign_time_end = val[1];
      } else {
        this.assignTime = [];
        this.ruleForm.assign_time_start = "";
        this.ruleForm.assign_time_end = "";
      }
    },
    openDialog() {
      this.creat_flag_visible = true;
      this.check_fit = true;
      this.validTime = [];
      this.assignTime = [];
      this.ruleForm = {
        coupon_type: 1,
        assign_method: 1,
        used_limit: ["2"],
        used_range: [],
        course_ids: [],
        article_ids: [],
        quota: "",
        course_name: "",
        article_name: "",
        valid_time_end: "",
        valid_time_start: "",
        assign_time_start: "",
        assign_time_end: ""
      };
    },
    initCouponType() {
      couponApi.GetCouponTypeList().then((res) => {
        this.data_list = res.data.data;
      });
    },
    goodsConfirm() {
      this.ruleForm.article_ids = this.goods_check_arr.map((t) => {
        return t.id;
      });
    },
    courseConfirm() {
      this.ruleForm.course_ids = this.course_check_arr.map((t) => {
        return t.id;
      });
    },
    checkChange(val) {
      if (val === "1" && this.ruleForm.used_limit.includes("2")) {
        this.ruleForm.used_limit.splice(
          this.ruleForm.used_limit.indexOf("2"),
          1
        );
        if (this.ruleForm.used_limit.includes("1")) {
          this.check_course = true;
          this.check_fit = false;
        } else {
          this.check_course = false;
        }
      } else if (val === "2" && this.ruleForm.used_limit.includes("1")) {
        this.ruleForm.used_limit.splice(
          this.ruleForm.used_limit.indexOf("1"),
          1
        );
        if (this.ruleForm.used_limit.includes("2")) {
          this.check_course = false;
          this.check_fit = true;
        } else {
          this.check_fit = false;
        }
      } else if (val === "3") {
        if (this.ruleForm.used_limit.includes("3")) {
          this.check_goods = true;
        } else {
          this.check_goods = false;
        }
      } else if (val === "1") {
        if (this.ruleForm.used_limit.includes("1")) {
          this.check_course = true;
          this.check_fit = false;
        } else {
          this.check_course = false;
        }
      } else if (val === "2") {
        if (this.ruleForm.used_limit.includes("2")) {
          this.check_course = false;
          this.check_fit = true;
        } else {
          this.check_fit = false;
        }
      }
    },
    close() {
      this.check_type = {
        course_type: true,
        course_year: true,
        course_level: true,
        lesson_type: true,
        course_attribute: true,
        student_type: true,
        course_name: true,
        article_name: true,
        assign_time_type: true
      };
      this.creat_flag_visible = false;
    },
    really() {
      if (this.ruleForm.amount <= 0) {
        this.$message.error("发行总量必须大于0!");
        return;
      }
      if (this.ruleForm.quota <= 0) {
        this.$message.error("优惠券面额必须大于0!");
        return;
      }
      if (this.ruleForm.limit_count <= 0) {
        this.$message.error("限发数量必须大于0!");
        return;
      }
      const data = JSON.parse(JSON.stringify(this.ruleForm));
      if (!this.checked) {
        data.quota_upper = 0;
      } else {
        data.quota_upper = this.ruleForm.quota_upper * 100;
      }
      if (this.radio === 0) {
        this.ruleForm.used_threshold = 0;
      } else {
        data.used_threshold = this.ruleForm.used_threshold * 100;
      }
      data.quota = data.coupon_type === 1 ? data.quota * 100 : data.quota;
      data.course_name =
        data.course_name.length > 0 ? data.course_name.split(",") : [];
      data.article_name =
        data.article_name.length > 0 ? data.article_name.split(",") : [];
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          couponApi
            .CreateCoupon(data)
            .then((res) => {
              this.loading = false;
              if (res.data.code === 0) {
                this.$message.success("优惠券添加成功!");
                this.creat_flag_visible = false;
                // 初始化
                this.check_type = {
                  course_type: true,
                  course_year: true,
                  course_level: true,
                  lesson_type: true,
                  course_attribute: true,
                  student_type: true,
                  course_name: true,
                  article_name: true,
                  assign_time_type: true
                };
                this.$emit("init");
              } else {
                this.$message.error(res.data.message);
              }
            })
            .catch(() => {
              this.loading = false;
              this.$message.error("优惠券添加失败!");
            });
        }
      });
    },
    getStudentStyleList(data) {
      studentInforApi.getStudentType(data).then((res) => {
        if (+res.status === 200 && +res.data.code === 0) {
          this.student_category = [
            { id: "unspecified", name: "未分类" },
            ...res.data.data
          ];
        }
      });
    },
    goodsSelect() {
      if (!this.check_type.article_name) {
        this.goods_visible = true;
      }
    },
    courseSelect() {
      if (!this.check_type.course_name) {
        this.choose_course_visible = true;
      }
    },
    changeChoose() {
      // 不限
      if (this.check_type.course_type) this.ruleForm.course_type = [];
      if (this.check_type.course_year) this.ruleForm.course_year = [];
      if (this.check_type.course_level) this.ruleForm.course_level = [];
      if (this.check_type.lesson_type) this.ruleForm.lesson_type = [];
      if (this.check_type.course_attribute) this.ruleForm.course_attribute = [];
      if (this.check_type.student_type) this.ruleForm.student_type = [];
      if (this.check_type.course_name) {
        this.ruleForm.course_name = "";
        this.ruleForm.course_ids = [];
        this.course_check_arr = [];
      }
      if (this.check_type.article_name) {
        this.ruleForm.article_name = "";
        this.ruleForm.article_ids = [];
        this.goods_check_arr = [];
      }
      if (this.check_type.assign_time_type) {
        this.ruleForm.assign_time_start = "";
        this.ruleForm.assign_time_end = "";
        this.assignTime = [];
      }
    }
  },
  components: { chooseWarehouseGoods }
};
</script>

<style lang="less">
.creat_coupon_dialog {
  .el-dialog__body {
    padding: 16px 20px;
    .el-select {
      line-height: 32px !important;
    }
  }
  .dialog_box {
    margin-bottom: 16px;
    .dialog_title {
      width: 100%;
      height: 48px;
      background: #f5f8fc;
      border: 1px solid #2d80ed;
      border-radius: 4px 4px 0 0;
      padding-left: 16px;
      line-height: 48px;
      box-sizing: border-box;
      font-size: 14px;
      color: #1f2d3d;
    }
  }
  .dialog_info {
    border: 1px solid #e0e6ed;
    border-radius: 0 0 4px 4px;
    width: 100%;
    padding: 8px 24px 16px;
    box-sizing: border-box;
    .el-form-item {
      .el-input {
        width: 400px;
      }
      .min_input {
        width: 100px;
      }
    }
  }

  .margin_check {
    margin-left: 10px;
  }

  .border--active {
    position: relative;
    &::after {
      content: "";
      position: absolute;
      width: 400px;
      height: 32px;
      left: -2px;
      top: 2px;
      border: 2px solid #ebf4ff;
      border-radius: 6px;
      z-index: 10;
    }
    ::v-deep .el-input__inner {
      border-color: @base-color;
    }
  }
  .el-input__icon.el-icon-date::before {
    margin-top: -1px !important;
  }
  .endText {
    width: 60px;
    height: 32px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    user-select: none;
    color: #ffffff;
    position: absolute;
    top: 5px;
    right: -5px;
  }
  .text_margin {
    margin-left: 20px;
    margin-right: 10px;
  }
  .el-switch__core {
    width: 30px !important;
    height: 16px;
  }
  .el-switch__core::after {
    width: 14px;
    height: 14px;
    margin-top: -1px;
  }
  .el-switch.is-checked .el-switch__core::after {
    margin-left: -15px;
  }
}
</style>
