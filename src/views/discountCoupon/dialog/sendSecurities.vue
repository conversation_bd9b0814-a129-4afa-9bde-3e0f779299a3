<template>
  <div class="sendSecuritiesBox">
    <el-dialog
      title="发劵"
      :visible="securitiesDialog"
      v-if="securitiesDialog"
      width="800px"
      :before-close="cancel"
    >
      <section>
        <el-form
          ref="form"
          :model="form"
          label-width="80px"
          label-position="right"
        >
          <el-form-item label="发放方式">
            <div>
              <div>
                <el-checkbox
                  v-model="form.studentState"
                  @change="stateChange('studentState')"
                  >指定学员</el-checkbox
                >
                <el-popover
                  placement="top-start"
                  title
                  width="200"
                  trigger="hover"
                  content="单次最多允许给200名学员发放优惠券。"
                >
                  <img
                    slot="reference"
                    class="imgBox m-l-12"
                    src="../../../assets/图片/icon_info.png"
                  />
                </el-popover>
                <el-button
                  type="primary"
                  class="tg-button--primary m-l-20"
                  v-if="form.studentState"
                  @click="choose"
                  >选择学员</el-button
                >
                <span v-if="form.studentState"
                  >（已选{{
                    form.studentList.length > 0 ? form.studentList.length : "0"
                  }}人）</span
                >
              </div>
              <div>
                <el-checkbox
                  v-model="form.intentionStudentState"
                  @change="stateChange('intentionStudentState')"
                  >指定意向客户</el-checkbox
                >
                <el-popover
                  placement="top-start"
                  title
                  width="200"
                  trigger="hover"
                  content="单次最多允许给200名学员发放优惠券。"
                >
                  <img
                    slot="reference"
                    class="imgBox m-l-12"
                    src="../../../assets/图片/icon_info.png"
                  />
                </el-popover>
                <el-button
                  type="primary"
                  class="tg-button--primary m-l-20"
                  @click="choose"
                  v-if="form.intentionStudentState"
                  >选择意向客户</el-button
                >
                <span v-if="form.intentionStudentState"
                  >（已选{{
                    form.studentList.length > 0 ? form.studentList.length : "0"
                  }}人）</span
                >
              </div>
            </div>
          </el-form-item>
          <el-form-item label="发放数量">
            <el-input-number
              v-model="form.assign_count"
              :min="1"
              :max="select_row.limit_count"
              :precision="0"
              @change="changeNum"
            ></el-input-number>
            <span class="m-l-12">张</span>
            <el-popover
              placement="top-start"
              title
              width="200"
              trigger="hover"
              content="发放数量不能大于优惠券活动设置的限发数量。"
            >
              <img
                slot="reference"
                class="imgBox m-l-12"
                src="../../../assets/图片/icon_info.png"
              />
            </el-popover>
            <span
              >（余量：{{ saveNum }}张，限发数量：{{
                select_row.limit_count
              }}张）</span
            >
          </el-form-item>
          <div class="m-top-16">
            <el-form-item label="备注">
              <el-input
                type="textarea"
                v-model="form.remark"
                resize="none"
                class="textareaVessel"
                placeholder="请输入备注信息"
              ></el-input>
            </el-form-item>
          </div>
        </el-form>
      </section>
      <span slot="footer" class="dialog-footer">
        <el-button class="tg-button--plain" @click="cancel">关闭</el-button>
        <el-button
          type="primary"
          class="tg-button--primary"
          @click="confirm"
          :loading="loading"
          >确定</el-button
        >
      </span>
    </el-dialog>
    <choose-student
      v-if="add_student_visible"
      :has_modal="false"
      :check_arr.sync="form.studentList"
      :department_id="schoolId"
      :select_row="select_row"
      @close="add_student_visible = false"
    ></choose-student>
    <choose-customer
      v-if="add_customer_visible"
      :has_modal="false"
      :check_arr.sync="form.studentList"
      :department_id="schoolId"
      :select_row="select_row"
      @close="add_customer_visible = false"
    ></choose-customer>
  </div>
</template>

<script>
import chooseStudent from "./chooseStudent.vue";
import chooseCustomer from "./chooseCustomer.vue";
import couponApi from "@/api/discountCoupon";
export default {
  props: {
    schoolId: {},
    select_row: {}
  },
  components: {
    chooseStudent,
    chooseCustomer
  },
  data() {
    return {
      securitiesDialog: false,
      add_student_visible: false,
      add_customer_visible: false,
      info: {
        overNum: 3,
        saveNum: 1000
      },
      form: {
        studentState: false,
        intentionStudentState: false,
        assign_count: 1,
        remark: "",
        studentList: []
      },
      code_no: "",
      num: 0,
      loading: false,
      saveNum: 0
    };
  },
  methods: {
    // 打开发劵弹窗
    openDialog() {
      this.securitiesDialog = true;
      this.form = {
        studentState: false,
        intentionStudentState: false,
        assign_count: 1,
        remark: "",
        studentList: []
      };
      setTimeout(() => {
        couponApi
          .GetCouponNumber({
            coupon_id: this.select_row.id,
            department_id: this.schoolId
          })
          .then((res) => {
            this.saveNum = res.data.data.num;
          });
      }, 500);
    },
    // 关闭弹窗
    cancel() {
      this.form = {
        studentState: false,
        intentionStudentState: false,
        num: undefined,
        memo: ""
      };
      this.securitiesDialog = false;
    },
    // 单选
    stateChange(type) {
      if (type === "studentState") {
        this.form.intentionStudentState = false;
        // 清除已选择的意向学员
      } else {
        // 清除已选择的学员
        this.form.studentState = false;
      }
      this.form.studentList = [];
    },
    // 发放张数限制
    changeNum(num) {
      this.$nextTick(() => {
        if (num < this.select_row.limit_count + 1) {
          if (num * this.form.studentList.length > this.saveNum) {
            this.form.assign_count = 1;
            this.$message.warning("发放数量超过优惠劵总量");
          }
        } else {
          this.$set(this.form, "assign_count", 1);
          this.$message.warning("发放数不允许大于限发数量!");
        }
      });
    },
    // 选择学员
    choose() {
      if (this.form.intentionStudentState) {
        this.add_customer_visible = true;
      } else {
        this.add_student_visible = true;
      }
    },
    // 发放学员优惠券
    async assginStudentCoupon() {
      this.loading = true;
      this.num = 0;

      while (this.num < this.form.studentList.length) {
        const data = {
          assign_count: this.form.assign_count,
          coupon_id: this.select_row.id,
          department_id: this.schoolId,
          mobile: this.form.studentList[this.num].student_mobile,
          remark: this.form.remark,
          student_id: this.form.studentList[this.num].student_id,
          student_name: this.form.studentList[this.num].student_name,
          student_number: this.form.studentList[this.num].student_number
        };

        try {
          const res = await couponApi.AssginStudentCoupon(data);
          if (res.data.code === 0) {
            // 成功处理下一条
            this.num++;
          } else if (res.data.code === 1) {
            if (res.data.data !== 1) {
              // 单条发放失败但可继续处理下一条
              this.$message.error(res.data.message);
              this.num++;
            } else {
              // 严重错误不继续处理
              this.$message.error(res.data.message);
              this.unlockCoupon(false);
              return;
            }
          }
        } catch (error) {
          console.error("发放学员优惠券出错:", error);
          this.unlockCoupon(false);
          return;
        }
      }

      // 全部处理完成
      this.unlockCoupon();
    },

    // 发放意向客户优惠券
    async assginCustomerCoupon() {
      this.loading = true;
      this.num = 0;

      while (this.num < this.form.studentList.length) {
        const data = {
          assign_count: this.form.assign_count,
          coupon_id: this.select_row.id,
          customer_id: this.form.studentList[this.num].customer_id,
          department_id: this.schoolId,
          mobile: this.form.studentList[this.num].student_mobile,
          name: this.form.studentList[this.num].student_name,
          remark: this.form.remark
        };

        try {
          const res = await couponApi.AssginCustomerCoupon(data);
          if (res.data.code === 0) {
            // 成功处理下一条
            this.num++;
          } else if (res.data.code === 1) {
            if (res.data.data !== 1) {
              // 单条发放失败但可继续处理下一条
              this.$message.error(res.data.message);
              this.num++;
            } else {
              // 严重错误不继续处理
              this.$message.error(res.data.message);
              this.unlockCoupon(false);
              return;
            }
          }
        } catch (error) {
          console.error("发放意向客户优惠券出错:", error);
          this.unlockCoupon(false);
          return;
        }
      }

      // 全部处理完成
      this.unlockCoupon();
    },
    // 确认发劵
    confirm() {
      if (this.form.studentList.length > 0) {
        if (
          this.form.assign_count * this.form.studentList.length >
          this.saveNum
        ) {
          this.$message.warning("发放数量超过优惠劵总量");
        } else {
          if (this.form.intentionStudentState) {
            this.assginCustomerCoupon();
          } else {
            this.assginStudentCoupon();
          }
          // couponApi
          //   .CouponLock({
          //     coupon_id: this.select_row.id,
          //     department_id: this.schoolId
          //   })
          //   .then((res) => {
          //     if (res.data.code === 0) {
          //       this.code_no = res.data.data.code_no;
          //       if (this.form.intentionStudentState) {
          //         this.assginCustomerCoupon();
          //       } else {
          //         this.assginStudentCoupon();
          //       }
          //     } else {
          //       this.$message.error(res.data.message);
          //     }
          //   });
        }
      } else {
        this.$message.error("请指定学员！");
      }
    },
    unlockCoupon(bool) {
      this.loading = false;
      this.securitiesDialog = false;
      this.$emit("init");
      console.log(bool);
      // if (bool !== false) {
      //   this.$message.success("优惠券发放完毕！");
      // }
      // couponApi
      //   .CouponUnlock({
      //     coupon_id: this.select_row.id,
      //     department_id: this.schoolId,
      //     code_no: this.code_no
      //   })
      //   .then((res) => {
      //     this.num = 0;
      //     this.loading = false;
      //     this.securitiesDialog = false;
      //     this.$emit("init");
      //     console.log(bool);
      //     if (res.data.code === 0 && bool !== false) {
      //       this.$message.success("优惠券发放完毕！");
      //     }
      //   })
      //   .catch(() => {
      //     this.num = 0;
      //     this.$message.success("优惠券释放锁失败！");
      //   });
    }
  }
};
</script>
<style scoped lang="less">
.sendSecuritiesBox {
  /deep/.el-dialog__body {
    padding: 20px !important;
  }
  ::v-deep .el-input {
    width: 180px;
  }
  ::v-deep .el-input__inner {
    height: 40px;
  }
  .imgBox {
    width: 10px;
    height: 10px;
  }
  .m-l-12 {
    margin-left: 12px;
  }
  .m-l-20 {
    margin-left: 20px;
  }
  .m-top-16 {
    margin-top: 16px;
  }
  .textareaVessel {
    ::v-deep .el-textarea__inner {
      min-height: 96px !important;
    }
  }
}
</style>
