<template>
  <div class="campusCouponList container">
    <tg-search
      :searchTitle.sync="searchTitle"
      :form.sync="search"
      @reset="reset"
      @search="searchVal"
      :showNum="3"
      class="tg-box--margin"
    ></tg-search>
    <div class="tg_tag" v-if="schoolName">
      <el-tag type="success" @click="openSchool"
        >当前选中校区：{{ schoolName }}</el-tag
      >
    </div>
    <div class="tg-table__box">
      <div class="tg-box--border"></div>
      <el-table
        ref="table"
        :data="list"
        tooltip-effect="dark"
        class="tg-table"
        :cell-style="{ borderRightColor: '#e0e6ed75' }"
        :header-cell-style="{ borderRightColor: '#e0e6ed75' }"
        @sort-change="sortChange"
        border
      >
        <el-table-column
          prop="name"
          label="优惠券名称"
          sortable="custom"
          width="160"
        >
          <template slot-scope="scope">
            <div class="copy_name">
              <el-button
                type="text"
                size="small"
                v-has="{ m: 'campus', o: 'info' }"
                :class="{ 'tg-text--black': !can_info }"
                class="tg-text--blue"
                @click="openDialog(3, scope.row)"
                >{{ scope.row.name }}</el-button
              >
              <div v-copy="scope.row.name"></div>
            </div>

            <!-- <el-button
              type="text"
              size="small"
              v-has="{ m: 'campus', o: 'info' }"
              :class="{ 'tg-text--black': !can_info }"
              class="tg-text--blue tg-span__divide-line"
              @click="openDialog(3, scope.row)"
              >{{ scope.row.name }}</el-button
            > -->
          </template>
        </el-table-column>
        <el-table-column prop="quota" sortable="custom" label="面额">
          <template slot-scope="scope">
            {{
              scope.row.coupon_type == 1
                ? (scope.row.quota / 100).toFixed(2)
                : `打${(scope.row.quota / 10).toFixed(1)}折`
            }}
          </template>
        </el-table-column>
        <el-table-column prop="coupon_type" sortable="custom" label="类型">
          <template slot-scope="scope">
            {{ scope.row.coupon_type == 1 ? "满减券" : "折扣券" }}
          </template>
        </el-table-column>
        <el-table-column
          prop="used_threshold"
          label="使用门槛"
          sortable="custom"
          width="110"
        >
          <template slot-scope="scope">
            {{
              scope.row.used_threshold == 0
                ? "无门槛"
                : `订单满${scope.row.used_threshold / 100}可用`
            }}
          </template>
        </el-table-column>
        <el-table-column
          label="发行张数"
          prop="offer_count"
          width="110"
          sortable="custom"
        >
          <template slot-scope="scope">
            <!-- <el-button
              type="text"
              size="small"
              :class="{ 'tg-text--black': !can_see }"
              v-has="{ m: 'campus', o: 'check' }"
              class="tg-text--blue tg-span__divide-line"
              @click="openDialog(4, scope.row)"
              >{{ scope.row.offer_count }}</el-button
            > -->
            <span>{{ scope.row.offer_count }}</span>
          </template>
        </el-table-column>
        <el-table-column
          width="130"
          label="已发放"
          prop="assign_count"
          sortable="custom"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              :class="{ 'tg-text--black': !can_see }"
              v-has="{ m: 'campus', o: 'check' }"
              class="tg-text--blue tg-span__divide-line"
              @click="openDialog(5, scope.row)"
              >{{ scope.row.assign_count }}</el-button
            >
          </template>
        </el-table-column>
        <el-table-column
          prop="used_count"
          sortable="custom"
          width="130"
          label="已使用"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              :class="{ 'tg-text--black': !can_see }"
              v-has="{ m: 'campus', o: 'check' }"
              class="tg-text--blue tg-span__divide-line"
              @click="openDialog(6, scope.row)"
              >{{ scope.row.used_count }}</el-button
            >
          </template>
        </el-table-column>
        <el-table-column
          label="有效时间"
          prop="valid_time_start"
          sortable="custom"
          width="220"
        >
          <template slot-scope="scope">
            {{ scope.row.valid_time_start | getDate }}至{{
              scope.row.valid_time_end | getDate
            }}
          </template>
        </el-table-column>
        <el-table-column
          label="发券时间"
          prop="assign_time_start"
          sortable="custom"
          width="220"
        >
          <template slot-scope="scope">
            <span
              v-if="
                scope.row.assign_time_start_str === '' &&
                scope.row.assign_time_end_str === ''
              "
              >不限</span
            >
            <span v-else>
              {{ scope.row.assign_time_start_str }}至{{
                scope.row.assign_time_end_str
              }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="发放方式">
          <template slot-scope="scope">
            {{
              scope.row.assign_method == 1
                ? "不限"
                : scope.row.assign_method == 2
                ? "小程序发放"
                : "手动发放"
            }}
          </template>
        </el-table-column>
        <el-table-column label="允许叠加">
          <template slot-scope="scope">
            <el-switch v-model="scope.row.is_overlay" disabled></el-switch>
          </template>
        </el-table-column>
        <el-table-column label="状态">
          <template slot-scope="scope">
            {{
              scope.row.valid_status == 1
                ? "进行中"
                : scope.row.valid_status == 2
                ? "已使用"
                : scope.row.valid_status == 3
                ? "已结束"
                : ""
            }}
          </template>
        </el-table-column>
        <el-table-column prop="name" width="110" label="停用/启用">
          <template slot-scope="scope">
            {{ scope.row.used_status ? "启用" : "停用" }}
          </template>
        </el-table-column>
        <el-table-column
          show-overflow-tooltip
          prop="description"
          min-width="120"
          label="说明"
        ></el-table-column>
        <el-table-column prop="name" label="操作" fixed="right" width="100">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              v-has="{ m: 'coupon_template', o: 'Issue_coupons' }"
              class="tg-text--blue tg-span__divide-line"
              v-if="scope.row.assign_status"
              @click="openDialog(1, scope.row)"
              >发券</el-button
            >
          </template>
        </el-table-column>
        <template slot="empty">
          <div style="margin-top: 15%">
            <TgLoading v-if="loading"></TgLoading>
            <div class="empty-container" v-else>暂无数据～</div>
          </div>
        </template>
      </el-table>
      <div class="tg-pagination">
        <span class="el-pagination__total">共 {{ total }} 条</span>
        <el-pagination
          background
          layout="sizes,prev,pager,next,jumper"
          :total="total"
          :page-size="pageSize"
          :current-page="page"
          @current-change="currentChange"
          @size-change="sizeChange"
          :page-sizes="[10, 20, 50, 100]"
        ></el-pagination>
      </div>
    </div>
    <issueDialog
      v-if="issue_flag_visible"
      :issue_flag_visible="issue_flag_visible"
      :select_row="select_row"
      :dialog_type="dialog_type"
      :department_id="schoolId"
      @close="issue_flag_visible = false"
    ></issueDialog>
    <sendSecurities
      ref="send_securities_dialog"
      :schoolId="schoolId"
      :select_row="select_row"
      @init="initCoupon"
    ></sendSecurities>
    <school-tree
      :flag.sync="school_tree_visible"
      :id.sync="schoolId"
      :name.sync="schoolName"
      :required="true"
      :has_modal="true"
      type="radio"
      @confirm="confirmSchool"
      :use_store_options="true"
    ></school-tree>
    <editCouponDialog
      ref="editCouponDialog"
      :data_list="data_type"
      :year="year"
      :course_level="searchTitle[6].selectOptions"
      :course_type="searchTitle[7].selectOptions"
      :course_genre_list="course_genre_list"
      :course_attribute_list="course_attribute_list"
      @init="initCoupon"
    ></editCouponDialog>
  </div>
</template>

<script>
import { getCourseConfigByType } from "@/api/courseManagement.js";
import issueDialog from "./dialog/issueDialog.vue";
import sendSecurities from "./dialog/sendSecurities.vue";
import schoolTree from "@/components/schoolTree/schoolTree"; // 校区弹框
import couponApi from "@/api/discountCoupon";
import editCouponDialog from "./dialog/editCoupon.vue";

const course_years = (function () {
  const currYear = new Date().getFullYear();
  const arr = [];
  for (let i = currYear - 5; i <= currYear + 5; i++) {
    arr.push({
      id: i,
      name: i
    });
  }
  arr.unshift({ name: "不限", id: "" });
  return arr;
})();
export default {
  data() {
    return {
      can_info: false,
      can_see: false,
      distributeDialog: false,
      hover_index: 0,
      categroy_index: 0,
      searchTitle: [
        { props: "name", label: "优惠券名称", type: "input", show: true },
        {
          props: "article_ids",
          label: "适用物品",
          type: "choose_goods",
          show: true,
          arr: []
        },
        {
          props: "course_ids",
          label: "适用课程",
          type: "choose_course",
          show: true
        },
        {
          props: "valid_status",
          label: "状态",
          type: "select",
          show: false,
          selectOptions: [
            { name: "不限", id: "" },
            { name: "进行中", id: 1 },
            { name: "未开启", id: 2 },
            { name: "已结束", id: 3 }
          ]
        },
        {
          props: "coupon_type",
          label: "优惠券类型",
          type: "select",
          show: false,
          selectOptions: [
            { name: "不限", id: "" },
            { name: "满减券", id: 1 },
            { name: "折扣券", id: 2 }
          ]
        },
        {
          props: "course_year",
          label: "年份",
          type: "select",
          show: false,
          selectOptions: course_years
        },
        {
          props: "course_level",
          label: "课程种类",
          type: "select",
          width: 400,
          show: false,
          placeholder: "请选择",
          selectOptions: []
        },
        {
          props: "course_type",
          label: "课程类型",
          type: "select",
          width: 400,
          show: false,
          placeholder: "请选择",
          selectOptions: []
        },
        {
          props: "course_attribute",
          label: "课程属性",
          type: "select",
          width: 400,
          show: false,
          placeholder: "请选择",
          selectOptions: []
        }
      ],
      search: {
        name: "",
        valid_status: 1,
        coupon_type: "",
        course_year: [],
        course_level: [],
        course_type: [],
        course_attribute: [],
        course_ids: [],
        article_ids: []
      },
      page: 1,
      pageSize: 10,
      total: 0,
      list: [],
      issue_flag_visible: false,
      creat_flag_visible: false,
      select_row: {},
      dialog_type: "issue",
      loading: false,
      deleteDialog: false,
      add_type_visible: false,
      coupon_type_name: "",
      coupon_type_id: "",
      delete_type: "",
      school_tree_visible: false,
      schoolId: "",
      idList: [],
      year: [],
      course_genre_list: [],
      editInfo: {},
      course_attribute_list: [],
      data_type: [],
      schoolName: ""
    };
  },

  computed: {
    ids() {
      return this.$store.getters.doneGetSchoolId.toString();
    },
    names() {
      return this.$store.getters.doneGetSchoolName.toString();
    }
  },
  watch: {
    ids: {
      handler() {
        this.init();
      },
      immediate: true
    }
  },
  created() {
    this.getSelect("course_level");
    this.getSelect("course_attribute");
    this.getSelect("course_type");
    this.getSelect("course_genre");
    if (this.$_has({ m: "campus", o: "info" })) {
      this.can_info = true;
    }
    if (this.$_has({ m: "campus", o: "check" })) {
      this.can_see = true;
    }
  },
  mounted() {
    // this.init();
    this.initCouponType();
  },
  methods: {
    init() {
      if (this.$route.query.departmentId) {
        this.school_tree_visible = false;
        const { departmentId, departmentName } = this.$route.query;
        this.schoolId = departmentId;
        this.schoolName = departmentName;
        this.confirmSchool();
      } else {
        this.idList = this.ids.split(",");
        if (this.idList.length > 1) {
          this.list = [];
          this.total = 0;
          this.schoolId = "";
          this.schoolName = "";
          this.school_tree_visible = true;
        } else {
          this.schoolId = this.idList.join("");
          this.schoolName = this.names;
          this.initCoupon();
        }
      }
    },
    openSchool() {
      // this.schoolId = "";
      // this.schoolName = "";
      this.school_tree_visible = true;
    },
    confirmSchool() {
      if (this.schoolId) {
        this.school_tree_visible = false;
        this.page = 1;
        this.pageSize = 10;
        this.initCoupon();
      } else {
        this.$message.info("请勾选校区");
      }
    },
    initCouponType() {
      // 优惠券分类接口
      couponApi.GetCouponTypeList().then((res) => {
        this.data_type = res.data.data ?? [];
      });
    },
    initCoupon() {
      // 优惠券列表接口
      const data = {
        category_id: this.category_id,
        school_id: this.schoolId,
        page: this.page,
        page_size: this.pageSize,
        name: this.search.name,
        valid_status: this.search.valid_status,
        coupon_type: this.search.coupon_type,
        sort: this.search.sort,
        course_year: this.search.course_year || undefined,
        course_level: this.search.course_level || undefined,
        course_type: this.search.course_type || undefined,
        course_attribute: this.search.course_attribute || undefined,
        course_ids: this.search.course_ids,
        article_ids: this.search.article_ids
      };
      this.loading = true;
      this.list = [];
      couponApi.getCouponDepartmentList(data).then((res) => {
        this.list = res.data.data.results ?? [];
        this.total = res.data.data.count;
        this.loading = false;
      });
    },
    sortChange(val) {
      let { prop, order } = val;
      let _oreder = "";
      if (order === "ascending") {
        _oreder = "asc";
      } else if (order === "descending") {
        _oreder = "desc";
      }
      if (prop.startsWith("student_base.")) {
        prop = prop.replace("student_base.", "");
      }
      this.search.sort = `${prop} ${_oreder}`;
      this.searchVal();
    },
    searchVal() {
      // 搜索
      this.page = 1;
      this.initCoupon();
    },
    reset() {
      // 重置
      this.search = {
        name: "",
        valid_status: 1,
        course_year: "",
        course_level: "",
        course_type: "",
        coupon_type: "",
        course_attribute: "",
        course_ids: [],
        article_ids: []
      };
      this.searchTitle[1].arr = [];
      this.pageSize = 10;
      this.searchVal();
    },
    openDialog(val, row) {
      this.select_row = row;
      if (val === 1) {
        this.$refs.send_securities_dialog.openDialog();
      } else if (val === 3) {
        couponApi.GetCouponInfo({ id: row.id }).then((res) => {
          this.$refs.editCouponDialog.openDialog(res.data.data, "look");
        });
      } else if (val === 4) {
        this.dialog_type = "1";
        this.issue_flag_visible = true;
      } else if (val === 5) {
        this.dialog_type = "2";
        this.issue_flag_visible = true;
      } else if (val === 6) {
        this.dialog_type = "3";
        this.issue_flag_visible = true;
      }
    },
    currentChange(val) {
      this.page = val;
      this.initCoupon();
    },
    sizeChange(val) {
      this.page = 1;
      this.pageSize = val;
      this.initCoupon();
    },
    async getSelect(str) {
      const { data } = await getCourseConfigByType({}, str);
      if (data) {
        const arr = [];
        data.map((item) => {
          arr.push({
            id: str === "course_type" ? item.config_value : item.config_name,
            name: item.config_name
          });
        });
        if (str === "course_type") {
          this.searchTitle[7].selectOptions = arr;
          this.course_type_list = arr;
          this.searchTitle[7].selectOptions.unshift({ name: "不限", id: "" });
        }
        if (str === "course_level") {
          this.searchTitle[6].selectOptions = arr;
          this.searchTitle[6].selectOptions.unshift({ name: "不限", id: "" });
        }
        // if (str === "course_class_type") {
        //   this.searchTitle[5].selectOptions = arr;
        // }
        if (str === "course_attribute") {
          this.course_attribute_list = data;
          // this.searchTitle[7].selectOptions = data;
          for (const key in data) {
            this.searchTitle[8].selectOptions.push({
              id: data[key].config_value,
              name: data[key].config_name
            });
          }
          this.searchTitle[8].selectOptions.unshift({ name: "不限", id: "" });
        }
        if (str === "course_genre") {
          this.course_genre_list = data;
        }
      }
    }
  },
  components: {
    issueDialog,
    sendSecurities,
    schoolTree,
    editCouponDialog
  }
};
</script>

<style lang="less" scoped>
.campusCouponList {
  ::v-deep .el-table td:last-child {
    border-right-color: transparent !important;
  }
  ::v-deep .el-table__header th:last-child {
    border-right-color: transparent !important;
  }
  .tg_tag {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    cursor: pointer;
  }
  .dialog_content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  .tg-table__box {
    margin: 6px;
  }
  ::v-deep .el-table__body-wrapper {
    height: calc(100% - 46px);
    .empty-container {
      position: absolute;
      top: 50%;
      left: 50%;
    }
    .loading-container {
      position: absolute;
      top: 30%;
      left: 1%;
      background: transparent;
      .box {
        height: 100%;
      }
    }
  }
  .el-tag.el-tag--success {
    background-color: #cee3fe;
    border-color: #a4c9f8;
    color: #2d80ed;
  }
}
</style>
