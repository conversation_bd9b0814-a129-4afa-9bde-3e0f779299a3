<template>
  <div>
    <el-dialog
      title="模版导入"
      :visible.sync="dialogVisible"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="handleClose"
    >
      <el-row :gutter="10">
        <el-col :span="12">
          <el-button
            type="primary"
            size="small"
            icon="el-icon-download"
            @click="downloadTemplate('shichangzhuanyuan')"
            style="width: 100%"
          >
            市场专员模版
          </el-button>
        </el-col>
        <el-col :span="12">
          <el-button
            type="primary"
            size="small"
            icon="el-icon-download"
            @click="downloadTemplate('kechengguwen')"
            style="width: 100%"
          >
            课程顾问模版
          </el-button>
        </el-col>
      </el-row>
      <el-row :gutter="10" style="margin-top: 10px">
        <el-col :span="12">
          <el-button
            type="primary"
            size="small"
            icon="el-icon-download"
            @click="downloadTemplate('jiaowu')"
            style="width: 100%"
          >
            教务模版
          </el-button>
        </el-col>
        <el-col :span="12">
          <el-button
            type="primary"
            size="small"
            icon="el-icon-download"
            @click="downloadTemplate('xiaozhang')"
            style="width: 100%"
          >
            分校长模版
          </el-button>
        </el-col>
      </el-row>
      <el-form ref="importForm" :model="form" :rules="rules" label-width="80px">
        <!-- 选择月份 -->
        <el-form-item label="选择月份" prop="selectedMonth" required>
          <div class="month-selector">
            <span>
              <el-select
                v-model="form.selectedYear"
                placeholder="请选择年份"
                class="year-selector"
                style="width: 100px; margin-right: 10px"
              >
                <el-option
                  v-for="year in yearOptions"
                  :key="year"
                  :label="year + '年'"
                  :value="year"
                />
              </el-select>
            </span>
            <el-select
              v-model="form.selectedMonth"
              placeholder="请选择月份"
              class="month-selector"
              style="width: 100%"
            >
              <el-option
                v-for="month in monthOptions"
                :key="month"
                :label="month + '月'"
                :value="month"
              />
            </el-select>
          </div>
        </el-form-item>

        <!-- 选择角色 -->
        <el-form-item label="选择角色" prop="target_type" required>
          <el-select v-model="form.target_type" placeholder="请选择角色">
            <el-option label="市场专员" value="responsible" />
            <el-option label="课程顾问" value="advisor" />
            <el-option label="教务" value="educator" />
            <el-option label="分校长" value="school_master" />
          </el-select>
        </el-form-item>
        <!-- 上传文件 -->
        <el-form-item label="上传文件" prop="uploadFile" required>
          <el-upload
            ref="upload"
            :file-list="fileList"
            :auto-upload="false"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            :limit="1"
            :on-exceed="handleExceed"
            accept=".xlsx,.xls"
            action=""
            class="upload-area"
          >
            <el-button
              slot="trigger"
              size="primary"
              type="primary"
              style="width: 100%"
              >选择文件</el-button
            >
            <div slot="tip" class="el-upload__tip">
              只允许上传xlsx/xls格式的文件，限制文件大小为5MB
            </div>
          </el-upload>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="success"
          @click="handleImport"
          :loading="importLoading"
          :disabled="!form.uploadFile"
        >
          <i class="el-icon-upload" style="margin-right: 5px"></i>
          确认导入
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import targetValueApi from "@/api/targetValueKanBan";
export default {
  name: "TemplateImportDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    target_type: {
      type: String,
      default: "shichangzhuanyuan"
    }
  },
  data() {
    return {
      dialogVisible: false,
      downloadLoading: false,
      importLoading: false,

      // 表单数据
      form: {
        department_name: "",
        selectedYear: new Date().getFullYear(),
        selectedMonth: new Date().getMonth() + 1,
        uploadFile: null,
        target_type: ""
      },

      school_tree_visible: false,
      school_flag: false,

      // 年份选项
      yearOptions: [],

      // 月份选项
      monthOptions: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],

      // 文件列表
      fileList: [],

      // 验证规则
      rules: {
        target_type: [
          { required: true, message: "请选择角色", trigger: "change" }
        ],
        selectedMonth: [
          { required: true, message: "请选择月份", trigger: "change" }
        ],
        uploadFile: [
          { required: true, message: "请选择要上传的文件", trigger: "change" }
        ]
      }
    };
  },

  watch: {
    visible: {
      handler(newVal) {
        this.dialogVisible = newVal;
      },
      immediate: true
    }
  },

  created() {
    this.initYearOptions();
  },

  methods: {
    // 初始化年份选项
    initYearOptions() {
      const currentYear = new Date().getFullYear();
      this.yearOptions = [currentYear - 1, currentYear, currentYear + 1];
    },

    // 下载模版
    downloadTemplate(name) {
      // 验证必填项
      //   if (this.form.department_id.length === 0) {
      //     this.$message.error("请先选择校区");
      //     return;
      //   }

      this.downloadLoading = true;

      // 这里应该调用实际的下载API
      // 例如：this.downloadTemplateFile()
      const downloadElement = document.createElement("a");
      downloadElement.href = `./${name}.xlsx?v=` + window.__APP_VERSION__;
      downloadElement.download = `动态看板指标模版.xlsx`; // 下载后文件名
      document.body.appendChild(downloadElement);
      downloadElement.click(); // 点击下载
      document.body.removeChild(downloadElement);
      this.$message.success("模版下载成功");
      // 模拟下载过程
      this.downloadLoading = false;
    },

    // 文件选择变化
    handleFileChange(file, fileList) {
      this.fileList = fileList;
      this.form.uploadFile = file.raw;

      // 验证文件类型
      const allowedTypes = [
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "application/vnd.ms-excel"
      ];

      if (!allowedTypes.includes(file.raw.type)) {
        this.$message.error("只能上传 Excel 文件！");
        this.handleFileRemove();
        return;
      }

      // 验证文件大小 (5MB)
      if (file.raw.size > 5 * 1024 * 1024) {
        this.$message.error("文件大小不能超过 5MB！");
        this.handleFileRemove();
        return;
      }

      // 触发表单验证
      this.$nextTick(() => {
        this.$refs.importForm.validateField("uploadFile");
      });
    },

    // 文件数量超出限制
    handleExceed(files, fileList) {
      this.$message.warning("只能上传一个文件，请先删除已上传的文件再重新选择");
    },

    // 移除文件
    handleFileRemove() {
      this.fileList = [];
      this.form.uploadFile = null;

      // 触发表单验证
      this.$nextTick(() => {
        this.$refs.importForm.validateField("uploadFile");
      });
    },

    // 确认导入
    handleImport() {
      this.$refs.importForm.validate((valid) => {
        if (!valid) {
          return;
        }

        if (!this.form.uploadFile) {
          this.$message.error("请选择要上传的文件");
          return;
        }

        this.importLoading = true;

        // 这里应该调用实际的导入API
        console.log("导入参数:", {
          department_name: this.form.department_name,
          year: this.form.selectedYear,
          month: this.form.selectedMonth,
          file: this.form.uploadFile
        });
        const formData = new FormData();
        console.log(this.form.uploadFile);
        formData.append("file", this.form.uploadFile);
        formData.append("department_name", this.form.department_name);
        formData.append("year", this.form.selectedYear);
        formData.append("target_type", this.form.target_type);
        let param;
        if (this.form.selectedMonth >= 10) {
          param = this.form.selectedYear + "-" + this.form.selectedMonth;
        } else {
          param = this.form.selectedYear + "-0" + this.form.selectedMonth;
        }
        formData.append("month", param);
        targetValueApi.importIndicator(formData).then((res) => {
          console.log(res);
          if (res.data.code === 0) {
            // 通知父组件导入完成
            this.importLoading = false;
            this.$message.success("导入成功！");
            this.$emit("import-success");
            this.handleClose();
          } else {
            this.importLoading = false;
            this.$message.error(res.data.message);
          }
        });
      });
    },

    // 关闭对话框
    handleClose() {
      this.dialogVisible = false;
      this.$emit("update:visible", false);

      // 重置表单
      this.$refs.importForm.resetFields();
      this.handleFileRemove();
    }
  }
};
</script>

<style lang="less" scoped>
.school-checkbox-group {
  //   border: 1px solid #dcdfe6;
  //   border-radius: 4px;
  //   padding: 12px;
  //   background-color: #fafafa;

  .school-checkbox {
    display: block;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.month-selector {
  display: flex;
  align-items: center;
  span {
    ::v-deep .el-input {
      width: 120px;
    }
  }
}

.upload-area {
  width: 100%;

  ::v-deep .el-upload {
    width: 100%;
  }

  ::v-deep .el-upload__tip {
    margin-top: 8px;
    color: #909399;
    font-size: 12px;
  }
}

.dialog-footer {
  text-align: right;

  .el-button {
    margin-left: 10px;
  }
}

// 按钮样式优化
::v-deep .el-button--success {
  background-color: #28a745;
  border-color: #28a745;

  &:hover {
    background-color: #218838;
    border-color: #1e7e34;
  }

  &:focus {
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
  }
}
::v-deep .year-selector {
  .el-input {
    width: 100px !important;
  }
}
::v-deep .month-selector {
  .el-input {
    width: 100% !important;
  }
}
</style>
