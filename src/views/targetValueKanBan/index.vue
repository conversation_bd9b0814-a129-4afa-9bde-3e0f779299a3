<template>
  <div class="container">
    <!-- 筛选条件 -->
    <div class="tg-search tg-box--margin">
      <el-form
        ref="form"
        :model="form"
        :inline="true"
        @submit.native.prevent
        class="filter-form"
      >
        <el-form-item label="选择校区">
          <el-input
            placeholder="请选择校区"
            readonly
            show-word-limit
            :validate-event="false"
            @click.native="school_tree_visible = true"
            v-model="form.department_name"
            class="tg-select tg-select--dialog"
            @mouseenter.native="school_flag = true"
            @mouseleave.native="school_flag = false"
          >
            <!-- <i slot="suffix" class="el-input__icon el-icon-arrow-down"></i> -->
            <img
              slot="suffix"
              :src="
                !school_flag
                  ? require('../../assets/图片/icon_more.png')
                  : require('../../assets/图片/icon_more_ac.png')
              "
              alt
              class="btn__img--dotted"
            />
          </el-input>
          <school-tree
            :flag.sync="school_tree_visible"
            v-if="school_tree_visible"
            :id.sync="form.department_id"
            :name.sync="form.department_name"
            :type="'radio'"
            :use_store_options="true"
          ></school-tree>
        </el-form-item>
        <el-form-item
          label="选择员工"
          v-if="form.target_type !== 'school_master'"
        >
          <course-staff
            :check_id.sync="form.employee_id"
            :check_name.sync="form.employee_name"
            :is_leave="true"
            :has_modal="true"
            staff_placeholder="请选择员工"
          ></course-staff>
        </el-form-item>
        <el-form-item label="选择月份">
          <el-date-picker
            v-model="form.date_range"
            type="daterange"
            align="right"
            unlink-panels
            value-format="yyyy-MM-dd"
            :clearable="false"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            prefix-icon
            popper-class="tg-date-picker tg-date--range"
            :picker-options="pickerOptions"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="类型">
          <el-select v-model="form.target_type" placeholder="请选择">
            <el-option
              v-for="item in type_list"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <div style="display: flex; gap: 10px">
            <el-button
              type="primary"
              @click="handleQuery"
              class="tg-button--primary tg-button__icon"
            >
              <span style="display: flex; justify-content: center">
                <img
                  src="../../assets/图片/icon_search.png"
                  alt=""
                  class="tg-button__icon--large"
                />
                查询
              </span>
            </el-button>
            <el-button
              type="primary"
              @click="handleReset"
              class="tg-button--primary tg-button__icon"
            >
              <span style="display: flex; justify-content: center">
                <img
                  src="../../assets/图片/icon_reset.png"
                  alt=""
                  class="tg-button__icon--large"
                />
                重置
              </span>
            </el-button>
          </div>
        </el-form-item>

        <!-- 右侧按钮区域 -->
        <div class="button-group">
          <el-button
            v-has="{ m: 'targetValueKanBan', o: 'importIndicator' }"
            type="success"
            @click="handleExport"
            class="export-button"
            :loading="export_loading"
          >
            <i class="el-icon-download" style="margin-right: 5px"></i>
            模版导入
          </el-button>
        </div>
      </el-form>
    </div>

    <!-- 表格选项卡和操作按钮 -->
    <!-- <div class="table-header tg-box--margin"></div> -->

    <!-- 数据表格 -->
    <div class="tg-table__box tg-box--margin">
      <div class="table-header">
        <div class="tab-section">
          <el-tabs v-model="form.field" @tab-click="handleTabClick">
            <el-tab-pane
              :key="'stats-tab-' + index"
              :label="key"
              :name="value"
              v-for="(value, key, index) in target_type_list"
            ></el-tab-pane>
          </el-tabs>
        </div>
        <div
          class="action-buttons"
          v-has="{ m: 'targetValueKanBan', o: 'indicatorUpdate' }"
        >
          <el-button
            type="success"
            @click="handleSave"
            class="save-button"
            :loading="save_loading"
            :disabled="form.fields.length === 0"
          >
            <i class="el-icon-check" style="margin-right: 5px"></i>
            保存
          </el-button>
        </div>
      </div>

      <el-table
        :data="table_data"
        border
        height="600"
        class="target-value-table"
        :header-cell-style="{
          background: '#ebf4ff',
          color: '#1f2d3d',
          fontWeight: '600'
        }"
        ref="table"
      >
        <el-table-column
          prop="department_name"
          label="校区"
          width="150"
          align="center"
          fixed="left"
        />
        <el-table-column
          prop="employee_name"
          label="员工"
          width="150"
          align="center"
          fixed="left"
        />

        <el-table-column
          v-for="header in table_headers"
          :key="header.dateKey"
          :prop="header.dateKey"
          :label="header.label"
          width="80"
          align="center"
        >
          <template slot-scope="scope">
            <div
              @dblclick="startEdit(scope.row, header.dateKey)"
              class="editable-cell"
              :class="{
                'readonly-cell': !scope.row.editable,
                'modified-cell': isModified(scope.row, header.dateKey)
              }"
            >
              <el-input
                v-if="isEditing(scope.row, header.dateKey)"
                v-model="scope.row[header.dateKey]"
                size="mini"
                style="width: 60px"
                @blur="finishEdit(scope.row, header.dateKey)"
                @keyup.enter="finishEdit(scope.row, header.dateKey)"
                @keyup.esc="cancelEdit()"
                :ref="`editInput_${scope.$index}_${header.dateKey}`"
              />
              <span v-else class="cell-value">{{
                scope.row[header.dateKey] || ""
              }}</span>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 模版导入对话框 -->
    <TemplateImportDialog
      v-if="showImportDialog"
      :visible.sync="showImportDialog"
      @import-success="handleImportSuccess"
      :target_type="target_template[form.target_type]"
    />
  </div>
</template>

<script>
import targetValueApi from "@/api/targetValueKanBan";
import TemplateImportDialog from "./components/TemplateImportDialog.vue";
import schoolTree from "@/components/schoolTree/schoolTree.vue";

export default {
  name: "TargetValueKanBan",
  components: {
    TemplateImportDialog,
    schoolTree
  },
  data() {
    return {
      type_list: [
        { label: "市场专员", value: "responsible" },
        { label: "课程顾问", value: "advisor" },
        { label: "教务", value: "educator" },
        { label: "分校长", value: "school_master" }
      ],
      target_type_list: {},

      // 表单数据
      form: {
        date_range: this.getDefaultDateRange(),
        department_id: [],
        department_name: "",
        search_start: "",
        search_end: "",
        target_type: "responsible",
        fields: [],
        employee_id: "",
        employee_name: "",
        field: ""
      },
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            }
          }
          //   {
          //     text: "最近三个月",
          //     onClick(picker) {
          //       const end = new Date();
          //       const start = new Date();
          //       start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
          //       picker.$emit("pick", [start, end]);
          //     }
          //   }
        ]
      },

      // 加载状态
      save_loading: false,
      export_loading: false,

      // 编辑状态管理
      editingCell: null, // 当前正在编辑的单元格 {rowIndex, fieldKey}

      // 选项卡管理
      // 当前激活的选项卡

      // 对话框控制
      showImportDialog: false,

      // 表格数据
      table_data: [],
      school_flag: false,
      school_tree_visible: false,

      // 修改过的单元格记录
      modifiedCells: new Set(), // 存储修改过的单元格 "rowIndex_fieldKey"
      target_template: {
        responsible: "shichangzhuanyuan",
        advisor: "kechengguwen",
        educator: "jiaowu",
        school_master: "xiaozhang"
      }
    };
  },

  computed: {
    // 根据选择的日期范围生成表头
    table_headers() {
      if (!this.form.date_range || this.form.date_range.length !== 2) {
        return [];
      }

      // 确保日期字符串格式正确
      let startDateStr = this.form.date_range[0];
      let endDateStr = this.form.date_range[1];

      // 如果是Date对象，转换为字符串
      if (startDateStr instanceof Date) {
        startDateStr = this.formatDateToString(startDateStr);
      }
      if (endDateStr instanceof Date) {
        endDateStr = this.formatDateToString(endDateStr);
      }

      //   const startDate = new Date(startDateStr);
      //   const endDate = new Date(endDateStr);
      const startDate =
        new Date(this.form.search_start) || new Date(startDateStr);
      const endDate = new Date(this.form.search_end) || new Date(endDateStr);
      const headers = [];

      // 生成日期范围内的所有日期
      const currentDate = new Date(startDate);
      // eslint-disable-next-line no-unmodified-loop-condition
      while (currentDate <= endDate) {
        const day = currentDate.getDate();
        const month = currentDate.getMonth() + 1;
        const year = currentDate.getFullYear();
        const dateKey = `${year}-${month.toString().padStart(2, "0")}-${day
          .toString()
          .padStart(2, "0")}`;

        headers.push({
          day,
          month,
          year,
          dateKey,
          label: `${month}/${day}`
        });

        currentDate.setDate(currentDate.getDate() + 1);
      }

      return headers;
    }
  },

  created() {},
  async mounted() {
    await this.getTargetList();
  },
  watch: {
    "form.field": {
      handler(val) {
        console.log("form.field", val);
        if (val) {
          this.initTableData();
        }
      },
      deep: true
    },
    // 监听选项卡变化，重新加载数据
    "form.target_type": {
      handler(val) {
        if (val) {
          this.getTargetList();
        }
      }
    },
    "form.department_id": {
      handler() {
        this.getTargetList();
      }
    }
  },

  methods: {
    getTargetList() {
      targetValueApi
        .targetList({ target_type: this.form.target_type })
        .then((res) => {
          console.log("targetList", res);
          if (res.data.code === 0) {
            this.target_type_list = res.data.data ?? {};
            const keys = Object.keys(this.target_type_list);
            this.form.field = null;
            this.$nextTick(() => {
              // 下一帧赋值，确保变化被捕捉
              this.form.field = this.target_type_list[keys[0]];
            });

            this.$refs.table.doLayout();
            // this.initTableData();
          } else {
            this.$message.error(res.data.message);
          }
        })
        .catch(() => {
          // API调用失败时使用模拟数据
          //   this.table_data = this.generateMockTableData();
        });
    },
    // 获取默认日期范围（当前月份）
    getDefaultDateRange() {
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth();

      // 当前月份的第一天
      const firstDay = new Date(year, month, 1);
      // 当前月份的最后一天
      const lastDay = new Date(year, month + 1, 0);

      // 返回 yyyy-MM-dd 格式的字符串
      return [
        this.formatDateToString(firstDay),
        this.formatDateToString(lastDay)
      ];
    },

    // 将Date对象格式化为 yyyy-MM-dd 字符串
    formatDateToString(date) {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      return `${year}-${month}-${day}`;
    },

    // 初始化表格数据
    initTableData() {
      if (!this.$_has({ m: "targetValueKanBan", o: "indicatorList" })) return;
      if (!this.form.date_range || this.form.date_range.length !== 2) {
        return;
      }
      if (this.form.date_range) {
        this.form.search_start = this.form.date_range[0];
        this.form.search_end = this.form.date_range[1];
      } else {
        this.form.search_start = "";
        this.form.search_end = "";
      }
      if (this.form.target_type === "school_master") {
        this.form.employee_id = "";
        this.form.employee_name = "";
      }
      // 调用API获取数据
      targetValueApi
        .indicatorView(this.form)
        .then((res) => {
          if (res.data.code === 0) {
            this.table_data = res.data.data
              ? this.transformApiDataToTableData(res.data.data)
              : [];
            this.$refs.table.doLayout();
          } else {
            this.$message.error(res.data.message);
          }
        })
        .catch(() => {
          // API调用失败时使用模拟数据
          //   this.table_data = this.generateMockTableData();
        });
    },

    // 转换API数据为表格数据格式
    transformApiDataToTableData(apiData) {
      if (!apiData || !Array.isArray(apiData)) {
        return this.generateMockTableData();
      }

      return apiData.map((item) => {
        const rowData = {
          department_id: item.department_id,
          employee_id: item.employee_id,
          department_name: item.department_name,
          employee_name: item.employee_name,
          editable: this.isFieldEditable(item.field)
        };

        // 将values数组转换为以日期为key的对象
        if (item.values && Array.isArray(item.values)) {
          item.values.forEach((valueItem) => {
            rowData[valueItem.date] = valueItem.value;
          });
        }

        return rowData;
      });
    },

    // 判断字段是否可编辑
    isFieldEditable(field) {
      // 比率类字段设置为不可编辑（通常为计算字段）
      const ratioFields = [
        "channel_transfer_ratio",
        "invite_ratio",
        "try_listen_transfer_ratio",
        "valid_ratio"
      ];
      return !ratioFields.includes(field);
    },

    // 生成模拟表格数据（API调用失败时使用）
    generateMockTableData() {
      return [
        {
          target_name: "表单数量",
          field: "prompt_people",
          editable: true,
          ...this.generateDateRangeData()
        },
        {
          target_name: "正课新招人数",
          field: "normal_new_people",
          editable: true,
          ...this.generateDateRangeData()
        },
        {
          target_name: "当月到店试听人数",
          field: "visit_try_listen_people",
          editable: true,
          ...this.generateDateRangeData()
        },
        {
          target_name: "有效客户信息数量",
          field: "valid_people",
          editable: true,
          ...this.generateDateRangeData()
        },
        {
          target_name: "表单有效率",
          field: "valid_ratio",
          editable: false,
          ...this.generatePercentageRangeData()
        },
        {
          target_name: "邀约率",
          field: "invite_ratio",
          editable: false,
          ...this.generatePercentageRangeData()
        },
        {
          target_name: "渠道转化率",
          field: "channel_transfer_ratio",
          editable: false,
          ...this.generatePercentageRangeData()
        },
        {
          target_name: "试听转化率",
          field: "try_listen_transfer_ratio",
          editable: false,
          ...this.generatePercentageRangeData()
        }
      ];
    },

    // 根据日期范围生成数据
    generateDateRangeData() {
      //   const data = {};
      //   this.table_headers.forEach((header) => {
      //     data[header.dateKey] = Math.floor(Math.random() * 100) + 10;
      //   });
      //   return data;
    },

    // 根据日期范围生成百分比数据
    generatePercentageRangeData() {
      //   const data = {};
      //   this.table_headers.forEach((header) => {
      //     data[header.dateKey] = (Math.random() * 100).toFixed(0) + "%";
      //   });
      //   return data;
    },

    // 处理查询
    handleQuery() {
      console.log("查询条件:", this.form);
      //   this.$message.success("查询成功");
      this.initTableData();
      // 这里可以调用API获取数据
    },

    // 处理重置
    handleReset() {
      const defaultDateRange = this.getDefaultDateRange();
      this.form = {
        date_range: defaultDateRange,
        department_id: [],
        department_name: "",
        search_start: defaultDateRange[0],
        search_end: defaultDateRange[1],
        target_type: "responsible",
        fields: [],
        employee_id: "",
        employee_name: "",
        field: ""
      };
      this.form.fields = [];
      // 清除修改标记
      this.modifiedCells.clear();
      // this.initTableData();
    },

    // 处理保存
    handleSave() {
      this.save_loading = true;
      targetValueApi
        .indicatorUpdate({
          field: this.form.field,
          fields: this.form.fields,
          target_type: this.form.target_type
        })
        .then((res) => {
          if (res.data.code === 0) {
            this.save_loading = false;
            this.$message.success("保存成功");
            this.form.fields = [];
            // 清除修改标记
            this.modifiedCells.clear();
            this.initTableData();
          } else {
            this.save_loading = false;
            this.$message.error(res.data.message);
          }
        });
    },

    // 处理导出（打开模版导入对话框）
    handleExport() {
      this.showImportDialog = true;
    },

    // 处理导入成功
    handleImportSuccess() {
      this.$message.success("模版导入成功！");
      // 重新加载表格数据
      this.initTableData();
    },

    // 开始编辑单元格
    startEdit(row, fieldKey) {
      // 找到行索引
      const rowIndex = this.table_data.findIndex(
        (item) => item.target_name === row.target_name
      );

      // 如果是只读行，给出提示但仍允许编辑
      //   if (!row.editable) {
      //     this.$message.info("此为计算字段，修改后将按输入值显示");
      //   }

      this.editingCell = { rowIndex, fieldKey };

      // 下一帧聚焦输入框
      this.$nextTick(() => {
        const inputRef = `editInput_${rowIndex}_${fieldKey}`;
        const input = this.$refs[inputRef];
        if (input && input[0]) {
          input[0].focus();
          input[0].select();
        }
      });
    },

    // 判断是否正在编辑
    isEditing(row, fieldKey) {
      if (!this.editingCell) return false;
      const rowIndex = this.table_data.findIndex(
        (item) => item.target_name === row.target_name
      );
      return (
        this.editingCell.rowIndex === rowIndex &&
        this.editingCell.fieldKey === fieldKey
      );
    },

    // 判断单元格是否已修改
    isModified(row, fieldKey) {
      const rowIndex = this.table_data.findIndex(
        (item) => item.target_name === row.target_name
      );
      const cellKey = `${rowIndex}_${fieldKey}`;
      return this.modifiedCells.has(cellKey);
    },

    // 完成编辑
    finishEdit(row, fieldKey) {
      console.log(`${row.target_name} 的 ${fieldKey} 值变更为:`, row[fieldKey]);

      // 标记单元格为已修改
      const rowIndex = this.table_data.findIndex(
        (item) => item.target_name === row.target_name
      );
      const cellKey = `${rowIndex}_${fieldKey}`;
      this.modifiedCells.add(cellKey);

      this.editingCell = null;

      // 调用API保存单个单元格的数据
      this.saveCellData(row, fieldKey);
    },

    // 保存单元格数据
    saveCellData(row, dateKey) {
      const saveData = {
        target_date: dateKey,
        value: row[dateKey],
        department_id: row.department_id,
        employee_id: row.employee_id
      };

      console.log("保存数据:", saveData);
      this.form.fields.push(saveData);
    },

    // 取消编辑
    cancelEdit() {
      this.editingCell = null;
    },

    // 处理选项卡切换
    handleTabClick(tab) {
      console.log("切换到选项卡:", tab.name, tab.label);
      this.form.field = tab.name;
      // 数据会通过watch自动重新加载
    },

    // 处理单元格值变更
    handleCellChange(row, field) {
      console.log(`${row.target_name} 的 ${field} 值变更为:`, row[field]);
      // 这里可以实时保存或标记为已修改
      this.saveCellData(row, field);
    }
  }
};
</script>

<style lang="less" scoped>
.container {
  height: 100vh;
  background-color: #f5f7fa;
  padding: 6px;
  box-sizing: border-box;
}

// 筛选条件样式
.tg-search {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 6px 0 rgba(204, 208, 217, 0.35);
  padding: 16px;
  position: relative;

  .filter-form {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;

    ::v-deep .el-form-item {
      margin-bottom: 0;
      margin-right: 16px;

      .el-form-item__label {
        font-size: 14px;
        color: #1f2d3d;
        font-weight: 500;
      }
    }
  }

  .button-group {
    display: flex;
    gap: 12px;
    margin-left: auto;

    .save-button,
    .export-button {
      background-color: #28a745;
      border-color: #28a745;
      color: white;
      padding: 8px 16px;
      border-radius: 4px;
      font-size: 14px;

      &:hover {
        background-color: #218838;
        border-color: #1e7e34;
      }

      &:focus {
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
      }
    }
  }
}
::v-deep .el-table__fixed-header-wrapper {
  left: 0 !important;
}
::v-deep .el-table__fixed-body-wrapper {
  left: 0 !important;
}
// 表格头部区域
.table-header {
  //   background: #fff;
  //   border-radius: 4px;
  //   box-shadow: 0 2px 6px 0 rgba(204, 208, 217, 0.35);
  //   padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .tab-section {
    flex: 1;
  }

  .action-buttons {
    .save-button {
      background-color: #28a745;
      border-color: #28a745;
      color: white;
      padding: 8px 20px;
      border-radius: 4px;
      font-size: 14px;
      font-weight: 500;

      &:hover {
        background-color: #218838;
        border-color: #1e7e34;
      }

      &:focus {
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
      }
    }
  }
}

// 表格容器
.tg-table__box {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 6px 0 rgba(204, 208, 217, 0.35);
  padding: 16px;
  .el-table {
    padding: 0 !important;
  }

  .target-value-table {
    ::v-deep .el-table__header {
      th {
        background-color: #ebf4ff !important;
        color: #1f2d3d !important;
        font-weight: 600 !important;
        font-size: 14px;
        // border-bottom: 2px solid #2d80ed;
      }
    }

    ::v-deep .el-table__body {
      .el-table__row {
        &:nth-child(odd) {
          //   background-color: #fafbfc;
        }

        // &:hover {
        //   background-color: #ebf4ff !important;
        // }
      }

      .cell {
        font-size: 14px;
        color: #475669;
        padding: 8px;
      }
    }

    // 固定列样式
    ::v-deep .el-table__fixed-left {
      .el-table__fixed-body-wrapper {
        background-color: #fff;
      }
    }

    // 可编辑单元格样式
    .editable-cell {
      min-height: 32px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      padding: 2px;
      border-radius: 2px;
      transition: all 0.2s ease;

      &:hover {
        background-color: rgba(45, 128, 237, 0.1);
        border: 1px dashed #2d80ed;
      }

      &.readonly-cell {
        &:hover {
          //   background-color: rgba(255, 193, 7, 0.1);
          border: 1px dashed #1f6fd1;
        }
      }

      .cell-value {
        width: 100%;
        text-align: center;
        padding: 4px 8px;
        min-height: 20px;
        line-height: 20px;
      }
    }
  }
}

// 通用按钮样式
.tg-button--primary {
  height: 32px;
  font-family: "PingFangSC-Light, sans-serif, Arial";
  border-radius: 4px;
  padding: 0 22px;
  font-weight: normal;
  background-color: #2d80ed;
  border-color: #2d80ed;

  &:hover {
    background-color: #1f6fd1;
    border-color: #1f6fd1;
  }
}

.tg-button__icon {
  display: flex;
  align-items: center;

  .tg-button__icon--large {
    width: 14px;
    height: 14px;
    margin-right: 8px;
  }
}

// 工具类
.tg-box--margin {
  width: 100%;
  margin-bottom: 6px;
}
// Tab样式优化
::v-deep .el-tabs {
  .el-tabs__header {
    .el-tabs__nav-wrap {
      border-radius: 8px;
      padding: 4px;

      .el-tabs__nav {
        border: none;
        background-color: #f3f4f6;

        .el-tabs__item {
          border: none;
          background: transparent;
          color: #6b7280;
          font-weight: 500;
          padding: 0 16px;
          margin-right: 4px;
          border-radius: 6px;
          transition: all 0.2s ease;

          &:hover {
            color: #374151;
            background-color: rgba(59, 130, 246, 0.1);
          }

          &.is-active {
            background-color: #3b82f6;
            color: #ffffff;
            font-weight: 600;
          }
        }
      }

      .el-tabs__active-bar {
        display: none;
      }
    }
  }
}
::v-deep .modified-cell {
  background-color: rgb(254 252 232);
}
::v-deep .is-focus::after {
  border: none !important;
}
::v-deep .is-active::after {
  border: none !important;
}
</style>
