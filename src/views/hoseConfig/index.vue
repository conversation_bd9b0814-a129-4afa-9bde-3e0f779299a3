<!-- 仓库设置 -->
<template>
  <div class="labelListPage container">
    <!-- <pageHeader></pageHeader> -->
    <tg-search
      :searchTitle.sync="search_title"
      :form.sync="searchForm"
      :showNum="3"
      @reset="reset"
      @search="search"
    ></tg-search>

    <div class="tg-table__box">
      <div class="tg-box--border"></div>
      <el-table
        ref="table"
        :data="list.data"
        tooltip-effect="dark"
        class="tg-table"
        border
      >
        <el-table-column
          v-for="(item, index) in list.tableColumn"
          :key="index"
          :prop="item.prop"
          :label="item.label"
        >
          <template slot-scope="scope">
            <div v-if="item.prop === 'departments'">
              <el-button
                v-has="{ m: 'hoseConfig', o: 'view' }"
                v-if="scope.row.department_ids.length"
                type="text"
                size="small"
                @click="handler('view', scope.row)"
              >
                查看
              </el-button>
            </div>
            <div v-else-if="item.prop === 'yop_channel'">
              {{ scope.row.yop_channel | yop_channel }}
            </div>
            <span v-else>
              {{ scope.row[scope.column.property] }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <!-- v-if="scope.row.status == 2" -->
            <el-button
              @click="handler('edit', scope.row)"
              type="text"
              size="small"
              class="tg-text--blue tg-span__divide-line"
              v-has="{ m: 'hoseConfig', o: 'bind' }"
              >修改关联校区</el-button
            >
          </template>
        </el-table-column>
        <template slot="empty">
          <div style="margin-top: 15%">
            <TgLoading v-if="loading"></TgLoading>
            <div class="empty-container" v-else>暂无数据～</div>
          </div>
        </template>
      </el-table>
      <div class="tg-pagination">
        <span class="el-pagination__total">共 {{ list.total }} 条</span>
        <el-pagination
          background
          layout="sizes,prev,pager,next,jumper"
          :total="list.total"
          :page-size.sync="list.pageSize"
          :current-page.sync="list.page"
          @current-change="currentChange"
          @size-change="sizeChange"
          :page-sizes="[10, 20, 50, 100]"
        >
        </el-pagination>
      </div>
    </div>
    <saveSchoolTree
      :checkedControlSchool="true"
      :flag.sync="schools_authorize_show"
      :id.sync="department_id"
      :name.sync="department_name"
      :required="true"
      :has_modal="true"
      :type="isLook"
      @confirm="confirmSchool"
      @close="closeSchool"
      :use_store_options="false"
      :is_center="true"
    >
    </saveSchoolTree>
  </div>
</template>

<script>
import hoseConfigApi from "@/api/hoseConfig";
import { yop_channel_list } from "@/public/dict";
import saveSchoolTree from "@/components/schoolTree/saveSchoolTree.vue";
export default {
  components: {
    saveSchoolTree
  },
  data() {
    return {
      schools_authorize_show: false,
      isLook: "",
      form: {
        department_id: "",
        department_name: ""
      },
      search_title: [
        {
          label: "主体名称",
          props: "item_name",
          type: "input",
          show: true
        },
        {
          props: "department_id",
          label: "关联校区",
          show: true,
          selectOptions: [],
          type: "school", // 表单类型
          school_choose_type: "radio", // 校区是否单选
          use_store_options: true
        }
      ],

      searchForm: {
        item_name: "",
        department_id: ""
      },
      currentRow: {},
      department_id: [],
      department_name: [],
      list: {
        data: [],
        tableColumn: [
          {
            prop: "item_name",
            label: "主体名称"
          },
          {
            prop: "item_code",
            label: "主体编码"
          },

          {
            prop: "departments",
            label: "关联校区"
          },
          {
            prop: "binding_at",
            label: "绑定时间"
          },
          {
            prop: "operator",
            label: "操作人"
          }
        ],
        total: 0,
        pageSize: 10,
        page: 1
      },
      loading: false
    };
  },
  mounted() {
    this.getData();
  },
  filters: {
    yop_channel(val) {
      if (val) {
        return yop_channel_list.find((item) => item.id === val).name;
      } else {
        return "";
      }
    }
  },
  methods: {
    currentChange(page) {
      this.list.page = page;
      this.getData();
    },
    sizeChange(val) {
      this.list.page = 1;
      this.list.pageSize = val;
      this.getData();
    },
    getData() {
      this.list.data = [];
      this.loading = true;
      const data = {
        page: this.list.page,
        page_size: this.list.pageSize,
        ...this.searchForm,
        item_type: "frst"
      };

      hoseConfigApi.getHoseList(data).then((res) => {
        if (res.data.code === 0) {
          this.loading = false;
          this.list.data = res.data.data.results;
          this.list.total = res.data.data.count;
        }
      });
    },
    confirmSchool() {
      hoseConfigApi
        .dimensionBind({
          department_ids: this.department_id.split(","),
          item_id: this.currentRow.item_id
        })
        .then((res) => {
          if (res.data.code === 0) {
            this.$message.success("绑定成功");
            this.schools_authorize_show = false;
            this.getData();
          } else {
            this.$message.error(res.data.message);
          }
        });
    },
    closeSchool() {
      this.department_id = [];
      this.department_name = [];
    },
    handler(type, row) {
      if (type === "view") {
        this.isLook = "isLook";
      } else if (type === "edit") {
        this.currentRow = row;
        this.isLook = "chooseSchool";
      }
      this.schools_authorize_show = true;
      this.department_id = row.department_ids;
      this.department_name = row.departments;
    },

    // 重置查询条件
    reset() {
      this.searchForm = {
        item_name: "",
        department_id: ""
      };
      this.list.page = 1;
      this.list.pageSize = 10;
      this.search();
    },
    // 查询信息
    search() {
      this.list.page = 1;
      this.getData();
    }
  }
};
</script>

<style lang="less">
.labelListPage {
  padding-top: 16px;
  box-sizing: border-box;
  .dialog_content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  .button-container {
    width: 100%;
  }
  ::v-deep .el-table__body-wrapper {
    height: calc(100% - 46px);
    .empty-container {
      position: absolute;
      top: 50%;
      left: 50%;
    }
    .loading-container {
      position: absolute;
      top: 30%;
      left: 1%;
      background: transparent;
      .box {
        height: 100%;
      }
    }
  }
}
</style>
