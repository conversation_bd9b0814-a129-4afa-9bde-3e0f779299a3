module.exports = {
  root: true,
  env: {
    node: true
  },
  extends: ["standard", "plugin:vue/essential", "eslint:recommended", "prettier"],
  plugins: ["vue", "prettier"],
  parserOptions: {
    parser: "@babel/eslint-parser",
  },
  rules: {
    // "no-console": process.env.NODE_ENV === "production" ? "error" : "off",
    "no-debugger": process.env.NODE_ENV === "production" ? "error" : "off",
    "camelcase": "off", //跳过驼峰命名检测
    "array-callback-return": "off",
    "prettier/prettier": "error",
    "no-unused-vars": "error",
    "no-undef": "off",
    "eqeqeq": "error",
    "vue/multi-word-component-names": "off",
    "vue/no-mutating-props": "off"
  },
  globals: {
    "TXLivePusher": true,
    "jQuery": true,
    "$": true,
    TEduBoard: true,
    WGo: true,
    XLSX: true
  }
};
